module "kms" {
  source         = "git::ssh://***************************/FP20/infrastructure/terraform-modules/emh-aws-kms?ref=1.1.0"
  key_alias_name = local.repo
  role_name      = local.role_name

  services = ["logs.amazonaws.com"]
  role_arns = [
    data.aws_iam_role.glue_role.arn,
    data.aws_iam_role.ecs_task_role.arn,
    data.aws_iam_role.auditor_role.arn,
  ]
}

module "ssm_parameters_ecs" {
  for_each = local.params
  source   = "git::ssh://***************************/FP20/infrastructure/terraform-modules/emh-aws-ssm-parameter?ref=1.0.0"

  category    = each.value.category
  component   = each.value.component
  description = each.value.description
  param_name  = each.value.param_name
  repo        = each.value.repo
  type        = each.value.type

  kms_key_arn = module.kms.key_arn
}

module "ssm_parameters_api_consumer" {
  for_each = local.api_consumer_params
  source   = "git::ssh://***************************/FP20/infrastructure/terraform-modules/emh-aws-ssm-parameter?ref=1.0.0"

  category    = each.value.category
  component   = each.value.component
  description = each.value.description
  param_name  = each.value.param_name
  repo        = each.value.repo
  type        = each.value.type

  kms_key_arn = module.kms.key_arn
}

module "alb" {
  source = "git::ssh://***************************/FP20/infrastructure/terraform-modules/emh-aws-alb?ref=1.0.0"

  fqdn                 = var.vehicle_service_domain
  dns_hosted_zone_name = var.dns_hosted_zone_name
  load_balancer_name   = "${local.repo}-alb"

  target_group    = local.alb.target_group
  listeners       = local.alb.listeners
  security_groups = local.alb.security_groups

  vpc_id  = data.aws_vpc.vpc.id
  subnets = data.aws_subnets.private.ids
}

module "ibm_api_gw_nlb" {
  source = "git::ssh://***************************/FP20/infrastructure/terraform-modules/emh-aws-nlb?ref=1.0.1"

  alb_arn              = module.alb.alb_arn
  app                  = local.repo
  dns_hosted_zone_name = var.dns_hosted_zone_name
  private_dns_name     = var.vehicle_service_domain
  fqdn                 = var.vehicle_service_domain

  ibm_gateway_principal = "arn:aws:iam::599822918172:root"
  target_group          = local.nlb.target_group
  security_groups       = local.nlb.security_groups

  vpc_id  = data.aws_vpc.vpc.id
  subnets = data.aws_subnets.private.ids
}

module "ecc_ibm_gw" {
  source = "../modules/ibmgy"

  alb_arn              = module.alb.alb_arn
  app                  = local.ecc.app
  dns_hosted_zone_name = var.dns_hosted_zone_name
  private_dns_name     = "${local.ecc.app}.${var.dns_hosted_zone_name}"
  fqdn                 = "${local.ecc.app}.${var.dns_hosted_zone_name}"

  ibm_gateway_principal = "arn:aws:iam::599822918172:root"
  target_group          = local.ecc.target_group
  security_groups       = local.ecc.security_groups

  vpc_id  = data.aws_vpc.vpc.id
  subnets = data.aws_subnets.private.ids
}

# module is vtstamm and app is only vts because of string constrains (too long with vtstamm dns name)
module "vtstamm_ibm_gw" {
  source = "../modules/ibmgy"

  alb_arn              = module.alb.alb_arn
  app                  = local.vts.app
  dns_hosted_zone_name = var.dns_hosted_zone_name
  private_dns_name     = "${local.vts.app}.${var.dns_hosted_zone_name}"
  fqdn                 = "${local.vts.app}.${var.dns_hosted_zone_name}"

  ibm_gateway_principal = "arn:aws:iam::599822918172:root"
  target_group          = local.vts.target_group
  security_groups       = local.vts.security_groups

  vpc_id  = data.aws_vpc.vpc.id
  subnets = data.aws_subnets.private.ids
}

module "ecs_service" {
  source  = "git::ssh://***************************/FP20/infrastructure/terraform-modules/emh-aws-ecs-service?ref=1.2.0"
  vpc_id  = data.aws_vpc.vpc.id
  subnets = data.aws_subnets.private.ids

  repo        = local.repo
  role_name   = local.role_name
  cluster_arn = data.aws_ecs_cluster.ecs_cluster.arn

  container_cpu    = var.container_cpu
  container_memory = var.container_memory
  container_image  = local.ecs.container_image

  desired_count       = var.container_desired_count
  app_start_period    = 180
  healthcheck_command = "curl -f http://localhost:8080/api/vs/actuator/health || exit 1"

  env_variables = local.ecs.environments
  secrets       = local.ecs.secrets

  health_check_grace_period_seconds = 180

  service_registry_config = [
    {
      registry_arn   = aws_service_discovery_service.this.arn
      container_name = local.repo
    }
  ]

  load_balancer_config = local.ecs.load_balancer_config
  security_groups      = local.ecs.security_groups
  port_mapping         = local.ecs.port_mapping

  firehose_cloudwatch_role_name = var.firehose_cloudwatch_role_name
  firehose_s3_role_name         = var.firehose_s3_role_name
  alarm_actions                 = local.ecs.alarm_actions

  stream_logs_to_newrelic = var.stream_logs_to_newrelic
}

module "cloudwatch" {
  source     = "../modules/cloudwatch"
  dashboards = local.dashboards
}

module "postgres_secrets_kms" {
  source         = "git::ssh://***************************/FP20/infrastructure/terraform-modules/emh-aws-kms?ref=1.1.0"
  key_alias_name = "${local.repo}/athena_log_group"
  role_name      = local.role_name

  services = ["logs.amazonaws.com", "lambda.amazonaws.com"]
}

module "db_secret" {
  source      = "git::ssh://***************************/FP20/infrastructure/terraform-modules/emh-aws-secrets?ref=1.0.0"
  repo        = local.repo
  category    = "password"
  component   = "athena-rds"
  secret_name = "athena_vehicledata_user"
  description = "Vehicledata database read only user for athena postgres connector"
  kms_key_arn = module.postgres_secrets_kms.key_arn
  lambda_arn  = data.aws_lambda_function.postgres_secrets_rotation.arn
  tags = {
    Rotation = "postgres"
  }
}

module "rds_app_user_db_secret" {
  source      = "git::ssh://***************************/FP20/infrastructure/terraform-modules/emh-aws-secrets?ref=1.0.0"
  repo        = local.repo
  category    = "password"
  component   = "rds"
  secret_name = "app_user"
  description = "Database user to connect via RDS query console"
  kms_key_arn = module.postgres_secrets_kms.key_arn
  lambda_arn  = data.aws_lambda_function.postgres_secrets_rotation.arn
  tags = {
    Rotation = "postgres"
  }
}

module "athena_connector" {

  source        = "git::ssh://***************************/FP20/infrastructure/terraform-modules/emh-aws-athena-postgres-connector?ref=1.0.0"
  repo          = local.repo
  ci_role_name  = local.role_name
  function_name = "${local.repo}-emh-athena-postgres-connector"
  kms_key_arn   = module.postgres_secrets_kms.key_arn

  postgres = {
    host        = data.aws_rds_cluster.aurora_postgres_cluster.endpoint
    port        = data.aws_rds_cluster.aurora_postgres_cluster.port
    database    = "vehicleservice"
    secret_name = module.db_secret.secret_name
  }

  vpc_id              = data.aws_vpc.vpc.id
  subnet_ids          = data.aws_subnets.private.ids
  security_groups     = local.athena.security_groups
  athena_spill_bucket = local.athena.spill_bucket
}

module "s3_bucket_gobd" {
  source            = "../modules/gobd"
  repo              = local.repo
  ci_role_name      = local.role_name
  kms_key_arn       = module.kms.key_arn
  retention_in_days = var.retention_in_days
  s3_bucket_gobd    = var.s3_bucket_archive
  glue_role_name    = local.gobd.glue_role_name
  task_role_arn     = data.aws_iam_role.ecs_task_role.arn
  auditor_role_arn  = data.aws_iam_role.auditor_role.arn
  database_name     = "${local.repo}-archive"
}

module "s3_bucket_migration" {
  source              = "../modules/migration"
  ci_role_name        = local.role_name
  kms_key_arn         = module.kms.key_arn
  task_role_arn       = data.aws_iam_role.ecs_task_role.arn
  s3_bucket_migration = var.s3_bucket_fms_migration
}

module "s3_bucket_vehicle_transfer_gobd" {
  source            = "../modules/gobd"
  repo              = local.repo
  ci_role_name      = local.role_name
  kms_key_arn       = module.kms.key_arn
  retention_in_days = var.retention_in_days
  s3_bucket_gobd    = var.s3_bucket_vehicle_transfer_archive
  glue_role_name    = local.gobd.glue_role_name
  task_role_arn     = data.aws_iam_role.ecs_task_role.arn
  auditor_role_arn  = data.aws_iam_role.auditor_role.arn
  database_name     = "${local.repo}-vehicle-transfer-archive"
}

module "s3_bucket_vehicle_history_gobd" {
  source            = "../modules/gobd"
  repo              = local.repo
  ci_role_name      = local.role_name
  kms_key_arn       = module.kms.key_arn
  retention_in_days = var.retention_in_days
  s3_bucket_gobd    = var.s3_bucket_vehicle_history_archive
  glue_role_name    = local.gobd.glue_role_name
  task_role_arn     = data.aws_iam_role.ecs_task_role.arn
  auditor_role_arn  = data.aws_iam_role.auditor_role.arn
  database_name     = "${local.repo}-vehicle-history-archive"
}

module "s3_bucket_vehicle_sales_gobd" {
  source            = "../modules/gobd"
  repo              = local.repo
  ci_role_name      = local.role_name
  kms_key_arn       = module.kms.key_arn
  retention_in_days = var.retention_in_days
  s3_bucket_gobd    = var.s3_bucket_vehicle_sales_archive
  glue_role_name    = local.gobd.glue_role_name
  task_role_arn     = data.aws_iam_role.ecs_task_role.arn
  auditor_role_arn  = data.aws_iam_role.auditor_role.arn
  database_name     = "${local.repo}-vehicle-sales-archive"
}

module "s3_bucket_legalhold" {
  source              = "../modules/legalhold"
  ci_role_name        = local.role_name
  kms_key_arn         = module.kms.key_arn
  s3_bucket_legalhold = var.s3_bucket_legal_hold
  task_role_arn       = data.aws_iam_role.ecs_task_role.arn
}

module "s3_bucket_vehicle_sales_legalhold" {
  source              = "../modules/legalhold"
  ci_role_name        = local.role_name
  kms_key_arn         = module.kms.key_arn
  s3_bucket_legalhold = var.s3_bucket_vehicle_sales_legal_hold
  task_role_arn       = data.aws_iam_role.ecs_task_role.arn
}

module "athena_named_queries" {
  source    = "../modules/athena"
  database  = "${local.repo}-archive"
  workgroup = module.athena_connector.athena_workgroup_id
}

module "predefined_autoscaling" {
  source               = "git::ssh://***************************/FP20/infrastructure/terraform-modules/emh-aws-autoscaling?ref=1.0.0"
  min_desired_capacity = var.container_desired_count
  max_desired_capacity = 15
  cluster_name         = data.aws_ecs_cluster.ecs_cluster.cluster_name
  service_name         = local.repo
  auto_scaling_conf = [
    {
      name                   = "MemoryAutoscaling"
      policy_type            = "TargetTrackingScaling"
      predefined_metric_type = "ECSServiceAverageMemoryUtilization"
      target_value           = 80
      scale_in_cooldown      = 60
      scale_out_cooldown     = 300
    },
    {
      name                   = "CpuAutoscaling"
      policy_type            = "TargetTrackingScaling"
      predefined_metric_type = "ECSServiceAverageCPUUtilization"
      target_value           = 80
      scale_in_cooldown      = 60
      scale_out_cooldown     = 300
    },
    {
      name                   = "RequestAutoscaling"
      policy_type            = "TargetTrackingScaling"
      predefined_metric_type = "ALBRequestCountPerTarget"
      target_value           = 1000
      scale_in_cooldown      = 60
      scale_out_cooldown     = 300
      # https://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-applicationautoscaling-scalingpolicy-predefinedmetricspecification.html
      resource_label = join("/", [
        "app",
        element(split("app/", data.aws_alb.ui_alb.arn), 1),
        "targetgroup",
        element(split("targetgroup/", data.aws_alb_target_group.ui_target_group.arn), 1)
      ])
    },

  ]
}
