variable "commit_hash" {
  type        = string
  description = "The commit hash of the code to deploy"
}

variable "vehicle_service_domain" {
  type        = string
  description = "Domain for the vehicle service"
}

variable "dns_hosted_zone_name" {
  type        = string
  description = "DNS hosted zone for the vehicle service"
}

variable "container_desired_count" {
  type        = number
  description = "Number of containers to run inside the ECS service"
  default     = 2
}

variable "firehose_cloudwatch_role_name" {
  type        = string
  description = "Role name to send logs from cloudwatch to firehose data stream"
}

variable "firehose_s3_role_name" {
  type        = string
  description = "Role name to ship logs from firehose to compliance s3 bucket"
}

variable "retention_in_days" {
  description = "number of days to retain"
  type        = number
}

variable "s3_bucket_archive" {
  type        = string
  description = "bucket name"
}

variable "s3_bucket_vehicle_transfer_archive" {
  type        = string
  description = "bucket name for vehicle transfer to archive"
}

variable "s3_bucket_vehicle_history_archive" {
  type        = string
  description = "bucket name for vehicle history to archive"
}

variable "s3_bucket_vehicle_sales_archive" {
  type        = string
  description = "bucket name for archiving vehicle sales"
}

variable "s3_bucket_legal_hold" {
  type        = string
  description = "S3 bucket name for legal holds"
}

variable "s3_bucket_vehicle_sales_legal_hold" {
  type        = string
  description = "Vehicle sales S3 bucket name for legal holds"
}

variable "fms_migration_enabled" {
  type        = string
  description = "flag to determine if FMS migration module is active"
}

variable "s3_bucket_fms_migration" {
  type        = string
  description = "S3 bucket name for the FMS migration"
}

variable "env" {
  type        = string
  description = "Current environment"
}

variable "oidc_token_url" {
  type = string
  #TODO: Add description
  description = ""
}

variable "pvh_base_url" {
  type = string
  #TODO: Add description
  description = ""
}

variable "pvh_client_id" {
  type = string
  #TODO: Add description
  description = ""
}

variable "oauth2_issuer_uri" {
  type = string
  #TODO: Add description
  description = ""
}

variable "oauth2_token_uri" {
  type        = string
  description = "OAUTH2 uri to fetch token for m2m communication"
}

variable "oauth2_vehicle_audience" {
  type = string
  #TODO: Add description
  description = ""
}

variable "oauth2_dm_audience" {
  type = string
  #TODO: Add description
  description = ""
}

variable "vehicle_registration_base_uri" {
  type = string
  #TODO: Add description
  description = ""
}

variable "vehicle_registration_base_url" {
  type = string
  #TODO: Add description
  description = ""
}

variable "logout_uri" {
  type = string
  #TODO: Add description
  description = ""
}

variable "oauth2_vehicle_registration_app_id" {
  type = string
  #TODO: Add description
  description = ""
}

variable "app_domain" {
  type        = string
  description = "Domain name of the fleet vehicle management app"
}

variable "kafka_user" {
  type = string
  #TODO: Add description
  description = ""
}

variable "kafka_bootstrap_servers" {
  type        = string
  description = "Kafka Bootstrap Servers for the Spring Kafka Consumers"
}

variable "kafka_enable_pvh_ingestion" {
  type = string
  #TODO: Add description
  description = ""
}

variable "kafka_pvh_stop_on_error" {
  type        = string
  description = "feature toggle to stop pvh kafka consumer immediately in case of error"
}

variable "kafka_auto_offset_reset" {
  type        = string
  description = "feature toggle to tell kafka consumer where to start listening, values: earliest, latest or none"
}

variable "timestamp_filtering_disable_after" {
  type        = string
  description = "the filtering will be disabled after this date/time (2024-12-31T08:00:00Z) and allow all records through"
}

variable "kafka_pvh_consumer_group_id" {
  type        = string
  description = "the KAFKA consumer group ID"
}

variable "enable_albheaders_endpoint_for_testing" {
  type        = string
  description = "Exposes an endpoint that exposes x-amzn-oidc-accesstoken and x-amzn-oidc-data JWT tokens for local-testing"
}

variable "allow_overwrite_of_groups_from_request_header" {
  type        = string
  description = "A developer-environment only feature-toggle to allow user-groups to be defined from the HTTP request, and not from the IDP"
}

variable "vehicle_archive_enable_scheduler" {
  type        = string
  description = "whether to enable or disable quartz scheduler"
  default     = "true"
}

variable "vehicle_archive_cron_schedule" {
  type        = string
  description = "the cron schedule to trigger archive service"
  default     = "0 0 0 1 * ?"
}

variable "vehicle_registration_archive_client_id" {
  type    = string
  default = "consumer client for vehicle-registration app. Required for archival"
}

variable "cost_center_update_cron_schedule" {
  type        = string
  description = "the cron scheduler to trigger cost center update to PACE"
  default     = "0 0 0 1 * ?"
}

variable "cost_center_update_preparation_cron_schedule" {
  type        = string
  description = "the cron scheduler to trigger preparation of cost center for update to PACE"
  default     = "0 0 0 L * ?"
}

variable "vehicle_registration_client_id" {
  type    = string
  default = "consumer client for vehicle-registration app"
}

variable "vehicle_registration_legalhold_client_id" {
  type    = string
  default = "consumer client for vehicle-registration app. Required for Legal Hold"
}

variable "vehicle_location_client_id" {
  type    = string
  default = "client id for vehicle location client"
}

variable "vehicle_location_scope" {
  type    = string
  default = "scope for vehicle location client"
}

variable "vehicle_location_azure_tenant_id" {
  type    = string
  default = "azure tenant id for vehicle location"
}

variable "entra_id_azure_tenant_id" {
  type        = string
  description = "azure tenant id"
}

variable "enable_entra_id_scheduler" {
  type        = bool
  description = "Feature Flag whether to enable entraId sync"
}

variable "entra_id_administrative_unit_id" {
  type        = string
  description = "Administrative Unit ID that provides the user-groups for FVM application"
}

variable "entra_id_app_role_assignment_id" {
  type        = string
  description = "ObjectID in Azure Local Directory for FVM App. This can be found on the App that is registered in Azure"
}

variable "entra_id_oauth2_client_id" {
  type        = string
  description = "client ID to connect to Entra ID"
}

variable "entra_id_sync_cron_schedule" {
  type        = string
  description = "Quartz Cron schedule to execute the sync job with Entra ID"
}

variable "vehicle_location_base_url" {
  type    = string
  default = "base url for vehicle location"
}

variable "object_location_updated_topic" {
  type    = string
  default = "object_location_updated topic name to listen"
}

variable "object_location_updated_topic_group_id" {
  type    = string
  default = "group id for the object_location_updated_topic"
}

variable "vehicle_location_vls_kafka_integration_enabled" {
  type        = bool
  description = "flag to determine if kafka integration with VLS is enabled"
}

variable "location_updated_topic" {
  type    = string
  default = "location updated topic name to listen"
}

variable "location_updated_topic_group_id" {
  type    = string
  default = "group id for the location_updated_topic"
}

variable "location_deleted_topic" {
  type    = string
  default = "location_deleted topic name to listen"
}

variable "location_deleted_topic_group_id" {
  type    = string
  default = "group id for the location_deleted_topic"
}

variable "employee_base_url" {
  type    = string
  default = "base url when connection to employee provider"
}

variable "employee_api_client_id" {
  type    = string
  default = "client id used for authentication when connecting to employee provider"
}

variable "employee_api_scope" {
  type    = string
  default = "api scope used for authentication when connecting to employee provider"
}

variable "employee_oauth_token_uri" {
  type    = string
  default = "IDP used for authentication when connecting to employee provider"
}

variable "cap_oauth2_client_id" {
  type        = string
  description = "client id to get token from PPN"
}

variable "ppn_host" {
  type        = string
  description = "ppn host"
}

variable "cap_ibm_api_gateway_client_id" {
  type        = string
  description = "client id to connect to C@P via ibm api gateway"
}

variable "cap_base_url" {
  type        = string
  description = "base url for c@p"
}

variable "cap_region" {
  type        = string
  description = "c@p region"
}

variable "planned_vehicle_transfer_initialization_schedule" {
  type        = string
  description = "the cron schedule to trigger planned vehicle transfer initialization job"
  default     = "0 0 0 1 * ?"
}


variable "feature_flags_read_vehicle_person_enabled" {
  type        = bool
  description = "feature-flag to enable read vehicle person functionality within vehicle-person module"
}



variable "container_cpu" {
  type        = number
  description = "container cpu"
}


variable "container_memory" {
  type        = number
  description = "container memory"
}

variable "license_plate_topic" {
  type        = string
  description = "kafka topic for consuming license plate update event from vehicle registration"
}

variable "license_plate_group_id" {
  type        = string
  description = "group id for the license_plate_topic"
}

variable "license_plate_kafka_consumers" {
  type        = bool
  description = "flag to determine if to consume msg vehicle registration"
}

variable "vehicle_transfer_delivery_lead_time" {
  type        = number
  description = "delivery lead time in working days"
}

variable "pre_delivery_inspection_lead_time" {
  type        = number
  description = "pre delivery inspection lead time in working days"
}

variable "number_of_damages_enable" {
  type        = bool
  description = "flag to determine if to consume msg from damage management number of damages"
}

variable "number_of_damages_group_id" {
  type        = string
  description = "group id for number of damages"
}

variable "number_of_damages_topic" {
  type        = string
  description = "topic name for number_of_damages"
}
variable "pdi_ordering_email_schedule" {
  type        = string
  description = "the cron schedule to trigger pdi ordering email job"
  default     = "0 0 0 1 * ?"
}

variable "pdi_ordering_recipient_to_email_address" {
  type        = string
  description = "comma separated email address in to"
}

variable "pdi_ordering_recipient_cc_email_address" {
  type        = string
  description = "comma separated email address in cc"
}

variable "pdi_ordering_sender_email_address" {
  type        = string
  description = "email address of sender"
}

variable "vehicle_transfer_delivery_sender_email_address" {
  type        = string
  description = "email address of sender for vehicle transfer delivery email"
}

variable "smtp_host" {
  type        = string
  description = "smtp host"
}

variable "smtp_port" {
  type        = number
  description = "smtp port"
}

variable "smtp_username" {
  type        = string
  description = "smtp username"
}

variable "dmsi_base_uri" {
  type        = string
  description = "DMSI Service Base URI"
}

variable "vehicle_campaigns_enabled" {
  type        = bool
  description = "DMSI Sync of vehicle campaign module is enabled"
}

variable "rwil_idp_host" {
  type        = string
  description = "DMSI Service RWIL IDP Host "
}

variable "dmsi_oauth2_client_id" {
  type        = string
  description = "DMSI OAUTH2 Client ID"
}

variable "vehicle_status_recalculation_cron" {
  type        = string
  description = "the cron schedule to trigger vehicle status recalculation job"
  default     = "0 0 0 1 * ?"
}

variable "ms_booking_client_client_id" {
  type        = string
  description = "azure client id for ms_booking_client"
}

variable "ms_booking_client_scope" {
  type        = string
  description = "azure scope id for ms_booking_client"
}

variable "ms_booking_appointments_job_schedule" {
  type        = string
  description = "cron scheduler to run ms booking job"
}

variable "booking_id" {
  type        = string
  description = "calendar booking id"
}

variable "service_id" {
  type        = string
  description = "ms booking calendar service id"
}

variable "msbooking_questions_id_delivery_vin" {
  type        = string
  description = "the question id for the delivery vin"
}

variable "msbooking_questions_id_delivery_license_plate" {
  type        = string
  description = "the question id for the delivery license_plate"
}

variable "msbooking_questions_id_delivery_model" {
  type        = string
  description = "the question id for the delivery model"
}

variable "msbooking_questions_id_return_vin" {
  type        = string
  description = "the question id for the return vin"
}
variable "msbooking_questions_id_return_license_plate" {
  type        = string
  description = "the question id for the return license plate"
}
variable "msbooking_questions_id_return_model" {
  type        = string
  description = "the question id for the return model"
}
variable "msbooking_enable" {
  type        = bool
  description = "flag if to activate msbooking"
}
variable "msbooking_username" {
  type        = string
  description = "username for the azure msbooking delegated user"
}
variable "oauth2_client_id_for_pace" {
  type        = string
  description = "The OAuth2 VehicleService clientId used with PACE API"
}
variable "oauth2_host_pace" {
  type        = string
  description = "The host used by the OAuth2 client during authentication with PACE API"
}
variable "pace_base_url" {
  type        = string
  description = "Base url pointing to the service that is currently providing PACE API"
}
variable "employee_updated_event_topic" {
  type        = string
  description = "kafka topic for employee_updated_event from user-service"
}
variable "employee_updated_event_group_id" {
  type        = string
  description = "kafka group id for employee_updated_event from user-service"
}
variable "employee_updated_event_enabled" {
  type        = bool
  description = "feature flag to determine if to recieve messages from employee_updated_event"
}
variable "maintenance_order_number_update_schedule" {
  type        = string
  description = "the cron schedule to trigger maintenance order number update job"
  default     = "0 0 0 1 * ?"
}

variable "factory_car_preparation_order_number_update_schedule" {
  type        = string
  description = "the cron schedule to trigger factory car preparation order number update job"
  default     = "0 0 0 1 1 ? 2099"
}

variable "fleet_master_data_topic" {
  type        = string
  description = "the topic to publish fleet vehicle master data"
}

variable "enable_fleet_master_data" {
  type        = bool
  description = "Enable or disable publishing fleet vehicle master data to external systems (PVCC, MOVES)"
}

variable "fleet_master_data_cron_schedule" {
  type        = string
  description = "This schedule is to set the frequency of publishing data to external systems"
  default     = "0 * * * * ?"
}

variable "kafka_schema_registry_user" {
  type        = string
  description = "The schema registry user for fleet-vehicle-management-service"
}

variable "kafka_schema_registry_url" {
  type        = string
  description = "The schema registry url for fleet-vehicle-management-service"
}

variable "kafka_schema_auto_register" {
  type        = bool
  description = "Whether to register the schema automatically or not"
}

variable "tire_management_enabled" {
  type        = string
  description = "Module level feature-toggle for tire-management"
}

variable "tire_management_data_export_cron" {
  type        = string
  description = "the cron schedule to trigger tire-management data export job"
}

variable "tire_management_email_recipient" {
  type        = string
  description = "A single email recipient who gets tire-management (relas) report"
}

variable "tire_management_email_sender" {
  type        = string
  description = "Email address from who the the tire-management (relas) report is sent out"
}
variable "repairfix_base_url" {
  type        = string
  description = "Base url of repair fix"
}
variable "tuv_team_recipient_to_email_address" {
  type        = string
  description = "comma-separated recipient email address of tüv team"
}
variable "tuv_team_sender_email_address" {
  type        = string
  description = "sender email address"
}
variable "logistics_team_recipient_to_email_address" {
  type        = string
  description = "comma-separated recipient email address of logistics provider team"
}
variable "logistics_team_sender_email_address" {
  type        = string
  description = "sender email address"
}
variable "synchronize_scrapping_status_update_cron" {
  type        = string
  description = "cron for synchronizing scrapping status update to p40"
}

variable "ecc_allow_incoming_traffic" {
  type        = bool
  description = "allow or disable incoming request from ecc"
}
variable "synchronize_blocked_for_sale_update_cron" {
  type        = string
  description = "cron for synchronizing blocked for sale update to p40"
}

variable "kafka_pia_auto_offset_reset" {
  type        = string
  description = "feature toggle to tell kafka consumer where to start listening, values: earliest, latest or none"
}

variable "enable_kafka_pia_integration" {
  type        = bool
  description = "Whether to enable or disable PIA integration"
}

variable "kafka_pia_consumer_group_id" {
  type        = string
  description = "The kafka consumer group id for pia integration"
}
variable "prepare_blocked_for_sale_update_cron" {
  type        = string
  description = "cron for preparing blocked for sale update"
}
variable "vehicle_transfer_allow_incoming_traffic" {
  type        = bool
  description = "allow or disable incoming request from public clients to vehicle transfer endpoints"
}
variable "enable_kafka_p40_integration" {
  type        = bool
  description = "Whether to enable or disable P40 financial asset type integration"
}
variable "kafka_financial_asset_type_consumer_group_id" {
  type        = string
  description = "The kafka consumer group id for P40 financial asset type integration"
}
variable "kafka_financial_asset_type_auto_offset_reset" {
  type        = string
  description = "feature toggle to tell kafka consumer where to start listening, values: earliest, latest or none"
}
variable "dms_vehicle_migration_topic" {
  type        = string
  description = "the topic to publish dms vehicle migration data"
}
variable "oauth2_client_id_for_pace_balance_sheet" {
  type        = string
  description = "The OAuth2 VehicleService clientId used with PACE Balance Sheet API"
}
variable "oauth2_host_pace_balance_sheet" {
  type        = string
  description = "The host used by the OAuth2 client during authentication with PACE Balance Sheet API"
}
variable "pace_balance_sheet_base_url" {
  type        = string
  description = "Base url pointing to the service that is currently providing PACE Balance Sheet API"
}
variable "dms-vehicle-migration-enabled" {
  type        = string
  description = "enable/disable dms vehicle migration process"
}

variable "carsync_enabled" {
  type        = bool
  description = "enable/disable carsync web client"
}

variable "carsync_username" {
  type        = string
  description = "the user for the basic auth"
}

variable "carsync_base_url" {
  type        = string
  description = "api base url"
}

variable "sync_mileage_cron" {
  type        = string
  description = "crom job to read mileage data from car sync"
}

variable "financial_asset_type_update_sync_cron" {
  type        = string
  description = "cron job to sync financial asset type updates to FVM vehicles"
}

variable "vtstamm_allow_incoming_traffic" {
  type        = bool
  description = "allow or disable incoming request from public vtstamm clients to vtstamm endpoints"
}

variable "mailclient_override_email_recipients" {
  type        = string
  description = "configures if the email recipients should be overriden"
}

variable "mailclient_override_recipients_list" {
  type        = string
  description = "configures the email address to which the application sends emails if the recipients should be overridden"
}

variable "vehicle_sales_b2b_integration_enabled" {
  type        = bool
  description = "flag to determine if the B2B integration is enabled"
}

variable "kafka_b2b_consumer_group_id" {
  type        = string
  description = "B2B Kafka consumer group id"
}

variable "vehicle_registration_mail_export_cron" {
  type        = string
  description = "Cron schedule for vehicle registration mail export export"
}

variable "vehicle_registration_mail_export_enabled" {
  type        = bool
  description = "Flag to enable/disable vehicle registration mail export functionality"
}

variable "vehicle_registration_mail_export_sender" {
  type        = string
  description = "Email address of vehicle registration mail export sender"
}

variable "vehicle_registration_mail_export_recipient" {
  type        = string
  description = "Email address of vehicle registration mail export recipient"
}

variable "stream_logs_to_newrelic" {
  type        = bool
  description = "Flag to enable streaming logs to New Relic"
  default     = false
}

variable "rela_enabled" {
  type        = bool
  description = "Flag to enable/disable rela integration"
}

variable "rela_base_url" {
  type        = string
  description = "rela base url"
}

variable "tire_change_rela_sync_job_schedule" {
  type        = string
  description = "cron job to sync tire change appointments to rela"
}

variable "tire_change_appointment_bookings_id" {
  type        = string
  description = "tire change appointment bookings id"
}

variable "tire_change_appointment_service_id" {
  type        = string
  description = "tire change appointment service id"
}

variable "tire_change_appointment_ms_bookings_username" {
  type        = string
  description = "tire change appointment ms bookings username"
}

variable "tire_change_appointment_ms_bookings_client_id" {
  type        = string
  description = "tire change appointment ms bookings client id"
}

variable "tire_change_appointment_ms_bookings_client_scope" {
  type        = string
  description = "tire change appointment ms bookings client scope"
}

variable "tire_change_appointment_ms_bookings_enabled" {
  type        = string
  description = "enable/disable tire change appointment ms bookings integration"
}
