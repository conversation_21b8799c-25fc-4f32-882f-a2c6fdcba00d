locals {
  repo         = "vehicle-service"
  role_name    = "emh-${local.repo}-cicd"
  cluster_name = data.aws_ecs_cluster.ecs_cluster.cluster_name
  account_id   = data.aws_caller_identity.current.account_id
  region       = data.aws_region.current.name

  params = {
    MS_BOOKING_PASSWORD = {
      category    = "external"
      component   = "msbooking"
      param_name  = "delegate_user_password"
      description = "The user password from azure"
      repo        = local.repo
      type        = "SecureString"
    }

    MS_BOOKING_CLIENT_CLIENT_SECRET = {
      category    = "external"
      component   = "msbooking"
      param_name  = "oauth_m2m_client_secret"
      description = "The client secret used to fetch the token from Porsche Vehicle Hub"
      repo        = local.repo
      type        = "SecureString"
    }

    PVH_CLIENT_SECRET = {
      category    = "external"
      component   = "pvh"
      param_name  = "oauth_m2m_client_secret"
      description = "The client secret used to fetch the token from Porsche Vehicle Hub"
      repo        = local.repo
      type        = "SecureString"
    }

    KAFKA_PASSWORD = {
      category    = "external"
      component   = "streamzilla"
      param_name  = "kafka_password"
      description = "Streamzilla Password"
      repo        = local.repo
      type        = "SecureString"
    }

    VEHICLE_REGISTRATION_API_CLIENT_SECRET = {
      category    = "secret"
      component   = "azure-idp"
      param_name  = "vehicle_registration_api_m2m_client_secret"
      description = "This secret is used by the vehicle data service to connect to vehicle registration API, we expect to use this for all the use cases in recent future"
      repo        = local.repo
      type        = "SecureString"
    }

    VEHICLE_REGISTRATION_ARCHIVE_API_CLIENT_SECRET = {
      category    = "secret"
      component   = "azure-idp"
      param_name  = "vehicle_registration_archive_m2m_client_secret"
      description = "This secret is used by the vehicle data service to connect to vehicle registration archiving API"
      repo        = local.repo
      type        = "SecureString"
    }

    VEHICLE_REGISTRATION_LEGALHOLD_API_CLIENT_SECRET = {
      category    = "secret"
      component   = "azure-idp"
      param_name  = "vehicle_registration_legalhold_m2m_client_secret"
      description = "This secret is used by the vehicle data service to connect to vehicle registration legal hold API"
      repo        = local.repo
      type        = "SecureString"
    }

    VEHICLE_LOCATION_API_CLIENT_SECRET = {
      category    = "secret"
      component   = "azure-idp"
      param_name  = "emh_vehicle_location_service_fmv_client_secret"
      description = "This secret is used by the vehicle service to connect to vehicle location API"
      repo        = local.repo
      type        = "SecureString"
    }

    EMPLOYEE_API_CLIENT_SECRET = {
      category    = "secret"
      component   = "azure-idp"
      param_name  = "emh_user_service_fmv_client_secret"
      description = "This secret is used by the vehicle service to connect to user-service API"
      repo        = local.repo
      type        = "SecureString"
    }

    OAUTH2_CLIENT_SECRET_FOR_PACE = {
      category    = "external"
      component   = "oauth2"
      param_name  = "oauth2_vs_client_secret_for_pace"
      description = "OAuth2 VehicleService Client Secret for PACE API"
      repo        = local.repo
      type        = "SecureString"
    }

    CAP_OAUTH2_CLIENT_SECRET = {
      category    = "secret"
      component   = "ppn-idp"
      param_name  = "cap_oauth2_client_secret"
      description = "This secret is used by the vehicle service to obtain token from PPN to connect to C@P"
      repo        = local.repo
      type        = "SecureString"
    }

    DMSI_OAUTH2_CLIENT_SECRET = {
      category    = "secret"
      component   = "rwil-idp"
      param_name  = "dmsi_oauth2_client_secret"
      description = "This secret is used by the vehicle service to obtain token from RWIL IDP to connect to DMSI"
      repo        = local.repo
      type        = "SecureString"
    }

    ENTRA_ID_OAUTH2_CLIENT_SECRET = {
      category    = "secret"
      component   = "azure-idp"
      param_name  = "entra_id_oauth2_client_secret"
      description = "This secret is used by the vehicle service to connect to Azure Entra ID for synchronizing user groups"
      repo        = local.repo
      type        = "SecureString"
    }

    CAP_IBM_API_GATEWAY_CLIENT_SECRET = {
      category    = "secret"
      component   = "developer-hub"
      param_name  = "cap_ibm_api_gateway_client_secret"
      description = "This secret is used by the vehicle service to connect to C@P via ibm api gateway"
      repo        = local.repo
      type        = "SecureString"
    }

    SMTP_PASSWORD = {
      category    = "external"
      component   = "smtp"
      param_name  = "smtp_password"
      description = "SMTP Server Password"
      repo        = local.repo
      type        = "SecureString"
    }

    KAFKA_SCHEMA_REGISTRY_PASSWORD = {
      category    = "external"
      component   = "streamzilla"
      param_name  = "kafka_schema_registry_password"
      description = "Streamzilla Kafka Schema Registry Password"
      repo        = local.repo
      type        = "SecureString"
    }

    OAUTH2_CLIENT_SECRET_FOR_PACE_BALANCE_SHEET = {
      category    = "external"
      component   = "oauth2"
      param_name  = "oauth2_vs_client_secret_for_pace_balance_sheet"
      description = "OAuth2 VehicleService Client Secret for PACE Balance Sheet API"
      repo        = local.repo
      type        = "SecureString"
    }

    CARSYNC_USER_PASSWORD = {
      category    = "external"
      component   = "carsync"
      param_name  = "carsync_password"
      description = "Carsync password for basic auth"
      repo        = local.repo
      type        = "SecureString"
    }

    CARSYNC_TRUSTSTORE = {
      category    = "external"
      component   = "carsync"
      param_name  = "carsync_cert"
      description = "Carsync base64 certificate (public)"
      repo        = local.repo
      type        = "SecureString"
    }

    CARSYNC_KEYSTORE = {
      category    = "external"
      component   = "carsync"
      param_name  = "carsync_private_key"
      description = "Carsync base64 Privatekey"
      repo        = local.repo
      type        = "SecureString"
    }

    TIRE_CHANGE_APPOINTMENT_MS_BOOKINGS_PASSWORD = {
      category    = "secret"
      component   = "ms-bookings"
      param_name  = "tire_change_appointment_ms_bookings_password"
      description = "This secret is used by the vehicle service to connect to ms bookings for tire change appointment"
      repo        = local.repo
      type        = "SecureString"
    }

    TIRE_CHANGE_APPOINTMENT_MS_BOOKING_CLIENT_SECRET = {
      category    = "secret"
      component   = "ms-bookings"
      param_name  = "tire_change_appointment_ms_bookings_client_secret"
      description = "This secret is used by the vehicle service to connect to ms bookings for tire change appointment"
      repo        = local.repo
      type        = "SecureString"
    }
  }

  api_consumer_params = {
    CLIENT_API_SECRET = {
      category    = "secret"
      component   = "developer-hub"
      param_name  = "client_api_secret"
      description = "The client API secret used when calling IBM API Gateway for FleetVehicleDataProvider endpoints"
      repo        = local.repo
      type        = "SecureString"
    }

    APP_M2M_CLIENT_SECRET = {
      category    = "secret"
      component   = "azure-idp"
      param_name  = "app_m2m_client_secret"
      description = "The client secret used to fetch the token to authorize FleetVehicleDataProvider endpoints from Azure Idp"
      repo        = local.repo
      type        = "SecureString"
    }

    VEHICLE_SERVICE_M2M_TEST_CLIENT_SECRET = {
      category    = "secret"
      component   = "azure-idp"
      param_name  = "oauth_m2m_test_api_client_secret"
      description = "This secret is used by the vehicle data service to connect to test locally"
      repo        = local.repo
      type        = "SecureString"
    }

    RELA_API_KEY = {
      category    = "external"
      component   = "rela"
      param_name  = "rela_api_key"
      description = "The api key for the rela api"
      repo        = local.repo
      type        = "SecureString"
    }
  }

  athena = {
    spill_bucket = "${local.repo}-athena-spill-bucket-${local.account_id}"

    security_groups = [
      {
        name = "${local.repo}-athena-sg"

        egress_rules = [
          {
            from_port   = 443
            to_port     = 443
            protocol    = "TCP"
            description = "Allow outbound traffic to Target Instances"
            cidr_blocks = ["0.0.0.0/0"]
          },
          {
            from_port   = 5432
            to_port     = 5432
            protocol    = "TCP"
            description = "Allow outbound traffic to Target Instances"

            security_groups = [data.aws_security_group.aurora_postgres.id]
          }
        ]
      }
    ]
  }

  alb = {
    security_groups = [
      {
        name = "${local.repo}-alb-security-group"

        ingress_rules = [
          {
            from_port   = 443
            to_port     = 443
            protocol    = "TCP"
            description = "Allow secure inbound traffic within VPC"
            cidr_blocks = [for subnet in data.aws_subnet.subnets_private_cidr : subnet.cidr_block]
          }
        ]
        egress_rules = [
          {
            from_port   = 8080
            to_port     = 8080
            protocol    = "TCP"
            description = "Allow outbound traffic to Target Instances"
            cidr_blocks = [for subnet in data.aws_subnet.subnets_private_cidr : subnet.cidr_block]
          }
        ]
      }
    ]

    listeners = [
      {
        port         = 443
        protocol     = "HTTPS"
        target_group = "${local.repo}-alb-target-group"
        ssl_policy   = "ELBSecurityPolicy-FS-1-2-Res-2020-10"
      }
    ]

    target_group = {
      name        = "${local.repo}-alb-target-group"
      port        = 8080
      protocol    = "HTTP"
      target_type = "ip"

      health_check = {
        healthy_threshold   = 2
        unhealthy_threshold = 10
        protocol            = "HTTP"
        path                = "/api/vs/actuator/health"
        timeout             = 5
        interval            = 25
        matcher             = "200"
      }
    }
  }

  nlb = {
    security_groups = [
      {
        name = "${local.repo}-ibm-gateway-nlb"

        ingress_rules = [
          {
            from_port   = 443
            to_port     = 443
            protocol    = "TCP"
            description = "Allow secure inbound traffic from the internet"
            cidr_blocks = ["0.0.0.0/0"]
          }
        ]
        egress_rules = [
          {
            from_port       = 443
            to_port         = 443
            protocol        = "TCP"
            description     = "Allow outbound traffic to ALB"
            security_groups = module.alb.security_group_ids
          }
        ]
      }
    ]

    target_group = {
      name        = "${local.repo}-nlb-target-group"
      port        = 443
      protocol    = "TCP"
      target_type = "alb"

      health_check = {
        healthy_threshold   = 2
        unhealthy_threshold = 10
        protocol            = "HTTPS"
        path                = "/api/vs/actuator/health"
        timeout             = 5
        interval            = 10
        matcher             = "200"
      }
    }
  }

  vts = {
    app = "vts"
    security_groups = [
      {
        name = "vts-ibm-gateway"

        ingress_rules = [
          {
            from_port   = 443
            to_port     = 443
            protocol    = "TCP"
            description = "Allow secure inbound traffic from the internet"
            cidr_blocks = ["0.0.0.0/0"]
          }
        ]
        egress_rules = [
          {
            from_port       = 443
            to_port         = 443
            protocol        = "TCP"
            description     = "Allow outbound traffic to ALB"
            security_groups = module.alb.security_group_ids
          }
        ]
      }
    ]

    target_group = {
      name        = "vts-nlb-target-group"
      port        = 443
      protocol    = "TCP"
      target_type = "alb"

      health_check = {
        healthy_threshold   = 2
        unhealthy_threshold = 10
        protocol            = "HTTPS"
        path                = "/api/vs/actuator/health"
        timeout             = 5
        interval            = 10
        matcher             = "200"
      }
    }
  }

  ecc = {
    app = "ecc"
    security_groups = [
      {
        name = "ecc-ibm-gateway"

        ingress_rules = [
          {
            from_port   = 443
            to_port     = 443
            protocol    = "TCP"
            description = "Allow secure inbound traffic from the internet"
            cidr_blocks = ["0.0.0.0/0"]
          }
        ]
        egress_rules = [
          {
            from_port       = 443
            to_port         = 443
            protocol        = "TCP"
            description     = "Allow outbound traffic to ALB"
            security_groups = module.alb.security_group_ids
          }
        ]
      }
    ]

    target_group = {
      name        = "ecc-nlb-target-group"
      port        = 443
      protocol    = "TCP"
      target_type = "alb"

      health_check = {
        healthy_threshold   = 2
        unhealthy_threshold = 10
        protocol            = "HTTPS"
        path                = "/api/vs/actuator/health"
        timeout             = 5
        interval            = 10
        matcher             = "200"
      }
    }
  }

  ecs = {

    security_groups = [
      {
        name = "${local.repo}-ecs-task"

        ingress_rules = [
          {
            from_port   = 8080
            to_port     = 8080
            protocol    = "TCP"
            description = "Allow inbound traffic from VPC as we have ALB in public subnet and NLB is in private subnet"
            cidr_blocks = [data.aws_vpc.vpc.cidr_block]
          }
        ]

        egress_rules = [
          {
            from_port   = 443
            to_port     = 443
            protocol    = "TCP"
            description = "allows pulling docker images layers from s3"

            prefix_list_ids = ["pl-6da54004"]
          },
          {
            from_port   = 443
            to_port     = 443
            protocol    = "TCP"
            description = "Allow secure outbound traffic"
            cidr_blocks = ["0.0.0.0/0"]
          },
          {
            from_port   = 9092
            to_port     = 9092
            protocol    = "TCP"
            description = "Allow outbound traffic (Kafka)"
            cidr_blocks = ["0.0.0.0/0"]
          },
          {
            from_port   = 5432
            to_port     = 5432
            protocol    = "TCP"
            description = "Allow secure outbound traffic to Aurora Postgres"

            security_groups = [data.aws_security_group.aurora_postgres.id]
          },
          {
            from_port   = 4317
            to_port     = 4317
            protocol    = "TCP"
            description = "allow to sending data from agent for open telemetry"
            cidr_blocks = [data.aws_vpc.vpc.cidr_block]
          },
          {
            from_port   = 8080
            to_port     = 8080
            protocol    = "TCP"
            description = "allow sending data to other services inside the cluster"
            cidr_blocks = [for subnet in data.aws_subnet.subnets_private_cidr : subnet.cidr_block]
          },
          {
            from_port   = 465
            to_port     = 465
            protocol    = "TCP"
            description = "Allow sending email to SMTP server"
            cidr_blocks = ["0.0.0.0/0"]
          }
        ]
      }
    ]

    environments = [
      {
        name  = "SPRING_PROFILES_ACTIVE"
        value = var.env
      },
      {
        name  = "SPRING_DATASOURCE_USERNAME"
        value = "vehicle_service"
      },
      {
        name  = "RDS_ENABLED"
        value = true
      },
      {
        name  = "AWS_REGION"
        value = data.aws_region.current.name
      },
      {
        name  = "SPRING_DATASOURCE_URL"
        value = "jdbc:postgresql://${data.aws_rds_cluster.aurora_postgres_cluster.endpoint}:${data.aws_rds_cluster.aurora_postgres_cluster.port}/vehicleservice?sslmode=require"
      },
      {
        name  = "METRICS_REGISTRY"
        value = "cloudwatch"
      },
      {
        name  = "OIDC_TOKEN_URL"
        value = var.oidc_token_url
      },
      {
        name  = "PVH_BASE_URL"
        value = var.pvh_base_url
      },
      {
        name  = "PVH_CLIENT_ID"
        value = var.pvh_client_id
      },
      {
        name  = "OAUTH2_ISSUER_URI"
        value = var.oauth2_issuer_uri
      },
      {
        name  = "OAUTH2_TOKEN_URI"
        value = var.oauth2_token_uri
      },
      {
        name  = "EXPECTED_ALB_ARN"
        value = data.aws_lb.ui_alb.arn
      },
      {
        name  = "OAUTH2_JWKS_URI"
        value = "https://login.microsoftonline.com/common/discovery/keys"
      },
      {
        name  = "OAUTH2_VEHICLE_AUDIENCE"
        value = var.oauth2_vehicle_audience
      },
      {
        name  = "OAUTH2_DM_AUDIENCE"
        value = var.oauth2_dm_audience
      },
      {
        name  = "VR_BASE_URI"
        value = var.vehicle_registration_base_uri
      },
      {
        name  = "VR_BASE_URL"
        value = var.vehicle_registration_base_url
      },
      {
        name  = "OAUTH2_LOGOUT_URI"
        value = var.logout_uri
      },
      {
        name  = "OAUTH2_VEHICLE_REGISTRATION_APP_ID"
        value = var.oauth2_vehicle_registration_app_id
      },
      {
        name  = "APP_DOMAIN"
        value = var.app_domain
      },
      {
        name  = "FMS_MIGRATION_MODULE_ENABLED"
        value = var.fms_migration_enabled
      },
      {
        name  = "FMS_MIGRATION_BUCKET"
        value = var.s3_bucket_fms_migration
      },
      {
        name  = "FMS_MIGRATION_BUCKET_KMS_KEY_ID"
        value = module.kms.key_arn
      },
      {
        name  = "STORAGE_ARCHIVE_BUCKET_KMS_KEY_ID"
        value = module.kms.key_arn
      },
      {
        name  = "STORAGE_ARCHIVE_BUCKET"
        value = var.s3_bucket_archive
      },
      {
        name  = "STORAGE_ARCHIVE_BUCKET_VEHICLE_TRANSFER"
        value = var.s3_bucket_vehicle_transfer_archive
      },
      {
        name  = "STORAGE_ARCHIVE_BUCKET_VEHICLE_HISTORY"
        value = var.s3_bucket_vehicle_history_archive
      },
      {
        name  = "STORAGE_ARCHIVE_BUCKET_VEHICLE_SALES"
        value = var.s3_bucket_vehicle_sales_archive
      },
      {
        name  = "VEHICLE_REGISTRATION_API_CLIENT_ID"
        value = var.vehicle_registration_client_id
      },
      {
        name  = "VEHICLE_REGISTRATION_API_SCOPE"
        value = "api://${var.oauth2_vehicle_registration_app_id}/.default"
      },
      {
        name  = "VEHICLE_REGISTRATION_ARCHIVE_API_CLIENT_ID"
        value = var.vehicle_registration_archive_client_id
      },
      {
        name  = "VEHICLE_REGISTRATION_ARCHIVE_API_SCOPE"
        value = "api://${var.oauth2_vehicle_registration_app_id}/.default"
      },
      {
        name  = "VEHICLE_REGISTRATION_LEGALHOLD_API_CLIENT_ID"
        value = var.vehicle_registration_legalhold_client_id
      },
      {
        name  = "VEHICLE_REGISTRATION_LEGALHOLD_API_SCOPE"
        value = "api://${var.oauth2_vehicle_registration_app_id}/.default"
      },
      {
        name  = "VEHICLE_ARCHIVE_CRON_SCHEDULE"
        value = var.vehicle_archive_cron_schedule
      },
      {
        name  = "ENABLE_ARCHIVAL_SCHEDULER"
        value = var.vehicle_archive_enable_scheduler
      },
      {
        name  = "COST_CENTER_UPDATE_CRON"
        value = var.cost_center_update_cron_schedule
      },
      {
        name  = "COST_CENTER_UPDATE_PREPARATION_CRON"
        value = var.cost_center_update_preparation_cron_schedule
      },
      {
        name  = "VEHICLE_LOCATION_API_CLIENT_ID"
        value = var.vehicle_location_client_id
      },
      {
        name  = "VEHICLE_LOCATION_API_SCOPE"
        value = var.vehicle_location_scope
      },
      {
        name  = "VEHICLE_LOCATION_AZURE_TENANT_ID"
        value = var.vehicle_location_azure_tenant_id
      },
      {
        name  = "ENTRA_ID_AZURE_TENANT_ID"
        value = var.entra_id_azure_tenant_id
      },
      {
        name  = "ENABLE_ENTRA_ID_SCHEDULER"
        value = var.enable_entra_id_scheduler
      },
      {
        name  = "ENTRA_ID_ADMINISTRATIVE_UNIT_ID"
        value = var.entra_id_administrative_unit_id
      },
      {
        name  = "ENTRA_ID_APP_ROLE_ASSIGNMENT_ID"
        value = var.entra_id_app_role_assignment_id
      },
      {
        name  = "ENTRA_ID_SYNC_CRON_SCHEDULE"
        value = var.entra_id_sync_cron_schedule
      },
      {
        name  = "ENTRA_ID_OAUTH2_CLIENT_ID"
        value = var.entra_id_oauth2_client_id
      },
      {
        name  = "VEHICLE_LOCATION_BASE_URL"
        value = var.vehicle_location_base_url
      },
      {
        name  = "VEHICLE_LEGALHOLD_STORAGE_BUCKET_KMS_KEY_ID"
        value = module.kms.key_arn
      },
      {
        name  = "VEHICLE_LEGALHOLD_STORAGE_BUCKET"
        value = var.s3_bucket_legal_hold
      },
      {
        name  = "VEHICLE_LEGALHOLD_STORAGE_BUCKET_VEHICLE_SALES"
        value = var.s3_bucket_vehicle_sales_legal_hold
      },
      {
        name  = "KAFKA_BOOTSTRAP_SERVERS"
        value = var.kafka_bootstrap_servers
      },
      {
        name  = "KAFKA_USER"
        value = var.kafka_user
      },
      {
        name  = "KAFKA_SECURITY_PROTOCOL"
        value = "SASL_SSL"
      },
      {
        name  = "KAFKA_ENABLE_PVH_INGESTION"
        value = var.kafka_enable_pvh_ingestion
      },
      {
        name  = "KAFKA_PVH_STOP_ON_ERROR"
        value = var.kafka_pvh_stop_on_error
      },
      {
        name  = "KAFKA_AUTO_OFFSET_RESET"
        value = var.kafka_auto_offset_reset
      },
      {
        name  = "TIMESTAMP_FILTERING_DISABLE_AFTER"
        value = var.timestamp_filtering_disable_after
      },
      {
        name  = "KAFKA_PVH_CONSUMER_GROUP_ID"
        value = var.kafka_pvh_consumer_group_id
      },
      {
        name  = "FLEET_MASTER_DATA_TOPIC"
        value = var.fleet_master_data_topic
      },
      {
        name  = "ENABLE_FLEET_MASTER_DATA"
        value = var.enable_fleet_master_data
      },
      {
        name  = "FLEET_MASTER_DATA_CRON_SCHEDULE"
        value = var.fleet_master_data_cron_schedule
      },
      {
        name  = "KAFKA_SCHEMA_REGISTRY_USER"
        value = var.kafka_schema_registry_user
      },
      {
        name  = "KAFKA_SCHEMA_REGISTRY_URL"
        value = var.kafka_schema_registry_url
      },
      {
        name  = "KAFKA_SCHEMA_AUTO_REGISTER"
        value = var.kafka_schema_auto_register
      },
      {
        name  = "OTEL_EXPORTER_OTLP_ENDPOINT"
        value = "http://aws-distro-opentelemetry-service.${local.cluster_name}:4317"
      },
      {
        name  = "OTEL_RESOURCE_ATTRIBUTES"
        value = "service.namespace=${local.cluster_name},service.name=${local.repo},aws.log.group.names=${local.repo},aws.log.group.arns=arn:aws:logs:${local.region}:${local.account_id}:log-group:${local.repo}"
      },
      {
        name  = "OTEL_METRICS_EXPORTER"
        value = "none"
      },
      {
        name  = "ENABLE_ALBHEADERS_ENDPOINT_FOR_TESTING"
        value = var.enable_albheaders_endpoint_for_testing
      },
      {
        name  = "ALLOW_OVERWRITE_OF_GROUPS_FROM_REQUEST_HEADER"
        value = var.allow_overwrite_of_groups_from_request_header
      },
      {
        name  = "OBJECT_LOCATION_UPDATED_TOPIC"
        value = var.object_location_updated_topic
      },
      {
        name  = "OBJECT_LOCATION_UPDATED_TOPIC_GROUP_ID"
        value = var.object_location_updated_topic_group_id
      },
      {
        name  = "VEHICLE_LOCATION_VLS_KAFKA_INTEGRATION_ENABLED"
        value = var.vehicle_location_vls_kafka_integration_enabled
      },
      {
        name  = "LOCATION_UPDATED_TOPIC"
        value = var.location_updated_topic
      },
      {
        name  = "LOCATION_UPDATED_TOPIC_GROUP_ID"
        value = var.location_updated_topic_group_id
      },
      {
        name  = "LOCATION_DELETED_TOPIC"
        value = var.location_deleted_topic
      },
      {
        name  = "LOCATION_DELETED_TOPIC_GROUP_ID"
        value = var.location_deleted_topic_group_id
      },
      {
        name  = "EMPLOYEE_BASE_URL"
        value = var.employee_base_url
      },
      {
        name  = "EMPLOYEE_API_CLIENT_ID"
        value = var.employee_api_client_id
      },
      {
        name  = "EMPLOYEE_API_SCOPE"
        value = var.employee_api_scope
      },
      {
        name  = "EMPLOYEE_OAUTH2_TOKEN_URI"
        value = var.employee_oauth_token_uri
      },
      {
        name  = "CAP_OAUTH2_CLIENT_ID"
        value = var.cap_oauth2_client_id
      },
      {
        name  = "PPN_HOST"
        value = var.ppn_host
      },
      {
        name  = "CAP_IBM_API_GATEWAY_CLIENT_ID"
        value = var.cap_ibm_api_gateway_client_id
      },
      {
        name  = "CAP_BASE_URL"
        value = var.cap_base_url
      },
      {
        name  = "CAP_REGION"
        value = var.cap_region
      },
      {
        name  = "PLANNED_VEHICLE_TRANSFER_INITIALIZATION_SCHEDULE"
        value = var.planned_vehicle_transfer_initialization_schedule
      },
      {
        name  = "FEATURE_FLAGS_READ_VEHICLE_PERSON_ENABLED"
        value = var.feature_flags_read_vehicle_person_enabled
      },
      {
        name  = "LICENSE_PLATE_TOPIC"
        value = var.license_plate_topic
      },
      {
        name  = "LICENSE_PLATE_GROUP_ID"
        value = var.license_plate_group_id
      },
      {
        name  = "LICENSE_PLATE_KAFKA_CONSUMERS"
        value = var.license_plate_kafka_consumers
      },
      {
        name  = "VEHICLE_TRANSFER_DELIVERY_LEAD_TIME"
        value = var.vehicle_transfer_delivery_lead_time
      },
      {
        name  = "PRE_DELIVERY_INSPECTION_PDI_LEAD_TIME"
        value = var.pre_delivery_inspection_lead_time
      },
      {
        name  = "NUMBER_OF_DAMAGES_TOPIC"
        value = var.number_of_damages_topic
      },
      {
        name  = "NUMBER_OF_DAMAGES_GROUP_ID"
        value = var.number_of_damages_group_id
      },
      {
        name  = "NUMBER_OF_DAMAGES_TOPIC_KAFKA_CONSUMERS"
        value = var.number_of_damages_enable
      },
      {
        name  = "PDI_ORDERING_EMAIL_SCHEDULE"
        value = var.pdi_ordering_email_schedule
      },
      {
        name  = "PDI_ORDERING_RECIPIENT_TO_EMAIL_ADDRESS"
        value = var.pdi_ordering_recipient_to_email_address
      },
      {
        name  = "PDI_ORDERING_RECIPIENT_CC_EMAIL_ADDRESS"
        value = var.pdi_ordering_recipient_cc_email_address
      },
      {
        name  = "PDI_ORDERING_SENDER_EMAIL_ADDRESS"
        value = var.pdi_ordering_sender_email_address
      },
      {
        name  = "VEHICLE_TRANSFER_DELIVERY_SENDER_EMAIL_ADDRESS"
        value = var.vehicle_transfer_delivery_sender_email_address
      },
      {
        name  = "SMTP_HOST"
        value = var.smtp_host
      },
      {
        name  = "SMTP_PORT"
        value = var.smtp_port
      },
      {
        name  = "SMTP_USERNAME"
        value = var.smtp_username
      },
      {
        name  = "DMSI_BASE_URI"
        value = var.dmsi_base_uri
      },
      {
        name  = "VEHICLE_CAMPAIGNS_ENABLED"
        value = var.vehicle_campaigns_enabled
      },
      {
        name  = "RWIL_IDP_HOST"
        value = var.rwil_idp_host
      },
      {
        name  = "DMSI_OAUTH2_CLIENT_ID"
        value = var.dmsi_oauth2_client_id
      },
      {
        name  = "VEHICLE_STATUS_RECALCULATION_SCHEDULE"
        value = var.vehicle_status_recalculation_cron
      },
      {
        name  = "MS_BOOKING_CLIENT_CLIENT_ID"
        value = var.ms_booking_client_client_id
      },
      {
        name  = "MS_BOOKING_CLIENT_SCOPE"
        value = var.ms_booking_client_scope
      },
      {
        name  = "MS_BOOKING_APPOINTMENTS_JOB_SCHEDULE"
        value = var.ms_booking_appointments_job_schedule
      },
      {
        name  = "BOOKING_ID"
        value = var.booking_id
      },
      {
        name  = "SERVICE_ID"
        value = var.service_id
      },
      {
        name  = "DELIVERY_VIN"
        value = var.msbooking_questions_id_delivery_vin
      },
      {
        name  = "DELIVERY_LICENSE_PLATE"
        value = var.msbooking_questions_id_delivery_license_plate
      },
      {
        name  = "DELIVERY_MODEL"
        value = var.msbooking_questions_id_delivery_model
      },
      {
        name  = "RETURN_VIN"
        value = var.msbooking_questions_id_return_vin
      },
      {
        name  = "RETURN_LICENSE_PLATE"
        value = var.msbooking_questions_id_return_license_plate
      },
      {
        name  = "RETURN_MODEL"
        value = var.msbooking_questions_id_return_model
      },
      {
        name  = "MS_BOOKING_ENABLE"
        value = var.msbooking_enable
      },
      {
        name  = "MS_BOOKING_USERNAME"
        value = var.msbooking_username
      },
      {
        name  = "OAUTH2_CLIENT_ID_FOR_PACE",
        value = var.oauth2_client_id_for_pace
      },
      {
        name  = "OAUTH2_HOST_PACE",
        value = var.oauth2_host_pace
      },
      {
        name  = "PACE_BASE_URL",
        value = var.pace_base_url
      },
      {
        name  = "EMPLOYEE_UPDATED_EVENT_TOPIC"
        value = var.employee_updated_event_topic
      },
      {
        name  = "EMPLOYEE_UPDATED_EVENT_GROUP_ID"
        value = var.employee_updated_event_group_id
      },
      {
        name  = "EMPLOYEE_UPDATED_EVENT_KAFKA_CONSUMERS"
        value = var.employee_updated_event_enabled
      },
      {
        name  = "MAINTENANCE_ORDER_NUMBER_UPDATE_SCHEDULE"
        value = var.maintenance_order_number_update_schedule
      },
      {
        name  = "FACTORY_CAR_PREPARATION_ORDER_NUMBER_UPDATE_SCHEDULE"
        value = var.factory_car_preparation_order_number_update_schedule
      },
      {
        name  = "TIRE_MANAGEMENT_ENABLED"
        value = var.tire_management_enabled
      },
      {
        name  = "TIRE_MANAGEMENT_DATA_EXPORT_CRON"
        value = var.tire_management_data_export_cron
      },
      {
        name  = "TIRE_MANAGEMENT_EMAIL_RECIPIENT"
        value = var.tire_management_email_recipient
      },
      {
        name  = "TIRE_MANAGEMENT_EMAIL_SENDER"
        value = var.tire_management_email_sender
      },
      {
        name  = "REPAIR_FIX_BASE_URL"
        value = var.repairfix_base_url
      },
      {
        name  = "TUV_TEAM_RECIPIENT_TO_EMAIL_ADDRESS"
        value = var.tuv_team_recipient_to_email_address
      },
      {
        name  = "TUV_TEAM_SENDER_EMAIL_ADDRESS"
        value = var.tuv_team_sender_email_address
      },
      {
        name  = "LOGISTICS_TEAM_RECIPIENT_TO_EMAIL_ADDRESS"
        value = var.logistics_team_recipient_to_email_address
      },
      {
        name  = "LOGISTICS_TEAM_SENDER_EMAIL_ADDRESS"
        value = var.logistics_team_sender_email_address
      },
      {
        name  = "SYNCHRONIZE_SCRAPPING_STATUS_UPDATE_CRON"
        value = var.synchronize_scrapping_status_update_cron
      },
      {
        name  = "ECC_ALLOW_INCOMING_TRAFFIC"
        value = var.ecc_allow_incoming_traffic
      },
      {
        name  = "KAFKA_PIA_AUTO_OFFSET_RESET"
        value = var.kafka_pia_auto_offset_reset
      },
      {
        name  = "KAFKA_PIA_CONSUMER_GROUP_ID"
        value = var.kafka_pia_consumer_group_id
      },
      {
        name  = "ENABLE_KAFKA_PIA_INTEGRATION"
        value = var.enable_kafka_pia_integration
      },
      {
        name  = "SYNCHRONIZE_BLOCKED_FOR_SALE_UPDATE_CRON"
        value = var.synchronize_blocked_for_sale_update_cron
      },
      {
        name  = "PREPARE_BLOCKED_FOR_SALE_UPDATE_CRON"
        value = var.prepare_blocked_for_sale_update_cron
      },
      {
        name  = "VEHICLE_TRANSFER_ALLOW_INCOMING_TRAFFIC"
        value = var.vehicle_transfer_allow_incoming_traffic
      },
      {
        name  = "ENABLE_KAFKA_P40_INTEGRATION"
        value = var.enable_kafka_p40_integration
      },
      {
        name  = "KAFKA_FINANCIAL_ASSET_TYPE_CONSUMER_GROUP_ID"
        value = var.kafka_financial_asset_type_consumer_group_id
      },
      {
        name  = "KAFKA_FINANCIAL_ASSET_TYPE_AUTO_OFFSET_RESET"
        value = var.kafka_financial_asset_type_auto_offset_reset
      },
      {
        name  = "DMS_VEHICLE_MIGRATION_TOPIC"
        value = var.dms_vehicle_migration_topic
      },
      {
        name  = "OAUTH2_CLIENT_ID_FOR_PACE_BALANCE_SHEET"
        value = var.oauth2_client_id_for_pace_balance_sheet
      },
      {
        name  = "OAUTH2_HOST_PACE_BALANCE_SHEET"
        value = var.oauth2_host_pace_balance_sheet
      },
      {
        name  = "PACE_BALANCE_SHEET_BASE_URL"
        value = var.pace_balance_sheet_base_url
      },
      {
        name  = "DMS_VEHICLE_MIGRATION_ENABLED"
        value = var.dms-vehicle-migration-enabled
      },
      {
        name  = "ENABLE_CARSYNC"
        value = var.carsync_enabled
      },
      {
        name  = "CARSYNC_USER_USERNAME"
        value = var.carsync_username
      },
      {
        name  = "CARSYNC_BASE_URL"
        value = var.carsync_base_url
      },
      {
        name  = "SYNC_MILEAGE_CRON"
        value = var.sync_mileage_cron
      },
      {
        name  = "FINANCIAL_ASSET_TYPE_UPDATE_SYNC_CRON"
        value = var.financial_asset_type_update_sync_cron
      },
      {
        name  = "VTSTAMM_ALLOW_INCOMING_TRAFFIC"
        value = var.vtstamm_allow_incoming_traffic
      },
      {
        name  = "MAILCLIENT_OVERRIDE_EMAIL_RECIPIENTS"
        value = var.mailclient_override_email_recipients
      },
      {
        name  = "MAILCLIENT_OVERRIDE_EMAIL_RECIPIENTS_LIST"
        value = var.mailclient_override_recipients_list
      },
      {
        name  = "VEHICLE_SALES_B2B_INTEGRATION_ENABLED"
        value = var.vehicle_sales_b2b_integration_enabled
      },
      {
        name  = "KAFKA_B2B_CONSUMER_GROUP_ID"
        value = var.kafka_b2b_consumer_group_id
      },
      {
        name  = "VEHICLE_REGISTRATION_MAIL_EXPORT_CRON"
        value = var.vehicle_registration_mail_export_cron
      },
      {
        name  = "VEHICLE_REGISTRATION_MAIL_EXPORT_ENABLED"
        value = var.vehicle_registration_mail_export_enabled
      },
      {
        name  = "VEHICLE_REGISTRATION_MAIL_EXPORT_SENDER"
        value = var.vehicle_registration_mail_export_sender
      },
      {
        name  = "VEHICLE_REGISTRATION_MAIL_EXPORT_RECIPIENT"
        value = var.vehicle_registration_mail_export_recipient
      },
      {
        name  = "RELA_ENABLED"
        value = var.rela_enabled
      },
      {
        name  = "RELA_BASE_URL"
        value = var.rela_base_url
      },
      {
        name  = "TIRE_CHANGE_RELA_SYNC_JOB_SCHEDULE"
        value = var.tire_change_rela_sync_job_schedule
      },
      {
        name  = "TIRE_CHANGE_APPOINTMENT_BOOKINGS_ID"
        value = var.tire_change_appointment_bookings_id
      },
      {
        name  = "TIRE_CHANGE_APPOINTMENT_SERVICE_ID"
        value = var.tire_change_appointment_service_id
      },
      {
        name  = "TIRE_CHANGE_APPOINTMENT_MS_BOOKINGS_USERNAME"
        value = var.tire_change_appointment_ms_bookings_username
      },
      {
        name  = "TIRE_CHANGE_APPOINTMENT_MS_BOOKING_CLIENT_ID"
        value = var.tire_change_appointment_ms_bookings_client_id
      }, {
        name  = "TIRE_CHANGE_APPOINTMENT_MS_BOOKING_CLIENT_SCOPE"
        value = var.tire_change_appointment_ms_bookings_client_scope
      },
      {
        name  = "TIRE_CHANGE_APPOINTMENT_MS_BOOKING_ENABLED"
        value = var.tire_change_appointment_ms_bookings_enabled
      },
    ]

    secrets = [
      for key, secret in module.ssm_parameters_ecs : {
        name = key
        arn  = secret.param_arn
      }
    ]

    port_mapping = [
      {
        name          = local.repo
        containerPort = 8080
        hostPort      = 8080
      }
    ]

    load_balancer_config = [
      {
        target_group_arn = module.alb.target_group_arn
        container_name   = local.repo
        container_port   = 8080
      },
      {
        target_group_arn = data.aws_lb_target_group.ui_alb.arn
        container_name   = local.repo
        container_port   = 8080
      }
    ]

    container_image = "${local.account_id}.dkr.ecr.${local.region}.amazonaws.com/${local.repo}:${var.commit_hash}"
    alarm_actions   = "arn:aws:lambda:${local.region}:${local.account_id}:function:ms_teams_notifications"
  }

  sd = {
    dns_records_ttl  = 60
    dns_records_type = "A"
  }

  gobd = {
    glue_role_name = "${local.repo}-crawler-glue-role"
  }

  dashboards = [
    {
      name = "Vehicle-Archival-Overview"
      source = jsonencode({
        "widgets" : [
          {
            "height" : 6,
            "width" : 12,
            "y" : 0,
            "x" : 0,
            "type" : "log",
            "properties" : {
              "query" : "SOURCE '/aws/ecs/vehicle-service' | SOURCE '/aws/ecs/vehicle-registration-server' | fields @timestamp, @message\n| filter @message like /vehicles with / and @message like /archived successfully/\n| parse @message /vehicles with \\[(?<vehicleIds>[^\\]]+)\\] archived successfully/\n| stats count(*) as ArchiveCount by bin(1h)\n",
              "region" : "eu-west-1",
              "stacked" : false,
              "title" : "Total vehicles Archived",
              "view" : "bar"
            }
          },
          {
            "height" : 6,
            "width" : 24,
            "y" : 6,
            "x" : 0,
            "type" : "log",
            "properties" : {
              "query" : "SOURCE '/aws/ecs/vehicle-service' | fields @timestamp, @message\n| filter @message like /Starting scheduled vehicle archive job/ or @message like /Archive job failed for job execution/\n| stats \n    count(@message) as TotalExecutions,\n    sum(@message like /Starting scheduled vehicle archive job/) as SuccessfulExecutions, \n    sum(@message like /Archive job failed for job execution/) as FailedExecutions \n    by bin(1h)\n| sort @timestamp desc",
              "region" : "eu-west-1",
              "stacked" : false,
              "title" : "Vehicle Archival Job Overview",
              "view" : "bar"
            }
          },
          {
            "type" : "log",
            "x" : 12,
            "y" : 0,
            "width" : 12,
            "height" : 6,
            "properties" : {
              "query" : "SOURCE '/aws/ecs/vehicle-service' | fields @timestamp, @message\n| filter @message like /vehicles with \\[[a-f0-9-]+\\] archived successfully/\n| parse @message \"vehicles with [*] archived successfully\" as vehicleIds\n| display @timestamp, vehicleIds, `mdc.job-execution-id`\n",
              "region" : "eu-west-1",
              "stacked" : false,
              "view" : "table",
              "title" : "Archived Vehicles"
            }
          }
        ]
      })
    },
    {
      name = "Vehicle-Data-PVH-Integration"
      source = jsonencode({
        "widgets" : [
          {
            "height" : 6,
            "width" : 12,
            "y" : 0,
            "x" : 0,
            "type" : "log",
            "properties" : {
              "query" : "SOURCE '/aws/ecs/vehicle-service' | fields @timestamp, @message, @logStream, @log\n| filter @message like \"Handle kafka message\"\n| stats count(@message) as pvhMessagesHandled by bin (1m)",
              "region" : "eu-west-1",
              "stacked" : false,
              "title" : "Protokollgruppe: /aws/ecs/vehicle-service",
              "view" : "bar"
            }
          },
          {
            "height" : 10,
            "width" : 24,
            "y" : 6,
            "x" : 0,
            "type" : "log",
            "properties" : {
              "query" : "SOURCE '/aws/ecs/vehicle-service' | fields @timestamp, message, @logStream, @log\n| filter @message like \"Got kafka message\" OR @message like \"Handle kafka message\" or @message like \"Creating vehicle with identifier\" or @message like \"Updating vehicle with identifier\"\n| sort @timestamp desc\n| limit 1000",
              "region" : "eu-west-1",
              "stacked" : false,
              "title" : "Protokollgruppe: /aws/ecs/vehicle-service",
              "view" : "table"
            }
          },
          {
            "height" : 6,
            "width" : 12,
            "y" : 0,
            "x" : 12,
            "type" : "log",
            "properties" : {
              "query" : "SOURCE '/aws/ecs/vehicle-service' | fields @timestamp, @message, @logStream, @log\n| filter @message like \"Creating vehicle with VIN\" or @message like \"Updating existing vehicle with VIN\"\n| parse @message 'Updating existing vehicle with VIN' as @updated\n| parse @message 'Creating vehicle with VIN' as @created\n| stats count(@created) as created, count(@updated) as updated by bin (1m)",
              "region" : "eu-west-1",
              "stacked" : true,
              "title" : "Protokollgruppe: /aws/ecs/vehicle-service",
              "view" : "timeSeries"
            }
          }
        ]
      })
    },
    {
      name = "Fleet-Vehicle-Data-Product"
      source = jsonencode({
        "widgets" : [
          {
            "height" : 6,
            "width" : 24,
            "y" : 0,
            "x" : 0,
            "type" : "log",
            "properties" : {
              "query" : "SOURCE '/aws/ecs/vehicle-service' | fields @timestamp, @message, @logStream, @log\n| filter @message like \"Executing fleet vehicle master data producer job with ID\"\n| parse @message 'Executing fleet vehicle master data producer job with ID' as @jobExecutions\n| stats count(@jobExecutions) as jobExecutions by bin (1h)",
              "region" : "eu-west-1",
              "stacked" : false,
              "view" : "bar",
              "title" : "Total Job Executions"
            }
          },
          {
            "type" : "log",
            "x" : 0,
            "y" : 12,
            "width" : 24,
            "height" : 6,
            "properties" : {
              "query" : "SOURCE '/aws/ecs/vehicle-service' | fields @timestamp as timestamp, message, @logStream,`mdc.job-execution-id` as jobExecutionId\n| filter @message like \"Failed to publish snapshot for vehicle\"\n| sort @timestamp desc\n| limit 1000",
              "region" : "eu-west-1",
              "stacked" : false,
              "view" : "table",
              "title" : "Failed Events"
            }
          },
          {
            "type" : "log",
            "x" : 0,
            "y" : 6,
            "width" : 24,
            "height" : 6,
            "properties" : {
              "query" : "SOURCE '/aws/ecs/vehicle-service' | fields @timestamp, @message, @logStream, @log\n| filter @message like \"Successfully published fleet vehicle snapshot for vehicle\" OR @message like \"Failed to publish snapshot for vehicle\"\n| parse @message 'Successfully published fleet vehicle snapshot for vehicle' as @success\n| parse @message 'Failed to publish snapshot for vehicle' as @fail\n| stats count(@success) as success, count(@fail) as fail by bin (5m)",
              "region" : "eu-west-1",
              "stacked" : false,
              "title" : "Published Events",
              "view" : "bar"
            }
          },
          {
            "type" : "text",
            "x" : 0,
            "y" : 18,
            "width" : 6,
            "height" : 6,
            "properties" : {
              "markdown" : "# Further resources \n[Streamzilla Grafana Dashboard](https://grafana.strmzlaprod.aws.platform.porsche.cloud/d/d436fe0b-9597-4c2b-96c8-dd862e58374b/customer-dashboard?var-passed_prefixes=emhs&var-passed_prefixes=pvh&var-passed_prefixes=one_vms&var-passed_consumer_groups=FRA_emhs_fleet_master_vehicle&orgId=1&from=now-7d&to=now&timezone=browser&var-cluster=lkc-q8rn8d&var-prefix=emhs&var-topic=FRA_emhs_fleet_master_vehicle_dev&refresh=30s)\n\n\n[Data product documentation](https://cicd.skyway.porsche.com/FP20/fleet-management/vehicle-service/-/tree/main/src/main/kotlin/com/fleetmanagement/modules/masterdata?ref_type=heads)"
            }
          }
        ]
      })
    },
    {
      name = "Financial-Asset-Type-Update"
      source = jsonencode({
        "widgets" : [
          {
            "height" : 9,
            "width" : 12,
            "y" : 0,
            "x" : 0,
            "type" : "log",
            "properties" : {
              "query" : "SOURCE '/aws/ecs/vehicle-service' | fields @timestamp\n| filter logger = \"com.fleetmanagement.modules.vehicledata.integrations.p40.streamzilla.FinancialAssetTypeUpdateEventConsumer\"\n| filter message LIKE /Finished processing of .* to update financial asset type/\n| parse message \"Finished processing of * to update financial asset type: *.\" as vin, assetType\n| display assetType, assetType=\"UV\" as uv\n| display assetType, assetType=\"AV\" as av\n| stats count(*) as total, sum(av) as AV, sum(uv) as UV by bin(1d)",
              "region" : "eu-west-1",
              "stacked" : false,
              "view" : "timeSeries",
              "title" : "Financial Asset Type Updates"
            }
          },
          {
            "type" : "log",
            "height" : 9,
            "width" : 12,
            "y" : 0,
            "x" : 12,
            "properties" : {
              "query" : "SOURCE '/aws/ecs/vehicle-service' | fields @timestamp\n| filter logger = \"com.fleetmanagement.modules.vehicledata.integrations.p40.streamzilla.FinancialAssetTypeUpdateEventConsumer\"\n| display level, level=\"WARN\" as warnings\n| display level, level=\"ERRO\" as errors\n| stats sum(errors) as numberOfErrors, sum(warnings) as numberOfWarnings by bin(1d)",
              "region" : "eu-west-1",
              "stacked" : false,
              "view" : "timeSeries",
              "title" : "Errors and Warnings Updating Financial Asset Type"
            }
          },
          {
            "type" : "log",
            "height" : 9,
            "width" : 12,
            "y" : 9,
            "x" : 0,
            "properties" : {
              "query" : "SOURCE '/aws/ecs/vehicle-service' | fields @timestamp\n| filter logger = \"com.fleetmanagement.modules.vehicledata.integrations.p40.streamzilla.FinancialAssetTypeUpdateEventConsumer\"\n| filter message LIKE /Cannot find vehicle for vin(.*)/\n| parse message \"Cannot find vehicle for vin(*)\" as vin\n",
              "region" : "eu-west-1",
              "stacked" : false,
              "title" : "Update Financial Asset Type of Unknown Vehicles",
              "view" : "event_table"
            }
          },
        ]
      })
    },
    {
      name = "API-Response-Times-By-Endpoint"
      source = jsonencode({
        "widgets" : [
          {
            "type" : "log",
            "x" : 0,
            "y" : 0,
            "width" : 24,
            "height" : 3,
            "properties" : {
              "query" : "SOURCE '/aws/ecs/vehicle-service' | fields message, `mdc.x-trace-id`\n| filter @message like 'RequestTimingFilter'\n| parse @message  '[* *] took *ms to respond with' as httpVerb, endpoint, responseTime\n| parse endpoint /(?<uuidMask>[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})/\n| parse endpoint /(?<vin>[A-HJ-NPR-Z0-9]{17})/ \n| fields replace(replace(endpoint, coalesce(uuidMask, '') , \"\"), coalesce(vin, ''), \"\") as cleaned_parse \n| filter cleaned_parse like '/api/vs/ui/vehicle-transfers/update'\n| stats pct(responseTime, 50) as p50_response_time, \n        pct(responseTime, 75) as p75_response_time, \n        pct(responseTime, 90) as p90_response_time, \n        pct(responseTime, 95) as p95_response_time,\n        pct(responseTime, 99) as p99_response_time \n",
              "queryLanguage" : "CWLI",
              "region" : "eu-west-1",
              "title" : "POST /api/vs/ui/vehicle-transfers/update",
              "view" : "table"
            }
          },
          {
            "type" : "log",
            "x" : 0,
            "y" : 3,
            "width" : 24,
            "height" : 3,
            "properties" : {
              "query" : "SOURCE '/aws/ecs/vehicle-service' | fields message, `mdc.x-trace-id`\n| filter @message like 'RequestTimingFilter'\n| parse @message  '[* *] took *ms to respond with' as httpVerb, endpoint, responseTime\n| parse endpoint /(?<uuidMask>[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})/\n| parse endpoint /(?<vin>[A-HJ-NPR-Z0-9]{17})/ \n| fields replace(replace(endpoint, coalesce(uuidMask, '') , \"\"), coalesce(vin, ''), \"\") as cleaned_parse \n| filter cleaned_parse like '/api/vs/ui/vehicles/update'\n| stats pct(responseTime, 50) as p50_response_time, \n        pct(responseTime, 75) as p75_response_time, \n        pct(responseTime, 90) as p90_response_time, \n        pct(responseTime, 95) as p95_response_time,\n        pct(responseTime, 99) as p99_response_time \n",
              "queryLanguage" : "CWLI",
              "region" : "eu-west-1",
              "title" : "POST /api/vs/ui/vehicles/update",
              "view" : "table"
            }
          },
          {
            "type" : "log",
            "x" : 0,
            "y" : 6,
            "width" : 24,
            "height" : 3,
            "properties" : {
              "query" : "SOURCE '/aws/ecs/vehicle-service' | fields message, `mdc.x-trace-id`\n| filter @message like 'RequestTimingFilter'\n| parse @message  '[* *] took *ms to respond with' as httpVerb, endpoint, responseTime\n| parse endpoint /(?<uuidMask>[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})/\n| parse endpoint /(?<vin>[A-HJ-NPR-Z0-9]{17})/ \n| fields replace(replace(endpoint, coalesce(uuidMask, '') , \"\"), coalesce(vin, ''), \"\") as cleaned_parse \n| filter cleaned_parse like '/api/vs/ui/vehicleregistration/orders'\n| stats pct(responseTime, 50) as p50_response_time, \n        pct(responseTime, 75) as p75_response_time, \n        pct(responseTime, 90) as p90_response_time, \n        pct(responseTime, 95) as p95_response_time,\n        pct(responseTime, 99) as p99_response_time \n",
              "queryLanguage" : "CWLI",
              "region" : "eu-west-1",
              "title" : "GET /api/vs/ui/vehicleregistration/orders",
              "view" : "table"
            }
          },
          {
            "type" : "log",
            "x" : 0,
            "y" : 9,
            "width" : 24,
            "height" : 3,
            "properties" : {
              "query" : "SOURCE '/aws/ecs/vehicle-service' | fields message, `mdc.x-trace-id`\n| filter @message like 'RequestTimingFilter'\n| parse @message  '[* *] took *ms to respond with' as httpVerb, endpoint, responseTime\n| parse endpoint /(?<uuidMask>[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})/\n| parse endpoint /(?<vin>[A-HJ-NPR-Z0-9]{17})/ \n| fields replace(replace(endpoint, coalesce(uuidMask, '') , \"\"), coalesce(vin, ''), \"\") as cleaned_parse \n| filter cleaned_parse like '/api/vs/ui/vehicles/evaluation'\n| stats pct(responseTime, 50) as p50_response_time, \n        pct(responseTime, 75) as p75_response_time, \n        pct(responseTime, 90) as p90_response_time, \n        pct(responseTime, 95) as p95_response_time,\n        pct(responseTime, 99) as p99_response_time \n",
              "queryLanguage" : "CWLI",
              "region" : "eu-west-1",
              "title" : "POST /api/vs/ui/vehicles/evaluation",
              "view" : "table"
            }
          },
          {
            "type" : "log",
            "x" : 0,
            "y" : 12,
            "width" : 24,
            "height" : 3,
            "properties" : {
              "query" : "SOURCE '/aws/ecs/vehicle-service' | fields message, `mdc.x-trace-id`\n| filter @message like 'RequestTimingFilter'\n| parse @message  '[* *] took *ms to respond with' as httpVerb, endpoint, responseTime\n| parse endpoint /(?<uuidMask>[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})/\n| parse endpoint /(?<vin>[A-HJ-NPR-Z0-9]{17})/ \n| fields replace(replace(endpoint, coalesce(uuidMask, '') , \"\"), coalesce(vin, ''), \"\") as cleaned_parse \n| filter cleaned_parse like '/api/vs/ui/vehicles/'\n| stats pct(responseTime, 50) as p50_response_time, \n        pct(responseTime, 75) as p75_response_time, \n        pct(responseTime, 90) as p90_response_time, \n        pct(responseTime, 95) as p95_response_time,\n        pct(responseTime, 99) as p99_response_time \n",
              "queryLanguage" : "CWLI",
              "region" : "eu-west-1",
              "title" : "GET /api/vs/ui/vehicles/vehicle-id",
              "view" : "table"
            }
          },
          {
            "type" : "log",
            "x" : 0,
            "y" : 15,
            "width" : 24,
            "height" : 3,
            "properties" : {
              "query" : "SOURCE '/aws/ecs/vehicle-service' | fields message, `mdc.x-trace-id`\n| filter @message like 'RequestTimingFilter'\n| parse @message  '[* *] took *ms to respond with' as httpVerb, endpoint, responseTime\n| parse endpoint /(?<uuidMask>[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})/\n| parse endpoint /(?<vin>[A-HJ-NPR-Z0-9]{17})/ \n| fields replace(replace(endpoint, coalesce(uuidMask, '') , \"\"), coalesce(vin, ''), \"\") as cleaned_parse \n| filter cleaned_parse like '/api/vs/ui/vehicles/search'\n| stats pct(responseTime, 50) as p50_response_time, \n        pct(responseTime, 75) as p75_response_time, \n        pct(responseTime, 90) as p90_response_time, \n        pct(responseTime, 95) as p95_response_time,\n        pct(responseTime, 99) as p99_response_time \n",
              "queryLanguage" : "CWLI",
              "region" : "eu-west-1",
              "title" : "POST /api/vs/ui/vehicles/search (Vehicle Manager)",
              "view" : "table"
            }
          }
        ]
      })
    },
    {
      name = "Trace-ID-analyzer"
      source = jsonencode({
        "variables" : [
          {
            "type" : "pattern",
            "pattern" : "traceId",
            "inputType" : "input",
            "id" : "traceId",
            "label" : "Trace ID from response header",
            "visible" : true
          }
        ],
        "widgets" : [
          {
            "type" : "log",
            "x" : 0,
            "y" : 0,
            "width" : 24,
            "height" : 6,
            "properties" : {
              "query" : "SOURCE '/aws/ecs/vehicle-location-service' | SOURCE '/aws/ecs/vehicle-registration-server' | SOURCE '/aws/ecs/vehicle-service' | SOURCE '/aws/ecs/aws-distro-opentelemetry-service' | SOURCE '/aws/ecs/damage-management-service' | SOURCE '/aws/ecs/user-service' | fields message, @timestamp, @entity.KeyAttributes.Name, @message, @logStream, @log \n| filter mdc.trace_id = \"traceId\" or mdc.x-trace-id= \"traceId\"\n| sort @timestamp desc \n| limit 10000",
              "region" : "eu-west-1",
              "stacked" : false,
              "title" : "",
              "view" : "table"
            }
          }
        ]
      })
    },
  ]
}
