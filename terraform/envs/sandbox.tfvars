################################################################################
# Sandbox Overrides
#
# NOTE: This file is not intended to be applied on its own.
# It serves only to override specific variables in the respective environment
# configuration files (e.g., dev.tfvars, staging.tfvars, prod.tfvars).
#
# Apply this file *after* the base environment file to override selected values:
# Example:
#   terraform apply -var-file=dev.tfvars -var-file=sandbox.tfvars
#
#
# NOTE: Any changes to this file should include an update of the runbook
# docs/arc42/chapters/runbooks/disable_individual_outgoing_interfaces.adoc
################################################################################

##############################
# Archiving / GOBD Jobs
##############################

# Disable vehicle archive jobs
vehicle_archive_cron_schedule    = "0 0 0 1 1 ? 2099"
vehicle_archive_enable_scheduler = "false"

##############################
# Cost Center Update Job
##############################

# Disable cost center update jobs
cost_center_update_cron_schedule             = "0 0 0 1 1 ? 2099"
cost_center_update_preparation_cron_schedule = "0 0 0 1 1 ? 2099"

##############################
# Feature Flags
##############################

# Send API calls to user service
feature_flags_read_vehicle_person_enabled = true

# Enable Kafka consumer for license plate updates
license_plate_kafka_consumers = true

# Enable fetching number of damages from DMS
number_of_damages_enable = true

# Enable employee update event from user service
employee_updated_event_enabled = true

##############################
# PDI Ordering
##############################

# Disable PDI ordering email scheduler
pdi_ordering_email_schedule = "0 0 0 1 1 ? 2099"

# TODO: Set recipients — ask Luisa (FDC3)
pdi_ordering_recipient_to_email_address        = ""
pdi_ordering_recipient_cc_email_address        = ""
pdi_ordering_sender_email_address              = ""
vehicle_transfer_delivery_sender_email_address = ""

##############################
# MS Bookings
##############################

# Disable MS Bookings cron job
ms_booking_appointments_job_schedule = "0 0 0 1 1 ? 2099"
msbooking_enable                     = false

##############################
# Order Number Updates
##############################

# Use PACE, so disable these
maintenance_order_number_update_schedule             = "0 0 0 1 1 ? 2099"
factory_car_preparation_order_number_update_schedule = "0 0 0 1 1 ? 2099"

##############################
# Fleet Master Data
##############################

# Enable publishing fleet master vehicle updates on given kafka topic [Data Product]
fleet_master_data_cron_schedule = "0 0 0 1 1 ? 2099"
enable_fleet_master_data        = false
fleet_master_data_topic         = "FRA_emhs_fleet_master_vehicle_dev"

# Tire management email contacts - ask Philipp
tire_management_email_recipient  = ""
tire_management_email_sender     = "<EMAIL>"
tire_management_enabled          = false
tire_management_data_export_cron = "0 0 0 1 1 ? 2099"

##############################
# Vehicle Registration Mail Export
##############################
vehicle_registration_mail_export_cron      = "0 0 0 1 1 ? 2099"
vehicle_registration_mail_export_enabled   = false
vehicle_registration_mail_export_sender    = "<EMAIL>"
vehicle_registration_mail_export_recipient = "<EMAIL>"

##############################
# TÜV Notifications
##############################

# TODO: Set email addresses — ask Luisa
tuv_team_recipient_to_email_address       = ""
tuv_team_sender_email_address             = ""
logistics_team_recipient_to_email_address = ""
logistics_team_sender_email_address       = ""

##############################
# Balance sheet jobs use PACE, so disable these
##############################
synchronize_scrapping_status_update_cron = "0 0 0 1 1 ? 2099"
synchronize_blocked_for_sale_update_cron = "0 0 0 1 1 ? 2099"
prepare_blocked_for_sale_update_cron     = "0 0 0 1 1 ? 2099"


##############################
# incoming traffic for ecc
##############################
ecc_allow_incoming_traffic = false

##############################
# incoming traffic for public vehicle transfer endpoints
##############################
vehicle_transfer_allow_incoming_traffic = false

##############################
# incoming traffic for P40 account2report topic
##############################
enable_kafka_p40_integration = false

############################
# DMS vehicle migration
############################
dms-vehicle-migration-enabled = false

#####
# CarSync
#####
carsync_enabled = false

#####
# update of FVM vehicles with financial asset type
#####
financial_asset_type_update_sync_cron = "0 0 0 1 1 ? 2099"

##############################
# incoming traffic for public vtstamm endpoints
##############################
vtstamm_allow_incoming_traffic = false

##############################
# global app configuration
##############################
mailclient_override_email_recipients = true
mailclient_override_recipients_list  = "<EMAIL>, <EMAIL>"

##############################
# Tire Change Appointment
##############################
rela_enabled                        = false
tire_change_appointment_ms_bookings_enabled  = false
tire_change_rela_sync_job_schedule = "0 0 0 1 1 ? 2099"
