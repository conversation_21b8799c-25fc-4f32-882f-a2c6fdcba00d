env                           = "prod"
firehose_cloudwatch_role_name = "vehicle-service-firehose-cloudwatch-role"
firehose_s3_role_name         = "vehicle-service-firehose-s3-role"
vehicle_service_domain        = "vs.mobilityservicesprod.aws.platform.porsche.cloud"
dns_hosted_zone_id            = "Z037097710YRZ0UWMFTC7"
dns_hosted_zone_name          = "mobilityservicesprod.aws.platform.porsche.cloud"
app_domain                    = "mobilityservices.porsche.services"
#TODO: Add values before prod deployment
oidc_token_url          = "https://identity.vwgroup.io/oidc/v1/token"
pvh_base_url            = "https://api.emea.gravity.porsche.com/prod-business-asdcs-vehicle-service"
oauth2_issuer_uri       = "https://login.microsoftonline.com/56564e0f-83d3-4b52-92e8-a6bb9ea36564/v2.0"
oauth2_token_uri        = "https://login.microsoftonline.com/56564e0f-83d3-4b52-92e8-a6bb9ea36564/oauth2/v2.0"
pvh_client_id           = "cfb09cc0-402d-4661-87c6-6a06cdb4e9ca@apps_vw-dilab_com"
oauth2_vehicle_audience = "ee31711f-d16a-473b-b94a-9e140e775882"
#TODO: verify this variable and throw it if not used in code
oauth2_dm_audience                               = ""
kafka_user                                       = "LRHX445WMBQRCHKS"
kafka_enable_pvh_ingestion                       = true
kafka_pvh_stop_on_error                          = false
kafka_pvh_consumer_group_id                      = "FRA_emhs_pvh_Vehicle_Embargo_01_2024"
kafka_bootstrap_servers                          = "pkc-zxm13.eu-west-1.aws.confluent.cloud:9092"
enable_albheaders_endpoint_for_testing           = false
vehicle_registration_base_uri                    = "http://vehicle-registration-server.emh-fleet-management:8080"
vehicle_registration_base_url                    = "http://vehicle-registration-server.emh-fleet-management:8080/api/vr"
vehicle_location_base_url                        = "https://ls.mobilityservicesprod.aws.platform.porsche.cloud"
logout_uri                                       = "https://login.microsoftonline.com/56564e0f-83d3-4b52-92e8-a6bb9ea36564/oauth2/v2.0/logout"
oauth2_vehicle_registration_app_id               = "adf1f8f7-67bc-441b-bd2f-bcdec37c6a16"
allow_overwrite_of_groups_from_request_header    = false
retention_in_days                                = 5475
s3_bucket_archive                                = "vehicle-service-archive-906037121137"
s3_bucket_vehicle_transfer_archive               = "vehicle-transfer-service-archive-906037121137"
s3_bucket_vehicle_history_archive                = "vehicle-service-history-archive-906037121137"
s3_bucket_vehicle_sales_archive                  = "vehicle-service-sales-archive-906037121137"
s3_bucket_legal_hold                             = "vehicle-service-legal-hold-906037121137"
s3_bucket_vehicle_sales_legal_hold               = "vehicle-service-sales-legal-hold-906037121137"
fms_migration_enabled                            = true
s3_bucket_fms_migration                          = "vehicle-service-fms-migration-906037121137"
vehicle_registration_client_id                   = "d410e939-ce74-4c77-a7c7-269fffdd4c04"
vehicle_registration_archive_client_id           = "d410e939-ce74-4c77-a7c7-269fffdd4c04"
vehicle_registration_legalhold_client_id         = "950cbecc-2ad9-4662-abf5-bab0183a7163"
vehicle_archive_cron_schedule                    = "0 0 1 * * ?"
vehicle_archive_enable_scheduler                 = "true"
cost_center_update_cron_schedule                 = "0 0 0 1 1 ? 2099"
cost_center_update_preparation_cron_schedule     = "0 0 0 1 1 ? 2099"
object_location_updated_topic_group_id           = "FRA_emhs_vls_object_location_event.prod.consumer"
object_location_updated_topic                    = "FRA_emhs_vls_object_location_event"
vehicle_location_vls_kafka_integration_enabled   = false
vehicle_location_client_id                       = "40e9aaa5-7613-410b-a81e-25a4c3fe35a3"
vehicle_location_scope                           = "2a28c3b3-da68-457f-8165-5694eadbcd04/.default"
vehicle_location_azure_tenant_id                 = "56564e0f-83d3-4b52-92e8-a6bb9ea36564"
location_updated_topic_group_id                  = "FRA_emhs_vls_location_updated_event.prod.consumer"
location_updated_topic                           = "FRA_emhs_vls_location_updated_event"
location_deleted_topic_group_id                  = "FRA_emhs_vls_location_deleted_event.prod.consumer"
location_deleted_topic                           = "FRA_emhs_vls_location_deleted_event"
employee_base_url                                = "https://user.mobilityservicesprod.aws.platform.porsche.cloud/api"
employee_api_client_id                           = "3b381739-6f57-4e6f-a48c-8b4e6adb7c24"
employee_api_scope                               = "a49eb9f7-36ef-4fdf-8105-378f64b55985/.default"
employee_oauth_token_uri                         = "https://login.microsoftonline.com/56564e0f-83d3-4b52-92e8-a6bb9ea36564/oauth2/v2.0/token"
cap_oauth2_client_id                             = "replace-before-deployment"
ppn_host                                         = "ppn.porsche.com"
cap_ibm_api_gateway_client_id                    = "replace-before-deployment"
cap_base_url                                     = "replace-before-deployment"
cap_region                                       = "europe"
planned_vehicle_transfer_initialization_schedule = "0 9 * * * ?"
feature_flags_read_vehicle_person_enabled        = false
kafka_auto_offset_reset                          = "latest"
timestamp_filtering_disable_after                = "2024-11-13T12:25:00Z"
container_cpu                                    = 2048
container_memory                                 = 4096
entra_id_azure_tenant_id                         = "56564e0f-83d3-4b52-92e8-a6bb9ea36564"
enable_entra_id_scheduler                        = true
entra_id_sync_cron_schedule                      = "0 0 0 * * ?"
entra_id_app_role_assignment_id                  = "58e1aef1-af0c-4c13-bfbe-8a6cd4537a35"
entra_id_administrative_unit_id                  = "e5c3e177-8e61-4c16-b085-1ffa5efa83a0"
entra_id_oauth2_client_id                        = "072e2326-691d-47e3-899a-8164b20a6592"
// todo: create consumer in prod
license_plate_topic                                  = "FRA_emhs_vr_license_plate"
license_plate_group_id                               = "FRA_emhs_vr_license_plate.consumer"
license_plate_kafka_consumers                        = false
vehicle_transfer_delivery_lead_time                  = 5
pre_delivery_inspection_lead_time                    = 5
number_of_damages_topic                              = "FRA_emhs_dms_numer_of_damages_topic"
number_of_damages_group_id                           = "FRA_emhs_dms_numer_of_damages_topic.consumer"
number_of_damages_enable                             = true
pdi_ordering_email_schedule                          = "0 0 0 1 1 ? 2099"
pdi_ordering_recipient_to_email_address              = "<EMAIL>,<EMAIL>"
pdi_ordering_recipient_cc_email_address              = "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>"
pdi_ordering_sender_email_address                    = "<EMAIL>"
vehicle_transfer_delivery_sender_email_address       = "<EMAIL>"
smtp_host                                            = "appmail-auth.fw.porsche.de"
smtp_port                                            = 465
smtp_username                                        = "EXHXIV8"
dmsi_base_uri                                        = "replace-me"
rwil_idp_host                                        = "idp.rwil.prod.eu.bp.aws.cloud.vwgroup.com"
dmsi_oauth2_client_id                                = "FleetVehicleManager"
vehicle_campaigns_enabled                            = false
vehicle_status_recalculation_cron                    = "0 0 2 * * ?"
ms_booking_client_client_id                          = ""
ms_booking_client_scope                              = "https://graph.microsoft.com/.default"
ms_booking_appointments_job_schedule                 = "0 */10 * * * ?"
booking_id                                           = "<EMAIL>"
service_id                                           = ""
msbooking_questions_id_delivery_vin                  = ""
msbooking_questions_id_delivery_license_plate        = ""
msbooking_questions_id_delivery_model                = ""
msbooking_questions_id_return_vin                    = ""
msbooking_questions_id_return_license_plate          = ""
msbooking_questions_id_return_model                  = ""
msbooking_enable                                     = false
msbooking_username                                   = ""
oauth2_client_id_for_pace                            = "not-empty-or-spring-validation-crashed"                              #TODO replace me with PROD
oauth2_host_pace                                     = "porsche-prd-spine-integration.authentication.eu10.hana.ondemand.com" #TODO replace me with PROD
pace_base_url                                        = "https://porsche-prd-spine-integration.it-cpi018-rt.cfapps.eu10-003.hana.ondemand.com"
employee_updated_event_topic                         = "FRA_emhs_us_employee_update_topic"
employee_updated_event_group_id                      = "FRA_emhs_us_employee_update_topic.consumer"
employee_updated_event_enabled                       = false
maintenance_order_number_update_schedule             = "30 */5 * * * ?"
fleet_master_data_topic                              = "FRA_emhs_fleet_master_vehicle"
enable_fleet_master_data                             = false
fleet_master_data_cron_schedule                      = "0 * * * * ?"
kafka_schema_registry_user                           = ""
kafka_schema_registry_url                            = "https://psrc-p6o1m.eu-central-1.aws.confluent.cloud"
kafka_schema_auto_register                           = false
factory_car_preparation_order_number_update_schedule = "15 */5 * * * ?"
tire_management_enabled                              = false
tire_management_data_export_cron                     = "0 0 1 * * ?"
tire_management_email_recipient                      = "replace-me"
tire_management_email_sender                         = "<EMAIL>"
repairfix_base_url                                   = "https://portal.motum.eu"
tuv_team_recipient_to_email_address                  = "<EMAIL>,<EMAIL>,<EMAIL>"
tuv_team_sender_email_address                        = "<EMAIL>"
logistics_team_recipient_to_email_address            = "<EMAIL>,<EMAIL>"
logistics_team_sender_email_address                  = "<EMAIL>"
synchronize_scrapping_status_update_cron             = "0 0 0 1 1 ? 2099"
ecc_allow_incoming_traffic                           = true
kafka_pia_consumer_group_id                          = "FRA_emhs_one_vms_piaom_importer_invoice_prod"
enable_kafka_pia_integration                         = false
kafka_pia_auto_offset_reset                          = "latest"
synchronize_blocked_for_sale_update_cron             = "0 0 0 1 1 ? 2099"
# when enabled, prepare job should run last day of the month "0 0 0 L * ?"
prepare_blocked_for_sale_update_cron         = "0 0 0 1 1 ? 2099"
vehicle_transfer_allow_incoming_traffic      = true
kafka_financial_asset_type_consumer_group_id = "FRA_emhs_p40_account_2_report_fleet_vehicle_manager_asset_class_prod"
enable_kafka_p40_integration                 = false
kafka_financial_asset_type_auto_offset_reset = "latest"
dms_vehicle_migration_topic                  = "FRA_emhs_dms_vehicle_migration"
oauth2_client_id_for_pace_balance_sheet      = "sb-e94825d5-b482-4e63-8245-e5de4fc8e4bf!b138742|it-rt-porsche-dev-spine-integration!b117912"
oauth2_host_pace_balance_sheet               = "porsche-prd-spine-integration.authentication.eu10.hana.ondemand.com"
pace_balance_sheet_base_url                  = "https://porsche-prd-spine-integration.it-cpi018-rt.cfapps.eu10-003.hana.ondemand.com"
dms-vehicle-migration-enabled                = false
carsync_enabled                              = false
carsync_username                             = "<EMAIL>"
carsync_base_url                             = "https://porsche.carsync-log.de/api/"
sync_mileage_cron                            = "0 0 0 6 * ? *"
financial_asset_type_update_sync_cron        = "0 0 0 1 1 ? 2099"
vtstamm_allow_incoming_traffic               = true
mailclient_override_email_recipients         = false
mailclient_override_recipients_list          = ""
vehicle_sales_b2b_integration_enabled        = false
kafka_b2b_consumer_group_id                  = "FRA_emhs_honeypotters_completed_auctions_prod"
vehicle_registration_mail_export_cron        = "0 5 0 ? * TUE-SAT"
vehicle_registration_mail_export_enabled     = false
vehicle_registration_mail_export_sender      = "<EMAIL>"
vehicle_registration_mail_export_recipient   = "<EMAIL>"
rela_enabled                                 = false
# TODO Update the base Url for prod when known, the current one is for staging!
rela_base_url                                = "https://rela.sdnord.de"
tire_change_rela_sync_job_schedule           = "0 0/15 * * * ?"
#TODO: fill in details
tire_change_appointment_bookings_id                  =  ""
tire_change_appointment_service_id                   =  ""
tire_change_appointment_ms_bookings_username         =  ""
tire_change_appointment_ms_bookings_client_id        =  ""
tire_change_appointment_ms_bookings_client_scope     =  ""
tire_change_appointment_ms_bookings_enabled          = false
