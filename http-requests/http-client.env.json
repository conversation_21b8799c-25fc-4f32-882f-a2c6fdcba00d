{"local": {"alb_host": "app.mobilityservicesdev.aws.platform.porsche-preview.cloud", "azure_tenant_id": "07f8138c-e3e4-4b45-842b-9ecc3ba58cf4", "oauth_m2m_api_client_id": "7b4c1caa-a989-4491-9399-ef013de03768", "oauth_m2m_legalhold_api_client_id": "d594f124-a777-41ca-b09b-9af3def7379a", "api_scope": "1d612e2b-317e-4d59-80f5-f80b3e3a33c0", "msbooking_host_idp": "login.microsoftonline.com", "msbooking_scope": "https://graph.microsoft.com/.default", "msbooking_tenant_id": "07f8138c-e3e4-4b45-842b-9ecc3ba58cf4", "msbooking_oauth_m2m_api_client_id": "f1f4be7a-1609-4126-88ba-a635cb8dde53", "bookings_id": "<EMAIL>"}, "staging": {"gravity_idp_host": "identity-sandbox.vwgroup.io", "gravity_host": "api.staging.emea.gravity.porsche.com"}, "prod": {"alb_host": "app.mobilityservicesprod.aws.platform.porsche.cloud", "azure_tenant_id": "56564e0f-83d3-4b52-92e8-a6bb9ea36564", "oauth_m2m_api_client_id": "09ba166f-8bd4-4c48-9bd1-4a7b7b94a0ea", "oauth_m2m_legalhold_api_client_id": "950cbecc-2ad9-4662-abf5-bab0183a7163", "api_scope": "ee31711f-d16a-473b-b94a-9e140e775882"}, "ibm-gw-dev": {"host": "eu-1.dev.api.porsche.io", "environment": "dev", "azure_test_tenant_id": "07f8138c-e3e4-4b45-842b-9ecc3ba58cf4", "oauth_m2m_test_api_client_id": "7b4c1caa-a989-4491-9399-ef013de03768", "api_test_scope": "1d612e2b-317e-4d59-80f5-f80b3e3a33c0", "azure_tenant_id": "56564e0f-83d3-4b52-92e8-a6bb9ea36564", "api_client_id": "7746b107f6a8fa8a8655e7deb65a49d2", "ecc_ibm_api_gateway_client_id": "b57ca3ff928615bc2d9f5a4368f0deaf", "oauth_m2m_api_client_id": "784b80a2-49dd-4ea0-a10a-fe05fd199ac3", "ecc_oauth_m2m_api_client_id": "da6d503a-6101-4d6d-9677-4e8b1c6a9870", "api_scope": "784b80a2-49dd-4ea0-a10a-fe05fd199ac3"}, "ibm-gw-test": {"host": "eu-1.test.api.porsche.io", "environment": "test", "azure_test_tenant_id": "07f8138c-e3e4-4b45-842b-9ecc3ba58cf4", "oauth_m2m_test_api_client_id": "7b4c1caa-a989-4491-9399-ef013de03768", "api_test_scope": "1d612e2b-317e-4d59-80f5-f80b3e3a33c0", "azure_tenant_id": "56564e0f-83d3-4b52-92e8-a6bb9ea36564", "api_client_id": "abfd50f3549d353f1a46ceff04709b32", "oauth_m2m_api_client_id": "c3ad0499-04ad-4b89-b1e0-2c577f6679c6", "ecc_oauth_m2m_api_client_id": "da6d503a-6101-4d6d-9677-4e8b1c6a9870", "ecc_ibm_api_gateway_client_id": "a35bd76497b9f138605746db48be0268", "api_scope": "c3ad0499-04ad-4b89-b1e0-2c577f6679c6", "ppn_tenant": "ppnlite.porsche.com", "ppn_oauth_m2m_api_client_id": "df5d1b6a-2e6d-4053-ba20-bc3ab6b460ac", "host_gateway": "eu-0.test.api.porsche.io", "cap_ibm_api_gateway_client_id": "8d81e088c8b68810f3e32ba025dc4a7f", "commissionNumber": "7045335", "capVehicleGuid": "44B28868C94C64F1E10000008D24B6A2", "bpId": "20007858", "region": "europe"}, "ibm-gw-prod": {"host": "eu-1.api.porsche.io", "environment": "prod", "azure_test_tenant_id": "56564e0f-83d3-4b52-92e8-a6bb9ea36564", "oauth_m2m_test_api_client_id": "43fcdeae-751c-49f9-8961-6be3ed238f85", "api_test_scope": "43fcdeae-751c-49f9-8961-6be3ed238f85", "azure_tenant_id": "56564e0f-83d3-4b52-92e8-a6bb9ea36564", "api_client_id": "b7b96fe581cda6d0792fd5ad26c78634", "oauth_m2m_api_client_id": "43fcdeae-751c-49f9-8961-6be3ed238f85", "api_scope": "43fcdeae-751c-49f9-8961-6be3ed238f85", "ppn_tenant": "ppn.porsche.com", "ppn_oauth_m2m_api_client_id": "", "host_gateway": "eu-0.api.porsche.io", "cap_ibm_api_gateway_client_id": "", "commissionNumber": "I64455", "capVehicleGuid": "005056BBC8521EECBE8837469A7A9388", "bpId": "2010026694", "region": "europe"}, "dmsi-test": {"oauth_m2m_api_client_id": "DEUP50050_OTHER", "host_idp": "https://idp.rwil.qa.eu.bp.aws.cloud.vwgroup.com", "host_gateway": "rwil-qa.volkswagenag.com", "KVPS": "DEUP01040", "VIN": "WP1ZZZ9YZKDA45051"}, "dmsi-prod": {"oauth_m2m_api_client_id": "FleetVehicleManager", "host_idp": "idp.rwil.prod.eu.bp.aws.cloud.vwgroup.com", "host_gateway": "rwil.volkswagenag.com", "KVPS": "DEUP01040", "VIN": "WP1ZZZ9Y0RDA77224"}, "entraId-staging": {"oauth_m2m_api_client_id": "4025ad4f-5367-46cb-ae91-05e21d176efe", "host_idp": "login.microsoftonline.com", "scope": "https://graph.microsoft.com/.default", "tenant_id": "07f8138c-e3e4-4b45-842b-9ecc3ba58cf4", "userId": "e14e5528-5286-4d00-b604-43c796694594", "appRoleAssignmentId": "e14e5528-5286-4d00-b604-43c796694594", "administrativUnitId": "6bbcd412-527e-4e4d-89c1-5862b4e5d239", "groupId": "b78d429b-c2e4-46ea-bed4-6b1c9ca6f1c0"}, "spine-gw-test": {"host_cpi": "porsche-qas-spine-integration.it-cpi018-rt.cfapps.eu10-003.hana.ondemand.com", "host_idp": "porsche-qas-spine-integration.authentication.eu10.hana.ondemand.com", "environment": "test", "oauth_m2m_api_client_id": "sb-f39ece74-12bd-4f49-87c3-8eb9e20d37fe!b139374|it-rt-porsche-qas-spine-integration!b117912"}, "spine-gw-prod": {"host_cpi": "porsche-prd-spine-integration.it-cpi018-rt.cfapps.eu10-003.hana.ondemand.com", "host_idp": "porsche-prd-spine-integration.authentication.eu10.hana.ondemand.com", "environment": "prod"}}