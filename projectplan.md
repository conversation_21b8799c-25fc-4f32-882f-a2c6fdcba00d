# TireChangeEntry Vehicle Data Integration Plan

## Aufgabe
TireChangeEntry mit Vehicle-Daten befüllen: orderType, modelDescription und verschiedene Vehicle Options Codes.

## Mapping-Logik
- **orderType**: `vehicle.model?.orderType`
- **modelDescription**: `vehicle.model?.description`
- **PCCBCode**: Family "BAV" - bestimmte IDs => "PCCB", sonst null
- **WheelCode**: Family "RAD" - options.id (C2Q)
- **CWLCode**: Family "ABR" - 1PJ oder 478 => "CWL", sonst null
- **RASCode**: Family "HIA" - 0N5 oder 470 => "RAS", sonst null
- **PCCBBeschreibung**: Beschreibung wenn PCCBCode vorhanden, sonst leer

## Todo Items

### [ ] 1. Helper-Methoden für Vehicle Options Mapping erstellen
- Methode für PCCB Code Mapping (BAV Family)
- Methode für Wheel Code Mapping (RAD Family)
- Methode für CWL Code Mapping (ABR Family)
- Methode für RAS Code Mapping (HIA Family)
- Methode für PCCB Beschreibung

### [ ] 2. prepareTireChangeEntry Methode erweitern
- Vehicle Model Daten extrahieren (orderType, modelDescription)
- Vehicle Options Daten extrahieren und mappen
- TireChangeEntry Konstruktor mit neuen Parametern aufrufen

### [ ] 3. Tests anpassen
- vehicleDTO() Methode in Tests erweitern mit Model und Options
- Erwartete TireChangeEntry Objekte in Tests mit neuen Feldern erstellen
- Neue Test-Cases für verschiedene Vehicle Options Szenarien

### [ ] 4. Integration Tests
- Test mit vollständigen Vehicle Options
- Test mit fehlenden Vehicle Options
- Test mit verschiedenen Code-Kombinationen

## Implementierungsdetails

### PCCB Code Mapping
```
Codes: 1KI, 2EC, 2EH, 1KQ, 1LT, PB1, PB3, 1KK, 1IA, 2ER, 1LX, 1LQ, 450, 1ZW, 1LY, 2ED, 2EU, 1ZV, 1LV, 1LZ, 1ZP, 1ZQ
=> "PCCB"
Sonst: null
```

### CWL Code Mapping
```
Codes: 1PJ, 478 => "CWL"
Sonst: null
```

### RAS Code Mapping
```
Codes: 0N5, 470 => "RAS"
Sonst: null
```
