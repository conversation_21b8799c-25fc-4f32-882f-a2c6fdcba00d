FROM public.ecr.aws/amazoncorretto/amazoncorretto:21.0.8

VOLUME /tmp

# Update system packages and fix Python vulnerabilities (CVE-2025-6069)
RUN yum install -y python python-libs && \
    yum clean all && \
    rm -rf /var/cache/yum

# Install Arial font necessary for printing documents
COPY docker/fonts/arial/* /usr/share/fonts/truetype/
RUN fc-cache -fv

RUN mkdir -p /app && curl -L -o /app/aws-opentelemetry-agent.jar https://github.com/aws-observability/aws-otel-java-instrumentation/releases/download/v1.32.0/aws-opentelemetry-agent.jar
ENV JAVA_TOOL_OPTIONS "-javaagent:aws-opentelemetry-agent.jar"
RUN chmod 755 app/aws-opentelemetry-agent.jar

USER 1000

WORKDIR /app
ARG JAR_FILE=build/libs/vehicle-service-0.0.1-SNAPSHOT.jar
COPY ${JAR_FILE} app.jar

EXPOSE 8080

HEALTHCHECK --interval=60s --timeout=5s --retries=3 CMD curl --fail http://localhost:8080/api/vs/actuator/health || exit 1

ENV JAVA_OPTS="-XX:InitialRAMPercentage=25.0 -XX:MaxRAMPercentage=40.0 -Djava.util.concurrent.ForkJoinPool.common.parallelism=4"
ENTRYPOINT ["sh", "-c", "java ${JAVA_OPTS} -jar app.jar"]
