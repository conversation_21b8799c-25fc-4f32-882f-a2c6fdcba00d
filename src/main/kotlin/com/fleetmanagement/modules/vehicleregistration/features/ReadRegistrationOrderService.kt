package com.fleetmanagement.modules.vehicleregistration.features

import com.fleetmanagement.integrations.aggrid.api.dto.SearchRequest
import com.fleetmanagement.modules.vehicleregistration.api.ReadRegistrationOrder
import com.fleetmanagement.modules.vehicleregistration.api.dto.LatestRegistrationOrder
import com.fleetmanagement.modules.vehicleregistration.api.dto.RegistrationPeriod
import com.fleetmanagement.modules.vehicleregistration.api.dto.VehicleRegistrationAPIResponse
import com.fleetmanagement.modules.vehicleregistration.api.dto.VehicleRegistrationOrder
import com.fleetmanagement.modules.vehicleregistration.client.VehicleRegistrationClient
import org.springframework.stereotype.Service
import java.time.OffsetDateTime
import java.util.UUID

@Service
class ReadRegistrationOrderService(
    private val vehicleRegistrationClient: VehicleRegistrationClient,
) : ReadRegistrationOrder {
    override fun getRegistrationOrders(): VehicleRegistrationAPIResponse<List<VehicleRegistrationOrder>> =
        vehicleRegistrationClient.orders()

    override fun getLatestOrderBy(vehicleId: UUID): VehicleRegistrationAPIResponse<VehicleRegistrationOrder?> =
        vehicleRegistrationClient.latestOrderBy(vehicleId)

    override fun getLatestOrdersBy(vehicleIds: List<UUID>): VehicleRegistrationAPIResponse<List<LatestRegistrationOrder>> =
        vehicleRegistrationClient.getLatestOrdersBy(vehicleIds)

    override fun getCompletedOrdersBy(vehicleId: UUID): VehicleRegistrationAPIResponse<List<VehicleRegistrationOrder>> =
        vehicleRegistrationClient.getCompletedOrdersBy(vehicleId)

    override fun getUncompletedOrders(): VehicleRegistrationAPIResponse<List<VehicleRegistrationOrder>> =
        vehicleRegistrationClient.getUncompletedOrders()

    override fun getRegistrationPeriodByLicencePlate(
        licencePlate: String,
        date: String?,
    ): VehicleRegistrationAPIResponse<List<RegistrationPeriod>> =
        vehicleRegistrationClient.getRegistrationPeriodByLicencePlate(licencePlate, date)

    override fun getRegistrationsModifiedSince(
        modifiedSince: OffsetDateTime,
    ): VehicleRegistrationAPIResponse<List<VehicleRegistrationOrder>> =
        vehicleRegistrationClient.getRegistrationsModifiedSince(modifiedSince.toString())

    override fun getRegistrationPeriodByVehicleId(
        vehicleId: UUID,
        activeAfter: OffsetDateTime?,
        activeBefore: OffsetDateTime?,
    ): VehicleRegistrationAPIResponse<List<RegistrationPeriod>> =
        vehicleRegistrationClient.getRegistrationPeriodByVehicleId(
            vehicleId,
            activeAfter?.toString(),
            activeBefore?.toString(),
        )

    override fun search(searchRequest: SearchRequest): VehicleRegistrationAPIResponse<List<VehicleRegistrationOrder>> =
        vehicleRegistrationClient.search(searchRequest)
}
