/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicleregistration.client

import com.fasterxml.jackson.databind.ObjectMapper
import com.fleetmanagement.oidc.client.OIDCConfigurationProperties
import com.fleetmanagement.oidc.client.OIDCTokenClient
import com.fleetmanagement.oidc.service.OIDCTokenService
import feign.Client
import feign.RequestTemplate
import feign.Retryer
import feign.codec.Encoder
import feign.form.spring.SpringFormEncoder
import feign.jackson.JacksonEncoder
import feign.okhttp.OkHttpClient
import okhttp3.Request
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.web.context.request.RequestContextHolder
import org.springframework.web.context.request.ServletRequestAttributes
import java.util.concurrent.TimeUnit

@Configuration
class VehicleRegistrationClientConfigProperties {
    @Bean("vrClientConfiguration")
    @ConfigurationProperties("vehicle-registration.client")
    fun oidcConfigurationProperties(): OIDCConfigurationProperties = OIDCConfigurationProperties()
}

class VehicleRegistrationClientConfiguration {
    @Bean
    fun encoder(
        objectMapper: ObjectMapper,
        @Qualifier("vrTokenService") vrTokenService: OIDCTokenService,
    ): Encoder =
        Encoder { obj, bodyType, requestTemplate ->
            HeaderUtils.transferHeadersAndEnsureAuth(requestTemplate, vrTokenService)
            val objectMapperCopy = objectMapper.copy()
            SpringFormEncoder(JacksonEncoder(objectMapperCopy)).encode(obj, bodyType, requestTemplate)
        }

    @Bean
    fun feignClient(
        @Qualifier("vrTokenService") vrTokenService: OIDCTokenService,
    ): Client {
        val okHttpClientBuilder = okhttp3.OkHttpClient.Builder()
        okHttpClientBuilder.addInterceptor { chain ->
            val originalRequest = chain.request()
            val requestBuilder = originalRequest.newBuilder()
            HeaderUtils.transferHeadersAndEnsureAuth(requestBuilder, vrTokenService)
            chain.proceed(requestBuilder.build())
        }
        return OkHttpClient(okHttpClientBuilder.build())
    }

    @Bean("vrTokenService")
    fun vrTokenService(
        vrTokenClient: OIDCTokenClient,
        @Qualifier("vrClientConfiguration") vrClientConfiguration: OIDCConfigurationProperties,
    ): OIDCTokenService = OIDCTokenService(vrTokenClient, vrClientConfiguration)

    @Bean
    fun retryer(): Retryer = Retryer.Default(100L, TimeUnit.SECONDS.toMillis(3L), 1)
}

object HeaderUtils {
    private val headersToCopy = setOf("Content-Type", "X-Amzn-Oidc-Accesstoken", "X-Amzn-Oidc-Data")
    private val requiredHeaders = setOf("X-Amzn-Oidc-Accesstoken", "X-Amzn-Oidc-Data")
    private val log = LoggerFactory.getLogger(HeaderUtils::class.java)

    fun transferHeadersAndEnsureAuth(
        request: Any,
        vrTokenService: OIDCTokenService,
    ) {
        val incomingRequest =
            try {
                (RequestContextHolder.currentRequestAttributes() as? ServletRequestAttributes)?.request
            } catch (ex: IllegalStateException) {
                log.warn("Cannot find current request attributes, returning null")
                null
            }
        var m2mTokenRequired = (incomingRequest == null)

        if (incomingRequest != null) {
            headersToCopy.forEach { headerName ->
                val headerValue = incomingRequest.getHeader(headerName)
                if (headerValue != null) {
                    addHeader(request, headerName, headerValue)
                } else if (headerName in requiredHeaders) {
                    m2mTokenRequired = true
                }
            }
        }

        if (m2mTokenRequired) {
            val m2mAuthToken = vrTokenService.getAccessToken()
            addHeader(request, "Authorization", "Bearer $m2mAuthToken")
        }
    }

    private fun addHeader(
        request: Any,
        headerName: String,
        headerValue: String,
    ) {
        when (request) {
            is RequestTemplate -> request.header(headerName, headerValue)
            is Request.Builder -> request.header(headerName, headerValue)
            else -> throw IllegalArgumentException("Unsupported request type: ${request.javaClass.name}")
        }
    }
}
