/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicleregistration.client

import com.fleetmanagement.integrations.aggrid.api.dto.SearchRequest
import com.fleetmanagement.modules.vehicleregistration.api.dto.CreateBrieflistOrder
import com.fleetmanagement.modules.vehicleregistration.api.dto.CreateVehicleRegistration
import com.fleetmanagement.modules.vehicleregistration.api.dto.LatestRegistrationOrder
import com.fleetmanagement.modules.vehicleregistration.api.dto.RegistrationPeriod
import com.fleetmanagement.modules.vehicleregistration.api.dto.VehicleRegistrationAPIRequest
import com.fleetmanagement.modules.vehicleregistration.api.dto.VehicleRegistrationAPIResponse
import com.fleetmanagement.modules.vehicleregistration.api.dto.VehicleRegistrationOrder
import org.springframework.cloud.openfeign.FeignClient
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PatchMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RequestPart
import org.springframework.web.multipart.MultipartFile
import java.util.UUID

@FeignClient(
    name = "vr",
    url = "\${fvm.vehicle-registration-base-uri}",
    configuration = [VehicleRegistrationClientConfiguration::class],
)
interface VehicleRegistrationClient {
    @GetMapping(value = ["/api/vr/orders"])
    fun orders(): VehicleRegistrationAPIResponse<List<VehicleRegistrationOrder>>

    @GetMapping(value = ["/api/vr/orders/latest/{vehicleId}"])
    fun latestOrderBy(
        @PathVariable("vehicleId") vehicleId: UUID,
    ): VehicleRegistrationAPIResponse<VehicleRegistrationOrder?>

    @PutMapping(value = ["/api/vr/orders"], produces = ["application/json"], consumes = ["application/json"])
    fun putOrders(body: VehicleRegistrationAPIRequest): VehicleRegistrationAPIResponse<List<VehicleRegistrationOrder>>

    @PatchMapping(value = ["/api/vr/orders"])
    fun patchOrders(body: VehicleRegistrationAPIRequest): VehicleRegistrationAPIResponse<List<VehicleRegistrationOrder>>

    @PostMapping(value = ["/api/vr/orders/renew-test-numbers"])
    fun renewTestNumbers(): VehicleRegistrationAPIResponse<List<VehicleRegistrationOrder>>

    @PostMapping(value = ["/api/vr/orders"], consumes = ["application/json"], produces = ["application/json"])
    fun createRegistrationOrders(orderList: List<CreateVehicleRegistration>): VehicleRegistrationAPIResponse<List<VehicleRegistrationOrder>>

    @PostMapping(value = ["/api/vr/orders"])
    fun createRegistrationOrders(
        @RequestPart("file") file: MultipartFile,
    ): VehicleRegistrationAPIResponse<List<VehicleRegistrationOrder>>

    @PostMapping(value = ["/api/vr/brieflist"])
    fun createOrdersWithoutRegistration(
        orderList: List<CreateBrieflistOrder>,
    ): VehicleRegistrationAPIResponse<List<VehicleRegistrationOrder>>

    @PostMapping(value = ["/api/vr/brieflist"], consumes = [MediaType.MULTIPART_FORM_DATA_VALUE])
    fun createOrdersWithoutRegistration(
        @RequestPart("file") file: MultipartFile,
    ): VehicleRegistrationAPIResponse<List<VehicleRegistrationOrder>>

    @PostMapping(value = ["/api/vr/orders/latest"], consumes = ["application/json"])
    fun getLatestOrdersBy(vehicleIds: List<UUID>): VehicleRegistrationAPIResponse<List<LatestRegistrationOrder>>

    @GetMapping(value = ["/api/vr/orders/completed/{vehicleId}"])
    fun getCompletedOrdersBy(
        @PathVariable vehicleId: UUID,
    ): VehicleRegistrationAPIResponse<List<VehicleRegistrationOrder>>

    @GetMapping(value = ["/api/vr/orders/registrations"])
    fun getRegistrationsModifiedSince(
        @RequestParam(required = true) modifiedSince: String? = null,
    ): VehicleRegistrationAPIResponse<List<VehicleRegistrationOrder>>

    @GetMapping(value = ["/api/vr/orders/uncompleted"])
    fun getUncompletedOrders(): VehicleRegistrationAPIResponse<List<VehicleRegistrationOrder>>

    @GetMapping(value = ["/api/vr/orders/registration-period/vehicle/{vehicleId}"])
    fun getRegistrationPeriodByVehicleId(
        @PathVariable vehicleId: UUID,
        @RequestParam(required = false) activeAfter: String? = null,
        @RequestParam(required = false) activeBefore: String? = null,
    ): VehicleRegistrationAPIResponse<List<RegistrationPeriod>>

    @DeleteMapping("/api/vr/orders/{id}")
    fun softDeleteOrder(
        @PathVariable id: Long,
    ): VehicleRegistrationAPIResponse<VehicleRegistrationOrder>

    @GetMapping("/api/vr/orders/registration-period/{licencePlate}")
    fun getRegistrationPeriodByLicencePlate(
        @PathVariable licencePlate: String,
        @RequestParam(required = false) date: String?,
    ): VehicleRegistrationAPIResponse<List<RegistrationPeriod>>

    @PostMapping(value = ["/api/vr/vehicle-restrictions/{vehicleId}"], consumes = ["application/json"])
    fun restrictVehicle(
        @PathVariable vehicleId: UUID,
        @RequestParam(required = false) reasonCode: String?,
    ): ResponseEntity<Unit>

    @DeleteMapping(value = ["/api/vr/vehicle-restrictions/{vehicleId}"])
    fun unRestrictVehicle(
        @PathVariable vehicleId: UUID,
    ): ResponseEntity<Unit>

    @PostMapping(value = ["/api/vr/orders/search"], consumes = ["application/json"])
    fun search(searchRequest: SearchRequest): VehicleRegistrationAPIResponse<List<VehicleRegistrationOrder>>
}
