package com.fleetmanagement.modules.vehicleregistration.api

import com.fleetmanagement.integrations.aggrid.api.dto.SearchRequest
import com.fleetmanagement.modules.vehicleregistration.api.dto.LatestRegistrationOrder
import com.fleetmanagement.modules.vehicleregistration.api.dto.RegistrationPeriod
import com.fleetmanagement.modules.vehicleregistration.api.dto.VehicleRegistrationAPIResponse
import com.fleetmanagement.modules.vehicleregistration.api.dto.VehicleRegistrationOrder
import java.time.OffsetDateTime
import java.util.UUID

interface ReadRegistrationOrder {
    fun getRegistrationOrders(): VehicleRegistrationAPIResponse<List<VehicleRegistrationOrder>>

    fun getLatestOrderBy(vehicleId: UUID): VehicleRegistrationAPIResponse<VehicleRegistrationOrder?>

    fun getLatestOrdersBy(vehicleIds: List<UUID>): VehicleRegistrationAPIResponse<List<LatestRegistrationOrder>>

    fun getCompletedOrdersBy(vehicleId: UUID): VehicleRegistrationAPIResponse<List<VehicleRegistrationOrder>>

    fun getUncompletedOrders(): VehicleRegistrationAPIResponse<List<VehicleRegistrationOrder>>

    fun getRegistrationPeriodByLicencePlate(
        licencePlate: String,
        date: String?,
    ): VehicleRegistrationAPIResponse<List<RegistrationPeriod>>

    fun getRegistrationsModifiedSince(modifiedSince: OffsetDateTime): VehicleRegistrationAPIResponse<List<VehicleRegistrationOrder>>

    fun getRegistrationPeriodByVehicleId(
        vehicleId: UUID,
        activeAfter: OffsetDateTime? = null,
        activeBefore: OffsetDateTime? = null,
    ): VehicleRegistrationAPIResponse<List<RegistrationPeriod>>

    fun search(searchRequest: SearchRequest): VehicleRegistrationAPIResponse<List<VehicleRegistrationOrder>>
}
