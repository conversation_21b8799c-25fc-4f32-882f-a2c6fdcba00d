/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehicletransfer.adapter.incoming.rest

import ActiveVehicleTransferNotFoundException
import CurrentVehicleTransferNotFoundException
import com.fleetmanagement.modules.vehicledata.api.VehicleNotFoundInFVM
import com.fleetmanagement.modules.vehicledata.repository.entities.VehicleReturnInfoUpdateException
import com.fleetmanagement.modules.vehicletransfer.domain.VehicleTransferReturnException
import com.fleetmanagement.modules.vtstamm.adapter.incoming.rest.model.ErrorDto
import com.fleetmanagement.modules.vtstamm.adapter.incoming.rest.model.ErrorTypeDto
import org.springframework.core.annotation.Order
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.ControllerAdvice
import org.springframework.web.bind.annotation.ExceptionHandler

/**
 * Some exceptions are already handles by [com.fleetmanagement.modules.vehicledata.integrations.vtstamm.rest.VTStammVehicleRestExceptionHandler]
 * as they both target the same base package.
 */
@ControllerAdvice("com.fleetmanagement.modules.vtstamm.adapter.incoming.rest")
@Order(1)
class VTStammVehicleTransferRestExceptionHandler {
    @ExceptionHandler(VehicleNotFoundInFVM::class)
    private fun handleVehicleNotFoundInFVM(exception: VehicleNotFoundInFVM): ResponseEntity<ErrorDto> =
        ResponseEntity(
            ErrorDto(
                message = exception.message ?: "Error while trying fetch vehicle. Vehicle could not be found.",
                type = ErrorTypeDto.BUSINESS,
            ),
            HttpStatus.NOT_FOUND,
        )

    @ExceptionHandler(ActiveVehicleTransferNotFoundException::class)
    private fun handleActiveVehicleTransferNotFoundException(exception: ActiveVehicleTransferNotFoundException): ResponseEntity<ErrorDto> =
        ResponseEntity(
            ErrorDto(
                message =
                    exception.message
                        ?: "Could not find ACTIVE VehicleTransfer for vehicle with VIN [${exception.vin}].",
                type = ErrorTypeDto.BUSINESS,
            ),
            HttpStatus.NOT_FOUND,
        )

    @ExceptionHandler(CurrentVehicleTransferNotFoundException::class)
    private fun handleCurrentVehicleTransferNotFoundException(
        exception: CurrentVehicleTransferNotFoundException,
    ): ResponseEntity<ErrorDto> =
        ResponseEntity(
            ErrorDto(
                message =
                    exception.message
                        ?: "Could not determine current VehicleTransfer for vehicle with VIN [${exception.vin}].",
                type = ErrorTypeDto.BUSINESS,
            ),
            HttpStatus.NOT_FOUND,
        )

    @ExceptionHandler(MileageAtReturnMissing::class)
    private fun handleMileageAtReturnMissing(exception: MileageAtReturnMissing): ResponseEntity<ErrorDto> =
        ResponseEntity(
            ErrorDto(
                message =
                    exception.message
                        ?: (
                            "Could not perform return for vehicle with VIN [${exception.vin}]. " +
                                "MileageAtReturn is either not provided or latest mileage could not be obtained by the system."
                        ),
                type = ErrorTypeDto.BUSINESS,
            ),
            HttpStatus.CONFLICT,
        )

    @ExceptionHandler(VehicleTransferReturnException::class)
    private fun handleVehicleTransferReturnException(exception: VehicleTransferReturnException): ResponseEntity<ErrorDto> =
        ResponseEntity(
            ErrorDto(
                message =
                    exception.message
                        ?: ("Error while trying to return vehicle transfer with key [${exception.vehicleTransferKey}]."),
                type = ErrorTypeDto.BUSINESS,
            ),
            HttpStatus.CONFLICT,
        )

    @ExceptionHandler(VehicleReturnInfoUpdateException::class)
    private fun handleVehicleReturnInfoUpdateException(exception: VehicleReturnInfoUpdateException): ResponseEntity<ErrorDto> =
        ResponseEntity(
            ErrorDto(
                message =
                    exception.message
                        ?: ("Error while trying to update return info for vehicle."),
                type = ErrorTypeDto.BUSINESS,
            ),
            HttpStatus.CONFLICT,
        )
}
