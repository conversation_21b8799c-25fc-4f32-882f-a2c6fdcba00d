package com.fleetmanagement.modules.vehicletransfer.adapter.incoming.rest

import ActiveVehicleTransferNotFoundException
import com.fleetmanagement.modules.consigneedatasheet.application.CostCenterFinder
import com.fleetmanagement.modules.consigneedatasheet.application.port.vehicleusage.ReadVehicleUsageUseCase
import com.fleetmanagement.modules.consigneedatasheet.domain.CostCenterId
import com.fleetmanagement.modules.consigneedatasheet.domain.EmployeeNumber
import com.fleetmanagement.modules.vehicledata.api.ReadMileageReadings
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByVIN
import com.fleetmanagement.modules.vehicletransfer.application.VehicleTransferFinder
import com.fleetmanagement.modules.vehicletransfer.application.port.*
import com.fleetmanagement.modules.vtstamm.adapter.incoming.rest.VtstammVehicleTransferService
import com.fleetmanagement.modules.vtstamm.adapter.incoming.rest.model.VehicleTransferChangeVehicleResponsiblePersonDto
import com.fleetmanagement.modules.vtstamm.adapter.incoming.rest.model.VehicleTransferCreateAndDeliverDto
import com.fleetmanagement.modules.vtstamm.adapter.incoming.rest.model.VehicleTransferDto
import com.fleetmanagement.modules.vtstamm.adapter.incoming.rest.model.VehicleTransferReturnDto
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import kotlin.jvm.optionals.getOrNull

@Component
class VTStammVehicleTransferRestAdapter(
    private val createAndDeliverPlannedVehicleTransferUseCase: CreateAndDeliverPlannedVehicleTransferUseCase,
    private val returnVehicleTransferUseCase: ReturnVehicleTransferUseCase,
    private val changeVehicleResponsiblePersonUseCase: ChangeVehicleResponsiblePersonUseCase,
    private val vehicleTransferFinder: VehicleTransferFinder,
    private val readVehicleByVIN: ReadVehicleByVIN,
    private val readVehicleUsageUseCase: ReadVehicleUsageUseCase,
    private val costCenterFinder: CostCenterFinder,
    private val readMileageReadings: ReadMileageReadings,
) : VtstammVehicleTransferService {
    override fun changeVehicleResponsiblePerson(
        vin: String,
        vehicleTransferChangeVehicleResponsiblePersonDto: VehicleTransferChangeVehicleResponsiblePersonDto,
    ): VehicleTransferDto {
        log.info("Received VTSTAMM change vehicle responsible person request for vehicle with VIN [$vin].")
        val vehicle = readVehicleByVIN.readVehicleByVIN(vin = vin)

        val mileageAtReturn =
            vehicleTransferChangeVehicleResponsiblePersonDto.mileageAtReturn?.getOrNull() ?: readMileageReadings
                .getLatestMileageReading(
                    vehicleId = vehicle.id,
                )?.mileage
        if (null == mileageAtReturn) throw MileageAtReturnMissing(vin = vin)

        val deliveredVehicleTransfer =
            changeVehicleResponsiblePersonUseCase.changeResponsiblePerson(
                vehicleId = vehicle.id,
                vin = vin,
                vehicleResponsiblePerson = EmployeeNumber(vehicleTransferChangeVehicleResponsiblePersonDto.vehicleResponsiblePerson),
                deliveryComment = vehicleTransferChangeVehicleResponsiblePersonDto.deliveryComment?.getOrNull(),
                nextProcess = vehicleTransferChangeVehicleResponsiblePersonDto.nextProcess.toNextProcess(),
                latestMileage = mileageAtReturn,
                returnComment = vehicleTransferChangeVehicleResponsiblePersonDto.returnComment?.getOrNull(),
            )
        log.info("Successfully changed vehicle responsible person for vehicle transfer for vehicle with VIN [$vin].")

        return deliveredVehicleTransfer.toVehicleTransferDto(
            vin = requireNotNull(vehicle.vin),
            vehicleUsage = deliveredVehicleTransfer.vehicleUsage?.let { readVehicleUsageUseCase.findVehicleUsageById(it.value) }?.usage,
            depreciationRelevantCostCenter =
                deliveredVehicleTransfer.depreciationRelevantCostCenterId
                    ?.let { costCenterFinder.getCostCenter(CostCenterId(it.value)) }
                    ?.description
                    ?.value,
        )
    }

    override fun createAndDeliverVehicleTransfer(
        vehicleTransferCreateAndDeliverDto: VehicleTransferCreateAndDeliverDto,
    ): VehicleTransferDto {
        log.info(
            "Received VTSTAMM vehicle transfer create and deliver request for vehicle with VIN [${vehicleTransferCreateAndDeliverDto.vin}].",
        )
        val vehicle = readVehicleByVIN.readVehicleByVIN(vin = vehicleTransferCreateAndDeliverDto.vin)
        val vehicleUsage =
            readVehicleUsageUseCase
                .readAllVehicleUsage()
                .single {
                    vehicleTransferCreateAndDeliverDto.vehicleUsage.value.equals(
                        it.usage,
                        true,
                    )
                }

        val deliveredVehicleTransfer =
            createAndDeliverPlannedVehicleTransferUseCase.createAndDeliverPlannedVehicleTransfer(
                createAndDeliverPlannedVehicleTransferDto =
                    CreateAndDeliverPlannedVehicleTransferDto(
                        createPlannedVehicleTransferDto =
                            vehicleTransferCreateAndDeliverDto.toCreatePlannedVehicleTransferDto(
                                vehicleId = vehicle.id,
                                vehicleUsageId = vehicleUsage.id,
                            ),
                        deliverPlannedVehicleTransferDto =
                            DeliverPlannedVehicleTransferDto(
                                deliveryComment = vehicleTransferCreateAndDeliverDto.deliveryComment?.getOrNull(),
                                deliveryDate = vehicleTransferCreateAndDeliverDto.deliveryDate,
                                mileageAtDelivery = vehicleTransferCreateAndDeliverDto.mileageAtDelivery,
                                vehicleResponsiblePerson = vehicleTransferCreateAndDeliverDto.vehicleResponsiblePerson,
                            ),
                    ),
            )
        log.info("Successfully created and delivered vehicle transfer for vehicle with VIN [${vehicleTransferCreateAndDeliverDto.vin}].")

        return deliveredVehicleTransfer.toVehicleTransferDto(
            vin = requireNotNull(vehicle.vin),
            vehicleUsage = deliveredVehicleTransfer.vehicleUsage?.let { readVehicleUsageUseCase.findVehicleUsageById(it.value) }?.usage,
            depreciationRelevantCostCenter =
                deliveredVehicleTransfer.depreciationRelevantCostCenterId
                    ?.let { costCenterFinder.getCostCenter(CostCenterId(it.value)) }
                    ?.description
                    ?.value,
        )
    }

    override fun returnVehicleTransfer(
        vin: String,
        vehicleTransferReturnDto: VehicleTransferReturnDto,
    ): VehicleTransferDto {
        log.info("Received VTSTAMM vehicle transfer return request for vehicle with VIN [$vin].")

        val vehicle = readVehicleByVIN.readVehicleByVIN(vin = vin)
        val activeVehicleTransfer =
            vehicleTransferFinder.findActiveVehicleTransferForVehicle(vehicle.id)
                ?: throw ActiveVehicleTransferNotFoundException(vin = vin)

        val mileageAtReturn =
            vehicleTransferReturnDto.mileageAtReturn?.getOrNull() ?: readMileageReadings
                .getLatestMileageReading(
                    vehicleId = vehicle.id,
                )?.mileage
        if (null == mileageAtReturn) throw MileageAtReturnMissing(vin = vin)

        returnVehicleTransferUseCase.returnVehicleTransfer(
            vehicleTransferKey = activeVehicleTransfer.key.toVehicleTransferKey(),
            returnComment = vehicleTransferReturnDto.returnComment?.getOrNull(),
            mileageAtReturn = mileageAtReturn,
            returnDate = vehicleTransferReturnDto.returnDate,
        )

        val returnedVehicleTransfer = vehicleTransferFinder.getVehicleTransfer(key = activeVehicleTransfer.key)
        log.info("Successfully returned vehicle transfer for vehicle with VIN [$vin].")

        return returnedVehicleTransfer.toVehicleTransferDto(
            vin = requireNotNull(vehicle.vin),
            vehicleUsage = returnedVehicleTransfer.vehicleUsage?.let { readVehicleUsageUseCase.findVehicleUsageById(it.value) }?.usage,
            depreciationRelevantCostCenter =
                returnedVehicleTransfer.depreciationRelevantCostCenterId
                    ?.let { costCenterFinder.getCostCenter(CostCenterId(it.value)) }
                    ?.description
                    ?.value,
        )
    }

    companion object {
        private val log = LoggerFactory.getLogger(VTStammVehicleTransferRestAdapter::class.java)
    }
}

class MileageAtReturnMissing(
    val vin: String,
) : NoSuchElementException(
        "Could not perform return for vehicle with VIN [$vin]. " +
            "MileageAtReturn is either not provided or latest mileage could not be obtained by the system.",
    )
