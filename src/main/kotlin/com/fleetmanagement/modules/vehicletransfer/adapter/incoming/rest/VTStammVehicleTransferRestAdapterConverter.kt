/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehicletransfer.adapter.incoming.rest

import com.fleetmanagement.modules.vehicledata.api.domain.NextProcess
import com.fleetmanagement.modules.vehicletransfer.application.port.CreatePlannedVehicleTransferDto
import com.fleetmanagement.modules.vehicletransfer.domain.entities.UtilizationArea
import com.fleetmanagement.modules.vehicletransfer.domain.entities.VehicleTransfer
import com.fleetmanagement.modules.vehicletransfer.domain.entities.VehicleTransferStatus
import com.fleetmanagement.modules.vtstamm.adapter.incoming.rest.model.*
import java.util.*
import kotlin.jvm.optionals.getOrNull

fun VehicleTransferCreateAndDeliverDto.toCreatePlannedVehicleTransferDto(
    vehicleId: UUID,
    vehicleUsageId: UUID,
) = CreatePlannedVehicleTransferDto(
    vehicleId = vehicleId,
    vehicleUsageId = vehicleUsageId,
    vehicleResponsiblePerson = this.vehicleResponsiblePerson,
    internalContactPerson = this.internalContactPerson?.getOrNull(),
    usingCostCenter = this.usingCostCenter?.getOrNull(),
    internalOrderNumber = this.internalOrderNumber?.getOrNull(),
)

fun VehicleTransfer.toVehicleTransferDto(
    vin: String,
    vehicleUsage: String?,
    depreciationRelevantCostCenter: String?,
) = VehicleTransferDto(
    key = this.key.value.toString(),
    vin = vin,
    status = this.status.toVehicleTransferStatusDto(),
    vehicleUsage = vehicleUsage?.let { Optional.ofNullable(VehicleUsageDto.forValue(it)) },
    internalContactPerson = Optional.ofNullable(this.internalContactPerson?.value),
    vehicleResponsiblePerson = Optional.ofNullable(this.vehicleResponsiblePerson?.value),
    usingCostCenter = Optional.ofNullable(this.usingCostCenter?.value),
    depreciationRelevantCostCenter = Optional.ofNullable(depreciationRelevantCostCenter),
    internalOrderNumber = Optional.ofNullable(this.internalOrderNumber),
    maintenanceOrderNumber = Optional.ofNullable(this.maintenanceOrderNumber),
    deliveryDate = Optional.ofNullable(this.deliveryDate),
    plannedDeliveryDate = Optional.ofNullable(this.plannedDeliveryDate),
    mileageAtDelivery = Optional.ofNullable(this.mileageAtDelivery),
    deliveryComment = Optional.ofNullable(this.deliveryComment),
    returnDate = Optional.ofNullable(this.returnDate),
    plannedReturnDate = Optional.ofNullable(this.plannedReturnDate),
    latestReturnDate = Optional.ofNullable(this.latestReturnDate),
    mileageAtReturn = Optional.ofNullable(this.mileageAtReturn),
    returnComment = Optional.ofNullable(this.returnComment),
    utilizationArea = Optional.ofNullable(this.utilizationArea?.toUtilizationAreaDto()),
)

fun VehicleTransferStatus.toVehicleTransferStatusDto(): VehicleTransferStatusDto =
    when (this) {
        VehicleTransferStatus.ACTIVE -> VehicleTransferStatusDto.ACTIVE
        VehicleTransferStatus.CANCELLED -> VehicleTransferStatusDto.CANCELLED
        VehicleTransferStatus.FINISHED -> VehicleTransferStatusDto.FINISHED
    }

fun UtilizationArea.toUtilizationAreaDto(): UtilizationAreaDto =
    when (this) {
        UtilizationArea.LEIPZIG -> UtilizationAreaDto.LEIPZIG
        UtilizationArea.UNKNOWN -> UtilizationAreaDto.UNKNOWN
        UtilizationArea.ZUFFENHAUSEN -> UtilizationAreaDto.ZUFFENHAUSEN
        UtilizationArea.WEISSACH -> UtilizationAreaDto.WEISSACH
    }

fun NextProcessWithDefaultScrappingDto.toNextProcess(): NextProcess =
    when (this) {
        NextProcessWithDefaultScrappingDto.CHECK_IF_REUSAGE_IS_POSSIBLE -> NextProcess.CHECK_IF_REUSAGE_IS_POSSIBLE
        NextProcessWithDefaultScrappingDto.PROFITABILITY_AUDIT_IN_PREPARATION -> NextProcess.PROFITABILITY_AUDIT_IN_PREPARATION
        NextProcessWithDefaultScrappingDto.SALES -> NextProcess.SALES
        NextProcessWithDefaultScrappingDto.SCRAPPED_CAR_RETURNED -> NextProcess.SCRAPPED_CAR_RETURNED
    }
