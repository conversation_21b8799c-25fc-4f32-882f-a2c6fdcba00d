/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehicletransfer.application.vtstamm

import ActiveVehicleTransferNotFoundException
import CurrentVehicleTransferNotFoundException
import com.fleetmanagement.modules.consigneedatasheet.domain.EmployeeNumber
import com.fleetmanagement.modules.vehicledata.api.UpdateVehicleNextProcess
import com.fleetmanagement.modules.vehicledata.api.domain.NextProcess
import com.fleetmanagement.modules.vehicletransfer.application.PlannedVehicleTransferFinder
import com.fleetmanagement.modules.vehicletransfer.application.VehicleTransferFinder
import com.fleetmanagement.modules.vehicletransfer.application.port.*
import com.fleetmanagement.modules.vehicletransfer.domain.entities.VehicleTransfer
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.time.OffsetDateTime
import java.util.*

/**
 * This service offers transactional boundaries for a combined return-create-deliver process.
 */
@Component
@Transactional
class ChangeVehicleResponsiblePersonService(
    private val updateVehicleNextProcess: UpdateVehicleNextProcess,
    private val returnVehicleTransferUseCase: ReturnVehicleTransferUseCase,
    private val currentVehicleTransferUseCase: CurrentVehicleTransferUseCase,
    private val vehicleTransferFinder: VehicleTransferFinder,
    private val deliverPlannedVehicleTransferUseCase: DeliverPlannedVehicleTransferUseCase,
    private val plannedVehicleTransferFinder: PlannedVehicleTransferFinder,
    private val createAndDeliverPlannedVehicleTransferService: CreateAndDeliverPlannedVehicleTransferService,
) : ChangeVehicleResponsiblePersonUseCase {
    override fun changeResponsiblePerson(
        vehicleId: UUID,
        vin: String,
        vehicleResponsiblePerson: EmployeeNumber,
        nextProcess: NextProcess,
        latestMileage: Int,
        returnComment: String?,
        deliveryComment: String?,
    ): VehicleTransfer {
        val now = OffsetDateTime.now()
        // 1. fetch current transfer
        val currentVehicleTransferKey =
            currentVehicleTransferUseCase.retrieveCurrentVehicleTransferKey(vehicleId)
                ?: throw CurrentVehicleTransferNotFoundException(vin = vin)

        val plannedVehicleTransfer =
            plannedVehicleTransferFinder.findPlannedVehicleTransfer(currentVehicleTransferKey)

        // 1.1. deliver, if it is a planned one
        if (null != plannedVehicleTransfer) {
            // if current one is planned, just do a delivery
            deliverPlannedVehicleTransferUseCase.deliverPlannedVehicleTransfer(
                plannedVehicleTransferKey = plannedVehicleTransfer.key.toVehicleTransferKey(),
                deliveryComment = deliveryComment,
                vehicleResponsiblePerson = vehicleResponsiblePerson.value,
                deliveryDate = now,
                mileageAtDelivery = latestMileage,
            )
            return vehicleTransferFinder.getVehicleTransfer(plannedVehicleTransfer.key)
        }

        // 2. proceed with active transfer
        val activeVehicleTransfer =
            vehicleTransferFinder.findActiveVehicleTransferForVehicle(vehicleId)
                ?: throw ActiveVehicleTransferNotFoundException(vin = vin)

        // 2.1. update nextProcess, otherwise return can not be performed
        updateVehicleNextProcess.updateNextProcess(
            vehicleId = vehicleId,
            nextProcess = nextProcess,
        )

        // 2.2. perform return
        returnVehicleTransferUseCase.returnVehicleTransfer(
            vehicleTransferKey = activeVehicleTransfer.key.toVehicleTransferKey(),
            returnDate = now,
            mileageAtReturn = latestMileage,
            returnComment = returnComment,
        )

        val returnedVehicleTransfer = vehicleTransferFinder.getVehicleTransfer(activeVehicleTransfer.key)

        // 2.3. create and deliver a new transfer
        return createAndDeliverPlannedVehicleTransferService.createAndDeliverPlannedVehicleTransfer(
            createAndDeliverPlannedVehicleTransferDto =
                CreateAndDeliverPlannedVehicleTransferDto(
                    createPlannedVehicleTransferDto =
                        returnedVehicleTransfer.toCreatePlannedVehicleTransferDto(
                            vehicleResponsiblePerson = vehicleResponsiblePerson,
                        ),
                    deliverPlannedVehicleTransferDto =
                        DeliverPlannedVehicleTransferDto(
                            vehicleResponsiblePerson = vehicleResponsiblePerson.value,
                            mileageAtDelivery = latestMileage,
                            deliveryComment = deliveryComment,
                            deliveryDate = now,
                        ),
                ),
        )
    }

    private fun VehicleTransfer.toCreatePlannedVehicleTransferDto(vehicleResponsiblePerson: EmployeeNumber) =
        CreatePlannedVehicleTransferDto(
            vehicleId = this.vehicleId,
            vehicleUsageId = this.vehicleUsage?.value,
            vehicleResponsiblePerson = vehicleResponsiblePerson.value,
            internalContactPerson = this.internalContactPerson?.value,
            licensePlate = this.licensePlate,
            deliveryLeipzig = this.deliveryLeipzig,
            maximumServiceLifeInMonths = this.maximumServiceLifeInMonths,
            usingCostCenter = this.usingCostCenter?.value,
            desiredTireSet = this.desiredTireSet,
            serviceCards = this.serviceCards,
            registrationNeeded = this.registrationNeeded,
            provisionForDeliveryComment = null,
            desiredDeliveryDate = null,
            plannedDeliveryDate = null,
            plannedReturnDate = null,
            remark = this.remark,
            usageMhp = this.usageMhp,
            usageVdw = this.usageVdw,
            privateMonthlyKilometers = this.privateMonthlyKilometers,
        )
}
