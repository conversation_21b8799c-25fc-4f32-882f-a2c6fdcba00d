package com.fleetmanagement.modules.vehicletransfer.application.port

import com.fleetmanagement.modules.consigneedatasheet.domain.EmployeeNumber
import com.fleetmanagement.modules.vehicletransfer.domain.entities.VehicleTransfer
import com.fleetmanagement.modules.vehicletransfer.domain.entities.VehicleTransferKey
import com.fleetmanagement.modules.vehicletransfer.domain.entities.VehicleTransferStatus
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import java.time.OffsetDateTime
import java.util.UUID

interface ReadVehicleTransferUseCase {
    fun findVehicleTransfersByVehicleId(vehicleId: UUID): List<VehicleTransfer>

    fun findVehicleTransfersByVehicleTransferKey(
        vehicleTransferKey: com.fleetmanagement.modules.vehicletransfer.application.port.VehicleTransferKey,
    ): VehicleTransfer?

    fun findVehicleTransfersByVehicleId(
        vehicleId: UUID,
        status: List<VehicleTransferStatus>,
    ): List<VehicleTransfer>

    fun findActiveVehicleTransferForVehicle(vehicleId: UUID): VehicleTransfer?

    fun findActiveVehicleTransfersForEmployee(vehicleResponsiblePerson: EmployeeNumber): List<VehicleTransfer>

    fun readAllAppointmentsIdAndKeyWherePlannedReturnDateBetween(
        startTime: OffsetDateTime,
        endTime: OffsetDateTime,
    ): List<Pair<String, VehicleTransferKey>>

    fun findAllActiveTransfersForUsageGroup(usageGroupId: UUID): List<VehicleTransfer>

    fun findAllTransfersForUtilizationAreaZuffenhausenAndLeipzig(pageable: Pageable): Page<VehicleTransfer>

    fun findAllActiveTransfers(): List<VehicleTransfer>

    fun findActiveVehicleTransferByLicensePlate(licensePlate: String): List<VehicleTransfer>
}
