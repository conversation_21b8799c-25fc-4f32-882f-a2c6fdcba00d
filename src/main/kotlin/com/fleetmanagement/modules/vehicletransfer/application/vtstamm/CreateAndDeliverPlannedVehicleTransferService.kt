/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehicletransfer.application.vtstamm

import com.fleetmanagement.modules.vehicletransfer.application.VehicleTransferFinder
import com.fleetmanagement.modules.vehicletransfer.application.port.CreateAndDeliverPlannedVehicleTransferDto
import com.fleetmanagement.modules.vehicletransfer.application.port.CreateAndDeliverPlannedVehicleTransferUseCase
import com.fleetmanagement.modules.vehicletransfer.application.port.CreatePlannedVehicleTransferUseCase
import com.fleetmanagement.modules.vehicletransfer.application.port.DeliverPlannedVehicleTransferUseCase
import com.fleetmanagement.modules.vehicletransfer.application.port.toDomainVehicleTransferKey
import com.fleetmanagement.modules.vehicletransfer.domain.entities.VehicleTransfer
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

/**
 * A service that offers transactional boundaries to do creation and delivery of a planned vehicle transfer. Currently used by VTStamm.
 */
@Component
@Transactional
class CreateAndDeliverPlannedVehicleTransferService(
    private val createPlannedVehicleTransferUseCase: CreatePlannedVehicleTransferUseCase,
    private val deliverPlannedVehicleTransferUseCase: DeliverPlannedVehicleTransferUseCase,
    private val vehicleTransferFinder: VehicleTransferFinder,
) : CreateAndDeliverPlannedVehicleTransferUseCase {
    override fun createAndDeliverPlannedVehicleTransfer(
        createAndDeliverPlannedVehicleTransferDto: CreateAndDeliverPlannedVehicleTransferDto,
    ): VehicleTransfer {
        // create planned vehicle transfer
        val vehicleTransferKey =
            createPlannedVehicleTransferUseCase.manualCreatePlannedVehicleTransfer(
                createPlannedVehicleTransferDto = createAndDeliverPlannedVehicleTransferDto.createPlannedVehicleTransferDto,
            )

        // perform delivery
        deliverPlannedVehicleTransferUseCase.deliverPlannedVehicleTransfer(
            plannedVehicleTransferKey = vehicleTransferKey,
            vehicleResponsiblePerson = createAndDeliverPlannedVehicleTransferDto.deliverPlannedVehicleTransferDto.vehicleResponsiblePerson,
            mileageAtDelivery = createAndDeliverPlannedVehicleTransferDto.deliverPlannedVehicleTransferDto.mileageAtDelivery,
            deliveryDate = createAndDeliverPlannedVehicleTransferDto.deliverPlannedVehicleTransferDto.deliveryDate,
            deliveryComment = createAndDeliverPlannedVehicleTransferDto.deliverPlannedVehicleTransferDto.deliveryComment,
        )

        // return delivered vehicle transfer
        return vehicleTransferFinder.getVehicleTransfer(key = vehicleTransferKey.toDomainVehicleTransferKey())
    }
}
