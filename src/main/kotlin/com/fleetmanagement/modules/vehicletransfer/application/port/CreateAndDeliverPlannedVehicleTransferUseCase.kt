/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehicletransfer.application.port

import com.fleetmanagement.modules.vehicletransfer.domain.entities.VehicleTransfer
import java.time.OffsetDateTime

fun interface CreateAndDeliverPlannedVehicleTransferUseCase {
    /**
     * Will manually create a new plannedVehicleTransfer and perform immediate delivery on it.
     */
    fun createAndDeliverPlannedVehicleTransfer(
        createAndDeliverPlannedVehicleTransferDto: CreateAndDeliverPlannedVehicleTransferDto,
    ): VehicleTransfer
}

data class CreateAndDeliverPlannedVehicleTransferDto(
    val createPlannedVehicleTransferDto: CreatePlannedVehicleTransferDto,
    val deliverPlannedVehicleTransferDto: DeliverPlannedVehicleTransferDto,
)

data class DeliverPlannedVehicleTransferDto(
    val deliveryDate: OffsetDateTime,
    val mileageAtDelivery: Int,
    val deliveryComment: String?,
    val vehicleResponsiblePerson: String?,
)
