/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
class CurrentVehicleTransferNotFoundException(
    val vin: String,
) : NoSuchElementException("Could not determine current VehicleTransfer for vehicle with VIN [$vin].")

class ActiveVehicleTransferNotFoundException(
    val vin: String,
) : NoSuchElementException("Could not find ACTIVE VehicleTransfer for vehicle with VIN [$vin].")
