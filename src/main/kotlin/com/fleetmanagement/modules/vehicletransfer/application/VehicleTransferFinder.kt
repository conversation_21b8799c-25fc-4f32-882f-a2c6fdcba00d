/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehicletransfer.application

import com.fleetmanagement.emhshared.USAGE_GROUP_DESCRIPTION_PERSON
import com.fleetmanagement.modules.consigneedatasheet.application.port.usagegroup.ReadUsageGroupUseCase
import com.fleetmanagement.modules.consigneedatasheet.domain.EmployeeNumber
import com.fleetmanagement.modules.vehicletransfer.application.port.ReadVehicleTransferUseCase
import com.fleetmanagement.modules.vehicletransfer.application.port.toDomainVehicleTransferKey
import com.fleetmanagement.modules.vehicletransfer.domain.VehicleTransferNotFoundException
import com.fleetmanagement.modules.vehicletransfer.domain.VehicleTransferRepository
import com.fleetmanagement.modules.vehicletransfer.domain.entities.*
import org.slf4j.LoggerFactory
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.domain.Specification
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.time.OffsetDateTime
import java.util.*

@Component
@Transactional(readOnly = true)
class VehicleTransferFinder(
    private val vehicleTransferRepository: VehicleTransferRepository,
    private val readUsageGroupUseCase: ReadUsageGroupUseCase,
) : ReadVehicleTransferUseCase {
    fun getVehicleTransfer(key: VehicleTransferKey): VehicleTransfer =
        vehicleTransferRepository.findByKey(vehicleTransferKey = key)
            ?: throw VehicleTransferNotFoundException(vehicleTransferKey = key.value)

    override fun findVehicleTransfersByVehicleId(vehicleId: UUID): List<VehicleTransfer> =
        vehicleTransferRepository.findByVehicleId(vehicleId)

    override fun findVehicleTransfersByVehicleId(
        vehicleId: UUID,
        status: List<VehicleTransferStatus>,
    ): List<VehicleTransfer> = vehicleTransferRepository.findByVehicleIdAndStatusIn(vehicleId = vehicleId, status = status)

    override fun findVehicleTransfersByVehicleTransferKey(
        vehicleTransferKey: com.fleetmanagement.modules.vehicletransfer.application.port.VehicleTransferKey,
    ): VehicleTransfer? = vehicleTransferRepository.findByKey(vehicleTransferKey.toDomainVehicleTransferKey())

    override fun findActiveVehicleTransferForVehicle(vehicleId: UUID): VehicleTransfer? =
        vehicleTransferRepository
            .findByVehicleIdAndStatus(
                vehicleId = vehicleId,
                status = VehicleTransferStatus.ACTIVE,
            ).singleOrNull()

    override fun findActiveVehicleTransfersForEmployee(vehicleResponsiblePerson: EmployeeNumber): List<VehicleTransfer> =
        vehicleTransferRepository.findByVehicleResponsiblePersonAndStatus(
            vehicleResponsiblePerson = vehicleResponsiblePerson,
            status = VehicleTransferStatus.ACTIVE,
        )

    override fun readAllAppointmentsIdAndKeyWherePlannedReturnDateBetween(
        startTime: OffsetDateTime,
        endTime: OffsetDateTime,
    ): List<Pair<String, VehicleTransferKey>> =
        vehicleTransferRepository
            .findByPlannedReturnDateBetween(startTime, endTime)
            .mapNotNull { vehicleTransfer ->
                vehicleTransfer.msbookingAppointmentId?.let {
                    Pair(it, vehicleTransfer.key)
                }
            }

    override fun findAllActiveTransfersForUsageGroup(usageGroupId: UUID): List<VehicleTransfer> {
        val usageGroupIdVt = UsageGroupId(value = usageGroupId)
        return vehicleTransferRepository.findByStatusAndUsageGroup(
            status = VehicleTransferStatus.ACTIVE,
            usageGroup = usageGroupIdVt,
        )
    }

    fun findVehicleTransfersByVehicleIdAndPublishDateBefore(
        vehicleId: UUID,
        publishDate: OffsetDateTime,
    ): List<VehicleTransfer> {
        val specificationBeforeOrNull =
            Specification
                .where(specificationLicensePlateLastUpdateBefore(publishDate))
                .or(specificationLicensePlateLastUpdateIsNull())

        return vehicleTransferRepository.findAll(
            specificationBeforeOrNull.and(specificationByVehicleId(vehicleId)),
        )
    }

    fun getActiveVehicleTransferByUsageGroupAndVehicleUsageAndVehicleResponsiblePerson(
        vehicleUsageId: VehicleUsageId,
        usageGroupId: UsageGroupId,
        vehicleResponsiblePerson: EmployeeNumber,
    ): List<VehicleTransfer> =
        vehicleTransferRepository
            .findByStatusAndVehicleUsageAndUsageGroupAndVehicleResponsiblePerson(
                VehicleTransferStatus.ACTIVE,
                vehicleUsageId,
                usageGroupId,
                vehicleResponsiblePerson,
            )

    override fun findAllTransfersForUtilizationAreaZuffenhausenAndLeipzig(pageable: Pageable): Page<VehicleTransfer> =
        vehicleTransferRepository.findAll(
            Specification
                .where(
                    specificationForStatusEquals(VehicleTransferStatus.ACTIVE),
                ).and(
                    specificationForUtilizationAreaEquals(UtilizationArea.ZUFFENHAUSEN).or(
                        specificationForUtilizationAreaEquals(UtilizationArea.LEIPZIG),
                    ),
                ),
            pageable,
        )

    /**
     * finds predecessor for the planned vehicle transfer checks if the usageGroup of PVT is
     * "PERSON" (leasing vehicle) else skip
     */
    fun findPredecessor(
        vehicleResponsiblePerson: EmployeeNumber?,
        usageGroupId: UUID?,
        vehicleUsageId: UUID?,
    ): VehicleTransfer? {
        if (vehicleResponsiblePerson != null && vehicleUsageId != null && usageGroupId != null) {
            val usageGroup = readUsageGroupUseCase.findUsageGroupById(usageGroupId)
            if (usageGroup?.description.equals(USAGE_GROUP_LEASING, ignoreCase = true)) {
                val predecessor =
                    getActiveVehicleTransferByUsageGroupAndVehicleUsageAndVehicleResponsiblePerson(
                        VehicleUsageId(vehicleUsageId),
                        UsageGroupId(usageGroupId),
                        vehicleResponsiblePerson,
                    )
                if (predecessor.size == 1) return predecessor.single()
                if (predecessor.size > 1) {
                    @Suppress("ktlint:standard:max-line-length")
                    log.warn(
                        "More than one active vehicle transfer found for vehicle-responsible $vehicleResponsiblePerson, vehicle-usage $vehicleUsageId, " +
                            "usage-group $usageGroupId when fetching predecessor. Skipping",
                    )
                }
            }
        }

        return null
    }

    fun findVehicleTransferByPredecessor(predecessor: VehicleTransfer) = vehicleTransferRepository.findByPredecessor(predecessor)

    fun findVehicleTransferBySuccessor(successorKey: VehicleTransferKey) = vehicleTransferRepository.findBySuccessorKey(successorKey)

    fun findVehicleTransferByKey(key: VehicleTransferKey): VehicleTransfer? = vehicleTransferRepository.findByKey(key)

    fun findByLastModifiedBefore(lastModifiedBefore: OffsetDateTime): List<VehicleTransfer> =
        vehicleTransferRepository.findByLastModifiedBefore(lastModifiedBefore)

    fun findActiveVehicleTransfersWithoutMaintenanceOrderNumber(): List<VehicleTransfer> =
        vehicleTransferRepository.findAll(
            Specification.where(
                specificationForStatusEquals(VehicleTransferStatus.ACTIVE).and(
                    specificationMaintenanceOrderNumberIsNull(),
                ),
            ),
        )

    override fun findAllActiveTransfers(): List<VehicleTransfer> = vehicleTransferRepository.findAllByStatus(VehicleTransferStatus.ACTIVE)

    override fun findActiveVehicleTransferByLicensePlate(licensePlate: String): List<VehicleTransfer> =
        vehicleTransferRepository.findAll(
            Specification.where(
                specificationForStatusEquals(VehicleTransferStatus.ACTIVE).and(
                    specificationByLicensePlate(licensePlate),
                ),
            ),
        )

    companion object {
        private const val LICENSE_PLATE_LAST_UPDATE = "licensePlateLastUpdate"
        private const val VEHICLE_ID = "vehicleId"
        private const val USAGE_GROUP_LEASING = USAGE_GROUP_DESCRIPTION_PERSON
        private const val STATUS = "status"
        private const val MAINTENANCE_ORDER_NUMBER = "maintenanceOrderNumber"
        private const val UTILIZATION_AREA = "utilizationArea"
        private const val LICENSE_PLATE = "licensePlate"
        private const val ELECTRIC_VEHICLE_LICENCE_PLATE_SUFFIX = "E"
        private const val HISTORIC_VEHICLE_LICENSE_PLATE_SUFFIX = "H"

        private val log = LoggerFactory.getLogger(VehicleTransferFinder::class.java)

        private fun specificationLicensePlateLastUpdateBefore(date: OffsetDateTime) =
            Specification<VehicleTransfer> { root, _, criteriaBuilder ->
                criteriaBuilder.lessThan(root.get(LICENSE_PLATE_LAST_UPDATE), date)
            }

        private fun specificationForStatusEquals(status: VehicleTransferStatus) =
            Specification<VehicleTransfer> { root, query, criteriaBuilder ->
                criteriaBuilder.and(
                    criteriaBuilder.equal(
                        root.get<String>(STATUS),
                        status.name,
                    ),
                )
            }

        private fun specificationMaintenanceOrderNumberIsNull() =
            Specification<VehicleTransfer> { root, _, criteriaBuilder ->
                criteriaBuilder.isNull(root.get<String>(MAINTENANCE_ORDER_NUMBER))
            }

        private fun specificationLicensePlateLastUpdateIsNull() =
            Specification<VehicleTransfer> { root, _, criteriaBuilder ->
                criteriaBuilder.isNull(root.get<OffsetDateTime>(LICENSE_PLATE_LAST_UPDATE))
            }

        private fun specificationForUtilizationAreaEquals(utilizationArea: UtilizationArea) =
            Specification<VehicleTransfer> { root, query, criteriaBuilder ->
                criteriaBuilder.and(
                    criteriaBuilder.equal(
                        root.get<String>(UTILIZATION_AREA),
                        utilizationArea.name,
                    ),
                )
            }

        private fun specificationByVehicleId(vehicleId: UUID) =
            Specification<VehicleTransfer> { root, _, criteriaBuilder ->
                criteriaBuilder.equal(root.get<UUID>(VEHICLE_ID), vehicleId)
            }

        private fun specificationByLicensePlate(licensePlate: String): Specification<VehicleTransfer> {
            val normalizedLicensePlate = normalizeLicensePlate(licensePlate)
            val possibleVariants =
                listOf(
                    normalizedLicensePlate,
                    "$normalizedLicensePlate$ELECTRIC_VEHICLE_LICENCE_PLATE_SUFFIX",
                    "$normalizedLicensePlate$HISTORIC_VEHICLE_LICENSE_PLATE_SUFFIX",
                )
            return Specification<VehicleTransfer> { root, _, criteriaBuilder ->
                val expression = criteriaBuilder.upper(root.get(LICENSE_PLATE))
                expression.`in`(possibleVariants)
            }
        }

        private fun normalizeLicensePlate(licensePlate: String) =
            licensePlate
                .uppercase()
                .removeSuffix(ELECTRIC_VEHICLE_LICENCE_PLATE_SUFFIX)
                .removeSuffix(HISTORIC_VEHICLE_LICENSE_PLATE_SUFFIX)
    }
}
