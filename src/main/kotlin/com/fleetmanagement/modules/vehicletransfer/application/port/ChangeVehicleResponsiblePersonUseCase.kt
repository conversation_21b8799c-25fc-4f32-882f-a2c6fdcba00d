/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehicletransfer.application.port

import com.fleetmanagement.modules.consigneedatasheet.domain.EmployeeNumber
import com.fleetmanagement.modules.vehicledata.api.domain.NextProcess
import com.fleetmanagement.modules.vehicletransfer.domain.entities.VehicleTransfer
import java.util.*

fun interface ChangeVehicleResponsiblePersonUseCase {
    /**
     * A dedicated useCase to change the vehicle responsible person of an active vehicle transfer.
     * This function will book a return for the given transfer, and create a new planned one,
     * as well as perform a delivery, for the new vehicle responsible person. Unless you are VTStamm do not use this!
     */
    fun changeResponsiblePerson(
        vehicleId: UUID,
        vin: String,
        vehicleResponsiblePerson: EmployeeNumber,
        nextProcess: NextProcess,
        latestMileage: Int,
        returnComment: String?,
        deliveryComment: String?,
    ): VehicleTransfer
}
