/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehicletransfer.domain

import com.fleetmanagement.emhshared.domain.ConstraintViolation
import java.time.OffsetDateTime
import java.util.*

class PlannedVehicleTransferNotFoundException(
    val plannedVehicleTransferKey: Long,
) : NoSuchElementException("Could not find PlannedVehicleTransfer with key [$plannedVehicleTransferKey].", null)

class VehicleTransferNotFoundException(
    val vehicleTransferKey: Long,
) : NoSuchElementException("Could not find VehicleTrans<PERSON> with key [$vehicleTransferKey].", null)

class VehicleTransferNotFinishedException(
    val vehicleTransferKey: Long,
) : IllegalArgumentException("VehicleTransfer with key [$vehicleTransferKey] is not FINISHED.", null)

class VehicleTransferNotActiveException(
    vehicleTransferKey: Long,
) : IllegalArgumentException("VehicleTransfer with key [$vehicleTransferKey] is not ACTIVE.", null)

class VehicleTransferFutureDateException(
    val vehicleTransferKey: Long,
    val date: OffsetDateTime,
    val propertyName: String,
) : IllegalArgumentException(
        "Vehicle with id [$vehicleTransferKey] has a date for [$propertyName] not allowed in future: $date.",
        null,
    )

class VehicleTransferDeliveryException(
    val vehicleTransferKey: Long,
    val missingProperties: List<String>,
) : IllegalArgumentException(
        "VehicleTransfer with key [$vehicleTransferKey] missing required properties $missingProperties.",
        null,
    )

class PlannedVehicleTransferDeliveryLeasingPrivilegsException(
    val vehicleTransferKey: Long,
) : IllegalArgumentException(
        "planned VehicleTransfer with key [$vehicleTransferKey] missing required leasing privileges.",
        null,
    )

class VehicleTransferReturnException(
    val vehicleTransferKey: Long,
    val missingProperties: List<String>,
) : IllegalArgumentException(
        "VehicleTransfer with key [$vehicleTransferKey] missing required properties $missingProperties.",
        null,
    )

class PlannedVehicleTransferHasMsbookingAppointmentIdException(
    vehicleTransferKey: String,
) : IllegalArgumentException(
        "PlannedVehicleTransfer with key [$vehicleTransferKey] has an msbooking appointment id and can not be updated.",
        null,
    )

class VehicleTransferHasMsbookingAppointmentIdException(
    vehicleTransferKey: String,
) : IllegalArgumentException(
        "VehicleTransfer with key [$vehicleTransferKey] has an msbooking appointment id and can not be updated.",
        null,
    )

class VehicleTransferNotPlannedException(
    vehicleTransferKey: Long,
) : IllegalArgumentException("VehicleTransfer with key [$vehicleTransferKey] is not PLANNED.", null)

class VehicleTransferUpdateException(
    vehicleTransferKey: Long,
    val constraintViolation: ConstraintViolation? = null,
    override val cause: Throwable,
) : RuntimeException("VehicleTransfer with key [$vehicleTransferKey] could not be updated. ${cause.message}", null)

class VehicleTransferCreateException(
    vehicleId: UUID,
    override val cause: Throwable? = null,
) : RuntimeException("VehicleTransfer for vehicle [$vehicleId] could not be created. ${cause?.message}", null)

class VehicleTransferArchiveDeleteException(
    key: String,
) : NoSuchElementException("Did not delete any record as key $key not found in planned or vehicle transfer", null)

class ActiveVehicleTransferAlreadyExistsException(
    val vehicleId: UUID,
    val existingActiveTransferKey: Long,
) : IllegalArgumentException(
        "VehicleTransfer with vehicleId [$vehicleId] has already an " +
            "active transfer with key $existingActiveTransferKey.",
        null,
    )

class VehicleTransferInvalidUsingCostCenterException(
    val vehicleTransferKey: Long,
) : RuntimeException(
        "VehicleTransfer with key [$vehicleTransferKey] has a usage group which " +
            "force using cost center to be not nullable",
        null,
    )
