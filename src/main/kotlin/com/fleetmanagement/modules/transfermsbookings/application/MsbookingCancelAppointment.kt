package com.fleetmanagement.modules.transfermsbookings.application

import com.fleetmanagement.modules.transfermsbookings.adapter.out.MsBookingConfiguration
import com.fleetmanagement.modules.transfermsbookings.application.port.ReadAppointments
import com.fleetmanagement.modules.vehicletransfer.application.port.ReadPlannedVehicleTransferUseCase
import com.fleetmanagement.modules.vehicletransfer.application.port.ReadVehicleTransferUseCase
import com.fleetmanagement.modules.vehicletransfer.application.port.UpdatePlannedVehicleTransferUseCase
import com.fleetmanagement.modules.vehicletransfer.application.port.UpdateVehicleTransferMsBookingUseCase
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.stereotype.Component
import java.time.OffsetDateTime
import java.time.format.DateTimeFormatter

@Component
@ConditionalOnBean(MsBookingConfiguration::class)
class MsbookingCancelAppointment(
    @Value("\${msbooking.cancel-timeframe}") private val cancelTimeframeInDays: Int,
    private val readPlannedVehicleTransferUseCase: ReadPlannedVehicleTransferUseCase,
    private val readVehicleTransferUseCase: ReadVehicleTransferUseCase,
    private val updatePlannedVehicleTransferUseCase: UpdatePlannedVehicleTransferUseCase,
    private val updateVehicleTransferUseCase: UpdateVehicleTransferMsBookingUseCase,
    private val readAppointments: ReadAppointments,
) {
    /**
     * read all the appointments in a given timeframe
     * read all the db appointments where the delivery\return date is in the time frame
     * if an appointment in the db exist but not in the msbooking
     * the assumption that it was canceled
     */
    fun handleCancelAppointment() {
        val now = OffsetDateTime.now()
        val inTheFuture = OffsetDateTime.now().plusDays(cancelTimeframeInDays.toLong())
        val futureAppointments = retrieveFutureAppointments(now, inTheFuture)
        val appointmentsId = futureAppointments.map { it.appointmentId }
        val plannedVehicleTransferAppointmentIds =
            readPlannedVehicleTransferUseCase.readAllAppointmentsIdAndKeyWherePlannedDeliveryDateBetween(now, inTheFuture)
        val vehicleTransferAppointmentIds =
            readVehicleTransferUseCase.readAllAppointmentsIdAndKeyWherePlannedReturnDateBetween(now, inTheFuture)

        val canceledReturnAppointments = vehicleTransferAppointmentIds.filterNot { it.first in appointmentsId }
        val canceledDeliveryAppointments = plannedVehicleTransferAppointmentIds.filterNot { it.first in appointmentsId }
        log.info("found ${canceledReturnAppointments.size + canceledDeliveryAppointments.size} that was canceled on ms booking ")
        canceledReturnAppointments.forEach {
            updateVehicleTransferUseCase.updateMsbookingAppointmentId(null, it.second)
            updateVehicleTransferUseCase.updatePlannedReturnDate(null, it.second)
            log.info("reset PlannedReturnDate and msbookingAppointmentId for ${it.second} ")
        }
        canceledDeliveryAppointments.forEach {
            updatePlannedVehicleTransferUseCase.updateMsbookingAppointmentId(null, it.second)
            updatePlannedVehicleTransferUseCase.updatePlannedDeliveryDate(null, it.second)
            log.info("reset PlannedDeliveryDate and msbookingAppointmentId for ${it.second} ")
        }
    }

    private fun retrieveFutureAppointments(
        startTime: OffsetDateTime,
        endTime: OffsetDateTime,
    ): List<AppointmentDto> =
        readAppointments.readCalendar(
            startTime = startTime.format(DateTimeFormatter.ISO_DATE_TIME),
            endTime = endTime.format(DateTimeFormatter.ISO_DATE_TIME),
        )

    companion object {
        private val log = LoggerFactory.getLogger(MsbookingCancelAppointment::class.java)
    }
}
