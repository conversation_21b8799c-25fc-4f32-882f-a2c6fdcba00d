package com.fleetmanagement.modules.transfermsbookings.application

import com.fleetmanagement.modules.transfermsbookings.adapter.out.MsBookingConfiguration
import com.fleetmanagement.modules.transfermsbookings.adapter.out.MsbookingClientException
import com.fleetmanagement.modules.transfermsbookings.application.port.UpdateAppointment
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByVehicleId
import com.fleetmanagement.modules.vehicledata.api.dtos.VehicleDTO
import com.fleetmanagement.modules.vehicleperson.api.dtos.VehiclePersonDetail
import com.fleetmanagement.modules.vehicleperson.api.readvehicleperson.ReadVehiclePersonDetailByEmployeeNumber
import com.fleetmanagement.modules.vehicleperson.api.readvehicleperson.ReadVehiclePersonException
import com.fleetmanagement.modules.vehicletransfer.application.port.ReadPlannedVehicleTransferUseCase
import com.fleetmanagement.modules.vehicletransfer.application.port.UpdatePlannedVehicleTransferUseCase
import com.fleetmanagement.modules.vehicletransfer.application.port.UpdateVehicleTransferMsBookingUseCase
import com.fleetmanagement.modules.vehicletransfer.domain.entities.VehicleTransferKey
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.stereotype.Component
import java.time.OffsetDateTime
import java.util.UUID

@Component
@ConditionalOnBean(MsBookingConfiguration::class)
class MsbookingNewAppointment(
    private val updateAppointment: UpdateAppointment,
    private val updatePlannedVehicleTransferUseCase: UpdatePlannedVehicleTransferUseCase,
    private val updateVehicleTransferUseCase: UpdateVehicleTransferMsBookingUseCase,
    private val readVehiclePersonDetailByEmployeeNumber: ReadVehiclePersonDetailByEmployeeNumber,
    private val readVehicleByVehicleId: ReadVehicleByVehicleId,
    private val msbookingQuestionsId: MsbookingQuestionsId,
    private val readPlannedVehicleTransferUseCase: ReadPlannedVehicleTransferUseCase,
) {
    /**
     * update the user email and name in the appointment ( run patch commend)
     * update the vehicle data : vin license plate and model to the appointment questions
     * update the vehicle transfer planned delivery\return time with the corresponding appointment time
     *
     */
    fun handleNewAppointment(
        appointmentType: AppointmentType,
        keys: AppointmentKeys,
        appointmentId: String,
        startDateTime: OffsetDateTime,
    ) {
        when (appointmentType) {
            AppointmentType.DELIVERY -> {
                handleDelivery(keys.deliveryKey!!, startDateTime, appointmentId)
            }
            AppointmentType.RETURN -> handleReturn(keys.returnKey!!, startDateTime, appointmentId)
            AppointmentType.EXCHANGE -> {
                val deliveryKey = keys.deliveryKey!!

                val returnKey = findReturnedKey(keys.deliveryKey)
                if (returnKey == null) {
                    log.warn("could not find returned vehicle (Predecessor) for deliveryKey $deliveryKey")
                    return
                }

                handleExchange(deliveryKey, returnKey, startDateTime, appointmentId)
            }
        }
    }

    private fun createCombinedAppointmentUpdateRequest(
        deliveryData: RequiredVehicleInfo,
        returnData: RequiredVehicleInfo,
    ): AppointmentUpdateRequest {
        val deliveryVehicleInfo = fetchVehicleAndPersonData(deliveryData.vehicleResponsiblePerson, deliveryData.vehicleId)
        val returnVehicleInfo = fetchVehicleAndPersonData(returnData.vehicleResponsiblePerson, returnData.vehicleId)

        val deliveryCustomer = createCustomer(deliveryVehicleInfo, deliveryData.licensePlate, isDelivery = true)
        val returnCustomer = createCustomer(returnVehicleInfo, returnData.licensePlate, isDelivery = false)
        val questions = deliveryCustomer.customQuestionAnswers + returnCustomer.customQuestionAnswers
        return AppointmentUpdateRequest(
            odataType = ORDER_TYPE,
            customerName = deliveryCustomer.name,
            customerEmailAddress = deliveryCustomer.emailAddress,
            customers =
                listOf(
                    CustomerUpdateDTO(
                        odataType = CUSTOMER_TYPE,
                        name = deliveryCustomer.name,
                        emailAddress = deliveryCustomer.emailAddress,
                        customQuestionAnswers = questions,
                        timeZone = deliveryCustomer.timeZone,
                    ),
                ),
        )
    }

    private fun handleExchange(
        deliveryKey: VehicleTransferKey,
        returnKey: VehicleTransferKey,
        plannedDateTime: OffsetDateTime,
        appointmentId: String,
    ) {
        updatePlannedVehicleTransferUseCase.updatePlannedDeliveryDate(plannedDateTime, deliveryKey)
        updateVehicleTransferUseCase.updatePlannedReturnDate(plannedDateTime, returnKey)

        val deliveryTransfer = updatePlannedVehicleTransferUseCase.updateMsbookingAppointmentId(appointmentId, deliveryKey)
        if (deliveryTransfer == null) {
            log.warn("Planned vehicle transfer key $deliveryKey not found, not updating delivery data...")
            return
        }

        val returnTransfer = updateVehicleTransferUseCase.updateMsbookingAppointmentId(appointmentId, returnKey)
        if (returnTransfer == null) {
            log.warn("Vehicle transfer key $returnKey not found, not updating return data...")
            return
        }

        val deliveryData =
            RequiredVehicleInfo(
                deliveryTransfer.vehicleResponsiblePerson?.value!!,
                deliveryTransfer.vehicleId,
                deliveryTransfer.licensePlate,
            )
        val returnData =
            RequiredVehicleInfo(
                returnTransfer.vehicleResponsiblePerson?.value!!,
                returnTransfer.vehicleId,
                returnTransfer.licensePlate,
            )

        val combinedUpdateRequest = createCombinedAppointmentUpdateRequest(deliveryData, returnData)

        try {
            updateAppointment.updateMsBookingId(
                appointmentId = appointmentId,
                updateRequest = combinedUpdateRequest,
            )
            log.info("Successfully updated exchange appointment $appointmentId")
        } catch (e: MsbookingClientException) {
            log.warn("Could not update exchange appointment $appointmentId")
        }
    }

    /**
     * input is the delivery key
     * output will be the predecessor (the current planned active for this delivery key)
     */
    private fun findReturnedKey(plannedVehicleTransferKey: VehicleTransferKey): VehicleTransferKey? {
        val returnedVehicle = readPlannedVehicleTransferUseCase.findByKey(plannedVehicleTransferKey)
        return returnedVehicle?.predecessor?.key
    }

    private fun updateAppointment(
        externalDataResource: RequiredVehicleInfo,
        appointmentId: String,
        isDelivery: Boolean,
    ) {
        val externalData = fetchVehicleAndPersonData(externalDataResource.vehicleResponsiblePerson, externalDataResource.vehicleId)
        val customer = createCustomer(externalData, externalDataResource.licensePlate, isDelivery)
        try {
            updateAppointment.updateMsBookingId(
                appointmentId = appointmentId,
                updateRequest =
                    AppointmentUpdateRequest(
                        odataType = ORDER_TYPE,
                        customerName = "${externalData.vehiclePersonDetail?.firstName} ${externalData.vehiclePersonDetail?.lastName}",
                        customerEmailAddress = "${externalData.vehiclePersonDetail?.companyEmail}",
                        customers = listOf(customer),
                    ),
            )
            log.info("successfully updated appointment $appointmentId")
        } catch (e: MsbookingClientException) {
            log.warn("could not update appointment $appointmentId")
        }
    }

    private fun fetchVehicleAndPersonData(
        vehicleResponsiblePerson: String,
        vehicleId: UUID,
    ): AppointmentVehicleInfo {
        val vehiclePersonDetail =
            try {
                readVehiclePersonDetailByEmployeeNumber.readVehiclePersonDetailByEmployeeNumber(vehicleResponsiblePerson)
            } catch (e: ReadVehiclePersonException) {
                null
            }
        val vehicleData = readVehicleByVehicleId.readVehicleById(vehicleId)
        if (vehiclePersonDetail == null) {
            log.warn("could not read vehiclePersonDetail vehicleResponsiblePerson: [$vehicleResponsiblePerson]")
        }
        if (vehicleData == null) {
            log.warn("could not read vehicle [$vehicleId]")
        }
        return AppointmentVehicleInfo(vehicleData, vehiclePersonDetail)
    }

    private fun handleDelivery(
        key: VehicleTransferKey,
        plannedDeliveryDate: OffsetDateTime,
        appointmentId: String,
    ) {
        updatePlannedVehicleTransferUseCase.updatePlannedDeliveryDate(plannedDeliveryDate, key)
        val transfer = updatePlannedVehicleTransferUseCase.updateMsbookingAppointmentId(appointmentId, key)
        if (transfer == null) {
            log.warn("planned vehicle transfer key $key was not find, not updating...")
            return
        }
        val externalData = RequiredVehicleInfo(transfer.vehicleResponsiblePerson?.value!!, transfer.vehicleId, transfer.licensePlate)
        updateAppointment(externalData, appointmentId, true)
    }

    private fun handleReturn(
        key: VehicleTransferKey,
        plannedReturnDate: OffsetDateTime,
        appointmentId: String,
    ) {
        updateVehicleTransferUseCase.updatePlannedReturnDate(plannedReturnDate, key)
        val transfer = updateVehicleTransferUseCase.updateMsbookingAppointmentId(appointmentId, key)
        if (transfer == null) {
            log.warn("vehicle transfer key $key was not find, not updating...")
            return
        }
        val externalData = RequiredVehicleInfo(transfer.vehicleResponsiblePerson?.value!!, transfer.vehicleId, transfer.licensePlate)
        updateAppointment(externalData, appointmentId, false)
    }

    private fun createCustomer(
        appointmentVehicleInfo: AppointmentVehicleInfo,
        licensePlate: String?,
        isDelivery: Boolean,
    ): CustomerUpdateDTO {
        val customQuestionAnswers =
            listOf(
                createCustomQuestionAnswer(
                    getVinQuestionId(isDelivery),
                    getVinQuestion(isDelivery),
                    appointmentVehicleInfo.vehicleData?.vin ?: "",
                ),
                createCustomQuestionAnswer(
                    getLicensePlateQuestionId(isDelivery),
                    getLicensePlateQuestion(isDelivery),
                    licensePlate ?: "",
                ),
                createCustomQuestionAnswer(
                    getModelQuestionId(isDelivery),
                    getModelQuestion(isDelivery),
                    appointmentVehicleInfo.vehicleData?.model?.description ?: "",
                ),
            )

        return CustomerUpdateDTO(
            odataType = CUSTOMER_TYPE,
            name = "${appointmentVehicleInfo.vehiclePersonDetail?.firstName} ${appointmentVehicleInfo.vehiclePersonDetail?.lastName}",
            emailAddress = "${appointmentVehicleInfo.vehiclePersonDetail?.companyEmail}",
            timeZone = "W. Europe Standard Time",
            customQuestionAnswers = customQuestionAnswers,
        )
    }

    private fun createCustomQuestionAnswer(
        questionId: String,
        question: String,
        answer: String,
    ): UpdateCustomQuestionAnswerDTO =
        UpdateCustomQuestionAnswerDTO(
            questionId = questionId,
            question = question,
            answer = answer,
        )

    private fun getVinQuestionId(isDelivery: Boolean) = if (isDelivery) msbookingQuestionsId.deliveryVin else msbookingQuestionsId.returnVin

    private fun getVinQuestion(isDelivery: Boolean) = if (isDelivery) "VIN (Auslieferungsfahrzeug)" else "VIN (Rückgabefahrzeug)"

    private fun getLicensePlateQuestionId(isDelivery: Boolean) =
        if (isDelivery) msbookingQuestionsId.deliveryLicensePlate else msbookingQuestionsId.returnLicensePlate

    private fun getLicensePlateQuestion(isDelivery: Boolean) =
        if (isDelivery) "Kennzeichen (Auslieferungsfahrzeug)" else "Kennzeichen (Rückgabefahrzeug)"

    private fun getModelQuestionId(isDelivery: Boolean) =
        if (isDelivery) msbookingQuestionsId.deliveryModel else msbookingQuestionsId.returnModel

    private fun getModelQuestion(isDelivery: Boolean) = if (isDelivery) "Modell (Auslieferungsfahrzeug)" else "Modell (Rückgabefahrzeug)"

    companion object {
        private val log = LoggerFactory.getLogger(MsbookingNewAppointment::class.java)
        private val ORDER_TYPE = "#microsoft.graph.bookingAppointment"
        private val CUSTOMER_TYPE = "#microsoft.graph.bookingCustomerInformation"
    }
}

data class AppointmentVehicleInfo(
    val vehicleData: VehicleDTO?,
    val vehiclePersonDetail: VehiclePersonDetail?,
)

data class RequiredVehicleInfo(
    // Luisa: when we set appointment a vehicleResponsiblePerson must be existing
    val vehicleResponsiblePerson: String,
    val vehicleId: UUID,
    val licensePlate: String?,
)

@ConfigurationProperties("msbooking.questions-id")
data class MsbookingQuestionsId(
    val deliveryVin: String,
    val deliveryLicensePlate: String,
    val deliveryModel: String,
    val returnVin: String,
    val returnLicensePlate: String,
    val returnModel: String,
)

data class UpdateCustomQuestionAnswerDTO(
    val questionId: String,
    val question: String,
    val answer: String,
)
