package com.fleetmanagement.modules.transfermsbookings.application

import com.fleetmanagement.modules.transfermsbookings.adapter.out.MsBookingConfiguration
import com.fleetmanagement.modules.transfermsbookings.application.port.ReadAppointments
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.stereotype.Component
import java.time.OffsetDateTime
import java.time.format.DateTimeFormatter

@Component
@ConditionalOnBean(MsBookingConfiguration::class)
class MsBookingAppointmentsSync(
    private val readAppointments: ReadAppointments,
    private val msbookingNewAppointment: MsbookingNewAppointment,
    private val msbookingUpdateAppointment: MsbookingUpdateAppointment,
) {
    /**
     * fetch all the appointments ( as the api do not support filtering :-( )
     * filter for those who being updated in the last hour
     * looping and for each
     */
    fun syncAppointments() {
        val now = OffsetDateTime.now()
        val recentAppointments =
            readAppointments.readCalendar(
                startTime = now.format(DateTimeFormatter.ISO_DATE_TIME),
                endTime = null,
            )
        val lastUpdatedAppointments =
            recentAppointments.filter { it.lastUpdatedDateTime.isAfter(now.minusDays(1)) }
        log.info("starting appointments sync job with msbooking, retrieved ${recentAppointments.size}")
        lastUpdatedAppointments.forEach {
            syncAppointment(it)
            log.info("finish working on appointment with type: ${it.appointmentType} and keys AppointmentKeys ${it.keys}")
        }
    }

    /**
     * extract  key , appointment type, meeting start time, appointment id
     * decide if it is a new appointment or updated one and take the corresponding action
     */
    private fun syncAppointment(appointment: AppointmentDto) {
        val appointmentType = appointment.appointmentType
        val keys = appointment.keys
        log.info("start working on appointment with type: $appointmentType and keys $keys")
        if (appointment.customerEmailAddress.isNullOrEmpty()) {
            msbookingNewAppointment.handleNewAppointment(
                appointmentType,
                keys,
                appointment.appointmentId,
                appointment.startTime,
            )
            return
        }
        msbookingUpdateAppointment.handleUpdateAppointment(
            appointmentType,
            keys,
            appointment.startTime,
        )
    }

    companion object {
        private val log = LoggerFactory.getLogger(MsBookingAppointmentsSync::class.java)
    }
}
