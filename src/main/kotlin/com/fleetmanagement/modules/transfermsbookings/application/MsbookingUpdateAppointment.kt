package com.fleetmanagement.modules.transfermsbookings.application

import com.fleetmanagement.modules.transfermsbookings.adapter.out.MsBookingConfiguration
import com.fleetmanagement.modules.vehicletransfer.application.port.UpdatePlannedVehicleTransferUseCase
import com.fleetmanagement.modules.vehicletransfer.application.port.UpdateVehicleTransferMsBookingUseCase
import com.fleetmanagement.modules.vehicletransfer.domain.entities.VehicleTransferKey
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.stereotype.Component
import java.time.OffsetDateTime

@Component
@ConditionalOnBean(MsBookingConfiguration::class)
class MsbookingUpdateAppointment(
    private val updatePlannedVehicleTransferUseCase: UpdatePlannedVehicleTransferUseCase,
    private val updateVehicleTransferUseCase: UpdateVehicleTransferMsBookingUseCase,
) {
    /**
     * If the appointment was change just update time ( although it could be that the time did not )
     * therefore we do not need to do checks if the there was a time change
     */
    fun handleUpdateAppointment(
        appointmentType: AppointmentType,
        keys: AppointmentKeys,
        startDateTime: OffsetDateTime,
    ) {
        when (appointmentType) {
            AppointmentType.DELIVERY -> handleDelivery(keys.deliveryKey!!, startDateTime)
            AppointmentType.RETURN -> handleReturn(keys.returnKey!!, startDateTime)
            AppointmentType.EXCHANGE -> handleExchange(keys.deliveryKey!!, keys.returnKey!!, startDateTime)
        }
    }

    private fun handleReturn(
        key: VehicleTransferKey,
        startDateTime: OffsetDateTime,
    ) {
        val transfer = updateVehicleTransferUseCase.updatePlannedReturnDate(plannedReturnDate = startDateTime, key = key)
        if (transfer == null) {
            log.warn("did not update planned return date as transfer with key $key was not found")
        }
    }

    private fun handleDelivery(
        key: VehicleTransferKey,
        startDateTime: OffsetDateTime,
    ) {
        val transfer = updatePlannedVehicleTransferUseCase.updatePlannedDeliveryDate(plannedDeliveryDate = startDateTime, key = key)
        if (transfer == null) {
            log.warn("did not update planned delivery date as transfer with key $key was not found")
        }
    }

    private fun handleExchange(
        deliveryKey: VehicleTransferKey,
        returnKey: VehicleTransferKey,
        startDateTime: OffsetDateTime,
    ) {
        handleDelivery(deliveryKey, startDateTime)
        handleReturn(returnKey, startDateTime)
    }

    companion object {
        val log = LoggerFactory.getLogger(MsbookingUpdateAppointment::class.java)
    }
}
