package com.fleetmanagement.modules.transfermsbookings.adapter.out

import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.databind.ObjectMapper
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.http.MediaType
import org.springframework.web.reactive.function.client.ClientRequest
import org.springframework.web.reactive.function.client.ExchangeFilterFunction
import org.springframework.web.reactive.function.client.WebClient
import org.springframework.web.reactive.function.client.support.WebClientAdapter
import org.springframework.web.service.invoker.HttpServiceProxyFactory

@Configuration
@ConditionalOnProperty(name = ["msbooking.enabled"], havingValue = "true")
class MsBookingConfiguration

@Configuration
@ConditionalOnBean(MsBookingConfiguration::class)
class MsBookingClientConfiguration(
    private val msbookingSecurityProperties: MsbookingSecurityProperties,
    private val objectMapper: ObjectMapper,
) {
    @Bean
    @Qualifier("msbooking")
    fun msBookingServiceProxyFactory(
        @Value("\${msbooking.base-url}") baseUrl: String,
        msbookingClientExceptionHandler: MsbookingClientExceptionHandler,
    ): HttpServiceProxyFactory {
        val token = getOAuth2Token()

        val webClient =
            WebClient
                .builder()
                .baseUrl(baseUrl)
                .filter(ExchangeFilterFunction.ofResponseProcessor(msbookingClientExceptionHandler::clientErrorResponseProcessor))
                .filter(msbookingClientExceptionHandler::clientErrorRequestProcessor)
                .filter { request, next ->
                    val modifiedRequest =
                        ClientRequest
                            .from(request) // Create a modified request
                            .header("Authorization", "Bearer ${getOAuth2Token()}") // Add the Authorization header
                            .build()
                    next.exchange(modifiedRequest) // Return the modified request
                }.build()

        val adapter = WebClientAdapter.create(webClient)
        return HttpServiceProxyFactory.builderFor(adapter).build()
    }

    // use a custom as the password flow a depracated
    // https://docs.spring.io/spring-security/reference/reactive/oauth2/client/authorization-grants.html
    private fun getOAuth2Token(): String {
        val tokenResponse =
            WebClient
                .create()
                .post()
                .uri(msbookingSecurityProperties.tokenUri)
                .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                .bodyValue(
                    "client_id=${msbookingSecurityProperties.clientId}&" +
                        "client_secret=${msbookingSecurityProperties.clientSecret}&" +
                        "grant_type=password&" +
                        "scope=${msbookingSecurityProperties.scope}&" +
                        "username=${msbookingSecurityProperties.username}&" +
                        "password=${msbookingSecurityProperties.password}",
                ).retrieve()
                .bodyToMono(String::class.java)
                .block()

        val token = extractAccessToken(tokenResponse)
        return token
    }

    private fun extractAccessToken(response: String?): String {
        val tokenResponse = objectMapper.readValue(response, OAuth2TokenResponse::class.java)
        return tokenResponse.accessToken
    }

    @Bean
    @Qualifier("msbooking")
    fun getMsBookingClient(
        @Qualifier("msbooking") msBookingServiceProxyFactory: HttpServiceProxyFactory,
    ): MsBookingWebClient = msBookingServiceProxyFactory.createClient(MsBookingWebClient::class.java)
}

@ConfigurationProperties("msbooking.security")
data class MsbookingSecurityProperties(
    val username: String,
    val password: String,
    val clientId: String,
    val clientSecret: String,
    val authorizationGrantType: String,
    val scope: String,
    val tokenUri: String,
)

data class OAuth2TokenResponse(
    @JsonProperty("access_token") val accessToken: String,
)
