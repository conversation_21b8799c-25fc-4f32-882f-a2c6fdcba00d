package com.fleetmanagement.modules.reporting.service

import com.fleetmanagement.modules.reporting.configuration.ReportingFeatureFlagConfiguration
import com.fleetmanagement.modules.reporting.dto.ReportingVehicle
import com.fleetmanagement.modules.reporting.dto.ReportingVehicleRegistration
import com.fleetmanagement.modules.reporting.dto.ReportingVehicleTransfer
import com.fleetmanagement.modules.vehicledata.api.domain.FuelType
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.security.MessageDigest
import java.time.OffsetDateTime
import java.util.*

@Service
@ConditionalOnBean(ReportingFeatureFlagConfiguration::class)
class ReportingEventPublisher(
    private val reportingKafkaProducer: ReportingKafkaProducer<Any>,
) {
    private val logger: Logger = LoggerFactory.getLogger(ReportingEventPublisher::class.java)

    companion object {
        private const val REPORTING_VEHICLE_TOPIC = "FRA_emhs_reporting_vehicle_dev"
        private const val REPORTING_VEHICLE_TRANSFER_TOPIC = "FRA_emhs_reporting_vehicle_transfer_dev"
        private const val REPORTING_VEHICLE_REGISTRATION_TOPIC = "FRA_emhs_reporting_vehicle_registration_dev"
    }

    fun publishKafkaEvent() {
        logger.info("Publishing event")
        publishReportingVehicle()
        publishReportingVehicleTransfer()

        // TODO will be moved to registration service
        publishReportingVehicleRegistration()
    }

    private fun generateMessageKey(domainId: String): String {
        val bytes = MessageDigest.getInstance("SHA-256").digest(domainId.toByteArray())
        return bytes.joinToString("") { "%02x".format(it) }
    }

    private fun generateMessageHeaders(
        domainId: Any,
        eventType: String,
    ): Map<String, String> =
        mapOf(
            "id" to domainId.toString(),
            "timestamp" to OffsetDateTime.now().toString(),
            "eventType" to eventType,
        )

    private fun publishReportingVehicle() {
        try {
            val reportingVehicle =
                ReportingVehicle(
                    vguid = "VG1234567890",
                    vin = "WP0ZZZ99ZTS392124",
                    equiId = "EQ-001",
                    equipmentNumber = 10001L,
                    status = "ACTIVE",
                    productionNumber = "PN-2025-001",
                    productionFactory = "Stuttgart",
                    productionZp8Date = OffsetDateTime.parse("2024-07-01T10:00:00+01:00"),
                    productionPlannedEndDate = OffsetDateTime.parse("2024-07-10T10:00:00+01:00"),
                    productionTechnicalModelYear = 2025,
                    productionGearBoxClass = 'A',
                    modelOrderType = "ORDER-TYPE-1",
                    modelModelRange = "911",
                    modelManufacturer = "Porsche",
                    modelVehicleType = "Coupe",
                    modelDescription = "911 Carrera S",
                    priceVehicleFactoryGrossPriceEUR = BigDecimal("120000.00"),
                    priceVehicleFactoryNetPriceEUR = BigDecimal("100000.00"),
                    priceGrossPriceWithExtras = BigDecimal("125000.00"),
                    consumptionDriveType = "RWD",
                    consumptionTypification = "Sport",
                    consumptionPrimaryFuelType = FuelType.SUPER,
                    consumptionSecondaryFuelType = FuelType.ELECTRIC,
                    wltpCo2Combined = 180,
                    wltpElectricRangeCity = 60,
                    wltpElectricRange = 55,
                    currentMileageMileage = 5000,
                    currentMileageReadDate = OffsetDateTime.parse("2025-07-01T10:00:00+01:00"),
                    orderBlockedForSale = false,
                    orderPrimaryStatus = "CONFIRMED",
                    orderTradingPartnerNumber = "TP-12345",
                    fleetScrapVehicle = false,
                    fleetResidualValueMarket = false,
                    fleetRaceCar = false,
                    fleetClassic = false,
                    evaluationAppraisalNetPrice = BigDecimal("98000.00"),
                    salesReservedForB2C = true,
                    salesContractSigned = true,
                    technicalAmountSeats = 4,
                    technicalAcceleration0100KmhLaunchControl = 3.2f,
                    technicalAcceleration0100Kmh = 3.5f,
                    technicalAcceleration80120Kmh = 2.8f,
                    technicalCargoVolume = 132,
                    technicalChargingTimeAc11Kw0100 = 480.0f,
                    technicalChargingTimeAc22Kw = 240.0f,
                    technicalChargingTimeAc96Kw0100 = 60.0f,
                    technicalChargingTimeDcMaxPower580 = 30.0f,
                    technicalEngineCapacity = 3.0f,
                    technicalCurbWeightDin = 1570,
                    technicalCurbWeightEu = 1645,
                    technicalGrossBatteryCapacity = 93.4f,
                    technicalGrossVehicleWeight = 2080,
                    technicalHeight = 1300,
                    technicalLength = 4519,
                    technicalMaximumChargingPowerDc = 270,
                    technicalMaximumPayload = 435,
                    technicalMaxRoofLoadWithPorscheRoofTransportSystem = 75,
                    technicalNetBatteryCapacity = 83.7f,
                    technicalPowerKw = 283,
                    technicalTopSpeed = 275,
                    technicalTotalPowerKw = 350,
                    technicalVehicleWidthMirrorsExtended = 2144,
                    technicalWidthMirrorsFolded = 1967,
                    colorExterior = "C9X",
                    colorExteriorDescription = "Carrara White Metallic",
                    colorInterior = "BA",
                    colorInteriorDescription = "Black Leather",
                )
            val vehicleIdentifier =
                reportingVehicle.vguid ?: reportingVehicle.vin
                    ?: throw IllegalStateException("Vehicle must have a vguid or vin")
            val messageKey = generateMessageKey(vehicleIdentifier)
            val headers = generateMessageHeaders(vehicleIdentifier, "VEHICLE_UPDATED_EVENT")
            reportingKafkaProducer.send(REPORTING_VEHICLE_TOPIC, messageKey, reportingVehicle, headers)
            logger.info("Successfully published ReportingVehicle for VIN: ${reportingVehicle.vin}")
        } catch (ex: Exception) {
            logger.error("Failed to publish ReportingVehicle event: ${ex.message}", ex)
        }
    }

    private fun publishReportingVehicleTransfer() {
        try {
            val reportingVehicleTransfer =
                ReportingVehicleTransfer(
                    vehicleTransferKey = 12345L,
                    vin = "WP0ZZZ99ZTS392124",
                    deliveryIndex = 1,
                    deliveryDate = OffsetDateTime.parse("2025-01-15T10:00:00+01:00"),
                    returnDate = OffsetDateTime.parse("2025-12-31T17:00:00+01:00"),
                    leasingPrivilege = "EMPLOYEE_LEASING",
                    vehicleUsage = "BUSINESS_USE",
                    usageGroupDescription = "Executive Fleet",
                    mileageAtDelivery = 50,
                    mileageAtReturn = 150,
                    utilizationArea = "GERMANY_SOUTH",
                    maintenanceOrderNumber = "MO-2025-001",
                    usingCostCenter = "CC-1000-SALES",
                    depreciationRelevantCostCenterId = UUID.fromString("550e8400-e29b-41d4-a716-************"),
                    internalOrderNumber = "IO-2025-0001",
                    plannedDeliveryDate = OffsetDateTime.parse("2025-01-15T09:00:00+01:00"),
                    vehicleResponsiblePersonEmployeeNumber = "EMP-12345",
                    vehicleResponsiblePersonDepartment = "Sales & Marketing",
                    leasingArtId = "LA-EXEC-001",
                    leasingArtDescription = "Executive Leasing Standard",
                    leasingArtPool = false,
                )
            val messageKey = generateMessageKey(reportingVehicleTransfer.vehicleTransferKey.toString())
            val headers =
                generateMessageHeaders(reportingVehicleTransfer.vehicleTransferKey, "VEHICLE_TRANSFER_UPDATED_EVENT")
            reportingKafkaProducer.send(
                REPORTING_VEHICLE_TRANSFER_TOPIC,
                messageKey,
                reportingVehicleTransfer,
                headers,
            )
            logger.info("Successfully published ReportingVehicleTransfer for key: ${reportingVehicleTransfer.vehicleTransferKey}")
        } catch (ex: Exception) {
            logger.error("Failed to publish ReportingVehicleTransfer event: ${ex.message}", ex)
        }
    }

    // This is supposed to happen via registration service, but for testing purposes we publish it directly
    private fun publishReportingVehicleRegistration() {
        try {
            val reportingVehicleRegistration =
                ReportingVehicleRegistration(
                    id = 98765L,
                    vin = "WP0ZZZ99ZTS392124",
                    firstRegistrationDate = OffsetDateTime.parse("2025-01-20T14:30:00+01:00"),
                    licencePlate = "S-PO-911E",
                    registrationDate = OffsetDateTime.parse("2025-01-20T14:30:00+01:00"),
                    registrationType = 1,
                    testNumber = 1001234567L,
                    hsn = "0583",
                    tsn = "AHK",
                    sfme = false,
                )
            val messageKey = generateMessageKey(reportingVehicleRegistration.id.toString())
            val headers = generateMessageHeaders(reportingVehicleRegistration.id, "VEHICLE_REGISTRATION_UPDATED_EVENT")
            reportingKafkaProducer.send(
                REPORTING_VEHICLE_REGISTRATION_TOPIC,
                messageKey,
                reportingVehicleRegistration,
                headers,
            )
            logger.info("Successfully published ReportingVehicleRegistration for ID: ${reportingVehicleRegistration.id}")
        } catch (ex: Exception) {
            logger.error("Failed to publish ReportingVehicleRegistration event: ${ex.message}", ex)
        }
    }
}
