package com.fleetmanagement.modules.reporting.service

import com.fleetmanagement.modules.reporting.configuration.ReportingFeatureFlagConfiguration
import org.apache.kafka.clients.producer.ProducerRecord
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.kafka.core.KafkaTemplate
import org.springframework.stereotype.Component

@Component
@ConditionalOnBean(ReportingFeatureFlagConfiguration::class)
class ReportingKafkaProducer<T>(
    @Qualifier("reportingKafkaTemplate")
    private val kafkaTemplate: KafkaTemplate<String, T>,
) {
    private val log = LoggerFactory.getLogger(ReportingKafkaProducer::class.java)

    fun send(
        topic: String,
        key: String,
        payload: T,
        headers: Map<String, String?> = emptyMap(),
    ) {
        val record =
            ProducerRecord(topic, key, payload).apply {
                log.debug("Sending message to Kafka: topic={}, key={} message: {}", topic, key, payload)
                headers.forEach { (key, value) -> this.headers().add(key, value?.toByteArray()) }
            }
        kafkaTemplate.send(record)
    }
}
