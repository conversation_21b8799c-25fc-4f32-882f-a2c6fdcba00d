package com.fleetmanagement.modules.reporting.dto

import com.fleetmanagement.modules.vehicledata.api.domain.FuelType
import java.math.BigDecimal
import java.time.OffsetDateTime

data class ReportingVehicle(
    val vguid: String?,
    val vin: String?,
    val equiId: String?,
    val equipmentNumber: Long?,
    val status: String?,
    // Production
    val productionNumber: String?,
    val productionFactory: String?,
    val productionZp8Date: OffsetDateTime?,
    val productionPlannedEndDate: OffsetDateTime?,
    val productionTechnicalModelYear: Int?,
    val productionGearBoxClass: Char?,
    // Model
    val modelOrderType: String?,
    val modelModelRange: String?,
    val modelManufacturer: String?,
    val modelVehicleType: String?,
    val modelDescription: String?,
    // Price
    val priceVehicleFactoryGrossPriceEUR: BigDecimal?,
    val priceVehicleFactoryNetPriceEUR: BigDecimal?,
    val priceGrossPriceWithExtras: BigDecimal?,
    // Consumption
    val consumptionDriveType: String?,
    val consumptionTypification: String?,
    val consumptionPrimaryFuelType: FuelType?,
    val consumptionSecondaryFuelType: FuelType?,
    // WLTP
    val wltpCo2Combined: Int?,
    val wltpElectricRangeCity: Int?,
    val wltpElectricRange: Int?,
    // Current Mileage
    val currentMileageMileage: Int?,
    val currentMileageReadDate: OffsetDateTime?,
    // Order
    val orderBlockedForSale: Boolean?,
    val orderPrimaryStatus: String?,
    val orderTradingPartnerNumber: String?,
    // Fleet
    val fleetScrapVehicle: Boolean?,
    val fleetResidualValueMarket: Boolean?,
    val fleetRaceCar: Boolean?,
    val fleetClassic: Boolean?,
    // Evaluation
    val evaluationAppraisalNetPrice: BigDecimal?,
    // Sales
    val salesReservedForB2C: Boolean?,
    val salesContractSigned: Boolean?,
    // Technical
    val technicalAmountSeats: Int?,
    val technicalAcceleration0100KmhLaunchControl: Float?,
    val technicalAcceleration0100Kmh: Float?,
    val technicalAcceleration80120Kmh: Float?,
    val technicalCargoVolume: Int?,
    val technicalChargingTimeAc11Kw0100: Float?,
    val technicalChargingTimeAc22Kw: Float?,
    val technicalChargingTimeAc96Kw0100: Float?,
    val technicalChargingTimeDcMaxPower580: Float?,
    val technicalEngineCapacity: Float?,
    val technicalCurbWeightDin: Int?,
    val technicalCurbWeightEu: Int?,
    val technicalGrossBatteryCapacity: Float?,
    val technicalGrossVehicleWeight: Int?,
    val technicalHeight: Int?,
    val technicalLength: Int?,
    val technicalMaximumChargingPowerDc: Int?,
    val technicalMaximumPayload: Int?,
    val technicalMaxRoofLoadWithPorscheRoofTransportSystem: Int?,
    val technicalNetBatteryCapacity: Float?,
    val technicalPowerKw: Int?,
    val technicalTopSpeed: Int?,
    val technicalTotalPowerKw: Int?,
    val technicalVehicleWidthMirrorsExtended: Int?,
    val technicalWidthMirrorsFolded: Int?,
    // Color
    val colorExterior: String?,
    val colorExteriorDescription: String?,
    val colorInterior: String?,
    val colorInteriorDescription: String?,
)
