package com.fleetmanagement.modules.reporting.dto

import org.jetbrains.annotations.NotNull
import java.time.OffsetDateTime

// TODO will be moved to registration service
data class ReportingVehicleRegistration(
    @NotNull
    val id: Long,
    val vin: String?,
    val firstRegistrationDate: OffsetDateTime? = null,
    val licencePlate: String? = null,
    val registrationDate: OffsetDateTime? = null,
    val registrationType: Int? = null,
    val testNumber: Long? = null,
    val hsn: String? = null,
    val tsn: String? = null,
    val sfme: Boolean?,
)
