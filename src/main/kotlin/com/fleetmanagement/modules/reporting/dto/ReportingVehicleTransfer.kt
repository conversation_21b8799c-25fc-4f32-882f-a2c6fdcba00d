package com.fleetmanagement.modules.reporting.dto

import org.jetbrains.annotations.NotNull
import java.time.OffsetDateTime
import java.util.UUID

data class ReportingVehicleTransfer(
    @NotNull
    val vehicleTransferKey: Long,
    val vin: String?,
    val deliveryIndex: Int?,
    val deliveryDate: OffsetDateTime?,
    val returnDate: OffsetDateTime?,
    val leasingPrivilege: String?,
    val vehicleUsage: String?,
    val usageGroupDescription: String?,
    val mileageAtDelivery: Int?,
    val mileageAtReturn: Int?,
    val utilizationArea: String?,
    val maintenanceOrderNumber: String?,
    val usingCostCenter: String?,
    val depreciationRelevantCostCenterId: UUID?,
    val internalOrderNumber: String?,
    val plannedDeliveryDate: OffsetDateTime?,
    // person
    val vehicleResponsiblePersonEmployeeNumber: String?,
    val vehicleResponsiblePersonDepartment: String?,
    // leasing art
    val leasingArtId: String?,
    val leasingArtDescription: String?,
    val leasingArtPool: Boolean?,
)
