/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.reporting.configuration

import org.springframework.boot.context.properties.ConfigurationProperties

@ConfigurationProperties(prefix = "reporting")
class ReportingConfigurationProperties(
    val producer: ReportingProducerProperties,
    val enabled: Boolean,
)

class ReportingProducerProperties(
    val keySerializer: String,
    val valueSerializer: String,
    val schemaRegistryUrl: String,
    val schemaRegistryUser: String,
    val schemaRegistryPassword: String,
    val autoRegisterSchemas: Boolean,
)
