/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.tirechangeappointment.application

import com.fleetmanagement.modules.rela.application.RelaClientException
import com.fleetmanagement.modules.rela.application.port.CancelRelaAppointment
import com.fleetmanagement.modules.rela.application.port.CreateRelaAppointment
import com.fleetmanagement.modules.tirechangeappointment.domain.service.TireChangeEntryUpdateService
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class TireChangeRelaSyncUpdateService(
    private val createRelaAppointment: CreateRelaAppointment,
    private val cancelRelaAppointment: CancelRelaAppointment,
    private val tireChangeEntryFinder: TireChangeEntryFinder,
    private val tireChangeEntryUpdateService: TireChangeEntryUpdateService,
    private val tireChangeRelaRequestMapper: TireChangeRelaRequestMapper,
) {
    /**
     * fetch all the tire change appointments with update flag set to true and update appointment for them in rela
     */
    @Transactional
    fun syncUpdateRelaAppointments() {
        // TODO Change info logs to debug after testing
        log.info("Starting to update TireChange appointments in RELA")
        val appointmentsToBeUpdated = tireChangeEntryFinder.findAllEntriesWithPendingRelaUpdate()
        log.info("Found ${appointmentsToBeUpdated.size} appointments that need to be updated in RELA")

        appointmentsToBeUpdated.forEach { tireChangeEntry ->
            try {
                if (tireChangeEntry.relaOrderNumber.isNullOrEmpty()) {
                    log.error("Failed to update RELA appointment for VIN: ${tireChangeEntry.vin}. RelaOrderNumber is missing")
                    return@forEach
                }
                // FPT1-1300 Currently there is no Update Endpoint in Rela so we have to work around that by first canceling the old appointment and then create a new one
                cancelRelaAppointment.cancelAppointment(tireChangeEntry.relaOrderNumber!!)
                val relaRequest = tireChangeRelaRequestMapper.mapToRelaAppointmentRequest(tireChangeEntry)
                val relaResponse = createRelaAppointment.createAppointment(relaRequest)

                tireChangeEntryUpdateService.updateRelaOrderNumber(tireChangeEntry, relaResponse.orderNumber)
                tireChangeEntryUpdateService.markAsSyncedWithRela(tireChangeEntry)

                log.info("Successfully updated RELA appointment for VIN: ${tireChangeEntry.vin}, Order Number: ${relaResponse.orderNumber}")
            } catch (e: RelaClientException) {
                log.warn("Failed to update RELA appointment for VIN: ${tireChangeEntry.vin}", e)
            }
        }
        log.info("Finished updating of TireChange appointments in RELA")
    }

    companion object {
        private val log = LoggerFactory.getLogger(TireChangeRelaSyncUpdateService::class.java)
    }
}
