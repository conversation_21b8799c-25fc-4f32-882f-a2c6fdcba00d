/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.tirechangeappointment.application.port

fun interface TireChangeAppointmentCancelUseCase {
    fun cancelAppointment(
        appointmentID: String,
        cancellationAppointmentDto: CancelAppointmentRequestDTO,
    )
}

data class CancelAppointmentRequestDTO(
    val cancellationMessage: String,
)
