/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.tirechangeappointment.application

import com.fleetmanagement.modules.rela.application.port.RelaAppointmentRequest
import com.fleetmanagement.modules.tirechangeappointment.domain.TireChangeEntry
import org.springframework.stereotype.Component
import java.time.OffsetDateTime

@Component
class TireChangeRelaRequestMapper {
    fun mapToRelaAppointmentRequest(tireChangeEntry: TireChangeEntry): RelaAppointmentRequest =
        RelaAppointmentRequest(
            appointment = tireChangeEntry.msBookingsAppointmentDate,
            vehicleLicensePlate = tireChangeEntry.licensePlate,
            vehicleVin = tireChangeEntry.vin,
            customerLastName = tireChangeEntry.lastName,
            customerFirstName = tireChangeEntry.firstName,
            // TODO (FPT1-1300): Change to the real values once known and set in tireChangeEntry
            serviceBayNumber = 1,
            serviceTypeId = 1,
            vehicleTypeCode = "AB12",
            vehicleTypeDescription = "Porsche",
            pccbCode = null,
            wheelCode = "CB34",
            cwlCode = null,
            rasCode = null,
            pccbDescription = null,
            orderedByEmail = "<EMAIL>",
            orderDate = OffsetDateTime.now(),
        )
}
