package com.fleetmanagement.modules.tirechangeappointment.application

import com.fasterxml.jackson.annotation.JsonProperty
import java.time.OffsetDateTime

data class AppointmentDto(
    val appointmentId: String,
    val startTime: OffsetDateTime,
    val endTime: OffsetDateTime,
    val customerEmailAddress: String?,
    val customerFirstName: String,
    val customerLastName: String,
    val licensePlate: String,
    val appointmentType: AppointmentType,
    val lastUpdatedDateTime: OffsetDateTime,
)

/**
 * TODO probably needs to be changed as per the response
 */
enum class AppointmentType(
    val description: String,
    val normalizedDescription: String,
) {
    WINTER_TIRE_CHANGE("WinterTireChange", "Winterreifenwechsel"),
    SUMMER_TIRE_CHANGE("SummerTireChange", "Sommerreifenwechsel"),
}

data class AppointmentUpdateRequest(
    @JsonProperty("@odata.type") val odataType: String,
    val customerName: String,
    val customerEmailAddress: String,
    val customers: List<CustomerUpdateDTO>,
)

data class CustomerUpdateDTO(
    @JsonProperty("@odata.type") val odataType: String,
    val name: String,
    val emailAddress: String,
    val timeZone: String,
    val customQuestionAnswers: List<UpdateCustomQuestionAnswerDTO>,
)

data class UpdateCustomQuestionAnswerDTO(
    val questionId: String,
    val question: String,
    val answer: String,
)
