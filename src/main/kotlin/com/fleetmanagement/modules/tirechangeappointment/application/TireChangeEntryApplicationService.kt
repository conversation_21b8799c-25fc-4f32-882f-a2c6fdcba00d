/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.tirechangeappointment.application

import com.fleetmanagement.modules.consigneedatasheet.domain.EmployeeNumber
import com.fleetmanagement.modules.tirechangeappointment.application.port.CancelAppointmentRequestDTO
import com.fleetmanagement.modules.tirechangeappointment.application.port.TireChangeAppointmentCancelUseCase
import com.fleetmanagement.modules.tirechangeappointment.application.port.TireChangeAppointmentReadUseCase
import com.fleetmanagement.modules.tirechangeappointment.application.port.TireChangeAppointmentUpdateUseCase
import com.fleetmanagement.modules.tirechangeappointment.domain.TireChangeEntry
import com.fleetmanagement.modules.tirechangeappointment.domain.service.TireChangeEntryCreateService
import com.fleetmanagement.modules.tirechangeappointment.domain.service.TireChangeEntryUpdateService
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByVehicleId
import com.fleetmanagement.modules.vehicleperson.api.dtos.VehiclePersonDetail
import com.fleetmanagement.modules.vehicleperson.api.readvehicleperson.ReadVehiclePersonDetailByEmployeeNumber
import com.fleetmanagement.modules.vehicleperson.api.readvehicleperson.ReadVehiclePersonException
import com.fleetmanagement.modules.vehicletransfer.application.port.ReadVehicleTransferUseCase
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.time.OffsetDateTime
import java.time.format.DateTimeFormatter

@ConditionalOnBean(TireChangeAppointmentConfig::class)
@Component
class TireChangeEntryApplicationService(
    private val tireChangeEntryFinder: TireChangeEntryFinder,
    private val tireChangeEntryUpdateService: TireChangeEntryUpdateService,
    private val tireChangeCreateService: TireChangeEntryCreateService,
    private val readVehicleTransferUseCase: ReadVehicleTransferUseCase,
    private val readVehiclePersonDetailByEmployeeNumber: ReadVehiclePersonDetailByEmployeeNumber,
    private val tireChangeAppointmentReadUseCase: TireChangeAppointmentReadUseCase,
    private val tireChangeAppointmentUpdateUseCase: TireChangeAppointmentUpdateUseCase,
    private val tireChangeAppointmentCancelUseCase: TireChangeAppointmentCancelUseCase,
    private val readVehicleByVehicleId: ReadVehicleByVehicleId,
) {
    @Transactional
    fun syncTireChangeAppointmentsFromMsBookings() {
        val now = OffsetDateTime.now()
        // TODO: (FPT1-1328) clarify if the startTime and endTime needs to be adjusted, currently fetches all the future appointments,
        //  clarify if the dataset can be reduced to only process appointments that are new/updated
        val listOfAppointments =
            tireChangeAppointmentReadUseCase.readCalendar(now.format(DateTimeFormatter.ISO_DATE_TIME), null)
        listOfAppointments.forEach { appointment ->
            val existingTireChangeEntry =
                tireChangeEntryFinder.getTireChangeEntryByMsBookingsAppointmentId(appointment.appointmentId)
            if (existingTireChangeEntry == null) {
                val newTireChangeEntry = prepareTireChangeEntry(appointment)
                if (newTireChangeEntry != null) {
                    tireChangeCreateService.createTireChangeEntry(newTireChangeEntry)
                }
            } else if (existingTireChangeEntry.msBookingsAppointmentDate != appointment.startTime) {
                tireChangeEntryUpdateService.updateAppointmentDate(existingTireChangeEntry, appointment.startTime)
            }
        }
    }

    private fun prepareTireChangeEntry(appointment: AppointmentDto): TireChangeEntry? {
        val listOfVehicleTransfers =
            readVehicleTransferUseCase.findActiveVehicleTransferByLicensePlate(appointment.licensePlate)
        if (listOfVehicleTransfers.isEmpty() || listOfVehicleTransfers.size > 1) {
            log.warn(
                "TireChangeAppointment: Could not find vehicle transfer for license plate ${appointment.licensePlate}. Cancelling MsBookings Appointment ${appointment.appointmentId}",
            )
            // TODO: (FPT1-1328) cancellation message to be clarified
            cancelMsBookingsAppointment(appointment.appointmentId, "Invalid license plate")
            return null
        }
        val vehicleTransfer = listOfVehicleTransfers.first()
        val employeeNumber = vehicleTransfer.vehicleResponsiblePerson

        val vehiclePersonDetail = getVehicleResponsiblePersonDetails(employeeNumber)
        if (vehiclePersonDetail == null) {
            log.warn(
                "TireChangeAppointment: Could not find vehicle responsible person details. Cancelling MsBookings Appointment ${appointment.appointmentId}",
            )
            // TODO: (FPT1-1328) cancellation message to be clarified
            cancelMsBookingsAppointment(appointment.appointmentId, "Missing vehicle responsible person details")
            return null
        }

        val vehicle = readVehicleByVehicleId.readVehicleById(vehicleTransfer.vehicleId)
        if (vehicle?.vin == null) {
            // TODO: (FPT1-1328) clarify what needs to be done if the vehicle/vin is null
            return null
        }


        if (!vehiclePersonDetail.companyEmail.equals(appointment.customerEmailAddress, ignoreCase = true)) {
            // TODO: (FPT1-1328) clarify updateRequest payload, it should be possible to pass firstName and lastName as different properties
            tireChangeAppointmentUpdateUseCase.updateAppointment(
                appointment.appointmentId,
                AppointmentUpdateRequest(
                    odataType = "",
                    customerName = appointment.customerFirstName,
                    customerEmailAddress = vehiclePersonDetail.companyEmail,
                    customers = emptyList(),
                ),
            )
            return TireChangeEntry(
                msBookingsAppointmentId = appointment.appointmentId,
                msBookingsAppointmentDate = appointment.startTime,
                licensePlate = appointment.licensePlate,
                firstName = appointment.customerFirstName,
                lastName = appointment.customerLastName,
                vin = vehicle.vin,
                email = vehiclePersonDetail.companyEmail,
            )
        }

        // TODO: clarify if startTime is sufficient
        return TireChangeEntry(
            msBookingsAppointmentId = appointment.appointmentId,
            msBookingsAppointmentDate = appointment.startTime,
            licensePlate = appointment.licensePlate,
            firstName = appointment.customerFirstName,
            lastName = appointment.customerLastName,
            vin = vehicle.vin,
            email = appointment.customerEmailAddress ?: "",
        )
    }

    private fun getVehicleResponsiblePersonDetails(employeeNumber: EmployeeNumber?): VehiclePersonDetail? {
        val employee = employeeNumber?.value
        return if (!employee.isNullOrEmpty()) {
            try {
                readVehiclePersonDetailByEmployeeNumber.readVehiclePersonDetailByEmployeeNumber(employee)
            } catch (e: ReadVehiclePersonException) {
                log.warn("TireChangeAppointment: Could not find vehicle person from user service for: $employee ${e.message}")
                null
            }
        } else {
            null
        }
    }

    private fun cancelMsBookingsAppointment(
        appointmentId: String,
        cancellationMessage: String,
    ) {
        tireChangeAppointmentCancelUseCase.cancelAppointment(
            appointmentId,
            CancelAppointmentRequestDTO(cancellationMessage),
        )
    }

    companion object {
        private val log = LoggerFactory.getLogger(TireChangeEntryApplicationService::class.java)
    }
}
