/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.tirechangeappointment.application.port

import com.fleetmanagement.modules.tirechangeappointment.application.AppointmentUpdateRequest

fun interface TireChangeAppointmentUpdateUseCase {
    fun updateAppointment(
        appointmentId: String,
        updateRequest: AppointmentUpdateRequest,
    )
}
