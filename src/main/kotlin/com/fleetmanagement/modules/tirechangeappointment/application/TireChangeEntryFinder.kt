/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.tirechangeappointment.application

import com.fleetmanagement.modules.tirechangeappointment.domain.TireChangeEntry
import com.fleetmanagement.modules.tirechangeappointment.domain.TireChangeEntryId
import com.fleetmanagement.modules.tirechangeappointment.domain.TireChangeEntryRepository
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
@Transactional(readOnly = true)
class TireChangeEntryFinder(
    private val tireChangeEntryRepository: TireChangeEntryRepository,
) {
    fun getTireChangeEntryById(tireChangeEntryId: TireChangeEntryId): TireChangeEntry? =
        tireChangeEntryRepository.findById(tireChangeEntryId)

    fun getTireChangeEntryByMsBookingsAppointmentId(msBookingsAppointmentId: String): TireChangeEntry? =
        tireChangeEntryRepository.findByMsBookingsAppointmentId(msBookingsAppointmentId)

    fun findAllEntriesWithNullRelaOrderNumber(): List<TireChangeEntry> = tireChangeEntryRepository.findAllByRelaOrderNumberIsNull()

    fun findAllEntriesWithPendingRelaUpdate(): List<TireChangeEntry> = tireChangeEntryRepository.findAllByShouldUpdateRelaIsTrue()
}
