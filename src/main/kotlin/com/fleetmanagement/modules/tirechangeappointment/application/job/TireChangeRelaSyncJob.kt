/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.tirechangeappointment.application.job

import com.fleetmanagement.modules.tirechangeappointment.application.TireChangeRelaSyncCreateService
import com.fleetmanagement.modules.tirechangeappointment.application.TireChangeRelaSyncUpdateService
import org.quartz.*
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.stereotype.Component
import java.time.ZoneOffset
import java.util.*

@Component
@DisallowConcurrentExecution
class TireChangeRelaSyncJob(
    private val tireChangeRelaSyncCreateService: TireChangeRelaSyncCreateService,
    private val tireChangeRelaSyncUpdateService: TireChangeRelaSyncUpdateService,
) : Job {
    companion object {
        private val log = LoggerFactory.getLogger(TireChangeRelaSyncJob::class.java)
    }

    override fun execute(context: JobExecutionContext?) {
        log.info("Starting scheduled TireChangeRelaSyncJob for creation.")
        tireChangeRelaSyncCreateService.syncCreateRelaAppointments()
        log.info("Finished scheduled TireChangeRelaSyncJob for creation.")
        log.info("Starting scheduled TireChangeRelaSyncJob for update.")
        tireChangeRelaSyncUpdateService.syncUpdateRelaAppointments()
        log.info("Finished scheduled TireChangeRelaSyncJob for update.")
    }

    @Configuration
    class TireChangeRelaSyncJobConfiguration {
        @Bean("tireChangeRelaSyncJobDetail")
        fun tireChangeRelaSyncJobDetail(): JobDetail =
            JobBuilder
                .newJob()
                .ofType(TireChangeRelaSyncJob::class.java)
                .withIdentity("TireChangeRelaSyncJob")
                .withDescription("Sync tire change appointments to RELA system")
                .storeDurably()
                .build()

        @Bean("tireChangeRelaSyncJobTrigger")
        fun tireChangeRelaSyncJobTrigger(
            tireChangeRelaSyncJobDetail: JobDetail,
            @Value("\${tire-change.scheduler.rela-sync-cron}") cron: String,
        ): Trigger =
            TriggerBuilder
                .newTrigger()
                .forJob(tireChangeRelaSyncJobDetail)
                .withIdentity("TireChangeRelaSyncJobTrigger")
                .withDescription("Trigger for tire change RELA sync job")
                .withSchedule(
                    CronScheduleBuilder.cronSchedule(cron).inTimeZone(TimeZone.getTimeZone(ZoneOffset.UTC)),
                ).build()
    }
}
