/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.tirechangeappointment.application

import com.fleetmanagement.modules.rela.application.RelaClientException
import com.fleetmanagement.modules.rela.application.port.CreateRelaAppointment
import com.fleetmanagement.modules.tirechangeappointment.domain.service.TireChangeEntryUpdateService
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class TireChangeRelaSyncCreateService(
    private val createRelaAppointment: CreateRelaAppointment,
    private val tireChangeEntryFinder: TireChangeEntryFinder,
    private val tireChangeEntryUpdateService: TireChangeEntryUpdateService,
    private val tireChangeRelaRequestMapper: TireChangeRelaRequestMapper,
) {
    /**
     * fetch all the tire change appointments with empty relaOrderNumber and create appointment for them in rela
     */
    @Transactional
    fun syncCreateRelaAppointments() {
        // TODO Change info logs to debug after testing
        log.info("Starting creation of missing TireChange appointments in RELA")
        val appointmentsWithoutRelaOrderNumber = tireChangeEntryFinder.findAllEntriesWithNullRelaOrderNumber()
        log.info("Found ${appointmentsWithoutRelaOrderNumber.size} appointments without RELA order number")

        appointmentsWithoutRelaOrderNumber.forEach { tireChangeEntry ->
            try {
                val relaRequest = tireChangeRelaRequestMapper.mapToRelaAppointmentRequest(tireChangeEntry)
                val relaResponse = createRelaAppointment.createAppointment(relaRequest)

                tireChangeEntryUpdateService.updateRelaOrderNumber(tireChangeEntry, relaResponse.orderNumber)
                tireChangeEntryUpdateService.markAsSyncedWithRela(tireChangeEntry)

                log.info("Successfully created RELA appointment for VIN: ${tireChangeEntry.vin}, Order Number: ${relaResponse.orderNumber}")
            } catch (e: RelaClientException) {
                log.warn("Failed to create RELA appointment for VIN: ${tireChangeEntry.vin}", e)
            }
        }
        log.info("Finished creation of missing TireChange appointments in RELA")
    }

    companion object {
        private val log = LoggerFactory.getLogger(TireChangeRelaSyncCreateService::class.java)
    }
}
