/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.tirechangeappointment.domain

import jakarta.persistence.*
import org.hibernate.annotations.CreationTimestamp
import org.hibernate.annotations.UpdateTimestamp
import org.springframework.data.annotation.CreatedBy
import org.springframework.data.annotation.LastModifiedBy
import java.io.Serializable
import java.time.OffsetDateTime
import java.util.*

// TODO(FPT1-1327) add vehicle details including options once its known what we need to save
@Entity
@Table(name = "tire_change_entry", schema = "tirechangeappointment")
class TireChangeEntry(
    @Column(name = "msbookings_appointment_id") val msBookingsAppointmentId: String,
    msBookingsAppointmentDate: OffsetDateTime,
    @Column(name = "license_plate") val licensePlate: String,
    @Column(name = "first_name") val firstName: String,
    @Column(name = "last_name") val lastName: String,
    @Column(name = "email") val email: String,
    @Column(name = "vin") val vin: String,
    @Column(name = "order_type") val orderType: String,
    @Column(name = "model_description") val modelDescription: String,
    @Column(name = "pccb_code") val pccbCode: String? = null,
    @Column(name = "wheel_code") val wheelCode: String,
    @Column(name = "cwl_code") val cwlCode: String? = null,
    @Column(name = "ras_code") val rasCode: String? = null,
    @Column(name = "pccb_description") val pccbDescription: String,
) {
    @EmbeddedId
    @GeneratedValue
    @AttributeOverride(name = "value", column = Column(name = "id"))
    val id: TireChangeEntryId = TireChangeEntryId()

    @Column(name = "msbookings_appointment_date")
    var msBookingsAppointmentDate: OffsetDateTime = msBookingsAppointmentDate
        private set

    @Column(name = "should_update_rela")
    var shouldUpdateRela: Boolean = true
        private set

    @Column(name = "rela_order_number")
    var relaOrderNumber: String? = null
        private set

    @CreatedBy
    @Column(name = "created_by")
    var createdBy: String = ""

    @CreationTimestamp
    @Column(name = "created_date")
    var created: OffsetDateTime = OffsetDateTime.now()

    @LastModifiedBy
    @Column(name = "last_modified_by")
    var lastModifiedBy: String = ""

    @UpdateTimestamp
    @Column(name = "last_modified_date")
    var lastModified: OffsetDateTime = OffsetDateTime.now()

    internal fun updateAppointmentDate(msBookingsAppointmentDate: OffsetDateTime) {
        this.msBookingsAppointmentDate = msBookingsAppointmentDate
        this.shouldUpdateRela = true
    }

    internal fun updateRelaOrderNumber(orderNumber: String) {
        this.relaOrderNumber = orderNumber
    }

    internal fun markAsSyncedWithRela() {
        this.shouldUpdateRela = false
    }
}

@Embeddable
data class TireChangeEntryId(
    @Basic val value: UUID = UUID.randomUUID(),
) : Serializable
