package com.fleetmanagement.modules.tirechangeappointment.domain.service

import com.fleetmanagement.modules.tirechangeappointment.domain.TireChangeEntry
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional
import java.time.OffsetDateTime

@Service
@Transactional(propagation = Propagation.MANDATORY)
class TireChangeEntryUpdateService {
    fun updateAppointmentDate(
        tireChangeEntry: TireChangeEntry,
        msBookingsAppointmentDate: OffsetDateTime,
    ) {
        tireChangeEntry.updateAppointmentDate(msBookingsAppointmentDate)
    }

    fun updateRelaOrderNumber(
        tireChangeEntry: TireChangeEntry,
        relaOrderNumber: String,
    ) {
        tireChangeEntry.updateRelaOrderNumber(relaOrderNumber)
    }

    fun markAsSyncedWithRela(tireChangeEntry: TireChangeEntry) {
        tireChangeEntry.markAsSyncedWithRela()
    }
}
