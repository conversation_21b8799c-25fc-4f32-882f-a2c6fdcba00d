package com.fleetmanagement.modules.tirechangeappointment.domain.service

import com.fleetmanagement.modules.tirechangeappointment.domain.TireChangeEntry
import com.fleetmanagement.modules.tirechangeappointment.domain.TireChangeEntryRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional

@Service
@Transactional(propagation = Propagation.MANDATORY)
class TireChangeEntryCreateService(
    private val tireChangeEntryRepository: TireChangeEntryRepository,
) {
    fun createTireChangeEntry(tireChangeEntry: TireChangeEntry) {
        tireChangeEntryRepository.save(tireChangeEntry)
    }
}
