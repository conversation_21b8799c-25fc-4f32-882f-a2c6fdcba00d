/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.tirechangeappointment.domain

import org.springframework.data.jpa.repository.JpaSpecificationExecutor
import org.springframework.data.repository.Repository

interface TireChangeEntryRepository :
    Repository<TireChangeEntry, TireChangeEntryId>,
    JpaSpecificationExecutor<TireChangeEntry> {
    fun save(tireChangeEntry: TireChangeEntry): TireChangeEntry

    fun findById(tireChangeEntryId: TireChangeEntryId): TireChangeEntry?

    fun findByMsBookingsAppointmentId(msBookingsAppointmentId: String): TireChangeEntry?

    fun findAllByRelaOrderNumberIsNull(): List<TireChangeEntry>

    fun findAllByShouldUpdateRelaIsTrue(): List<TireChangeEntry>
}
