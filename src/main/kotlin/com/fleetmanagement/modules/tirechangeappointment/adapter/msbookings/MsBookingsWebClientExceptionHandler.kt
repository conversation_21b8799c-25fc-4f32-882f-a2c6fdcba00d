/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.tirechangeappointment.adapter.msbookings

import org.slf4j.LoggerFactory
import org.springframework.security.oauth2.client.ClientAuthorizationException
import org.springframework.security.oauth2.core.OAuth2AuthorizationException
import org.springframework.stereotype.Component
import org.springframework.web.reactive.function.client.ClientRequest
import org.springframework.web.reactive.function.client.ClientResponse
import org.springframework.web.reactive.function.client.ExchangeFunction
import org.springframework.web.reactive.function.client.WebClientResponseException
import reactor.core.publisher.Mono
import java.net.ConnectException

@Component
class MsBookingsWebClientExceptionHandler {
    fun clientErrorRequestProcessor(
        request: ClientRequest,
        next: ExchangeFunction,
    ): Mono<ClientResponse> =
        next.exchange(request).onErrorMap {
            when (it.cause) {
                is ConnectException -> {
                    log.warn("Error during request. Target not reachable.", it.cause)
                    MsBookingsClientException("Error during request. Target not reachable.", it.cause)
                }

                is ClientAuthorizationException -> {
                    log.error("Error during authorization.", it.cause)
                    MsBookingsClientException("Error during authorization.", it.cause)
                }

                is OAuth2AuthorizationException -> {
                    log.error("Error during authorization.${it.message}", it.cause)
                    MsBookingsClientException("Error during authorization.${it.message}", it.cause)
                }

                is WebClientResponseException -> {
                    log.warn("Error during service request. ${it.message}", it.cause)
                    MsBookingsClientException("Error during service request. ${it.message}", it.cause)
                }

                else -> it
            }
        }

    fun clientErrorResponseProcessor(clientResponse: ClientResponse): Mono<ClientResponse> {
        val statusCode = clientResponse.statusCode()
        return when {
            statusCode.isError -> {
                clientResponse.bodyToMono(String::class.java).flatMap {
                    log.warn(
                        "Error during msbookings request. Status Code: $statusCode, message: $it",
                    )
                    Mono.error(MsBookingsClientException(message = it, cause = null))
                }
            }

            else -> Mono.just(clientResponse)
        }
    }

    companion object {
        private val log = LoggerFactory.getLogger(MsBookingsWebClientExceptionHandler::class.java)
    }
}
