@file:Suppress("ktlint:standard:filename")
/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.tirechangeappointment.adapter.msbookings

import com.fleetmanagement.modules.tirechangeappointment.application.AppointmentUpdateRequest
import com.fleetmanagement.modules.tirechangeappointment.application.port.CancelAppointmentRequestDTO
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.service.annotation.GetExchange
import org.springframework.web.service.annotation.PatchExchange
import org.springframework.web.service.annotation.PostExchange

/**
 * seems like graph version 1 of appointments is very unbaked
 * the api do not support the $filter or the $top(paginate)
 */
interface MsBookingsWebClient {
    @GetExchange(
        url = "/solutions/bookingBusinesses/{bookings_id}/calendarView",
        accept = ["application/json"],
    )
    fun getCalendarView(
        @PathVariable("bookings_id") bookingsId: String,
        @RequestParam(value = "start", required = false) start: String?,
        @RequestParam(value = "end", required = false) end: String?,
    ): MsBookingsResponse

    // TODO: start using it once the filter works
    @GetExchange(
        url = "/solutions/bookingBusinesses/{bookings_id}/appointments",
        accept = ["application/json"],
    )
    fun getFilteredAppointments(
        @PathVariable("bookings_id") bookingsId: String,
        @RequestParam(value = "\$filter", required = true) filter: String,
    ): MsBookingsResponse

    @PatchExchange(
        url = "/solutions/bookingBusinesses/{bookings_id}/appointments/{appointment_id}",
        accept = ["application/json"],
        contentType = "application/json",
    )
    fun updateAppointment(
        @PathVariable("bookings_id") bookingsId: String,
        @PathVariable("appointment_id") appointmentId: String,
        @RequestBody updateRequest: AppointmentUpdateRequest,
    ): ResponseEntity<Void>

    @PostExchange(
        url = "/solutions/bookingBusinesses/{bookings_id}/appointments/{appointment_id}/cancel",
        accept = ["application/json"],
        contentType = "application/json",
    )
    fun cancelAppointment(
        @PathVariable("bookings_id") bookingsId: String,
        @PathVariable("appointment_id") appointmentId: String,
        @RequestBody cancelAppointmentRequestDTO: CancelAppointmentRequestDTO,
    ): ResponseEntity<Void>
}
