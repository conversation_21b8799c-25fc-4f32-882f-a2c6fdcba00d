/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.tirechangeappointment.adapter.msbookings.config

import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.databind.ObjectMapper
import com.fleetmanagement.modules.tirechangeappointment.adapter.msbookings.MsBookingsWebClient
import com.fleetmanagement.modules.tirechangeappointment.adapter.msbookings.MsBookingsWebClientExceptionHandler
import com.fleetmanagement.modules.tirechangeappointment.application.TireChangeAppointmentConfig
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.http.MediaType
import org.springframework.web.reactive.function.client.ClientRequest
import org.springframework.web.reactive.function.client.ExchangeFilterFunction
import org.springframework.web.reactive.function.client.WebClient
import org.springframework.web.reactive.function.client.support.WebClientAdapter
import org.springframework.web.service.invoker.HttpServiceProxyFactory

@ConditionalOnBean(TireChangeAppointmentConfig::class)
@Configuration
class MsBookingsWebClientConfig(
    private val msBookingsSecurityProperties: MsBookingsSecurityProperties,
    private val objectMapper: ObjectMapper,
) {
    @Bean
    @Qualifier("tireChangeAppointmentMsBookings")
    fun tireChangeAppointmentMsBookingsServiceProxyFactory(
        @Value("\${tire-change-appointment.ms-bookings.base-url}") baseUrl: String,
        msBookingsWebClientExceptionHandler: MsBookingsWebClientExceptionHandler,
    ): HttpServiceProxyFactory {
        val webClient =
            WebClient
                .builder()
                .baseUrl(baseUrl)
                .filter(ExchangeFilterFunction.ofResponseProcessor(msBookingsWebClientExceptionHandler::clientErrorResponseProcessor))
                .filter(msBookingsWebClientExceptionHandler::clientErrorRequestProcessor)
                .filter { request, next ->
                    val modifiedRequest =
                        ClientRequest
                            .from(request) // Create a modified request
                            .header("Authorization", "Bearer ${getOAuth2Token()}") // Add the Authorization header
                            .build()
                    next.exchange(modifiedRequest) // Return the modified request
                }.build()

        val adapter = WebClientAdapter.create(webClient)
        return HttpServiceProxyFactory.builderFor(adapter).build()
    }

    // use custom as the password flow is deprecated
    // https://docs.spring.io/spring-security/reference/reactive/oauth2/client/authorization-grants.html
    private fun getOAuth2Token(): String {
        val tokenResponse =
            WebClient
                .create()
                .post()
                .uri(msBookingsSecurityProperties.tokenUri)
                .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                .bodyValue(
                    "client_id=${msBookingsSecurityProperties.clientId}&" +
                        "client_secret=${msBookingsSecurityProperties.clientSecret}&" +
                        "grant_type=password&" +
                        "scope=${msBookingsSecurityProperties.scope}&" +
                        "username=${msBookingsSecurityProperties.username}&" +
                        "password=${msBookingsSecurityProperties.password}",
                ).retrieve()
                .bodyToMono(String::class.java)
                .block()

        val token = extractAccessToken(tokenResponse)
        return token
    }

    private fun extractAccessToken(response: String?): String {
        val tokenResponse = objectMapper.readValue(response, AccessTokenResponse::class.java)
        return tokenResponse.accessToken
    }

    @Bean
    @Qualifier("tireChangeAppointmentMsBookings")
    fun getTireChangeAppointmentMsBookingsClient(
        @Qualifier("tireChangeAppointmentMsBookings") msBookingsServiceProxyFactory: HttpServiceProxyFactory,
    ): MsBookingsWebClient = msBookingsServiceProxyFactory.createClient(MsBookingsWebClient::class.java)
}

@ConfigurationProperties("tire-change-appointment.ms-bookings.security")
data class MsBookingsSecurityProperties(
    val username: String,
    val password: String,
    val clientId: String,
    val clientSecret: String,
    val authorizationGrantType: String,
    val scope: String,
    val tokenUri: String,
)

data class AccessTokenResponse(
    @JsonProperty("access_token") val accessToken: String,
)
