package com.fleetmanagement.modules.tirechangeappointment.adapter.msbookings

import com.fleetmanagement.modules.tirechangeappointment.application.AppointmentDto
import com.fleetmanagement.modules.tirechangeappointment.application.AppointmentType
import com.fleetmanagement.modules.tirechangeappointment.application.AppointmentUpdateRequest
import com.fleetmanagement.modules.tirechangeappointment.application.TireChangeAppointmentConfig
import com.fleetmanagement.modules.tirechangeappointment.application.port.CancelAppointmentRequestDTO
import com.fleetmanagement.modules.tirechangeappointment.application.port.TireChangeAppointmentCancelUseCase
import com.fleetmanagement.modules.tirechangeappointment.application.port.TireChangeAppointmentReadUseCase
import com.fleetmanagement.modules.tirechangeappointment.application.port.TireChangeAppointmentUpdateUseCase
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.stereotype.Component
import java.time.Instant
import java.time.LocalDateTime
import java.time.OffsetDateTime
import java.time.ZoneId
import java.time.ZoneOffset

@ConditionalOnBean(TireChangeAppointmentConfig::class)
@Component
class MsBookingsAdapter(
    @Value("\${tire-change-appointment.ms-bookings.bookings-id}") private val bookingsId: String,
    private val msBookingsWebClient: MsBookingsWebClient,
) : TireChangeAppointmentReadUseCase,
    TireChangeAppointmentCancelUseCase,
    TireChangeAppointmentUpdateUseCase {
    override fun readCalendar(
        startTime: String?,
        endTime: String?,
    ): List<AppointmentDto> {
        val msBookingsResponse =
            msBookingsWebClient.getCalendarView(
                bookingsId = bookingsId,
                start = startTime,
                endTime,
            )
        return msBookingsResponse.value.mapNotNull { toAppointmentDto(it) }
    }

    override fun cancelAppointment(
        appointmentID: String,
        cancellationAppointmentDto: CancelAppointmentRequestDTO,
    ) {
        try {
            msBookingsWebClient.cancelAppointment(
                bookingsId = bookingsId,
                appointmentId = appointmentID,
                cancelAppointmentRequestDTO = cancellationAppointmentDto,
            )
        } catch (e: MsBookingsClientException) {
            log.warn("Error cancelling ms bookings $bookingsId appointment $appointmentID , ${e.message}")
        }
    }

    override fun updateAppointment(
        appointmentId: String,
        updateRequest: AppointmentUpdateRequest,
    ) {
        try {
            msBookingsWebClient.updateAppointment(
                bookingsId = bookingsId,
                appointmentId = appointmentId,
                updateRequest = updateRequest,
            )
        } catch (e: MsBookingsClientException) {
            log.warn("Error updating ms bookings $bookingsId appointment $appointmentId , ${e.message}")
        }
    }

    /**
     * TODO: (FPT1-1328) AppointmentDto need to be changed once we know the expected response
     * licensePlate, firstName and lastName should be mandatory as per current logic
     */
    private fun toAppointmentDto(msBookingsDto: MsBookingsDTO): AppointmentDto? {
        try {
            val appointmentType = findAppointmentType(msBookingsDto.serviceName)
            return AppointmentDto(
                appointmentId = msBookingsDto.id,
                startTime = convertToUtcOffsetDateTime(msBookingsDto.startDateTime),
                endTime = convertToUtcOffsetDateTime(msBookingsDto.endDateTime),
                customerEmailAddress = msBookingsDto.customerEmailAddress,
                customerFirstName = msBookingsDto.customerName ?: "",
                customerLastName = msBookingsDto.customerName ?: "",
                appointmentType = appointmentType,
                licensePlate = "",
                lastUpdatedDateTime = convertFromStringToTime(msBookingsDto.lastUpdatedDateTime).atOffset(ZoneOffset.UTC),
            )
        } catch (e: IllegalServiceNameException) {
            log.warn("seems like the serviceName: ${msBookingsDto.serviceName} is illegal. appointment id ${msBookingsDto.id}")
            return null
        }
    }

    /**
     * determines the type of tire change appointment
     * TODO: (FPT1-1328) probably needs to be updated once we know the serviceName values received from msBookings
     */
    private fun findAppointmentType(serviceName: String): AppointmentType {
        // TODO: normalize may not be needed
        val normalizedServiceName = normalize(serviceName)

        return when {
            normalizedServiceName.contains(
                AppointmentType.WINTER_TIRE_CHANGE.normalizedDescription,
                ignoreCase = true,
            ) -> AppointmentType.WINTER_TIRE_CHANGE

            normalizedServiceName.contains(
                AppointmentType.SUMMER_TIRE_CHANGE.normalizedDescription,
                ignoreCase = true,
            ) -> AppointmentType.SUMMER_TIRE_CHANGE

            else -> throw IllegalServiceNameException(serviceName)
        }
    }

    private fun convertToUtcOffsetDateTime(dateTime: DateTimeDTO): OffsetDateTime {
        val zoneId = ZoneId.of(dateTime.timeZone)

        val localDateTime = convertFromStringToTime(dateTime.dateTime)

        val zonedDateTime = localDateTime.atZone(zoneId)

        return zonedDateTime.withZoneSameInstant(ZoneOffset.UTC).toOffsetDateTime()
    }

    private fun convertFromStringToTime(timeAsString: String): LocalDateTime {
        val instant = Instant.parse(timeAsString)
        val dateTime = LocalDateTime.ofInstant(instant, ZoneOffset.UTC)
        return dateTime
    }

    // TODO: if we need to normalize the service name, check if all cases are covered or better move to a common place.
    private fun normalize(deutschString: String): String =
        deutschString
            .replace("ü", "ue")
            .lowercase()
            .trim()

    companion object {
        private val log = LoggerFactory.getLogger(MsBookingsAdapter::class.java)
    }
}
