package com.fleetmanagement.modules.rela.adapter.out.rest

import com.fleetmanagement.modules.rela.adapter.out.rest.model.CreateAppointmentRequestDto
import com.fleetmanagement.modules.rela.application.RelaAppointmentCancellationException
import com.fleetmanagement.modules.rela.application.RelaAppointmentCreationException
import com.fleetmanagement.modules.rela.application.RelaClientException
import com.fleetmanagement.modules.rela.application.port.CancelRelaAppointment
import com.fleetmanagement.modules.rela.application.port.CreateRelaAppointment
import com.fleetmanagement.modules.rela.application.port.RelaAppointmentRequest
import com.fleetmanagement.modules.rela.application.port.RelaAppointmentResponse
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.stereotype.Component

@Component
@ConditionalOnBean(RelaConfiguration::class)
class RelaClient(
    private val relaWebClient: RelaWebClient,
) : CreateRelaAppointment,
    CancelRelaAppointment {
    companion object {
        private val log = LoggerFactory.getLogger(RelaClient::class.java)
    }

    override fun createAppointment(request: RelaAppointmentRequest): RelaAppointmentResponse {
        log.info("Creating RELA appointment for VIN: ${request.vehicleVin}")

        try {
            val createRequest = mapToCreateAppointmentDto(request)
            val response = relaWebClient.createAppointment(createRequest)

            log.info("Successfully created RELA appointment with order number: ${response.auftragsnummer}")
            return RelaAppointmentResponse(orderNumber = response.auftragsnummer)
        } catch (e: RelaClientException) {
            log.warn("Failed to create RELA appointment for VIN: ${request.vehicleVin}", e)
            throw RelaAppointmentCreationException(
                "Failed to create RELA appointment for VIN: ${request.vehicleVin}",
                e,
            )
        }
    }

    override fun cancelAppointment(orderNumber: String) {
        log.info("Cancelling RELA appointment with order number: $orderNumber")

        try {
            relaWebClient.cancelAppointment(orderNumber)

            log.info("Successfully cancelled RELA appointment with order number: $orderNumber")
        } catch (e: RelaClientException) {
            log.warn("Failed to cancel RELA appointment with order number: $orderNumber", e)
            throw RelaAppointmentCancellationException(
                "Failed to cancel RELA appointment with order number: $orderNumber",
                e,
            )
        }
    }

    private fun mapToCreateAppointmentDto(request: RelaAppointmentRequest): CreateAppointmentRequestDto =
        CreateAppointmentRequestDto(
            werkstatttermin = request.appointment,
            kfZKennzeichen = request.vehicleLicensePlate,
            FIN = request.vehicleVin,
            name = request.customerLastName,
            vorname = request.customerFirstName,
            buehne = request.serviceBayNumber,
            dienstleistung = request.serviceTypeId,
            typCode = request.vehicleTypeCode,
            typBeschreibung = request.vehicleTypeDescription,
            pcCBCode = request.pccbCode,
            wheelCode = request.wheelCode,
            cwLCode = request.cwlCode,
            raSCode = request.rasCode,
            pcCBBeschreibung = request.pccbDescription,
            bestelltVon = request.orderedByEmail,
            bestelltAm = request.orderDate,
        )
}
