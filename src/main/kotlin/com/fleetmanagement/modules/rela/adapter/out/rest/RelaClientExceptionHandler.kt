package com.fleetmanagement.modules.rela.adapter.out.rest

import com.fleetmanagement.modules.rela.application.RelaClientException
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Component
import org.springframework.web.reactive.function.client.ClientRequest
import org.springframework.web.reactive.function.client.ClientResponse
import org.springframework.web.reactive.function.client.ExchangeFunction
import reactor.core.publisher.Mono
import java.net.ConnectException

/**
 * Exception handler for RELA WebClient operations.
 */
@Component
class RelaClientExceptionHandler {
    companion object {
        private val log = LoggerFactory.getLogger(RelaClientExceptionHandler::class.java)
    }

    fun clientErrorRequestProcessor(
        request: ClientRequest,
        next: ExchangeFunction,
    ): Mono<ClientResponse> =
        next.exchange(request).onErrorMap { throwable ->
            when (throwable.cause) {
                is ConnectException -> {
                    log.debug("Error during RELA request. Target not reachable.", throwable.cause)
                    RelaClientException("RELA service not reachable", throwable.cause)
                }
                else -> {
                    log.debug("Unexpected error during RELA request", throwable)
                    RelaClientException("Unexpected error during RELA request", throwable)
                }
            }
        }

    fun clientErrorResponseProcessor(response: ClientResponse): Mono<ClientResponse> =
        if (response.statusCode().isError) {
            response
                .bodyToMono(String::class.java)
                .defaultIfEmpty("No response body")
                .flatMap { body ->
                    val exception =
                        when (response.statusCode()) {
                            HttpStatus.BAD_REQUEST -> {
                                log.debug("RELA API returned 400 Bad Request: $body")
                                RelaClientException("Invalid request to RELA API: $body")
                            }
                            HttpStatus.UNAUTHORIZED -> {
                                log.error("RELA API returned 401 Unauthorized: $body")
                                RelaClientException("RELA authentication failed: $body")
                            }
                            HttpStatus.FORBIDDEN -> {
                                log.error("RELA API returned 403 Forbidden: $body")
                                RelaClientException("RELA authorization failed: $body")
                            }
                            HttpStatus.NOT_ACCEPTABLE -> {
                                log.debug("RELA API returned 406 Not Acceptable: $body")
                                RelaClientException("RELA API rejected request (406): $body")
                            }
                            HttpStatus.INTERNAL_SERVER_ERROR -> {
                                log.debug("RELA API returned 500 Internal Server Error: $body")
                                RelaClientException("RELA service internal error: $body")
                            }
                            HttpStatus.SERVICE_UNAVAILABLE -> {
                                log.debug("RELA API returned 503 Service Unavailable: $body")
                                RelaClientException("RELA service unavailable: $body")
                            }
                            else -> {
                                log.debug("RELA API returned error {}: {}", response.statusCode(), body)
                                RelaClientException("RELA API error ${response.statusCode()}: $body")
                            }
                        }
                    Mono.error<ClientResponse>(exception)
                }
        } else {
            Mono.just(response)
        }
}
