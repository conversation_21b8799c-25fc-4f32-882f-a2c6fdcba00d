package com.fleetmanagement.modules.rela.application

/**
 * Base exception for RELA client operations.
 */
open class RelaClientException(
    override val message: String?,
    override val cause: Throwable? = null,
) : RuntimeException(message, cause)

/**
 * Exception thrown when appointment creation fails.
 */
class RelaAppointmentCreationException(
    message: String?,
    cause: Throwable? = null,
) : RelaClientException(message, cause)

/**
 * Exception thrown when appointment cancellation fails.
 */
class RelaAppointmentCancellationException(
    message: String?,
    cause: Throwable? = null,
) : RelaClientException(message, cause)
