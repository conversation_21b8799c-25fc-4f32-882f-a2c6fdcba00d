package com.fleetmanagement.modules.rela.application.port

import com.fleetmanagement.modules.rela.application.RelaAppointmentCancellationException

fun interface CancelRelaAppointment {
    /**
     * Cancels an existing tire service appointment in the RELA system.
     *
     * @param orderNumber The order number of the appointment to cancel
     * @throws RelaAppointmentCancellationException if the appointment cancellation fails
     */
    fun cancelAppointment(orderNumber: String)
}
