package com.fleetmanagement.modules.rela.application.port

import com.fleetmanagement.modules.rela.application.RelaAppointmentCreationException
import java.time.OffsetDateTime

data class RelaAppointmentRequest(
    val appointment: OffsetDateTime,
    val vehicleLicensePlate: String,
    val vehicleVin: String,
    val customerLastName: String,
    val customerFirstName: String,
    val serviceBayNumber: Int,
    val serviceTypeId: Int,
    val vehicleTypeCode: String,
    val vehicleTypeDescription: String,
    val pccbCode: String?,
    val wheelCode: String,
    val cwlCode: String?,
    val rasCode: String?,
    val pccbDescription: String?,
    val orderedByEmail: String,
    val orderDate: OffsetDateTime,
)

data class RelaAppointmentResponse(
    val orderNumber: String,
)

fun interface CreateRelaAppointment {
    /**
     * Creates a new tire service appointment in the RELA system.
     *
     * @param request The appointment request containing all necessary details
     * @return The response containing the order number
     * @throws RelaAppointmentCreationException if the appointment creation fails
     */
    fun createAppointment(request: RelaAppointmentRequest): RelaAppointmentResponse
}
