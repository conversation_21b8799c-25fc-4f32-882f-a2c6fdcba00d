# Migration Module

The migration module coordinates the FMS to FVM migration by processing multiple Excel/CSV data sources and
orchestrating the migration of vehicles, transfers, registrations, and related data through various domain modules.

It is intended to be removed without hassle after the migration is complete.

## Architecture Overview

The migration module consists of several key components:

### Core Components

- **FMSMigrationJobService**: Main orchestrator that coordinates the entire migration process
- **Preprocessors**: Process Excel/CSV files from S3 and aggregate data into `FMSVehicleAggregate` objects
- **Postprocessors**: Handle the actual migration of different data domains (vehicles, transfers, registrations, etc.)
- **FMSVehicleAggregateCache**: In-memory cache that accumulates all vehicle-related data during preprocessing
- **Migration Reports**: Generate CSV reports of migration results and errors

### Data Sources

The migration processes multiple Excel/CSV files from FMS exports, each handled by a specific preprocessor:

#### Vehicle Data

- **DBMVIMODEL** (`PreProcessorDBMVIMODEL`): Vehicle model information including order type (MCODECS) and vehicle
  classification (VCLASS) for determining vehicle type (PKW, TRUCK, MOTORCYCLE, etc.)
- **DLZM** (`PreProcessorDLZM`): Core vehicle master data including VIN, model description, equipment number, mileage,
  various lifecycle dates (sold, scrapped, preparation), delivery settings, vehicle responsible person, maximum service
  life, model range classification, ZP8 production date, loading completed date, and new vehicle invoice date
- **EQUI** (`PreProcessorEQUI`): Equipment reference data linking equipment numbers (EQUNR) to equipment
  descriptions/IDs (EQKTX)
- **YDBMA3302_ADD** (`PreProcessorYDBMA3302ADD`): Additional vehicle properties including financial asset type, blocked
  for sale status, manufacturer information, and indicators for vehicles sold outside FMS
- **YDBMA3302_CLAI** (`PreProcessorYDBMA3302CLAI`): Vehicle damage/claims data tracking stolen/unstolen status based on
  damage classifications
- **YDBMA4992_ADD2** (`PreProcessorYDBMA4992ADD2`): Additional data including scrap vehicle flags, residual value market
  indicators, PDI (Pre-Delivery Inspection) dates
- **YDBMA3302_FMS** (`PreProcessorYDBMA3302FMS`): Extended FMS-specific data including net prices, internal contact
  persons, cost centers, usage IDs, registration details (brief numbers, TSN/HSN, SFME flags), location data (compound,
  parking lot), and various vehicle-specific flags

#### Vehicle Registration Data

- **YDBMA3302_REG1** (`PreProcessorYDBMA3302REG1`): Vehicle registration data including registration types, dates,
  license plates (with sanitization and E/H suffix detection), registration areas, and remarks
  Additional fields in YDBMA3302_FMS

#### Vehicle Sales Data

- **SalesInvoice** (`PreProcessorSalesInvoice`): Sales transaction data including vehicle sales price, discounts, winter
  tires information (ID, price, discount), sales person details, and customer partner numbers

#### Vehicle Transfer

- **YDBMA3302_DEL** (`PreProcessorYDBMA3302DEL`): Vehicle transfer/delivery data including delivery dates, mileage at
  delivery/return, responsible persons, usage groups, and cost centers
- **YDBMA4992_TERM** (`PreProcessorYDBMA4992TERM`): Vehicle transfer appointment data for planned delivery and return
  appointments with timestamps, responsible persons, and appointment types (delivery/return)
  Additional fields in YDBMA4992_ADD2, DLZM and others.

### Processing Flow

```mermaid
sequenceDiagram
    actor User
    participant S3 bucket
    participant MigrationModule
    box rgba(255, 255, 50, .1) "Migration Components"
        participant Preprocessors
        participant AggregateCache
        participant Postprocessors
    end
    box rgba(255, 255, 50, .1) "vehicle-service"
        participant VehicleData
        participant VehicleTransfer
        participant PDI
    end
    box rgba(255, 255, 50, .1) "vehicle-registration"
        participant VehicleRegistration
    end
    box rgba(255, 255, 50, .1) "location-service"
        participant LocationService
    end
    box rgba(0, 0, 255, .1) "MS Bookings"
        participant MS Bookings
    end

    User ->> MigrationModule: Trigger FmsMigrationJob
    MigrationModule ->> Preprocessors: Validate & process files
    Preprocessors ->> S3 bucket: Read Excel/CSV files
    Preprocessors ->> AggregateCache: Aggregate vehicle data
    MigrationModule ->> Postprocessors: Process aggregated data
    
    Postprocessors ->> VehicleData: Import PVH vehicles or create manual vehicles
    Postprocessors ->> VehicleData: Update vehicle data
    Postprocessors ->> VehicleRegistration: Create registrations
    Postprocessors ->> VehicleTransfer: Upsert vehicle transfers
    Postprocessors ->> PDI: Upsert PDI data
    Postprocessors ->> LocationService: Update vehicle locations
    Postprocessors ->> VehicleData: Upsert sales data
    
    Note over MigrationModule: Generate migration report
```

## Migration Execution / Jobs

### Overview Full Migration

The following steps need to be executed to finalise the migration on switch-over day:
Please note, that Sandbox mode must be turned on during the first migration phase!

1. **Trigger the FMS Migration Job**: This will process all vehicles and migrate them into FVM
2. **Review Migration Report**: Analyze the generated CSV report and Log insights error logs. Use the
   dev-tools/migration/report_analyzer. Verify some appointments in the database.

After confirmation with business side, execute these **post-migration** steps:

3. **Trigger the MS Bookings Appointment Scheduler Job**: This will create appointments in MS Bookings for vehicle
   transfers. You can do so in batches for intermediate verification.
4. **Post-Migration Actions**: Execute manual jobs to apply Vehicle Registrations restrictions
4. **Post-Migration Actions**: Execute manual jobs to emit Data Product for all vehicles

### Primary Migration Job

**Job Name**: `FmsMigrationJob`

The main migration job processes vehicles through the following steps:

1. **PVH Import**: Attempt to import vehicle from PVH system
2. **Manual Creation**: Create vehicle manually if not found in PVH
3. **Data Update**: Update vehicle with FMS data
4. **Registration Creation**: Create vehicle registrations
5. **Transfer Upsert**: Create/update vehicle transfers
6. **PDI Upsert**: Create/update PDI data
7. **Location Update**: Update vehicle location information
8. **Sales Upsert**: Update sales-related data

## How to trigger the migration steps

> **⚠️ Future deprecation Notice**  
> Access to the jobs API will be removed in the future,
> see [FPT3-2913](https://skyway.porsche.com/jira/browse/FPT3-2913).

### Prerequisites

- Valid authentication credentials for the target environment
- Appropriate permissions to trigger migration jobs
- FMS export files uploaded to the configured S3 bucket
- Migration module enabled via `fms-migration-job.enabled=true`

### Basic Migration Trigger

```bash
curl -X POST 'https://<env>/api/vs/jobs/trigger?jobName=FmsMigrationJob' \
  -H 'Authorization: Bearer <your-token>' \
  -H 'Content-Type: application/json'
```

### Selective VIN Migration

You can specify specific VINs to be migrated by passing a `vinFilter` in the JSON body:

```bash
curl -X POST 'https://<env>/api/vs/jobs/trigger?jobName=FmsMigrationJob' \
  -H 'Authorization: Bearer <your-token>' \
  -H 'Content-Type: application/json' \
  -d '{
    "vinFilter": "WP0ZZZ99ZTS392124,WVWZZZ1JZXW000001,WAUZZZ8V0JA000001"
  }'
```

### Appointments Scheduler Job

Handles the creation of appointments in MS Bookings system for specific vehicle transfers.

```bash
curl -X POST 'https://<env>/api/vs/jobs/trigger?jobName=MSBookingAppointmentSchedulerJob' \
  -H 'Authorization: Bearer <your-token>' \
  -H 'Content-Type: application/json'
```

| Parameter   | Type    | Required | Description                                                         |
|-------------|---------|----------|---------------------------------------------------------------------|
| `batchSize` | Integer | No       | Schedules a single batch of specified size of pending appointments. |

### Hotfix Migration (optional)

A separate job for applying targeted fixes to already migrated data without re-running the full migration.

```bash
curl -X POST 'https://<env>/api/vs/jobs/trigger?jobName=FmsMigrationHotfixJob' \
  -H 'Authorization: Bearer <your-token>' \
  -H 'Content-Type: application/json'
```

## Post-Migration Actions

After vehicles have been migrated, the following manual jobs should be executed **one time**:

1. **Vehicle Restriction Manual Job**:
    - **Endpoint**: `/api/vs/jobs/trigger?jobName=vehicleRestrictionManualJob`
    - **Purpose**: Restricts registrations for vehicles with active transfers

2. **Emit Data Product for All Vehicles**:
    - **Endpoint**: `/api/vs/jobs/trigger?jobName=fleetVehicleMasterDataManualJob`
    - **Purpose**: Publishes all vehicles as data products in their latest state

## Error Handling and Reporting

- **Parallel Processing**: Vehicle migrations run in parallel for performance
- **Error Isolation**: Errors in one vehicle don't stop migration of others
- **Comprehensive Reporting**: Detailed CSV reports with migration results and errors
- **Logging**: Extensive logging with MDC context for traceability

## Technical Implementation

## Configuration

The migration module requires the following configuration:

```yaml
fms-migration-job:
  enabled: true
  fms-source:
    storage:
      bucket: "your-s3-bucket-name"
      sseKmsKey: "your-kms-key"
```

### Preprocessors

### Postprocessors

Domain-specific migration handlers:

- `PVHVehicleImporter`: Imports vehicles from PVH system
- `ManualVehicleCreator`: Creates vehicles manually when not in PVH
- `VehicleDataUpdater`: Updates vehicle master data
- `VehicleRegistrationCreator`: Creates vehicle registrations
- `VehicleTransferUpserter`: Handles vehicle transfers
- `VehiclePDIUpserter`: Manages PDI data
- `VehicleLocationUpdater`: Updates location information
- `VehicleSalesUpserter`: Handles sales data
- `MigrationHotfixUpdater`: Applies targeted fixes

## Migration Flow Strategy

```mermaid
flowchart LR
    subgraph "S3 Data Sources"
        A1["DBMVIMODEL.xlsx"]
        A2["DLZM.xlsx"]
        A3["EQUI.xlsx"]
        A4["SalesInvoice.xlsx"]
        A5["YDBMA3302*.xlsx"]
        A6["YDBMA4992*.xlsx"]
    end

    subgraph "Preprocessing"
        B1["File validation"]
        B2["Data parsing & mapping"]
        B3["Aggregation by ProductGUID"]
    end
    
    C1[("FMSVehicleAggregateCache")]

    subgraph "Postprocessing"
        D1["PVH Import/Manual Creation"]
        D2["Vehicle Data Update"]
        D3["Registration Creation"]
        D4["Transfer Upsert"]
        D5["PDI Upsert"]
        D6["Location Update"]
        D7["Sales Data Upsert"]
    end

    subgraph "Domain APIs"
        E1["Vehicle Data API"]
        E2["Vehicle Registration API"]
        E3["Vehicle Transfer API"]
        E4["Location Service API"]
    end

    A1 --> B1
    A2 --> B1
    A3 --> B1
    A4 --> B1
    A5 --> B1
    A6 --> B1

    B1 --> B2
    B2 --> B3
    B3 --> C1

    C1 --> D1
    C1 --> D2
    C1 --> D3
    C1 --> D4
    C1 --> D5
    C1 --> D6
    C1 --> D7

    D1 --> E1
    D2 --> E1
    D3 --> E2
    D4 --> E3
    D6 --> E4
    D7 --> E1
```

> **Note**: Ensure all preconditions mentioned in
>
the [Confluence Migration Runbook](https://skyway.porsche.com/confluence/spaces/FP20/pages/1913807300/Runbook+Full+FMS+FVM+Migration)
> are met before initiating the migration.

### Manual Jobs API

The manual job API is the central piece to trigger the migration steps.

```bash
curl -X POST 'https://<env>/api/vs/jobs/trigger?jobName=<migration step job name>' \
  -H 'Authorization: Bearer <your-token>' \
  -H 'Content-Type: application/json'
```

### Parameters

| Parameter   | Type   | Required | Description                                                                                                                                                         |
|-------------|--------|----------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| `jobName`   | String | Yes      | Job name to trigger: `FmsMigrationJob`, `FmsMigrationHotfixJob`, `MSBookingAppointmentSchedulerJob`,`vehicleRestrictionManualJob`,`fleetVehicleMasterDataManualJob` |
| `vinFilter` | String | No       | FmsMigrationJob: Comma-separated list of VINs to migrate. If omitted, all available vehicles will be migrated.                                                      |
| `batchSize` | Integer | No       | MSBookingAppointmentSchedulerJob: Schedules a single batch of specified size of pending appointments.                                                               |

