package com.fleetmanagement.modules.migration.appointments

import com.fleetmanagement.modules.consigneedatasheet.domain.EmployeeNumber
import com.fleetmanagement.modules.migration.MigrationModuleConfiguration
import com.fleetmanagement.modules.migration.appointments.FMSMigrationAppointmentStatus.FAILED_TO_SEND
import com.fleetmanagement.modules.migration.appointments.FMSMigrationAppointmentStatus.SENT_TO_MS_BOOKINGS
import com.fleetmanagement.modules.migration.job.PlannedAppointment
import com.fleetmanagement.modules.transfermsbookings.application.AppointmentType
import com.fleetmanagement.modules.transfermsbookings.application.port.CreateAppointment
import com.fleetmanagement.modules.vehicleperson.api.peoplesearch.PeopleSearch
import com.fleetmanagement.modules.vehicletransfer.domain.entities.VehicleTransferKey
import jakarta.transaction.Transactional
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.data.domain.Limit
import org.springframework.stereotype.Component
import java.time.OffsetDateTime

@ConditionalOnBean(MigrationModuleConfiguration::class)
@Component
@Transactional
class MsBookingAppointmentsHandler(
    @Autowired private val jpaFMSAppointmentsRepository: JPAFMSAppointmentRepository,
    // having CreateAppointment not required, to make migration module independent of
    @Autowired(required = false) private val createAppointmentService: CreateAppointment?,
    @Autowired private val peopleSearchService: PeopleSearch,
) {
    private val logger = LoggerFactory.getLogger(MsBookingAppointmentsHandler::class.java)

    fun upsertPendingMSBookingAppointment(
        appointment: PlannedAppointment,
        vin: String,
        vehicleTransferKey: VehicleTransferKey,
        appointmentType: AppointmentType,
    ) {
        val existing = jpaFMSAppointmentsRepository.findByVehicleVinAndAppointmentType(vin, appointmentType)
        if (existing?.status == SENT_TO_MS_BOOKINGS) {
            logger.error("Appointment already sent to MS Bookings for $vin and transfer $vehicleTransferKey.")
            return
        }

        if (existing != null) {
            existing.update(
                appointmentFrom = appointment.from,
                appointmentTo = appointment.to ?: appointment.from.plusMinutes(30),
                personNumber = EmployeeNumber(appointment.responsiblePerson),
                vehicleTransferKey = vehicleTransferKey,
            )
        } else {
            val appointment =
                JPAFMSAppointment(
                    vehicleVin = vin,
                    appointmentFrom = appointment.from,
                    appointmentTo = appointment.to ?: appointment.from.plusMinutes(30),
                    personNumber = EmployeeNumber(appointment.responsiblePerson),
                    vehicleTransferKey = vehicleTransferKey,
                    appointmentType = appointmentType,
                )
            jpaFMSAppointmentsRepository.save(appointment)
        }
    }

    fun scheduleMSBookingAppointments(batchSize: Int) {
        if (createAppointmentService == null) {
            // this can only happen if MS Booking module is not activated. In this case we cannot trigger, fail the job.
            throw IllegalStateException("No CreateAppointment bean injected. Is the MS Booking module activated?")
        }
        val appointments =
            jpaFMSAppointmentsRepository.findByStatus(
                limit = Limit.of(batchSize),
            )
        val now = OffsetDateTime.now()
        appointments.forEach { appointment ->
            if (appointment.appointmentFrom.isBefore(now)) {
                return@forEach
            }
            runCatching {
                val person =
                    peopleSearchService
                        .peopleSearch(
                            employeeNumber = appointment.personNumber.value,
                            firstName = null,
                            lastName = null,
                            companyEmail = null,
                        ).firstOrNull() ?: throw IllegalStateException(
                        "Responsible person with employee number ${appointment.personNumber.value} not found",
                    )
                createAppointmentService!!.createAppointment(
                    startDateTime = appointment.appointmentFrom,
                    endDateTime = appointment.appointmentTo,
                    serviceNotes = appointment.vehicleTransferKey.value.toString(),
                    customerEmail = person.companyEmail,
                    serviceName = appointment.appointmentType,
                )
                appointment.updateStatus(SENT_TO_MS_BOOKINGS)
            }.onFailure { error ->
                logger.error("Could not create MS booking appointment for ${appointment.id}", error)
                appointment.updateStatus(FAILED_TO_SEND)
            }
        }
    }
}
