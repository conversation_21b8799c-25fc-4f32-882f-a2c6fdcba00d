package com.fleetmanagement.modules.migration.appointments

import com.fleetmanagement.modules.consigneedatasheet.domain.EmployeeNumber
import com.fleetmanagement.modules.transfermsbookings.application.AppointmentType
import com.fleetmanagement.modules.vehicletransfer.domain.entities.VehicleTransferKey
import jakarta.persistence.*
import java.time.OffsetDateTime
import java.util.*

@Entity
@Table(name = "appointments", schema = "fms_migration")
class JPAFMSAppointment {
    constructor(
        vehicleVin: String,
        appointmentType: AppointmentType,
        appointmentFrom: OffsetDateTime,
        appointmentTo: OffsetDateTime,
        personNumber: EmployeeNumber,
        vehicleTransferKey: VehicleTransferKey,
        status: FMSMigrationAppointmentStatus = FMSMigrationAppointmentStatus.PENDING,
    ) {
        this.id
        this.vehicleVin = vehicleVin
        this.appointmentType = appointmentType
        this.appointmentFrom = appointmentFrom
        this.appointmentTo = appointmentTo
        this.personNumber = personNumber
        this.vehicleTransferKey = vehicleTransferKey
        this.status = status
    }

    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @Column(name = "id", unique = true, nullable = false)
    val id: UUID? = null

    @Column(name = "vehicle_vin", nullable = false, unique = true)
    val vehicleVin: String

    @Enumerated(EnumType.STRING)
    @Column(name = "appointment_type", nullable = false)
    val appointmentType: AppointmentType

    @Column(name = "appointment_time_from", nullable = false)
    var appointmentFrom: OffsetDateTime
        private set

    @Column(name = "appointment_time_to", nullable = false)
    var appointmentTo: OffsetDateTime
        private set

    @Embedded
    @AttributeOverride(name = "value", column = Column(name = "person_number"))
    var personNumber: EmployeeNumber
        private set

    @Embedded
    @AttributeOverride(name = "value", column = Column(name = "vehicle_transfer_key"))
    var vehicleTransferKey: VehicleTransferKey
        private set

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    var status: FMSMigrationAppointmentStatus
        private set

    fun update(
        appointmentFrom: OffsetDateTime,
        appointmentTo: OffsetDateTime,
        personNumber: EmployeeNumber,
        vehicleTransferKey: VehicleTransferKey,
    ): JPAFMSAppointment {
        this.appointmentFrom = appointmentFrom
        this.appointmentTo = appointmentTo
        this.personNumber = personNumber
        this.vehicleTransferKey = vehicleTransferKey
        return this
    }

    fun updateStatus(status: FMSMigrationAppointmentStatus): JPAFMSAppointment {
        this.status = status
        return this
    }
}

enum class FMSMigrationAppointmentStatus {
    PENDING,
    SENT_TO_MS_BOOKINGS,
    FAILED_TO_SEND,
}
