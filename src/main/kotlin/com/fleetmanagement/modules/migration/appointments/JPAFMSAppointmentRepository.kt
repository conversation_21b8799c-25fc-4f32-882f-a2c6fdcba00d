package com.fleetmanagement.modules.migration.appointments

import com.fleetmanagement.modules.migration.appointments.FMSMigrationAppointmentStatus.PENDING
import com.fleetmanagement.modules.transfermsbookings.application.AppointmentType
import org.springframework.data.domain.Limit
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.util.UUID

@Repository
interface JPAFMSAppointmentRepository : JpaRepository<JPAFMSAppointment, UUID> {
    fun findByStatus(
        status: FMSMigrationAppointmentStatus = PENDING,
        limit: Limit = Limit.unlimited(),
    ): List<JPAFMSAppointment>

    fun findByVehicleVinAndAppointmentType(
        vin: String,
        type: AppointmentType,
    ): JPAFMSAppointment?
}
