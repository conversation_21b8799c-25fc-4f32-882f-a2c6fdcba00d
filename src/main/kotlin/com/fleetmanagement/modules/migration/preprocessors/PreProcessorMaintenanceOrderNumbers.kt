package com.fleetmanagement.modules.migration.preprocessors

import com.fleetmanagement.modules.migration.MigrationModuleConfiguration
import com.fleetmanagement.modules.migration.job.FMSMaintenanceOrderNumber
import com.fleetmanagement.modules.migration.job.FMSVehicleAggregateCache
import com.fleetmanagement.modules.migration.job.ports.Preprocessor
import com.fleetmanagement.modules.migration.preprocessors.ParseUtils.toGermanyOffsetDateTime
import com.fleetmanagement.modules.migration.preprocessors.RowProcessingParsingErrorHandler.withErrorHandling
import com.fleetmanagement.modules.migration.preprocessors.excelparsing.ExcelPreprocessor
import com.fleetmanagement.modules.migration.preprocessors.excelparsing.FMSRow
import com.fleetmanagement.modules.migration.preprocessors.excelparsing.FMSRowPreprocessor
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.stereotype.Component

@ConditionalOnBean(MigrationModuleConfiguration::class)
@Component
class PreProcessorMaintenanceOrderNumbers(
    val excelPreprocessor: ExcelPreprocessor,
) : Preprocessor,
    FMSRowPreprocessor {
    val fileName = "ORDER_NUMBERS.XLSX"
    private final val logger: Logger = LoggerFactory.getLogger(PreProcessorMaintenanceOrderNumbers::class.java)

    companion object {
        private const val HEADER_ORDER_NUMBER = "AUFNR"
        private const val HEADER_DESCRIPTION_WITH_VIN = "KTEXT"
        private const val HEADER_ORDER_NUMBER_TYPE = "AUART"
        private const val HEADER_CREATION_DATE = "ERDAT"
        private const val HEADER_RESPONSIBLE_USER = "USER2"

        // This prefix indicates an order number of type "maintenance order number".
        private const val MAINTENANCE_ORDER_NUMBER_TYPE_PREFIX = "Y73"
    }

    private val expectedHeaders =
        setOf(
            HEADER_ORDER_NUMBER,
            HEADER_DESCRIPTION_WITH_VIN,
            HEADER_ORDER_NUMBER_TYPE,
            HEADER_CREATION_DATE,
            HEADER_RESPONSIBLE_USER,
        )

    override fun validate(): Boolean = excelPreprocessor.validate(fileName, expectedHeaders)

    override fun process(cache: FMSVehicleAggregateCache) {
        excelPreprocessor.processExcelFile(fileName, this, cache)
    }

    override fun processRow(
        row: FMSRow,
        cache: FMSVehicleAggregateCache,
    ) {
        val description = row.getStringValue(HEADER_DESCRIPTION_WITH_VIN)
        val vin = description?.let { parseVIN(description) }
        val aggregate = vin?.let { cache.getAggregateByVin(vin) }
        if (aggregate == null) {
            // if we don't have the vehicle, just ignore it
            logger.warn("No vehicle found for description $description. Cannot attach order number.")
            return
        }
        withErrorHandling(fileName, aggregate) {
            val orderNumberType = row.getStringValue(HEADER_ORDER_NUMBER_TYPE)
            if (orderNumberType?.startsWith(MAINTENANCE_ORDER_NUMBER_TYPE_PREFIX) == true) {
                val orderNumber = row.getStringValue(HEADER_ORDER_NUMBER)
                val creationDate = row.getDateValue(HEADER_CREATION_DATE)?.toGermanyOffsetDateTime()
                val responsibleUser = row.getStringValue(HEADER_RESPONSIBLE_USER)

                if (orderNumber != null && creationDate != null) {
                    val maintenanceOrderNumber =
                        FMSMaintenanceOrderNumber(
                            orderNumber = orderNumber,
                            creationDate = creationDate,
                            responsibleUser = responsibleUser,
                        )
                    aggregate.maintenanceOrderNumbers.add(maintenanceOrderNumber)
                } else {
                    aggregate.reportError(
                        "Missing required data for order number: orderNumber=$orderNumber, creationDate=$creationDate, responsibleUser=$responsibleUser",
                    )
                }
            }
        }
    }

    fun parseVIN(description: String): String? {
        try {
            return description.split(" ").last()
        } catch (e: NoSuchElementException) {
            // if we can't parse the value, we might have a bug
            logger.error("ORDER_NUMBERS.XLSX: Could not parse VIN from description $description.", e)
            return null
        }
    }
}
