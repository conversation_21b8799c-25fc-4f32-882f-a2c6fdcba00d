package com.fleetmanagement.modules.migration.job

import com.fleetmanagement.emhshared.domain.VehicleType
import com.fleetmanagement.modules.vehicledata.api.domain.FinancialAssetType
import com.fleetmanagement.modules.vehicledata.api.domain.NextProcess
import java.math.BigDecimal
import java.time.LocalDate
import java.time.OffsetDateTime

data class FMSVehicleAggregate(
    val productGuid: String,
) {
    var isClassic: Boolean = false

    // data set for create vehicle manually:
    var vin: String? = null

    var modelDescription: String? = null
    var manufacturer: String? = null
    var orderType: String? = null
    var vehicleType: VehicleType? = null
    var financialAssetType: FinancialAssetType? = null
    var blockedForSale: Boolean = false
    var scrappedDate: OffsetDateTime? = null
    var equipmentNumber: Long? = null
    var equipmentId: String? = null
    var zp8Date: OffsetDateTime? = null

    // vehicle location
    var compoundNameId: String? = null
    var parkingLot: String? = null
    var parkingLotDate: OffsetDateTime? = null

    // more fields:
    var currentMileage: Int? = null
    var scrapVehicle: Boolean = false
    var soldDate: OffsetDateTime? = null
    var approvedForScrappingDate: OffsetDateTime? = null
    var vehicleSentToSalesDate: OffsetDateTime? = null
    var preparationDoneDate: OffsetDateTime? = null
    var isSoldOutsideFMS: Boolean = false // if true, then last vehicle transfer's return date = soldCupCarDate
    val soldCupCarDate: OffsetDateTime?
        get() =
            if (isSoldOutsideFMS) {
                transfers.mapNotNull { it.returnDate }.maxOrNull()
            } else {
                null
            }
    var lastUnstolenDate: OffsetDateTime? = null
    var lastStolenDate: OffsetDateTime? = null
    val stolenDate: OffsetDateTime?
        get() =
            lastStolenDate?.let {
                if (lastUnstolenDate == null || lastUnstolenDate!!.isBefore(lastStolenDate)) {
                    lastStolenDate
                } else {
                    null
                }
            }

    var isResidualValueMarket: Boolean = false
    var maximumServiceLifeInMonths: Int? = null

    var pdiOrderedDate: OffsetDateTime? = null
    var pdiCompletedDate: OffsetDateTime? = null

    var netPriceNewVehicle: BigDecimal? = null
    var newVehicleInvoiceDate: LocalDate? = null

    var fmsPrimaryStatus: String? = null

    val nextProcess: NextProcess?
        get() =
            when {
                !scrapVehicle &&
                    !blockedForSale &&
                    fmsPrimaryStatus in listOf("YF35", "YF40", "YQ98", "YF30")
                -> NextProcess.SALES

                scrapVehicle && fmsPrimaryStatus in listOf("YF69", "YF70", "YXXX") -> NextProcess.SCRAPPED_CAR_RETURNED

                blockedForSale && !scrapVehicle && fmsPrimaryStatus == "YF30" -> NextProcess.PROFITABILITY_AUDIT_IN_PREPARATION

                // we set this, so that a vehicle transfer can be correctly closed. might not be needed though.
                fmsPrimaryStatus == "YF90" -> NextProcess.CHECK_IF_REUSAGE_IS_POSSIBLE

                else -> null
            }

    // sales invoice fields
    val sales: FMSVehicleSalesAggregate = FMSVehicleSalesAggregate()

    // transfer fields
    val transfers: MutableList<FMSTransferAggregate> = mutableListOf()
    var latestTransferAdditionalData: FMSLatestTransferAggregate = FMSLatestTransferAggregate()

    // maintenance order numbers with detailed information for proper mapping
    val maintenanceOrderNumbers: MutableList<FMSMaintenanceOrderNumber> = mutableListOf()

    // registrations
    val registrations: MutableList<FMSRegistrationAggregate> = mutableListOf()
    var extendedRegistrationData: FMSExtendedRegistrationAggregate = FMSExtendedRegistrationAggregate()

    fun calculateTransfersWithOrderNumbers(): List<FMSTransferAggregate> {
        val matcher = OrderNumberMatcher(maintenanceOrderNumbers, this::reportError)
        transfers.forEach { transfer ->
            val matchingOrderNumber = matcher.findForTransfer(transfer)
            transfer.maintenanceOrderNumber = matchingOrderNumber?.orderNumber
        }
        return transfers
    }

    val errors: MutableList<String?> = mutableListOf()

    fun reportError(error: String?) {
        errors.add(error)
    }

    fun hasCompletedTransfer(): Boolean = transfers.any({ it.returnDate != null })

    val vehicleLocationCompoundName: String?
        get() {
            val fmsCompoundIdTofvmCompoundNameMap: Map<Int, String> =
                mapOf(
                    1 to "ZWZ",
                    2 to "ZWZ",
                    3 to "Compound Siegelsbach",
                    4 to "Compound Vaihingen",
                    5 to "Räderservice / DL",
                    8 to "Compound Weissach",
                    9 to "Werkscompound Kornwestheim",
                    10 to "Compound Illingen",
                    13 to "Abschleppservice / DL",
                    14 to "ZWZ",
                    16 to "Werkscompound Leipzig",
                    17 to "Hafen Bremerhaven",
                    20 to "ZWZ",
                    21 to "ZWZ - VA9 Nacharbeit",
                    22 to "Compound Bietigheim-Bissingen",
                    24 to "Hafen Emden",
                    25 to "Compound Renningen",
                )
            return if (this.compoundNameId.isNullOrBlank()) {
                null
            } else {
                fmsCompoundIdTofvmCompoundNameMap[this.compoundNameId!!.toInt()]
            }
        }

    private class OrderNumberMatcher(
        private val maintenanceOrderNumbers: List<FMSMaintenanceOrderNumber>,
        private val reportError: (String) -> Unit,
    ) {
        fun findForTransfer(transfer: FMSTransferAggregate): FMSMaintenanceOrderNumber? {
            val candidatesInTimeRange = findOrderNumbersInTransferTimeRange(transfer)
            return selectByResponsiblePerson(candidatesInTimeRange, transfer)
        }

        private fun findOrderNumbersInTransferTimeRange(transfer: FMSTransferAggregate): List<FMSMaintenanceOrderNumber> {
            requireNotNull(transfer.deliveryDate) { "deliveryDate should never be null from FMS" }

            return maintenanceOrderNumbers.filter { orderNumber ->
                isOrderCreatedDuringTransfer(orderNumber, transfer)
            }
        }

        private fun isOrderCreatedDuringTransfer(
            orderNumber: FMSMaintenanceOrderNumber,
            transfer: FMSTransferAggregate,
        ): Boolean {
            val creationDate = orderNumber.creationDate.toLocalDate()
            val deliveryDate = transfer.deliveryDate!!.toLocalDate()
            val returnDate = transfer.returnDate?.toLocalDate()

            val isAfterOrOnDelivery = !creationDate.isBefore(deliveryDate)
            val isBeforeOrOnReturn = returnDate == null || !creationDate.isAfter(returnDate)

            return isAfterOrOnDelivery && isBeforeOrOnReturn
        }

        private fun selectByResponsiblePerson(
            candidates: List<FMSMaintenanceOrderNumber>,
            transfer: FMSTransferAggregate,
        ): FMSMaintenanceOrderNumber? =
            when {
                candidates.isEmpty() -> null
                candidates.size == 1 -> validateSingleCandidate(candidates.first(), transfer)
                else -> handleMultipleCandidates(candidates, transfer)
            }

        private fun validateSingleCandidate(
            candidate: FMSMaintenanceOrderNumber,
            transfer: FMSTransferAggregate,
        ): FMSMaintenanceOrderNumber? =
            if (responsibleUserMatches(candidate, transfer)) {
                candidate
            } else {
                reportError(
                    "Maintenance order number '${candidate.orderNumber}' does not match responsible person for fmsDeliveryIndex ${transfer.fmsDeliveryIndex}.",
                )
                null
            }

        private fun handleMultipleCandidates(
            candidates: List<FMSMaintenanceOrderNumber>,
            transfer: FMSTransferAggregate,
        ): FMSMaintenanceOrderNumber? {
            val matchingByUser = findOrderNumberWithMatchingUser(candidates, transfer)

            return if (matchingByUser != null) {
                matchingByUser
            } else {
                val orderNumbers = candidates.map { it.orderNumber }
                reportError(
                    "Maintenance order numbers '$orderNumbers' do not match responsible person for fmsDeliveryIndex ${transfer.fmsDeliveryIndex}.",
                )
                null
            }
        }

        private fun findOrderNumberWithMatchingUser(
            candidates: List<FMSMaintenanceOrderNumber>,
            transfer: FMSTransferAggregate,
        ): FMSMaintenanceOrderNumber? =
            candidates.firstOrNull { orderNumber ->
                responsibleUserMatches(orderNumber, transfer)
            }

        private fun responsibleUserMatches(
            orderNumber: FMSMaintenanceOrderNumber,
            transfer: FMSTransferAggregate,
        ): Boolean = orderNumber.responsibleUser == transfer.vehicleResponsiblePerson
    }
}
