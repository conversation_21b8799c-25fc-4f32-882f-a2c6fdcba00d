package com.fleetmanagement.modules.registrationmailexport

import com.fleetmanagement.jobmanagement.configuration.QuartzJobManager
import com.fleetmanagement.modules.registrationmailexport.configuration.VehicleRegistrationExportProperties
import org.quartz.CronScheduleBuilder
import org.quartz.JobBuilder
import org.quartz.JobDetail
import org.quartz.JobKey
import org.quartz.Scheduler
import org.quartz.Trigger
import org.quartz.TriggerBuilder
import org.springframework.beans.factory.ObjectProvider
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import java.time.ZoneOffset
import java.util.TimeZone

@ConditionalOnProperty(name = ["registration-mail-export.enabled"], havingValue = "true")
class VehicleRegistrationExportJobConfiguration {
    @Bean("vehicleRegistrationExportJobDetail")
    fun vehicleRegistrationExportJobDetail(): JobDetail =
        JobBuilder
            .newJob()
            .ofType(VehicleRegistrationExportJob::class.java)
            .withIdentity("VehicleRegistrationMailExportJob")
            .withDescription("Vehicle Registration Mail Export Job")
            .storeDurably()
            .build()

    @Bean("vehicleRegistrationExportJobTrigger")
    fun vehicleRegistrationExportJobTrigger(
        @Qualifier("vehicleRegistrationExportJobDetail") vehicleRegistrationExportJobDetail: JobDetail,
        properties: VehicleRegistrationExportProperties,
    ): Trigger =
        TriggerBuilder
            .newTrigger()
            .forJob(vehicleRegistrationExportJobDetail)
            .withIdentity("VehicleRegistrationMailExportJobTrigger")
            .withDescription("Vehicle Registration Mail Export Job Trigger")
            .withSchedule(
                CronScheduleBuilder
                    .cronSchedule(properties.cron)
                    .inTimeZone(TimeZone.getTimeZone(ZoneOffset.UTC)),
            ).build()
}

@Configuration
@ConditionalOnMissingBean(VehicleRegistrationExportJobConfiguration::class)
class VehicleRegistrationExportDisabledJobConfiguration {
    @Bean("vehicleRegistrationExportJob")
    fun pauseQuartzJob(schedulerProvider: ObjectProvider<Scheduler>): QuartzJobManager =
        QuartzJobManager(
            jobKey = JobKey("VehicleRegistrationMailExportJob"),
            schedulerProvider = schedulerProvider,
            isJobEnabled = false,
        )
}
