package com.fleetmanagement.modules.registrationmailexport

import com.fleetmanagement.modules.registrationmailexport.csv.VehicleRegistrationExportCSV
import com.fleetmanagement.modules.registrationmailexport.csv.VehicleRegistrationExportCSVRow
import com.fleetmanagement.modules.registrationmailexport.mailclient.VehicleRegistrationExportMailClient
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByVIN
import com.fleetmanagement.modules.vehicleregistration.api.ReadRegistrationOrder
import com.fleetmanagement.modules.vehicleregistration.api.dto.VehicleRegistrationOrder
import org.slf4j.LoggerFactory
import java.time.OffsetDateTime

class VehicleRegistrationExportService(
    private val readRegistrationOrder: ReadRegistrationOrder,
    private val mailClient: VehicleRegistrationExportMailClient,
    private val readVehicleByVIN: ReadVehicleByVIN,
) {
    fun createAndSendRegistrationsData(lastSuccessfulDateTime: OffsetDateTime? = null) {
        val previousExecutionDate =
            lastSuccessfulDateTime
                ?: OffsetDateTime.now().minusDays(1) // Default to 24 hours ago if no last successful date is provided
        val registrations = getRegistrations(previousExecutionDate)
        val csv = createCSVFromRegistrations(registrations)

        if (!csv.isEmpty()) {
            mailClient.sendEmail(csv)
        } else {
            mailClient.sendEmailWithoutAttachment()
        }
    }

    private fun getRegistrations(updatedAfter: OffsetDateTime): List<VehicleRegistrationOrder> =
        try {
            readRegistrationOrder.getRegistrationsModifiedSince(updatedAfter).data
        } catch (exception: Exception) {
            logger.error("Failed to fetch vehicle registrations updated in the last 24 hours", exception)
            // Return empty list to prevent email sending when data fetch fails
            emptyList()
        }

    private fun createCSVFromRegistrations(registrations: List<VehicleRegistrationOrder>): VehicleRegistrationExportCSV {
        val csv = VehicleRegistrationExportCSV()

        // Cache vehicle types to avoid duplicate API calls for same VIN
        val vehicleTypeCache = mutableMapOf<String, String>()

        registrations.forEach { registration ->
            val vin = registration.vin?.orElse("").orEmpty()
            val vehicleType =
                vehicleTypeCache.getOrPut(vin) {
                    getVehicleTypeFromVehicleModule(vin)
                }

            val csvRow =
                VehicleRegistrationExportCSVRow(
                    vin = vin,
                    licensePlate = registration.licencePlate?.orElse("").orEmpty(),
                    hsn = registration.hsn?.orElse("").orEmpty(),
                    tsn = registration.tsn?.orElse("").orEmpty(),
                    vehicleType = vehicleType,
                    registrationType = mapRegistrationType(registration.registrationType?.orElse(null)),
                    registrationDeregistrationDate =
                        registration.registrationDate?.orElse(null)?.toLocalDateTime(),
                    lastUpdateDate =
                        registration.updatedAt?.orElse(null)?.toLocalDateTime(),
                )
            csv.addRow(csvRow)
        }

        return csv
    }

    private fun getVehicleTypeFromVehicleModule(vin: String): String =
        try {
            val vehicle = readVehicleByVIN.readVehicleByVIN(vin)
            vehicle.model?.vehicleType?.name ?: ""
        } catch (exception: Exception) {
            logger.error("Failed to fetch vehicle type for VIN $vin from vehicle module", exception)
            ""
        }

    private fun mapRegistrationType(registrationType: Int?): String =
        when (registrationType) {
            1 -> "1 Erstzulassung"
            2 -> "2 Wiederzulassung"
            3 -> "3 Umkennzeichnung"
            4 -> "4 Abmeldung"
            null -> ""
            else -> "$registrationType"
        }

    companion object {
        private val logger = LoggerFactory.getLogger(VehicleRegistrationExportService::class.java)
    }
}
