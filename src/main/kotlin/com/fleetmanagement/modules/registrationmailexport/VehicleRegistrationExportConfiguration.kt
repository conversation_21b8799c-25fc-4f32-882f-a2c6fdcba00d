package com.fleetmanagement.modules.registrationmailexport

import com.fleetmanagement.integrations.mailclient.application.port.EmailOutPort
import com.fleetmanagement.modules.registrationmailexport.configuration.VehicleRegistrationExportProperties
import com.fleetmanagement.modules.registrationmailexport.mailclient.VehicleRegistrationExportMailClient
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByVIN
import com.fleetmanagement.modules.vehicleregistration.api.ReadRegistrationOrder
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Import

/**
 * Central configuration for all dependency injection related to the vehicle registration export feature.
 *
 * This class serves as the single source for wiring beans and dependencies
 * used within the vehicle registration export feature.
 */
@Configuration
@ConditionalOnProperty(name = ["registration-mail-export.enabled"], havingValue = "true")
@Import(VehicleRegistrationExportJobConfiguration::class)
class VehicleRegistrationExportConfiguration {
    @Bean
    fun vehicleRegistrationExportMailClient(
        emailOutPort: EmailOutPort,
        properties: VehicleRegistrationExportProperties,
    ) = VehicleRegistrationExportMailClient(
        emailOutPort,
        properties.emailSender,
        properties.emailRecipient,
    )

    @Bean
    fun vehicleRegistrationExportService(
        readRegistrationOrder: ReadRegistrationOrder,
        mailClient: VehicleRegistrationExportMailClient,
        readVehicleByVIN: ReadVehicleByVIN,
    ) = VehicleRegistrationExportService(
        readRegistrationOrder,
        mailClient,
        readVehicleByVIN,
    )

    @Bean
    fun vehicleRegistrationExportJob(vehicleRegistrationExportService: VehicleRegistrationExportService) =
        VehicleRegistrationExportJob(
            vehicleRegistrationExportService,
        )
}
