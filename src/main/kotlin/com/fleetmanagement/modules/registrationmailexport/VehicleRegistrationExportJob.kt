package com.fleetmanagement.modules.registrationmailexport

import org.quartz.DisallowConcurrentExecution
import org.quartz.Job
import org.quartz.JobExecutionContext
import org.slf4j.LoggerFactory
import java.time.OffsetDateTime
import java.time.ZoneOffset

/**
 * Quartz Job to automatically export vehicle registration data and send it via email.
 *
 * This job runs on a scheduled basis to fetch vehicle registrations that were updated
 * in the last 24 hours and sends them to the configured insurance company email address.
 */
@DisallowConcurrentExecution
class VehicleRegistrationExportJob(
    private val vehicleRegistrationExportService: VehicleRegistrationExportService,
) : Job {
    override fun execute(context: JobExecutionContext?) {
        if (context == null) {
            logger.error("VehicleRegistrationExportJob failed to execute: JobExecutionContext is null.")
            return
        }
        logger.info("Starting scheduled VehicleRegistrationExportJob.")

        // Read the last successful completion date from job context
        val jobDataMap = context.jobDetail.jobDataMap
        val lastSuccessfulCompletedDate = jobDataMap.getString(LAST_SUCCESSFUL_COMPLETED_DATE_KEY)

        if (lastSuccessfulCompletedDate != null) {
            logger.info("Last successful completion was on: $lastSuccessfulCompletedDate")
        } else {
            logger.info("This is the first execution of the job.")
        }

        try {
            val lastSuccessfulDateTime = lastSuccessfulCompletedDate?.let { OffsetDateTime.parse(it) }
            vehicleRegistrationExportService.createAndSendRegistrationsData(lastSuccessfulDateTime)

            // Set the last successful completion date on successful execution
            jobDataMap.put(LAST_SUCCESSFUL_COMPLETED_DATE_KEY, OffsetDateTime.now(ZoneOffset.UTC).toString())

            logger.info("Successfully completed VehicleRegistrationExportJob")
        } catch (exception: Exception) {
            logger.error("Failed to execute VehicleRegistrationExportJob", exception)
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(VehicleRegistrationExportJob::class.java)

        // Key for storing last successful completion date
        const val LAST_SUCCESSFUL_COMPLETED_DATE_KEY = "lastSuccessfulCompletedDate"
    }
}
