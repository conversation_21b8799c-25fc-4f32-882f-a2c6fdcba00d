package com.fleetmanagement.modules.registrationmailexport.csv

import com.opencsv.CSVWriter
import java.io.ByteArrayInputStream
import java.io.InputStream
import java.io.StringWriter
import java.nio.charset.StandardCharsets

class VehicleRegistrationExportCSV {
    private val rows: MutableList<VehicleRegistrationExportCSVRow> = mutableListOf()

    fun addRow(row: VehicleRegistrationExportCSVRow) {
        rows.add(row)
    }

    fun isEmpty(): Boolean = rows.isEmpty()

    fun inputStream(): InputStream {
        val stringWriter = StringWriter()
        CSVWriter(
            stringWriter,
            ';',
            '"',
            '\\',
            "\n",
        ).use { csvWriter ->
            // Add CSV header
            csvWriter.writeNext(CsvHeader.headers)

            // Add data rows
            rows.forEach { row ->
                csvWriter.writeNext(
                    row.toTypedArray(),
                )
            }
        }
        val csvContent = stringWriter.toString().trim()
        val inputStream = ByteArrayInputStream(csvContent.toByteArray(StandardCharsets.UTF_8))
        return inputStream
    }

    fun extract(predicate: (VehicleRegistrationExportCSVRow) -> Boolean): VehicleRegistrationExportCSVRow? = rows.firstOrNull(predicate)
}
