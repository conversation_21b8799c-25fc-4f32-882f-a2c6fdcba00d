package com.fleetmanagement.modules.registrationmailexport.csv

import java.time.LocalDateTime

data class VehicleRegistrationExportCSVRow(
    val vin: String,
    val licensePlate: String,
    val hsn: String,
    val tsn: String,
    val vehicleType: String,
    val registrationType: String,
    val registrationDeregistrationDate: LocalDateTime?,
    val lastUpdateDate: LocalDateTime?,
) {
    fun toTypedArray(): Array<String> =
        listOf( // don't change the order as it is used in the CSV header
            lastUpdateDate.toString(),
            vin,
            licensePlate,
            hsn,
            tsn,
            vehicleType,
            registrationType,
            registrationDeregistrationDate.toString(),
        ).toTypedArray()
}

object CsvHeader {
    val headers: Array<String> =
        arrayOf(
            "Datum/Uhrzeit der letzten Änderung",
            "FIN",
            "Aktuelles Kennzeichen",
            "HSN",
            "TSN",
            "Fahrzeugart",
            "Zulassungsart",
            "Zulassungs-/Abmeldedatum",
        )
}
