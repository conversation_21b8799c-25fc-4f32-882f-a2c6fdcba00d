package com.fleetmanagement.modules.registrationmailexport.mailclient

import com.aspose.email.Attachment
import com.aspose.email.MailAddress
import com.aspose.words.Document
import com.aspose.words.HtmlSaveOptions
import com.aspose.words.SaveFormat
import com.fleetmanagement.integrations.mailclient.application.port.EmailDto
import com.fleetmanagement.integrations.mailclient.application.port.EmailOutPort
import com.fleetmanagement.modules.registrationmailexport.csv.VehicleRegistrationExportCSV
import org.apache.commons.io.output.ByteArrayOutputStream
import org.springframework.core.io.ClassPathResource
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter

class VehicleRegistrationExportMailClient(
    private val emailOutPort: EmailOutPort,
    private val emailSender: String,
    private val emailRecipient: String,
) {
    fun sendEmail(exportCSV: VehicleRegistrationExportCSV) {
        val inputStream = exportCSV.inputStream()
        val attachment = Attachment(inputStream, "${utcTimestampForFilename()}_Vehicle_Registration_Data.csv")

        sendEmailInternal(
            subject = "${attachment.name}",
            htmlBody = createEmailBodyFromTemplate(TEMPLATE),
            attachments = listOf(attachment),
        )
    }

    fun sendEmailWithoutAttachment() {
        sendEmailInternal(
            subject = "Keine neuen Zulassungsdaten",
            htmlBody = createEmailBodyFromTemplate(TEMPLATE_NO_ATTACHMENT),
            attachments = emptyList(),
        )
    }

    private fun sendEmailInternal(
        subject: String,
        htmlBody: String,
        attachments: List<Attachment>,
    ) {
        val emailDto =
            EmailDto(
                subject = subject,
                htmlBody = htmlBody,
                attachment = attachments,
                recipientsMailAddressInTo = listOf(MailAddress(emailRecipient)),
                recipientsMailAddressInCC = emptyList(),
                senderMailAddress = MailAddress(emailSender),
            )

        emailOutPort.sendEmailEncrypted(emailDto)
    }

    private fun createEmailBodyFromTemplate(templateName: String): String {
        val template = ClassPathResource("documentgeneration/templates/$templateName")
        return Document(template.inputStream).let {
            val outputStream = ByteArrayOutputStream()
            val htmlSaveOptions =
                HtmlSaveOptions(SaveFormat.HTML).apply {
                    exportImagesAsBase64 = true
                }
            it.save(outputStream, htmlSaveOptions)
            outputStream.toString(Charsets.UTF_8.name())
        }
    }

    companion object {
        private const val TEMPLATE = "RegistrationExportEmail.docx"
        private const val TEMPLATE_NO_ATTACHMENT = "RegistrationExportEmailNoAttachment.docx"
    }
}

private fun utcTimestampForFilename(): String {
    val formatter = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")
    val utcNow = ZonedDateTime.now(ZoneOffset.UTC)
    return utcNow.format(formatter)
}
