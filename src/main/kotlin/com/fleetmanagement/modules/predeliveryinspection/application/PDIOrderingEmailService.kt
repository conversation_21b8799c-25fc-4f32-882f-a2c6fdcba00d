/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.predeliveryinspection.application

import com.aspose.email.Attachment
import com.aspose.email.MailAddress
import com.aspose.words.Document
import com.aspose.words.HtmlSaveOptions
import com.aspose.words.SaveFormat
import com.fleetmanagement.emhshared.addWorkingDays
import com.fleetmanagement.integrations.mailclient.application.port.EmailDto
import com.fleetmanagement.integrations.mailclient.application.port.EmailException
import com.fleetmanagement.integrations.mailclient.application.port.EmailOutPort
import com.fleetmanagement.modules.documentgeneration.api.GenerateDocumentException
import com.fleetmanagement.modules.predeliveryinspection.application.port.SendPdiOrderingEmailUseCase
import com.fleetmanagement.modules.predeliveryinspection.application.port.UpdatePreDeliveryInspectionUseCase
import com.fleetmanagement.modules.predeliveryinspection.application.port.toPreDeliveryInspectionId
import com.fleetmanagement.modules.predeliveryinspection.domain.PreDeliveryInspection
import com.fleetmanagement.modules.predeliveryinspection.domain.PreDeliveryInspectionFutureDateException
import com.fleetmanagement.modules.predeliveryinspection.domain.PreDeliveryInspectionId
import com.fleetmanagement.modules.predeliveryinspection.domain.service.PreDeliveryInspectionUpdateService
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByVehicleId
import com.fleetmanagement.modules.vehicledata.api.dtos.VehicleDTO
import com.fleetmanagement.modules.vehiclelocation.api.LastKnownLocation
import com.fleetmanagement.modules.vehiclelocation.api.dtos.VehicleLocation
import com.fleetmanagement.modules.vehicleregistration.api.ReadRegistrationOrder
import com.opencsv.CSVWriter
import org.apache.commons.io.output.ByteArrayOutputStream
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.core.io.ClassPathResource
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional
import java.io.ByteArrayInputStream
import java.io.StringWriter
import java.nio.charset.StandardCharsets
import java.time.OffsetDateTime
import java.time.temporal.ChronoUnit
import java.util.*

@Component
@Transactional
class PDIOrderingEmailService(
    private val preDeliveryInspectionFinder: PreDeliveryInspectionFinder,
    private val preDeliveryInspectionUpdateService: PreDeliveryInspectionUpdateService,
    private val emailOutPort: EmailOutPort,
    private val readVehicleByVehicleId: ReadVehicleByVehicleId,
    private val lastKnownLocation: LastKnownLocation,
    private val readRegistrationOrder: ReadRegistrationOrder,
    private val pdiEmailAsyncService: PDIEmailAsyncService,
    @Value("\${pre-delivery-inspection.pdi-lead-time}") val pdiLeadTime: Long,
    @Value("\${pre-delivery-inspection.pdi-ordering.email.sender-email-address}") val senderEmailAddress: String,
    @Value("\${pre-delivery-inspection.pdi-ordering.email.recipient-to-email-address}") val recipientEmailAddressInTo: String,
    @Value("\${pre-delivery-inspection.pdi-ordering.email.recipient-cc-email-address}") val recipientEmailAddressInCC: String,
) : SendPdiOrderingEmailUseCase {
    /**
     * FPT1-962 The service provider receiving the PDIOrderingEmail expects that email to be sent only once per day. If
     * the email was sent and another vehicle is subsequently planned for PDI for that day there will be a warning and
     * no second email will be sent. Instead, a warning will flash on the UI stating that manual steps has to be taken to
     * subsequently register it for PDI.
     */
    fun createAndSendPDIOrderingEmail() {
        val preDeliveryInspectionDate = OffsetDateTime.now().addWorkingDays(pdiLeadTime)
        /**
         * FPT1-962 PDIOrderingEmail will only be sent once at 0:05 (or later if retried). When PDI was already ordered
         * for that day, for at least one vehicle, do not send another email.
         */
        if (pdiOrderingEmailHasBeenSentFor(preDeliveryInspectionDate)) {
            return
        }

        val preDeliveryInspection =
            preDeliveryInspectionFinder.findPreDeliveryInspectionForPDIOrderingEmail(preDeliveryInspectionDate)

        /**
         * create map with date as we are interested in sending emails per plannedDate (not plannedDateTime)
         */
        val mapOfPlannedDateToPreDeliveryInspection =
            preDeliveryInspection.groupBy {
                requireNotNull(
                    it.plannedDate,
                ).truncatedTo(ChronoUnit.DAYS)
            }
        mapOfPlannedDateToPreDeliveryInspection.entries.forEach { pdi ->
            try {
                // FPT1-1225 move update before sending email since validation could fail for single pdi
                pdi.value.forEach {
                    preDeliveryInspectionUpdateService.updateOrderedDate(it, OffsetDateTime.now())
                }
                val email = createPDIOrderingEmail(pdi.value)
                emailOutPort.sendEmail(email)
            } catch (exception: EmailException) {
                log.error(
                    "Error sending PDI ordering email for vehicle ${
                        pdi.value.map { it.vehicleId }.joinToString(",")
                    } ${exception.message}",
                )
            } catch (exception: PreDeliveryInspectionFutureDateException) {
                log.error(
                    "Unexpected error updating order date for vehicle ${
                        pdi.value.map { it.vehicleId }.joinToString(",")
                    } ${exception.message}",
                )
            }
        }
    }

    /**
     * Will create a PDI ordering email for given list of vehicles. Prepared email will be sent asynchronously.
     */
    override fun createAndSendPdiEmailsAsync(vehicleIds: List<UUID>): List<PdiValidationError> {
        val errors = mutableListOf<PdiValidationError>()

        val (preDeliveryInspectionsReadyForEmailCreation, preDeliveryInspectionWithoutPlannedDate) =
            preDeliveryInspectionFinder
                .findPreDeliveryInspectionByVehicleIds(vehicleIds)
                .partition { null != it.plannedDate }

        errors.addAll(
            preDeliveryInspectionWithoutPlannedDate.map {
                // fetch vehicle to provide VIN information in error message
                val vehicle = requireNotNull(readVehicleByVehicleId.readVehicleById(it.vehicleId))
                PdiValidationError(
                    identifier = it.id.value.toString(),
                    detail = "PreDeliveryInspection with id [${it.id}] does not have plannedDate set.",
                    type = PdiValidationError.ValidationErrorType.PDI_MISSING_PLANNED_DATE,
                    properties = mapOf(PdiValidationError.PROPERTY_VIN to requireNotNull(vehicle.vin)),
                )
            },
        )

        val plannedDateToPdis =
            preDeliveryInspectionsReadyForEmailCreation.groupBy {
                requireNotNull(it.plannedDate).truncatedTo(ChronoUnit.DAYS)
            }

        plannedDateToPdis.entries.forEach { plannedDateToPdi ->
            val email =
                try {
                    createPDIOrderingEmail(
                        plannedDateToPdi.value,
                    )
                } catch (exception: GenerateDocumentException) {
                    // fetch vehicles to provide VIN information in error message
                    val vehicles = readVehicleByVehicleId.readVehiclesByIds(plannedDateToPdi.value.map { it.vehicleId })
                    errors.add(
                        PdiValidationError(
                            identifier = plannedDateToPdi.value.joinToString { it.id.value.toString() },
                            detail = exception.message,
                            type = PdiValidationError.ValidationErrorType.PDI_EMAIL_GENERATION_FAILED,
                            properties =
                                mapOf(
                                    PdiValidationError.PROPERTY_VINS to
                                        vehicles
                                            .mapNotNull { it.vin }
                                            .joinToString(),
                                ),
                        ),
                    )
                    log.warn(
                        "Error while generating PDI ordering email for vehicles ${
                            plannedDateToPdi.value.map { it.vehicleId }.joinToString(",")
                        } ${exception.message}",
                    )
                    return@forEach
                }

            pdiEmailAsyncService.sendPdiEmailAndUpdateOrderedDate(
                pdiEmailToSent =
                    PDIEmailAsyncService.PdiAndEmail(
                        pdiIds = plannedDateToPdi.value.map { it.id },
                        email = email,
                        vehicleIds = plannedDateToPdi.value.map { it.vehicleId },
                    ),
            )
        }

        return errors.toList()
    }

    private fun pdiOrderingEmailHasBeenSentFor(preDeliveryInspectionDate: OffsetDateTime): Boolean =
        preDeliveryInspectionFinder
            .findPreDeliveryInspectionForPDIOrderingEmailThatHasBeenSent(preDeliveryInspectionDate)
            .isNotEmpty()

    private fun createPDIOrderingEmail(preDeliveryInspections: List<PreDeliveryInspection>): EmailDto {
        val emailBody = createEmailBody()
        val csvAttachment = createEmailCSVAttachment(preDeliveryInspections)

        /**
         * aws ecs cannot accept list of string as environment variables, so we use comma separated email address
         * and split them into list here
         */
        val listOfRecipientEmailAddressInTo = recipientEmailAddressInTo.split(",").map { it.trim() }
        val listOfRecipientEmailAddressInCC = recipientEmailAddressInCC.split(",").map { it.trim() }

        return EmailDto(
            subject = SUBJECT,
            htmlBody = emailBody,
            recipientsMailAddressInTo = listOfRecipientEmailAddressInTo.map { MailAddress(it) },
            recipientsMailAddressInCC = listOfRecipientEmailAddressInCC.map { MailAddress(it) },
            senderMailAddress = MailAddress(senderEmailAddress),
            attachment = listOf(csvAttachment),
        )
    }

    private fun createEmailCSVAttachment(preDeliveryInspection: List<PreDeliveryInspection>): Attachment {
        val registrationOrder =
            readRegistrationOrder.getLatestOrdersBy(preDeliveryInspection.map { it.vehicleId })
        val pdiOrderingEmailAttachmentData =
            preDeliveryInspection.map { pdi ->
                PDIOrderingEmailAttachmentData(
                    vehicle = readVehicleByVehicleId.readVehicleById(pdi.vehicleId),
                    lastKnownLocation = lastKnownLocation.findLastKnownVehicleLocationBy(pdi.vehicleId),
                    licensePlate = registrationOrder.data.singleOrNull { it.vehicleId == pdi.vehicleId }?.licencePlate,
                    pdi = pdi,
                )
            }

        val csvRowList = pdiOrderingEmailAttachmentData.map { it.toCSVDataRow() }
        val stringWriter = StringWriter()

        CSVWriter(
            stringWriter,
            ';',
            '"',
            '\\',
            "\n",
        ).use { csvWriter ->
            csvWriter.writeNext(CSV_HEADERS)
            csvRowList.forEach { data ->
                csvWriter.writeNext(
                    arrayOf(
                        data.vin,
                        data.model,
                        data.licencePlate,
                        data.compoundName,
                        data.eventType,
                        data.foiling,
                        data.refuel,
                        data.charge,
                        data.digitalLogbook,
                        data.licencePlateMounting,
                        data.comment,
                    ),
                )
            }
        }
        val csvContent = stringWriter.toString().trim()
        val inputStream = ByteArrayInputStream(csvContent.toByteArray(StandardCharsets.UTF_8))
        val attachment = Attachment(inputStream, "$ATTACHMENT_NAME.csv")
        return attachment
    }

    private fun createEmailBody(): String {
        val template = ClassPathResource("documentgeneration/templates/$TEMPLATE")
        return Document(template.inputStream).let {
            val outputStream = ByteArrayOutputStream()
            it.save(outputStream, HtmlSaveOptions(SaveFormat.HTML))
            outputStream.toString(Charsets.UTF_8.name())
        }
    }

    companion object {
        private val log = LoggerFactory.getLogger(PDIOrderingEmailService::class.java)
        private const val TEMPLATE = "PDIOrderEmail.docx"
        private const val SUBJECT = "Fahrzeugabruf für die Auslieferung"
        private const val ATTACHMENT_NAME = "Übersicht Abruf"
        private val CSV_HEADERS =
            arrayOf(
                "FIN",
                "Modell",
                "KFZ-Kennzeichen",
                "Compound",
                "Location Event",
                "Folierung",
                "Tanken",
                "Laden",
                "dFB",
                "Kennzeichenmontage",
                "PDI Kommentar",
            )
    }
}

/**
 * A version of the [PDIOrderingEmailService] thatM allows for asynchronous sending of PDI emails.
 */
@Component
class PDIEmailAsyncService(
    private val updatePreDeliveryInspectionUseCase: UpdatePreDeliveryInspectionUseCase,
    private val emailOutPort: EmailOutPort,
) {
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    fun sendPdiEmailAndUpdateOrderedDate(pdiEmailToSent: PdiAndEmail) {
        // send email
        try {
            emailOutPort.sendEmail(pdiEmailToSent.email)
        } catch (exception: EmailException) {
            log.error(
                "Error sending PDI ordering email for vehicle ${pdiEmailToSent.vehicleIds.joinToString()}",
                exception,
            )
        }
        // update respective PDI ordered dates
        val orderedDate = OffsetDateTime.now()
        pdiEmailToSent.pdiIds.forEach {
            updatePreDeliveryInspectionUseCase.updatePreDeliveryInspectionOrderedDate(
                id = it.toPreDeliveryInspectionId(),
                orderedDate = orderedDate,
            )
        }
    }

    companion object {
        private val log = LoggerFactory.getLogger(PDIOrderingEmailService::class.java)
    }

    data class PdiAndEmail(
        val pdiIds: Collection<PreDeliveryInspectionId>,
        val email: EmailDto,
        val vehicleIds: Collection<UUID>,
    )
}

data class PDIOrderingEmailAttachmentData(
    val vehicle: VehicleDTO?,
    val lastKnownLocation: VehicleLocation?,
    val licensePlate: String?,
    val pdi: PreDeliveryInspection,
)

data class CSVRow(
    val vin: String?,
    val model: String?,
    val licencePlate: String?,
    val compoundName: String?,
    val eventType: String?,
    val foiling: String?,
    val refuel: String?,
    val charge: String?,
    val digitalLogbook: String?,
    val licencePlateMounting: String?,
    val comment: String?,
)

private fun PDIOrderingEmailAttachmentData.toCSVDataRow() =
    CSVRow(
        vin = this.vehicle?.vin,
        model = this.vehicle?.model?.description,
        licencePlate = this.licensePlate,
        compoundName = this.lastKnownLocation?.compoundName,
        eventType = lastKnownLocation?.eventType,
        foiling = this.pdi.foiling?.toGermanText(),
        refuel = this.pdi.refuel?.toGermanText(),
        charge = this.pdi.charge?.toGermanText(),
        digitalLogbook = this.pdi.digitalLogbook?.toGermanText(),
        licencePlateMounting = this.pdi.licencePlateMounting?.toGermanText(),
        comment = this.pdi.comment,
    )

private fun Boolean.toGermanText(): String =
    when (this) {
        true -> "Ja"
        false -> "Nein"
    }
