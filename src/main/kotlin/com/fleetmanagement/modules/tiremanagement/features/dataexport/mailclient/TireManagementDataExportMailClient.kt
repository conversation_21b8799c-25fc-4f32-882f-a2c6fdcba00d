package com.fleetmanagement.modules.tiremanagement.features.dataexport.mailclient

import com.aspose.email.Attachment
import com.aspose.email.MailAddress
import com.fleetmanagement.integrations.mailclient.application.port.EmailDto
import com.fleetmanagement.integrations.mailclient.application.port.EmailOutPort
import com.fleetmanagement.modules.tiremanagement.TireManagementEmailConfigurationProperties
import com.fleetmanagement.modules.tiremanagement.features.dataexport.csv.TireManagementDataExportCSV
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter

class TireManagementDataExportMailClient(
    private val emailOutPort: EmailOutPort,
    private val configuration: TireManagementEmailConfigurationProperties,
) {
    fun sendEmail(exportCSV: TireManagementDataExportCSV) {
        val inputStream = exportCSV.inputStream()
        val attachment = Attachment(inputStream, "${utcTimestampForFilename()}_Stammdaten_Porsche_VIW.csv")

        val emailDto =
            EmailDto(
                subject = "${attachment.name}",
                htmlBody = "Anbei erhalten Sie den Export der Daten für das Reifenmanagement.",
                attachment = listOf(attachment),
                recipientsMailAddressInTo = listOf(MailAddress(configuration.recipient)),
                recipientsMailAddressInCC = emptyList(),
                senderMailAddress = MailAddress(configuration.sender),
            )

        emailOutPort.sendEmailEncrypted(emailDto)
    }
}

private fun utcTimestampForFilename(): String {
    val formatter = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")
    val utcNow = ZonedDateTime.now(ZoneOffset.UTC)
    return utcNow.format(formatter)
}
