/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.api

import com.fleetmanagement.modules.vehicledata.api.dtos.VehicleDTO
import com.fleetmanagement.modules.vehicledata.api.exceptions.VehicleNotFoundException
import java.util.UUID

interface ReadVehicleByAnyIdentifier {
    /**
     * Returns vehicle in FVM (Fleet-Vehicle-Management) system with
     * identifier [vehicleId], [vin], [vguid], [equipmentNumber] or [equiId]
     * with a fallback mechanism.
     *
     * Does not create or refresh the vehicles.
     *
     * @throws VehicleNotFoundException if a vehicle cannot be found in our system
     */
    fun readVehicle(
        vehicleId: UUID?,
        vin: String?,
        vguid: String?,
        equipmentNumber: Long?,
        equiId: String?,
    ): VehicleDTO
}
