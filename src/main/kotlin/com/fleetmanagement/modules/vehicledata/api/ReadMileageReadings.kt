/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehicledata.api

import com.fleetmanagement.modules.vehicledata.api.dtos.MileageReadingDTO
import java.util.UUID

interface ReadMileageReadings {
    /**
     * Returns all mileage readings for vehicle
     */
    fun readMileageReadings(vehicleId: UUID): List<MileageReadingDTO>

    /**
     * Will return the latest (sorted by read-date) mileage reading for given vehicle if any exists.
     */
    fun getLatestMileageReading(vehicleId: UUID): MileageReadingDTO?
}
