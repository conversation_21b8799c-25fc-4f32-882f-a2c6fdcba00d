/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.api

import com.fleetmanagement.modules.vehicledata.api.dtos.VehicleDTO

/**
 * Returns vehicle in FVM (Fleet-Vehicle-Management) system with
 * identifier [vin]
 *
 * Does not create vehicles, and should NOT create a vehicle in FVM
 *
 * @throws VehicleNotFoundInFVM if vehicle cannot be found in our system
 */
interface ReadVehicleByVIN {
    fun readVehicleByVIN(vin: String): VehicleDTO
}

class VehicleNotFoundInFVM(
    identifierType: String,
    identifier: Any,
) : RuntimeException("Vehicle with $identifierType=$identifier not found in FVM")
