/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.features.readvehicle.moduleapi

import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByAnyIdentifier
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByEquiId
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByEquipmentNumber
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByVGUID
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByVIN
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByVehicleId
import com.fleetmanagement.modules.vehicledata.api.dtos.VehicleDTO
import com.fleetmanagement.modules.vehicledata.api.exceptions.VehicleNotFoundException
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Transactional(readOnly = true)
@Service
class DefaultReadVehicleByAnyIdentifier(
    private val readVehicleByVehicleId: ReadVehicleByVehicleId,
    private val readVehicleByVIN: ReadVehicleByVIN,
    private val readVehicleByVGUID: ReadVehicleByVGUID,
    private val readVehicleByEquiId: ReadVehicleByEquiId,
    private val readVehicleByEquipmentNumber: ReadVehicleByEquipmentNumber,
) : ReadVehicleByAnyIdentifier {
    override fun readVehicle(
        vehicleId: UUID?,
        vin: String?,
        vguid: String?,
        equipmentNumber: Long?,
        equiId: String?,
    ): VehicleDTO {
        if (vehicleId == null &&
            vin.isNullOrBlank() &&
            vguid.isNullOrBlank() &&
            equipmentNumber == null &&
            equiId.isNullOrBlank()
        ) {
            throw IllegalArgumentException("At least one identifying parameter must be provided to find a vehicle.")
        }

        vehicleId?.let {
            runCatching { readVehicleByVehicleId.readVehicleById(it) }
                .getOrNull()
                ?.let { vehicle -> return vehicle }
        }

        vin?.takeIf { it.isNotBlank() }?.let {
            runCatching { readVehicleByVIN.readVehicleByVIN(it) }
                .getOrNull()
                ?.let { vehicle -> return vehicle }
        }

        vguid?.takeIf { it.isNotBlank() }?.let {
            runCatching { readVehicleByVGUID.readVehicleByVGUID(it) }
                .getOrNull()
                ?.let { vehicle -> return vehicle }
        }

        equipmentNumber?.let {
            runCatching { readVehicleByEquipmentNumber.readVehicleByEquipmentNumber(it) }
                .getOrNull()
                ?.let { vehicle -> return vehicle }
        }

        equiId?.takeIf { it.isNotBlank() }?.let {
            runCatching { readVehicleByEquiId.readVehicleByEquiId(it) }
                .getOrNull()
                ?.let { vehicle -> return vehicle }
        }

        throw VehicleNotFoundException("No vehicle found for any of the provided parameters.")
    }
}
