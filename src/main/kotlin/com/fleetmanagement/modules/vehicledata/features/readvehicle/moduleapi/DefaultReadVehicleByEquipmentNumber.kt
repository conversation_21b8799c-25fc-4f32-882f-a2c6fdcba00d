package com.fleetmanagement.modules.vehicledata.features.readvehicle.moduleapi

import com.fasterxml.jackson.databind.ObjectMapper
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByEquipmentNumber
import com.fleetmanagement.modules.vehicledata.api.VehicleNotFoundInFVM
import com.fleetmanagement.modules.vehicledata.api.dtos.VehicleDTO
import com.fleetmanagement.modules.vehicledata.repository.JPAVehicleRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
@Transactional(readOnly = true)
class DefaultReadVehicleByEquipmentNumber(
    private val jpaVehicleRepository: JPAVehicleRepository,
    private val objectMapper: ObjectMapper,
) : ReadVehicleByEquipmentNumber {
    override fun readVehicleByEquipmentNumber(equipmentNumber: Long): VehicleDTO {
        val vehicleEntity = jpaVehicleRepository.findByEquipmentNumber(equipmentNumber)
        return vehicleEntity
            .map { VehicleDTO.from(it, objectMapper) }
            .orElseThrow { VehicleNotFoundInFVM("EquipmentNumber", equipmentNumber) }
    }
}
