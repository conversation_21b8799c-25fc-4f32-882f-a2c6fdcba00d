/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.features.readvehicle

import com.fasterxml.jackson.databind.ObjectMapper
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByAnyIdentifier
import com.fleetmanagement.modules.vehicledata.api.dtos.VehicleDTO
import com.fleetmanagement.modules.vehicledata.api.exceptions.VehicleNotFoundException
import io.opentelemetry.instrumentation.annotations.SpanAttribute
import io.opentelemetry.instrumentation.annotations.WithSpan
import org.springframework.http.HttpStatus
import org.springframework.http.ProblemDetail
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.ExceptionHandler
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.net.URI
import java.net.URLDecoder
import java.nio.charset.StandardCharsets
import java.util.UUID

@RestController
@RequestMapping("/vehicles")
class VehicleReadController(
    private val readVehicleByAnyIdentifier: ReadVehicleByAnyIdentifier,
    private val vehicleDataService: DeprecatedVehicleDataReadService,
    private val objectMapper: ObjectMapper,
) {
    @WithSpan("read.vehicle")
    @GetMapping("/vin/{vin}")
    fun byVinNumber(
        @SpanAttribute("vin") @PathVariable vin: String,
    ): ResponseEntity<VehicleDTO> {
        val vehicleDataOrNull = vehicleDataService.readVehicleDataByVINOrCreateIfMissing(vin)
        return ResponseEntity.ok(VehicleDTO.from(vehicleDataOrNull, objectMapper))
    }

    @WithSpan("read.vehicle")
    @GetMapping("/equipmentNumber/{equipment_number}")
    fun byEquipmentNumber(
        @SpanAttribute("equipment_number") @PathVariable("equipment_number") equipmentNumber: Long,
    ): ResponseEntity<VehicleDTO> {
        val vehicleDataOrNull = vehicleDataService.readVehicleDataByEquipmentNumberOrCreateIfMissing(equipmentNumber)
        return ResponseEntity.ok(VehicleDTO.from(vehicleDataOrNull, objectMapper))
    }

    @WithSpan("read.vehicle")
    @GetMapping("/equiId/{equi_id}")
    fun byEquiId(
        @SpanAttribute("equi_id") @PathVariable("equi_id") equiId: String,
    ): ResponseEntity<VehicleDTO> {
        val vehicleDataOrNull = vehicleDataService.readVehicleDataByEquiIdOrCreateIfMissing(equiId)
        return ResponseEntity.ok(VehicleDTO.from(vehicleDataOrNull, objectMapper))
    }

    @WithSpan("read.vehicle")
    @GetMapping("/vguid/{vguid}")
    fun byVguid(
        @SpanAttribute("vguid") @PathVariable vguid: String,
    ): ResponseEntity<VehicleDTO> {
        val vehicleDataOrNull = vehicleDataService.readVehicleDataByVGUID(urlDecode(vguid))
        return ResponseEntity.ok(VehicleDTO.from(vehicleDataOrNull, objectMapper))
    }

    @WithSpan("read.vehicle")
    @PostMapping("/vguid")
    fun byVguids(
        @SpanAttribute("vguids") @RequestBody vguids: List<String>,
    ): ResponseEntity<List<VehicleDTO>> {
        val vehicleDataList =
            vehicleDataService.readVehicleDataByVGUIDs(vguids.map { urlDecode(it) }).map {
                VehicleDTO.from(it, objectMapper)
            }
        return ResponseEntity.ok(vehicleDataList)
    }

    @GetMapping("/find")
    fun readVehicle(
        @RequestParam(name = "vehicleId", required = false) vehicleId: UUID?,
        @RequestParam(name = "vin", required = false) vin: String?,
        @RequestParam(name = "vguid", required = false) vguid: String?,
        @RequestParam(name = "equipmentNumber", required = false) equipmentNumber: Long?,
        @RequestParam(name = "equiId", required = false) equiId: String?,
    ): ResponseEntity<VehicleDTO> {
        val vehicle = readVehicleByAnyIdentifier.readVehicle(vehicleId, vin, vguid, equipmentNumber, equiId)
        return ResponseEntity.ok(vehicle)
    }

    private fun urlDecode(s: String): String = URLDecoder.decode(s, StandardCharsets.UTF_8)

    @ExceptionHandler(VehicleNotFoundException::class)
    fun handleVehicleNotFoundException(ex: VehicleNotFoundException): ProblemDetail =
        ProblemDetail.forStatus(HttpStatus.NOT_FOUND).apply {
            detail = ex.message
            type = URI.create("error.vehicle.notFound")
        }

    @ExceptionHandler(IllegalArgumentException::class)
    fun handleIllegalArgumentException(ex: IllegalArgumentException): ProblemDetail =
        ProblemDetail.forStatus(HttpStatus.BAD_REQUEST).apply {
            detail = ex.message
            type = URI.create("error.vehicle.badRequest")
        }
}
