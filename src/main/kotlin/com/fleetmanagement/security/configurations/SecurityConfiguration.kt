/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.security.configurations

import com.auth0.jwk.UrlJwkProvider
import com.fleetmanagement.security.features.accesscontrol.services.PrivilegeService
import com.fleetmanagement.security.features.tokenvalidation.JwtValidator
import com.fleetmanagement.security.features.tokenvalidation.SecurityFilter
import com.fleetmanagement.security.features.tokenvalidation.alb.ALBTokenValidator
import com.fleetmanagement.security.features.tokenvalidation.alb.ALBTokenValidatorConfigurationProperties
import com.fleetmanagement.security.features.tokenvalidation.alb.accesstoken.AccessTokenJwtValidator
import com.fleetmanagement.security.features.tokenvalidation.alb.datatoken.DataTokenJwtValidator
import com.fleetmanagement.security.features.tokenvalidation.alb.entraid.clients.GuavaCachedAzureGroups
import com.fleetmanagement.security.features.tokenvalidation.apigateway.APIGatewayTokenValidator
import com.fleetmanagement.security.features.tokenvalidation.apigateway.APIGatewayTokenVerificationConfigurationProperties
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity
import org.springframework.security.config.annotation.web.builders.HttpSecurity
import org.springframework.security.web.SecurityFilterChain
import org.springframework.security.web.authentication.preauth.AbstractPreAuthenticatedProcessingFilter
import org.springframework.security.web.context.RequestAttributeSecurityContextRepository
import org.springframework.security.web.header.writers.ReferrerPolicyHeaderWriter
import java.net.URL

@Configuration
@ConditionalOnProperty("security.enabled", havingValue = "true", matchIfMissing = true)
@EnableMethodSecurity
class SecurityConfiguration {
    @Bean
    fun securityFilterChain(
        http: HttpSecurity,
        securityFilter: SecurityFilter,
    ): SecurityFilterChain {
        http
            .headers {
                it
                    .frameOptions { options ->
                        options.deny() // Prevent clickjacking
                    }.contentTypeOptions {}
                    .contentSecurityPolicy { csp ->
                        csp.policyDirectives("default-src 'self'; script-src 'self'") // Restrict script sources
                    }.referrerPolicy { referrerPolicy ->
                        referrerPolicy.policy(ReferrerPolicyHeaderWriter.ReferrerPolicy.NO_REFERRER)
                    }
            }.csrf { it.disable() }
            .sessionManagement { it.disable() }
            .authorizeHttpRequests {
                it.requestMatchers("/actuator/health").permitAll()
                it.requestMatchers("/vehicles/equipmentNumber/*").permitAll()
                it.requestMatchers("/vehicles/equiId/*").permitAll()
                it.requestMatchers("/vehicles/vin/*").permitAll() // Skip IBM API Gateway endpoint
                it.requestMatchers("/vehicles/vguid/*").permitAll() // Skip IBM API Gateway endpoint
                it.requestMatchers("/vehicles/vguid").permitAll() // Skip IBM API Gateway endpoint
                it.requestMatchers("/vehicles/find").permitAll() // Skip IBM API Gateway endpoint
                it.requestMatchers("/fms/data/load").permitAll() // Skip IBM API Gateway endpoint
                it.requestMatchers("/ecc/*").permitAll() // Skip IBM API Gateway endpoint
                it.requestMatchers("/vehicletransfer/**").permitAll() // Skip IBM API Gateway endpoint
                it.requestMatchers("/vtstamm/**").permitAll() // Skip IBM API Gateway endpoint
                it.requestMatchers("/private/legal-hold/**").permitAll() // private endpoints
                it.requestMatchers("/private/kafka/**").permitAll() // private endpoints
                it.anyRequest().authenticated()
            }.addFilterBefore(
                securityFilter,
                AbstractPreAuthenticatedProcessingFilter::class.java,
            )

        return http.build()
    }

    @Bean
    fun albTokenValidator(
        accessTokenJwtValidator: AccessTokenJwtValidator,
        dataTokenJwtValidator: DataTokenJwtValidator,
        privilegeService: PrivilegeService,
        azureGroups: GuavaCachedAzureGroups,
        configurationProperties: ALBTokenValidatorConfigurationProperties,
    ): JwtValidator =
        ALBTokenValidator(
            accessTokenJwtValidator,
            dataTokenJwtValidator,
            privilegeService,
            azureGroups,
            configurationProperties.toggles.allowOverwriteOfGroupsFromRequestHeader,
        )

    @Bean
    fun requestAttributeSecurityContextRepository(): RequestAttributeSecurityContextRepository = RequestAttributeSecurityContextRepository()

    @Bean
    fun apiGatewayTokenValidator(configuration: APIGatewayTokenVerificationConfigurationProperties): JwtValidator =
        APIGatewayTokenValidator(
            config = configuration,
            urlJwkProvider = UrlJwkProvider(URL(configuration.jwksUri)),
        )
}
