/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.db.maintenance

import org.quartz.CronScheduleBuilder
import org.quartz.JobBuilder
import org.quartz.JobDetail
import org.quartz.Trigger
import org.quartz.TriggerBuilder
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import java.time.ZoneOffset
import java.util.TimeZone

/**
 * Configuration class for database maintenance job scheduling.
 *
 * This configuration sets up the Quartz job and trigger for automated database maintenance.
 * The job is scheduled to run on the first day of the month at 2:00 AM UTC to perform vacuum and analyze operations
 * during low-traffic hours to minimize the impact on application performance.
 */
@Configuration
class DatabaseMaintenanceJobConfig {
    /**
     * Creates the job detail for the database maintenance job.
     *
     * This bean defines the job configuration including its identity, description,
     * and durability settings. The job is stored durably, meaning it will persist
     * even if there are no triggers associated with it.
     *
     * @return JobDetail configured for database maintenance operations
     */
    @Bean("databaseMaintenanceJobDetail")
    fun databaseMaintenanceJobDetail(): JobDetail =
        JobBuilder
            .newJob()
            .ofType(DatabaseMaintenanceJob::class.java)
            .withIdentity("DatabaseMaintenanceJobDetail")
            .withDescription("Database Maintenance job detail")
            .storeDurably()
            .build()

    /**
     * Creates the trigger for scheduling the database maintenance job.
     *
     * This bean configures a cron trigger that executes the database maintenance job
     * on the first day of the month at 2:00 AM UTC. The UTC timezone ensures consistent execution time
     * regardless of server location or daylight-saving time changes.
     *
     * @param databaseMaintenanceJobDetail The job detail to associate with this trigger
     * @return Trigger configured to execute the job daily at 2:00 AM UTC
     */
    @Bean("databaseMaintenanceJobTrigger")
    fun databaseMaintenanceJobTrigger(databaseMaintenanceJobDetail: JobDetail): Trigger =
        TriggerBuilder
            .newTrigger()
            .forJob(databaseMaintenanceJobDetail)
            .withIdentity("databaseMaintenanceJobDetail")
            .withDescription("Database maintenance job trigger")
            .withSchedule(
                CronScheduleBuilder
                    .cronSchedule("0 0 2 1 * ?")
                    .inTimeZone(TimeZone.getTimeZone(ZoneOffset.UTC)),
            ).build()
}
