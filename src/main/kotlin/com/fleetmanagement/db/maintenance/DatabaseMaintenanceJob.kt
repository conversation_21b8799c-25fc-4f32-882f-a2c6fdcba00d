/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.db.maintenance

import org.quartz.Job
import org.quartz.JobExecutionContext
import org.slf4j.LoggerFactory
import org.springframework.jdbc.core.JdbcTemplate
import org.springframework.stereotype.Component

/**
 * Database maintenance job that performs regular vacuum and analyze operations.
 *
 * This job executes Postgres SQL's VACUUM ANALYZE command to optimize database performance
 * by reclaiming storage space and updating table statistics for the query planner.
 *
 * @property jdbcTemplate The JDBC template for executing database operations
 * @constructor Creates a database maintenance job with the specified JDBC template
 */
@Component
class DatabaseMaintenanceJob(
    private val jdbcTemplate: JdbcTemplate,
) : Job {
    companion object {
        private val logger = LoggerFactory.getLogger(DatabaseMaintenanceJob::class.java)
        private const val VACUUM_ANALYZE_SQL = "VACUUM ANALYZE"
    }

    /**
     * Executes the database maintenance operations.
     *
     * This method performs a VACUUM ANALYZE operation on the database, which:
     * - Reclaims storage space occupied by dead tuples
     * - Updates table statistics for the query planner
     * - Improves overall database performance
     *
     * The operation is executed using Spring's JdbcTemplate for proper connection
     * management and transaction handling.
     *
     * @param context The job execution context provided by Quartz scheduler
     */
    override fun execute(context: JobExecutionContext) {
        logger.info("Starting database maintenance job - VACUUM ANALYZE operation")

        try {
            val startTime = System.currentTimeMillis()
            jdbcTemplate.execute(VACUUM_ANALYZE_SQL)
            val duration = System.currentTimeMillis() - startTime
            logger.info("VACUUM ANALYZE completed successfully in {} ms", duration)
        } catch (e: Exception) {
            logger.error("Unexpected error during VACUUM ANALYZE operation", e)
            throw e
        }
    }
}
