/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.integrations.mailclient.adapter

import com.aspose.email.MailMessage
import com.aspose.email.SecurityOptions
import com.aspose.email.SmtpClient
import com.aspose.email.system.exceptions.Exception
import com.fleetmanagement.integrations.mailclient.application.port.EmailDto
import com.fleetmanagement.integrations.mailclient.application.port.EmailException
import com.fleetmanagement.integrations.mailclient.application.port.EmailOutPort
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component

@Component
class EmailAdapter(
    @Value("\${mailclient.smtp.host}") val smtpHost: String,
    @Value("\${mailclient.smtp.port}") val smtpPort: Int,
    @Value("\${mailclient.smtp.username}") val smtpUsername: String,
    @Value("\${mailclient.smtp.password}") val smtpPassword: String,
    private val emailRecipientResolver: EmailRecipientResolver,
) : EmailOutPort {
    private lateinit var client: SmtpClient

    init {
        try {
            client =
                SmtpClient(smtpHost, smtpPort, smtpUsername, smtpPassword, SecurityOptions.Auto.toInt())
        } catch (ex: Exception) {
            throw EmailException(message = "Error sending email ${ex.message}", cause = ex)
        }
    }

    override fun sendEmail(emailDto: EmailDto) {
        val message = prepareMailMessage(emailDto)
        try {
            client.send(message)
        } catch (ex: Exception) {
            throw EmailException(message = "Error sending email ${ex.message}", cause = ex)
        }
    }

    override fun sendEmailEncrypted(emailDto: EmailDto) {
        val encryptedEmailDto = emailDto.copy(subject = "#crypt_*${emailDto.subject}")
        sendEmail(encryptedEmailDto)
    }

    private fun prepareMailMessage(emailDto: EmailDto): MailMessage =
        MailMessage().apply {
            subject = emailDto.subject
            htmlBody = emailDto.htmlBody
            setFrom(emailDto.senderMailAddress)
            emailRecipientResolver.resolveToRecipients(emailDto).forEach {
                to.addMailAddress(it)
            }
            emailRecipientResolver.resolveCcRecipients(emailDto).forEach {
                getCC().addMailAddress(it)
            }
            emailDto.attachment.forEach {
                attachments.addItem(it)
            }
        }
}
