/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.integrations.mailclient.application.port

import com.aspose.email.Attachment
import com.aspose.email.MailAddress

/**
 * sends list of provided emails
 * @throws EmailException in case of error sending email
 */
interface EmailOutPort {
    fun sendEmail(emailDto: EmailDto)

    fun sendEmailEncrypted(emailDto: EmailDto)
}

data class EmailDto(
    val subject: String,
    val htmlBody: String,
    val attachment: List<Attachment>,
    val recipientsMailAddressInTo: List<MailAddress>,
    val recipientsMailAddressInCC: List<MailAddress>,
    val senderMailAddress: MailAddress,
)

class EmailException(
    override val message: String,
    override val cause: Throwable? = null,
) : RuntimeException()
