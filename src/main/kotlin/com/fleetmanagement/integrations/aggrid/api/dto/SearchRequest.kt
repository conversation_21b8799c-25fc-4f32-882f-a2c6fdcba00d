/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.integrations.aggrid.api.dto

import com.fleetmanagement.integrations.aggrid.repository.DataManagerViewRowFilterSpecifications
import org.slf4j.LoggerFactory
import org.springframework.data.domain.Sort
import org.springframework.data.jpa.domain.Specification

data class SearchRequest(
    val startRow: Int,
    val endRow: Int,
    val sortModel: List<AgGridSortModel> = emptyList(),
    val filterModel: Map<String, AgGridFilterModel> = emptyMap(),
    val columns: List<AgGridColumnState> = emptyList(),
) {
    companion object {
        private val logger = LoggerFactory.getLogger(SearchRequest::class.java)
    }

    fun <T> toSpecification(specifications: DataManagerViewRowFilterSpecifications): Specification<T> {
        logger
            .atInfo()
            .setMessage("Search endpoint called with parameters")
            .addKeyValue("filters", filterModel.keys)
            .addKeyValue("sort", sortModel.map { it.colId })
            .log()

        val jpaSpecifications =
            filterModel.mapNotNull {
                it.value.toSpecification<T>(it.key, specifications)
            }
        val combinedJPASpecification =
            when (jpaSpecifications.isNotEmpty()) {
                true -> Specification.allOf(jpaSpecifications)
                false -> Specification.where(null)
            }
        return combinedJPASpecification
    }

    fun toSort(): Sort =
        if (sortModel.isNotEmpty()) {
            val sorts =
                sortModel.map {
                    val colId = it.colId
                    when (it.sort.lowercase()) {
                        "asc" -> Sort.by(Sort.Order.asc(colId))
                        else -> Sort.by(Sort.Order.desc(colId))
                    }
                }
            sorts.reduce { acc, spec -> acc.and(spec) }
        } else {
            Sort.unsorted()
        }
}
