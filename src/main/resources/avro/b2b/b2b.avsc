{"type": "record", "name": "KafkaCompletedAuction", "namespace": "com.porsche.b2b.entities", "fields": [{"name": "vin", "type": "string", "doc": "Vehicle Identification Number"}, {"name": "auctionId", "type": "string", "doc": "ID of the completed auction"}, {"name": "customerVehiclePrice", "type": "double", "doc": "Price at which the vehicle is sold to the customer"}, {"name": "customer", "type": {"type": "record", "name": "Customer", "fields": [{"name": "name", "type": "string"}, {"name": "surname", "type": "string"}, {"name": "companyName", "type": "string"}, {"name": "debtorId", "type": "string"}]}, "doc": "Details of the dealer"}, {"name": "purchaseContractDate", "type": {"type": "int", "logicalType": "date"}}, {"name": "saleDate", "type": {"type": "int", "logicalType": "date"}}, {"name": "invoiceDate", "type": {"type": "int", "logicalType": "date"}}, {"name": "sellerId", "type": "string", "doc": "ID of the seller who prepared the auction", "default": ""}]}