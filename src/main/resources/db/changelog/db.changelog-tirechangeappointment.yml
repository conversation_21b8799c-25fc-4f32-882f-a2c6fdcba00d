databaseChangeLog:
  - changeSet:
      id: create-tire-change-entry
      author: sonia balodi
      changes:
        - createTable:
            tableName: tire_change_entry
            schemaName: tirechangeappointment
            columns:
              - column:
                  name: id
                  type: UUID
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: msbookings_appointment_id
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: msbookings_appointment_date
                  type: timestamp
                  constraints:
                    nullable: false
              - column:
                  name: license_plate
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: first_name
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: last_name
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: vin
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: should_update_rela
                  type: boolean
                  constraints:
                    nullable: false
              - column:
                  name: rela_order_number
                  type: varchar
              - column:
                  name: created_by
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: created_date
                  type: timestamp
                  constraints:
                    nullable: false
              - column:
                  name: last_modified_by
                  type: varchar
              - column:
                  name: last_modified_date
                  type: timestamp
  - changeSet:
      id: create-index-tire-change-entry-rela-order-number
      author: sonia balodi
      changes:
        - createIndex:
            tableName: tire_change_entry
            schemaName: tirechangeappointment
            indexName: idx_tce_ron_id
            columns:
              - column:
                  name: rela_order_number
      rollback:
        - dropIndex:
            tableName: tire_change_entry
            schemaName: tirechangeappointment
            indexName: idx_tce_ron_id
  - changeSet:
      id: create-index-tire-change-entry-msbookings-appointment-id
      author: sonia balodi
      changes:
        - createIndex:
            tableName: tire_change_entry
            schemaName: tirechangeappointment
            indexName: idx_tce_mbaid_id
            columns:
              - column:
                  name: msbookings_appointment_id
      rollback:
        - dropIndex:
            tableName: tire_change_entry
            schemaName: tirechangeappointment
            indexName: idx_tce_mbaid_id
  - changeSet:
      id: create-index-tire-change-entry-vin
      author: sonia balodi
      changes:
        - createIndex:
            tableName: tire_change_entry
            schemaName: tirechangeappointment
            indexName: idx_tce_v_id
            columns:
              - column:
                  name: vin
      rollback:
        - dropIndex:
            tableName: tire_change_entry
            schemaName: tirechangeappointment
            indexName: idx_tce_v_id
  - changeSet:
      id: add-unique-constraint-msbookings-appointment-id-tire-change-entry
      author: sonia balodi
      changes:
        - addUniqueConstraint:
            tableName: tire_change_entry
            columnNames: msbookings_appointment_id
            constraintName: uq_msbookings_appointment_id
            schemaName: tirechangeappointment
  - changeSet:
      id: add-email-column-to-tire-change-entry
      author: sonia balodi
      changes:
        - addColumn:
            tableName: tire_change_entry
            schemaName: tirechangeappointment
            columns:
              - column:
                  name: email
                  type: varchar
                  constraints:
                    nullable: false
  - changeSet:
      id: add-vehicle-info-columns-to-tire-change-entry
      author: timo pfaff
      changes:
        - addColumn:
            tableName: tire_change_entry
            schemaName: tirechangeappointment
            columns:
              - column:
                  name: order_type
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: model_description
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: pccb_code
                  type: varchar
                  constraints:
                    nullable: true
              - column:
                  name: wheel_code
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: cwl_code
                  type: varchar
                  constraints:
                    nullable: true
              - column:
                  name: ras_code
                  type: varchar
                  constraints:
                    nullable: true
              - column:
                  name: pccb_description
                  type: varchar
                  constraints:
                    nullable: false
