databaseChangeLog:
  - changeSet:
      id: create-vehicle-sale-table
      author: <PERSON>
      changes:
        - createTable:
            schemaName: vehiclesales
            tableName: vehicle_sale
            columns:
              - column:
                  name: id
                  type: UUID
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: vehicle_id
                  type: uuid
                  constraints:
                    nullable: false
              - column:
                  name: reserved_for_b2c
                  type: boolean
              - column:
                  name: contract_signed
                  type: boolean
              - column:
                  name: planned_delivery_date
                  type: timestamp(6)
              - column:
                  name: comment
                  type: text
              - column:
                  name: customer_delivery_date
                  type: timestamp(6)
  - changeSet:
      id: add-clean-up-for-deprecated-postgres-views
      author: <PERSON><PERSON><PERSON>
      runAlways: true
      changes:
        - sqlFile:
            relativeToChangelogFile: true
            path: 'vehiclesales/clean-up-deprecated-postgres-views.sql'
            splitStatements: false
            endDelimiter: '$$;'
  - changeSet:
      id: set-vehicle-sale-postgres-view-for-vehicle-manager
      author: <PERSON>
      run<PERSON>: last
      runAlways: false
      runOnChange: true
      changes:
        - sqlFile:
            relative<PERSON><PERSON>gFile: true
            path: 'vehiclesales/setup-postgres-view-for-vehicle-manager.sql'
            stripComments: true
  - changeSet:
      id: add-created-at-and-updated-at-columns
      author: Komal Shevale
      changes:
        - addColumn:
            tableName: vehicle_sale
            schemaName: vehiclesales
            columns:
              - column:
                  name: created_at
                  type: timestamptz
              - column:
                  name: updated_at
                  type: timestamptz
  - changeSet:
      id: create-vehicle-invoice-table
      author: Karthik Narahari
      changes:
        - createTable:
            tableName: vehicle_invoice
            schemaName: vehiclesales
            columns:
              - column:
                  name: id
                  type: UUID
                  constraints:
                    primaryKey: true
                    nullable: false
                    unique: true
              - column:
                  name: vehicle_id
                  type: UUID
                  constraints:
                    nullable: false
              - column:
                  name: sales_net_price_eur
                  type: DECIMAL(19,2)
                  constraints:
                    nullable: false
              - column:
                  name: sales_net_price_after_discount_eur
                  type: DECIMAL(19,2)
                  constraints:
                    nullable: false
              - column:
                  name: winter_tires_net_price_eur
                  type: DECIMAL(19,2)
                  constraints:
                    nullable: true
              - column:
                  name: winter_tires_id
                  type: BIGINT
                  constraints:
                    nullable: true
              - column:
                  name: customer_partner_number
                  type: VARCHAR(10)
                  constraints:
                    nullable: false
              - column:
                  name: customer_invoice_recipient
                  type: BOOLEAN
                  defaultValueBoolean: true
                  constraints:
                    nullable: false
              - column:
                  name: sales_person_number
                  type: VARCHAR(8)
                  constraints:
                    nullable: false
              - column:
                  name: vip_discount
                  type: BOOLEAN
                  defaultValueBoolean: false
                  constraints:
                    nullable: false
              - column:
                  name: discount_percentage
                  type: FLOAT
                  defaultValueNumeric: 0.0
                  constraints:
                    nullable: false
              - column:
                  name: transaction_id
                  type: VARCHAR(35)
                  constraints:
                    nullable: false
                    unique: true
              - column:
                  name: reference_transaction_id
                  type: VARCHAR(35)
                  constraints:
                    nullable: true
              - column:
                  name: invoice_recipient_number
                  type: VARCHAR(10)
                  constraints:
                    nullable: false
              - column:
                  name: payment_type
                  type: VARCHAR(50)
                  constraints:
                    nullable: false
              - column:
                  name: transaction_type
                  type: VARCHAR(50)
                  constraints:
                    nullable: false
              - column:
                  name: invoice_date
                  type: TIMESTAMPTZ
                  constraints:
                    nullable: true
              - column:
                  name: invoice_status
                  type: VARCHAR(50)
                  constraints:
                    nullable: false
              - column:
                  name: receipt_number
                  type: TEXT
                  constraints:
                    nullable: true
              - column:
                  name: invoice_number
                  type: TEXT
                  constraints:
                    nullable: true
              - column:
                  name: error_message
                  type: VARCHAR(255)
                  constraints:
                    nullable: true
              - column:
                  name: payment_received
                  type: BOOLEAN
                  defaultValueBoolean: false
                  constraints:
                    nullable: false
              - column:
                  name: customer_delivery_date
                  type: TIMESTAMPTZ
                  constraints:
                    nullable: true
              - column:
                  name: vehicle_sale_id
                  type: UUID
                  constraints:
                    nullable: false
              - column:
                  name: created_at
                  type: TIMESTAMPTZ
                  constraints:
                    nullable: false
              - column:
                  name: updated_at
                  type: TIMESTAMPTZ
                  constraints:
                    nullable: false
              - column:
                  name: is_current
                  type: boolean
                  valueBoolean: "false"
                  constraints:
                    nullable: false

        - addForeignKeyConstraint:
            constraintName: fk_vehicle_invoice_vehicle_sale
            baseTableName: vehicle_invoice
            baseColumnNames: vehicle_sale_id
            baseTableSchemaName: vehiclesales
            referencedTableName: vehicle_sale
            referencedColumnNames: id
            referencedTableSchemaName: vehiclesales
  - changeSet:
      id: drop-customer-delivery-date-if-exist-from-vehicle-sales-table
      author: Karthik Narahari
      failOnError: false
      preConditions:
        - onFail: CONTINUE
        - columnExists:
            columnName: customer_delivery_date
            schemaName: vehiclesales
            tableName: vehicle_sale
      comment: check if the customer_delivery_date exists then drop it
      changes:
        - dropColumn:
            schemaName: vehiclesales
            tableName: vehicle_sale
            columns:
              - column:
                  name: customer_delivery_date
  - changeSet:
      id: add-winter-tires-discount-and-discount-type
      author: Karthik Narahari
      changes:
        - addColumn:
            tableName: vehicle_invoice
            schemaName: vehiclesales
            columns:
              - column:
                  name: winter_tires_discount_percentage
                  type: FLOAT
                  defaultValueNumeric: 0.0
                  constraints:
                    nullable: false
              - column:
                  name: sales_discount_percentage
                  type: FLOAT
                  defaultValueNumeric: 0.0
                  constraints:
                    nullable: false
              - column:
                  name: winter_tires_net_price_after_discount_eur
                  type: DECIMAL(19,2)
              - column:
                  name: sales_discount_type
                  type: VARCHAR(50)
                  constraints:
                    nullable: false
  - changeSet:
      id: drop-vip_discount-discount_percentage-if-exist-from-vehicle-invoice-table
      author: Karthik Narahari
      failOnError: false
      preConditions:
        - onFail: CONTINUE
        - columnExists:
            columnName: vip_discount
            schemaName: vehiclesales
            tableName: vehicle_invoice
        - columnExists:
            columnName: discount_percentage
            schemaName: vehiclesales
            tableName: vehicle_invoice
      comment: check if the vip_discount and discount_percentage exists then drop it
      changes:
        - dropColumn:
            schemaName: vehiclesales
            tableName: vehicle_invoice
            columns:
              - column:
                  name: vip_discount
              - column:
                  name: discount_percentage
  - changeSet:
      id: add-final-invoice-number-column
      author: Karthik Narahari
      changes:
        - addColumn:
            tableName: vehicle_invoice
            schemaName: vehiclesales
            columns:
              - column:
                  name: final_invoice_number
                  type: TEXT
                  constraints:
                    nullable: true

  - changeSet:
      id: add-loading_completed_date
      author: Anji Yarramreddy
      changes:
        - addColumn:
            schemaName: vehiclesales
            tableName: vehicle_sale
            columns:
              - column:
                  name: loading_completed_date
                  type: timestamp(6)
                  constraints:
                    nullable: true
  - changeSet:
      id: add-reserved_for_b2b-column
      author: Karthik Narahari
      changes:
        - addColumn:
            tableName: vehicle_sale
            schemaName: vehiclesales
            columns:
              - column:
                  name: reserved_for_b2b
                  type: boolean
                  constraints:
                    nullable: true
  - changeSet:
      id: add-auction_id-column
      author: Karthik Narahari
      changes:
        - addColumn:
            tableName: vehicle_invoice
            schemaName: vehiclesales
            columns:
              - column:
                  name: auction_id
                  type: TEXT
                  constraints:
                    nullable: true
  - changeSet:
      id: add-index-vehicle-invoice-join-performance
      author: Karthik Narahari
      changes:
        - createIndex:
            indexName: idx_vehicle_invoice_join_filter
            schemaName: vehiclesales
            tableName: vehicle_invoice
            columns:
              - column:
                  name: vehicle_sale_id
              - column:
                  name: invoice_status
              - column:
                  name: is_current
