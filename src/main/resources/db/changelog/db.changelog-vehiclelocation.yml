databaseChangeLog:
  - changeSet:
      id: create-location-table
      author: alon cohn
      changes:
        - createTable:
            schemaName: vehiclelocation
            tableName: location
            columns:
              - column:
                  name: id
                  type: uuid
                  constraints:
                    nullable: false
                    primaryKey: true
                    primaryKeyName: pk_location
              - column:
                  name: location_key
                  type: varchar
                  constraints:
                    nullable: false
                    unique: true
              - column:
                  name: name
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: compound_id
                  type: varchar
                  constraints:
                    nullable: true
              - column:
                  name: building
                  type: varchar
                  constraints:
                    nullable: true
              - column:
                  name: parking
                  type: varchar
                  constraints:
                    nullable: true
              - column:
                  name: level
                  type: varchar
                  constraints:
                    nullable: true
      rollback:
        - dropTable:
            schemaName: vehiclelocation
            tableName: location
  - changeSet:
      id: create-last-known-location-table
      author: alon cohn
      changes:
        - createTable:
            schemaName: vehiclelocation
            tableName: last_known_location
            columns:
              - column:
                  name: id
                  type: uuid
                  constraints:
                    nullable: false
                    primaryKey: true
                    primaryKeyName: pk_last_known_location
              - column:
                  name: vehicle_id
                  type: uuid
                  constraints:
                    nullable: false
                    unique: true
              - column:
                  name: location_key
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: occurred_on
                  type: timestamptz
                  constraints:
                    nullable: false
              - column:
                  name: event_source
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: event_type
                  type: varchar
                  constraints:
                    nullable: false
      rollback:
        - dropTable:
            schemaName: vehiclelocation
            tableName: last_known_location
  - changeSet:
      id: create-fk-location-last-known-location
      author: alon cohn
      changes:
        - addForeignKeyConstraint:
            baseTableName: last_known_location
            baseColumnNames: location_key
            baseTableSchemaName: vehiclelocation
            referencedTableName: location
            referencedColumnNames: location_key
            constraintName: fk_last_known_location_location_key
            referencedTableSchemaName: vehiclelocation

      rollback:
        - dropForeignKeyConstraint:
            constraintName: fk_last_known_location_location_key
            baseTableName: last_known_location
  - changeSet:
      id: create-vehicle-id-index
      author: alon cohn
      changes:
        - createIndex:
            tableName: last_known_location
            schemaName: vehiclelocation
            indexName: idx_last_known_location_vehicle_id
            unique: true
            columns:
              - column:
                  name: vehicle_id
      rollback:
        - dropIndex:
            tableName: last_known_location
            schemaName: vehiclelocation
            indexName: idx_last_known_location_vehicle_id
  - changeSet:
      id: drop-deprecated-vehicle-location-views-if-exists
      failOnError: false
      runAlways: true
      author: Alon Cohn
      changes:
        - sql:
            sql: DROP VIEW IF EXISTS vehiclelocation.vehicle_manager_last_known_location_deprecated;
  - changeSet:
      id: add-location-of-interest-column
      author: ivan plese
      changes:
        - addColumn:
            tableName: location
            schemaName: vehiclelocation
            columns:
              - column:
                  name: location_of_interest
                  type: BOOLEAN
                  defaultValueBoolean: false
                  constraints:
                    nullable: false
      rollback:
        - dropColumn:
            columnName: location_of_interest
            tableName: location
            schemaName: vehiclelocation
  - changeSet:
      id: add-inactive-column
      author: ivan plese
      changes:
        - addColumn:
            tableName: location
            schemaName: vehiclelocation
            columns:
              - column:
                  name: inactive
                  type: BOOLEAN
                  valueBoolean: "false"
                  constraints:
                    nullable: false
      rollback:
        - dropColumn:
            columnName: inactive
            tableName: location
            schemaName: vehiclelocation
  - changeSet:
      id: truncate-table
      author: ivan plese
      changes:
        - sql:
            sql: "TRUNCATE TABLE vehiclelocation.last_known_location"
  - changeSet:
      id: remove-fk-constraint-to-location
      author: ivan plese
      changes:
        - dropForeignKeyConstraint:
            baseTableName: last_known_location
            baseTableSchemaName: vehiclelocation
            constraintName: fk_last_known_location_location_key
      rollback:
        - addForeignKeyConstraint:
            baseTableName: last_known_location
            baseColumnNames: location_key
            baseTableSchemaName: vehiclelocation
            referencedTableName: location
            referencedColumnNames: location_key
            constraintName: fk_last_known_location_location_key
            referencedTableSchemaName: vehiclelocation
  - changeSet:
      id: add-location-reference-fields
      author: ivan plese
      changes:
        - addColumn:
            tableName: last_known_location
            schemaName: vehiclelocation
            columns:
              - column:
                  name: location_name
                  type: varchar
              - column:
                  name: building
                  type: varchar
              - column:
                  name: level
                  type: varchar
              - column:
                  name: parking_lot
                  type: varchar
              - column:
                  name: comment
                  type: varchar
              - column:
                  name: geo_location_latitude
                  type: double
                  constraints:
                    nullable: false
              - column:
                  name: geo_location_longitude
                  type: double
                  constraints:
                    nullable: false
  - changeSet:
      id: delete-location-data
      author: sonia balodi
      changes:
        - delete:
            tableName: location
            schemaName: vehiclelocation
  - changeSet:
      id: break-dependencies-of-existing-vehicle-manager-last-known-locaion-view
      author: sonia balodi
      runAlways: false
      failOnError: true
      changes:
        - sql:
            sql: ALTER VIEW IF EXISTS vehiclelocation.vehicle_manager_last_known_location RENAME TO vehicle_manager_last_known_location_deprecated;
        - sql:
            sql: CREATE OR REPLACE VIEW vehiclelocation.vehicle_manager_last_known_location_deprecated AS SELECT NULL::UUID as "vehicle_id", NULL::VARCHAR as "compound_name", NULL::VARCHAR as "building", NULL::VARCHAR as "parking_lot", NULL::VARCHAR as "level", NULL::VARCHAR as "event_type", NULL::TIMESTAMPTZ as "occurred_on"
  - changeSet:
      id: location-name-unique-constraint
      author: sonia balodi
      changes:
        - addUniqueConstraint:
            columnNames: name
            schemaName: vehiclelocation
            tableName: location
  - changeSet:
      id: add-geolocation
      author: sonia balodi
      changes:
        - addColumn:
            tableName: location
            schemaName: vehiclelocation
            columns:
              - column:
                  name: latitude
                  type: double precision
                  constraints:
                    nullable: false
              - column:
                  name: longitude
                  type: double precision
                  constraints:
                    nullable: false
      rollback:
        - dropColumn:
            columnName: latitude
            tableName: location
            schemaName: vehiclelocation
        - dropColumn:
            columnName: longitude
            tableName: location
            schemaName: vehiclelocation
  - changeSet:
      id: alter-building-column-type
      author: sonia balodi
      changes:
        - modifyDataType:
            tableName: location
            schemaName: vehiclelocation
            columnName: building
            newDataType: jsonb
      rollback:
        - modifyDataType:
            tableName: location
            schemaName: vehiclelocation
            columnName: building
            newDataType: varchar
  - changeSet:
      id: drop-columns-from-location
      author: sonia balodi
      changes:
        - dropColumn:
            tableName: location
            schemaName: vehiclelocation
            columnName: compound_id
        - dropColumn:
            tableName: location
            schemaName: vehiclelocation
            columnName: level
      rollback:
        - addColumn:
            tableName: location
            schemaName: vehiclelocation
            columns:
              - column:
                  name: compound_id
                  type: varchar
              - column:
                  name: level
                  type: varchar
  - changeSet:
      id: alter-parking-column-name-location-table
      author: sonia balodi
      changes:
        - renameColumn:
            tableName: location
            schemaName: vehiclelocation
            oldColumnName: parking
            newColumnName: parking_lot
            columnDataType: varchar
      rollback:
        - renameColumn:
            tableName: location
            schemaName: vehiclelocation
            oldColumnName: parking_lot
            newColumnName: parking
            columnDataType: varchar
  - changeSet:
      id: delete-inactive-locations
      author: ivan plese
      changes:
        - delete:
            tableName: location
            schemaName: vehiclelocation
            where: "inactive = true"

  - changeSet:
      id: remove-inactive-column
      author: ivan plese
      changes:
        - dropColumn:
            columnName: inactive
            tableName: location
            schemaName: vehiclelocation
      rollback:
        - addColumn:
            tableName: location
            schemaName: vehiclelocation
            columns:
              - column:
                  name: inactive
                  type: BOOLEAN
                  valueBoolean: "false"
                  constraints:
                    nullable: false

  - changeSet:
      id: set-vehicle-location-postgres-view-for-vehicle-manager
      author: Alon Cohn
      runOrder: last
      runAlways: false
      runOnChange: true
      changes:
        - sqlFile:
            relativeToChangelogFile: true
            path: 'vehiclelocation/setup-postgres-view-for-vehicle-manager.sql'
            stripComments: true

  - changeSet:
      id: add-clean-up-for-deprecated-postgres-views
      author: Komal Shevale
      runAlways: true
      changes:
        - sqlFile:
            relativeToChangelogFile: true
            path: 'vehiclelocation/clean-up-deprecated-postgres-views.sql'
            splitStatements: false
            endDelimiter: '$$;'

  - changeSet:
      id: add-index-vehicle-location-lower
      author: Karthik Narahari
      changes:
        - sql:
            dbms: postgresql
            sql: "CREATE INDEX IF NOT EXISTS idx_last_known_location_lower_location_name ON vehiclelocation.last_known_location (lower(location_name));"
