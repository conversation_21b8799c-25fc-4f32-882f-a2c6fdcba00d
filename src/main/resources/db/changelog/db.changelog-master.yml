databaseChangeLog:
  - changeSet:
      id: create-tables
      author: <PERSON><PERSON><PERSON>
      changes:
        - createTable:
            columns:
              - column:
                  name: id
                  type: uuid
                  constraints:
                    primaryKey: true
              - column:
                  name: license_plate
                  type: varchar(255)
              - column:
                  name: vin
                  type: varchar(255)
              - column:
                  name: vguid
                  type: varchar(255)
              - column:
                  name: make
                  type: varchar(255)
              - column:
                  name: model
                  type: varchar(255)
              - column:
                  name: type
                  type: varchar(255)
              - column:
                  name: first_registration_date
                  type: timestamp(6)
              - column:
                  name: pool_id
                  type: uuid
              - column:
                  name: driver_id
                  type: uuid
            schemaName: vehicle
            tableName: vehicle
        - createTable:
            columns:
              - column:
                  name: id
                  type: uuid
                  constraints:
                    primaryKey: true
              - column:
                  name: first_name
                  type: varchar(255)
              - column:
                  name: last_name
                  type: varchar(255)
              - column:
                  name: email
                  type: varchar(255)
            schemaName: vehicle
            tableName: driver
        - createTable:
            columns:
              - column:
                  name: id
                  type: uuid
                  constraints:
                    primaryKey: true
              - column:
                  name: is_pool
                  type: boolean
              - column:
                  name: pool_location
                  type: var<PERSON>r(255)
              - column:
                  name: is_active
                  type: boolean
            schemaName: vehicle
            tableName: vehicle_pool
        - addForeignKeyConstraint:
            baseColumnNames: driver_id
            baseTableName: vehicle
            baseTableSchemaName: vehicle
            constraintName: vehicle_driver_id_fkey
            referencedColumnNames: id
            referencedTableName: driver
            referencedTableSchemaName: vehicle
            validate: true
        - addForeignKeyConstraint:
            baseColumnNames: pool_id
            baseTableName: vehicle
            baseTableSchemaName: vehicle
            constraintName: vehicle_pool_id_fkey
            referencedColumnNames: id
            referencedTableName: vehicle_pool
            referencedTableSchemaName: vehicle
            validate: true
        - addUniqueConstraint:
            columnNames: email
            constraintName: driver_email_key
            schemaName: vehicle
            tableName: driver
            validate: true
  - changeSet:
      id: drop-driver-and-vehicle-pool-table
      author: Vineeth Venudasan
      comment: no current requirement
      changes:
        - dropColumn:
            columnName: driver_id
            schemaName: vehicle
            tableName: vehicle
        - dropColumn:
            columnName: pool_id
            schemaName: vehicle
            tableName: vehicle
        - dropTable:
            cascadeConstraints: true
            schemaName: vehicle
            tableName: driver
        - dropTable:
            cascadeConstraints: true
            schemaName: vehicle
            tableName: vehicle_pool
  - changeSet:
      id: add-engine-info-table
      author: Vineeth Venudasan
      changes:
        - createTable:
            columns:
              - column:
                  name: id
                  type: bigint
                  constraints:
                    primaryKey: true
              - column:
                  name: engine_type
                  type: varchar(32)
            schemaName: vehicle
            tableName: engine_info
  - changeSet:
      id: add-registration-info-table
      author: Vineeth Venudasan
      changes:
        - createTable:
            columns:
              - column:
                  name: id
                  type: bigint
                  constraints:
                    primaryKey: true
              - column:
                  name: license_plate
                  type: varchar(32)
              - column:
                  name: first_registration_date
                  type: timestamp(6)
            schemaName: vehicle
            tableName: registration_info
  - changeSet:
      id: add-order-info-table
      author: Vineeth Venudasan
      changes:
        - createTable:
            columns:
              - column:
                  name: id
                  type: bigint
                  constraints:
                    primaryKey: true
              - column:
                  name: department
                  type: varchar(32)
              - column:
                  name: leasing_type
                  type: varchar(32)
            schemaName: vehicle
            tableName: order_info
  - changeSet:
      id: add-engine-info-id-to-vehicle-table
      author: Vineeth Venudasan
      changes:
        - addColumn:
            tableName: vehicle
            schemaName: vehicle
            columns:
              - column:
                  name: engine_info_id
                  type: bigint
        - addForeignKeyConstraint:
            baseTableName: vehicle
            baseColumnNames: engine_info_id
            baseTableSchemaName: vehicle
            referencedTableSchemaName: vehicle
            referencedTableName: engine_info
            referencedColumnNames: id
            constraintName: vehicle_engine_info_id_fkey
            validate: true
  - changeSet:
      id: make-id-columns-identity-columns
      author: Vineeth Venudasan
      changes:
        - addAutoIncrement:
            columnDataType: bigint
            columnName: id
            defaultOnNull: false
            generationType: ALWAYS
            incrementBy: 1
            schemaName: vehicle
            startWith: 1
            tableName: engine_info

  - changeSet:
      id: introduce-requires-refresh-column
      author: Vineeth Venudasan
      changes:
        - addColumn:
            schemaName: vehicle
            tableName: vehicle
            columns:
              - column:
                  name: requires_refresh
                  type: boolean
                  defaultValue: "true"

  - changeSet:
      id: add-order-info-id-to-vehicle-table
      author: Karthik Narahari
      changes:
        - addColumn:
            tableName: vehicle
            schemaName: vehicle
            columns:
              - column:
                  name: order_info_id
                  type: bigint
        - addForeignKeyConstraint:
            baseTableName: vehicle
            baseColumnNames: order_info_id
            baseTableSchemaName: vehicle
            referencedTableSchemaName: vehicle
            referencedTableName: order_info
            referencedColumnNames: id
            constraintName: vehicle_order_info_id_fkey
            validate: true
  - changeSet:
      id: make-order-id-columns-identity-columns
      author: Karthik Narahari
      changes:
        - addAutoIncrement:
            columnDataType: bigint
            columnName: id
            defaultOnNull: false
            generationType: ALWAYS
            incrementBy: 1
            schemaName: vehicle
            startWith: 1
            tableName: order_info
  - changeSet:
      id: modify-order-info-table
      author: Karthik Narahari
      changes:
        - dropColumn:
            schemaName: vehicle
            tableName: order_info
            columns:
              - column:
                  name: department
                  type: varchar(32)
              - column:
                  name: leasing_type
                  type: varchar(32)
        - addColumn:
            schemaName: vehicle
            tableName: order_info
            columns:
              - column:
                  name: consignee_number
                  type: varchar(32)
  - changeSet:
      id: require-refresh-for-consignee-number
      author: Vineeth Venudasan
      changes:
        - update:
            schemaName: vehicle
            tableName: vehicle
            columns:
              - column:
                  name: requires_refresh
                  value: "true"
  - changeSet:
      id: vehicle-vin-unique-constraint
      author: Karthik Narahari
      changes:
        - addUniqueConstraint:
            columnNames: vin
            schemaName: vehicle
            tableName: vehicle
            validate: true
  - changeSet:
      id: extract-model-info-to-dim-table
      author: Vineeth Venudasan
      changes:
        - createTable:
            columns:
              - column:
                  name: id
                  type: bigint
                  constraints:
                    primaryKey: true
              - column:
                  name: model_description
                  type: varchar(1024)
            schemaName: vehicle
            tableName: model_info
        - addColumn:
            tableName: vehicle
            schemaName: vehicle
            columns:
              - column:
                  name: model_info_id
                  type: bigint
        - addForeignKeyConstraint:
            baseTableName: vehicle
            baseColumnNames: model_info_id
            baseTableSchemaName: vehicle
            referencedTableSchemaName: vehicle
            referencedTableName: model_info
            referencedColumnNames: id
            constraintName: vehicle_model_info_id_fkey
            validate: true
        - addAutoIncrement:
            columnDataType: bigint
            columnName: id
            defaultOnNull: false
            generationType: ALWAYS
            incrementBy: 1
            schemaName: vehicle
            startWith: 1
            tableName: model_info
  - changeSet:
      id: drop-columns-already-dimensioned
      author: Vineeth Venudasan
      changes:
        - dropColumn:
            columnName: model
            schemaName: vehicle
            tableName: vehicle
        - dropColumn:
            columnName: make
            schemaName: vehicle
            tableName: vehicle
        - dropColumn:
            columnName: type
            schemaName: vehicle
            tableName: vehicle
  - changeSet:
      id: drop-registration-info-columns-from-vehicle
      author: Vineeth Venudasan
      changes:
        - dropColumn:
            columnName: license_plate
            schemaName: vehicle
            tableName: vehicle
        - dropColumn:
            columnName: first_registration_date
            schemaName: vehicle
            tableName: vehicle
  - changeSet:
      id: add-columns-to-model-info
      author: Komal Shevale
      changes:
        - addColumn:
            schemaName: vehicle
            tableName: model_info
            columns:
              - column:
                  name: product_id
                  type: varchar(32)
              - column:
                  name: product_code
                  type: varchar(32)
  - changeSet:
      id: create-production-info-table
      author: Komal Shevale
      changes:
        - createTable:
            columns:
              - column:
                  name: id
                  type: bigint
                  autoIncrement: true
                  constraints:
                    primaryKey: true
              - column:
                  name: production_number
                  type: int
              - column:
                  name: production_end_date
                  type: timestamp(6)
              - column:
                  name: planned_production_end_date
                  type: timestamp(6)
              - column:
                  name: technical_model_year
                  type: int
              - column:
                  name: factory
                  type: varchar(32)
              - column:
                  name: factory_vw
                  type: int
              - column:
                  name: quote_month
                  type: int
              - column:
                  name: quote_year
                  type: int
              - column:
                  name: gear_box_class
                  type: varchar(4)
            schemaName: vehicle
            tableName: production_info
  - changeSet:
      id: add-production-info-id-to-vehicle-table
      author: Komal Shevale
      changes:
        - addColumn:
            tableName: vehicle
            schemaName: vehicle
            columns:
              - column:
                  name: production_info_id
                  type: bigint
        - addForeignKeyConstraint:
            baseTableName: vehicle
            baseColumnNames: production_info_id
            baseTableSchemaName: vehicle
            referencedTableSchemaName: vehicle
            referencedTableName: production_info
            referencedColumnNames: id
            constraintName: vehicle_production_info_id_fkey
            validate: true
  - changeSet:
      id: create-country-info-table
      author: Komal Shevale
      changes:
        - createTable:
            columns:
              - column:
                  name: id
                  type: bigint
                  autoIncrement: true
                  constraints:
                    primaryKey: true
              - column:
                  name: bnr_value
                  type: varchar(6)
              - column:
                  name: cnr_value
                  type: varchar(6)
              - column:
                  name: cnr_country_description
                  type: varchar(255)
            schemaName: vehicle
            tableName: country_info
  - changeSet:
      id: add-country-info-id-to-vehicle-table
      author: Komal Shevale
      changes:
        - addColumn:
            tableName: vehicle
            schemaName: vehicle
            columns:
              - column:
                  name: country_info_id
                  type: bigint
        - addForeignKeyConstraint:
            baseTableName: vehicle
            baseColumnNames: country_info_id
            baseTableSchemaName: vehicle
            referencedTableSchemaName: vehicle
            referencedTableName: country_info
            referencedColumnNames: id
            constraintName: vehicle_country_info_id_fkey
            validate: true
  - changeSet:
      id: create-pmp-data-table
      author: Komal Shevale
      changes:
        - createTable:
            columns:
              - column:
                  name: id
                  type: bigint
                  autoIncrement: true
                  constraints:
                    primaryKey: true
              - column:
                  name: odometer
                  type: int
              - column:
                  name: timestamp
                  type: timestamp(6)
            schemaName: vehicle
            tableName: pmp_data
  - changeSet:
      id: add-pmp-data-id-to-vehicle-table
      author: Komal Shevale
      changes:
        - addColumn:
            tableName: vehicle
            schemaName: vehicle
            columns:
              - column:
                  name: pmp_data_id
                  type: bigint
        - addForeignKeyConstraint:
            baseTableName: vehicle
            baseColumnNames: pmp_data_id
            baseTableSchemaName: vehicle
            referencedTableSchemaName: vehicle
            referencedTableName: pmp_data
            referencedColumnNames: id
            constraintName: vehicle_pmp_data_id_fkey
            validate: true
  - changeSet:
      id: add-typification-to-engine-info-table
      author: Komal Shevale
      changes:
        - addColumn:
            tableName: engine_info
            schemaName: vehicle
            columns:
              - column:
                  name: typification
                  type: varchar(32)
  - changeSet:
      id: create-embargo-info-table
      author: Komal Shevale
      changes:
        - createTable:
            columns:
              - column:
                  name: id
                  type: bigint
                  autoIncrement: true
                  constraints:
                    primaryKey: true
              - column:
                  name: in_embargo
                  type: boolean
            schemaName: vehicle
            tableName: embargo_info
  - changeSet:
      id: add-embargo-info-id-to-vehicle-table
      author: Komal Shevale
      changes:
        - addColumn:
            tableName: vehicle
            schemaName: vehicle
            columns:
              - column:
                  name: embargo_info_id
                  type: bigint
        - addForeignKeyConstraint:
            baseTableName: vehicle
            baseColumnNames: embargo_info_id
            baseTableSchemaName: vehicle
            referencedTableSchemaName: vehicle
            referencedTableName: embargo_info
            referencedColumnNames: id
            constraintName: vehicle_embargo_info_id_fkey
            validate: true
  - changeSet:
      id: add-additional-fields-to-order-info-table
      author: Martin Grulich
      changes:
        - addColumn:
            tableName: order_info
            schemaName: vehicle
            columns:
              - column:
                  name: trading_partner_number
                  type: bigint
              - column:
                  name: purpose_order_type
                  type: varchar(32)
              - column:
                  name: importer_short_name
                  type: varchar(32)
              - column:
                  name: commission_number
                  type: varchar(32)
              - column:
                  name: invoice_number
                  type: bigint
              - column:
                  name: invoice_date
                  type: timestamp(6)
              - column:
                  name: purchase_order_date
                  type: timestamp(6)
              - column:
                  name: requested_delivery_date
                  type: timestamp(6)
              - column:
                  name: delivery_type
                  type: smallint
              - column:
                  name: primary_status
                  type: varchar(32)
  - changeSet:
      id: create-color-info-table
      author: Martin Grulich
      changes:
        - createTable:
            columns:
              - column:
                  name: id
                  type: bigint
                  autoIncrement: true
                  constraints:
                    primaryKey: true
              - column:
                  name: exterior
                  type: varchar(32)
              - column:
                  name: exterior_description
                  type: text
              - column:
                  name: interior
                  type: varchar(32)
              - column:
                  name: interior_description
                  type: text
            schemaName: vehicle
            tableName: color_info
  - changeSet:
      id: add-color-info-id-to-vehicle-table
      author: Martin Grulich
      changes:
        - addColumn:
            tableName: vehicle
            schemaName: vehicle
            columns:
              - column:
                  name: color_info_id
                  type: bigint
        - addForeignKeyConstraint:
            baseTableName: vehicle
            baseColumnNames: color_info_id
            baseTableSchemaName: vehicle
            referencedTableSchemaName: vehicle
            referencedTableName: color_info
            referencedColumnNames: id
            constraintName: vehicle_color_info_id_fkey
            validate: true
  - changeSet:
      id: add-options-to-vehicle-table
      author: Karthik Narahari
      changes:
        - addColumn:
            tableName: vehicle
            schemaName: vehicle
            columns:
              - column:
                  name: options
                  type: jsonb
  - changeSet:
      id: add-order-type-to-model-info-table
      author: Karthik Narahari
      changes:
        - addColumn:
            tableName: model_info
            schemaName: vehicle
            columns:
              - column:
                  name: order_type
                  type: varchar(32)
  - changeSet:
      id: change-delivery-type-field-type-of-order-info-table
      author: Martin Grulich
      changes:
        - modifyDataType:
            schemaName: vehicle
            tableName: order_info
            columnName: delivery_type
            newDataType: varchar(32)
  - changeSet:
      id: change-production-number-field-type-of-production-info-table
      author: Martin Grulich
      changes:
        - modifyDataType:
            schemaName: vehicle
            tableName: production_info
            columnName: production_number
            newDataType: varchar(32)
  - changeSet:
      id: add-missing-fields-from-kafka
      author: Karthik Narahari
      changes:
        - addColumn:
            columns:
              - column:
                  name: cnr_country_description_language
                  type: VARCHAR(255)
            schemaName: vehicle
            tableName: country_info
        - addColumn:
            columns:
              - column:
                  name: customer_delivery_date
                  type: timestamp(6)
              - column:
                  name: dealer_number
                  type: VARCHAR(255)
              - column:
                  name: planned_customer_delivery_date
                  type: timestamp(6)
              - column:
                  name: port_code
                  type: VARCHAR(255)
              - column:
                  name: shipping_code
                  type: VARCHAR(255)
              - column:
                  name: vehicle_status_oem
                  type: VARCHAR(255)
            schemaName: vehicle
            tableName: order_info
        - addColumn:
            columns:
              - column:
                  name: model_description_language
                  type: VARCHAR(255)
              - column:
                  name: model_year
                  type: INT
            schemaName: vehicle
            tableName: model_info
        - addColumn:
            columns:
              - column:
                  name: production_number_vw
                  type: VARCHAR(255)
            schemaName: vehicle
            tableName: production_info
        - addColumn:
            columns:
              - column:
                  name: exterior_description_language
                  type: VARCHAR(255)
              - column:
                  name: interior_description_language
                  type: VARCHAR(255)
            schemaName: vehicle
            tableName: color_info
  - changeSet:
      id: modify-invoice-number-data-type
      author: Karthik Narahari
      changes:
        - modifyDataType:
            schemaName: vehicle
            tableName: order_info
            columnName: invoice_number
            newDataType: varchar(32)
        - modifyDataType:
            schemaName: vehicle
            tableName: order_info
            columnName: trading_partner_number
            newDataType: varchar(32)
  - changeSet:
      id: add-department-and-leasing-type-to-order-info
      author: Vineeth Venudasan and Kapil Dube
      changes:
        - addColumn:
            columns:
              - column:
                  name: department
                  type: VARCHAR(32)
              - column:
                  name: leasing_type
                  type: VARCHAR(8)
            schemaName: vehicle
            tableName: order_info
  - changeSet:
      id: add-last-updated-at.created-at-columns
      author: Karthik Narahari
      changes:
        - addColumn:
            columns:
              - column:
                  name: last_updated_at
                  type: timestamp(6)
              - column:
                  name: created_at
                  type: timestamp(6)
            schemaName: vehicle
            tableName: vehicle
  - changeSet:
      id: add-vehicle-scrapped-or-sold-date-to-vehicle-table
      author: Karthik Narahari
      changes:
        - addColumn:
            tableName: vehicle
            schemaName: vehicle
            columns:
              - column:
                  name: vehicle_scrapped_or_sold_date
                  type: timestamptz
  - changeSet:
      id: modify-metadata-type-of-vehicle-table
      author: Karthik Narahari
      changes:
        - modifyDataType:
            schemaName: vehicle
            tableName: vehicle
            columnName: created_at
            newDataType: timestamptz
        - modifyDataType:
            schemaName: vehicle
            tableName: vehicle
            columnName: last_updated_at
            newDataType: timestamptz

  - changeSet:
      id: add-equi-columns-to-vehicle
      author: Komal Shevale
      changes:
        - addColumn:
            schemaName: vehicle
            tableName: vehicle
            columns:
              - column:
                  name: equi_id
                  type: varchar(255)
              - column:
                  name: equipment_number
                  type: bigint
  - changeSet:
      id: copy-dealer-number-to-consignee-number-if-null
      author: Komal Shevale
      changes:
        - update:
            schemaName: vehicle
            tableName: order_info
            columns:
              - column:
                  name: consignee_number
                  valueComputed: dealer_number
            where: "consignee_number IS NULL"
  - changeSet:
      id: drop-dealer-number
      author: Komal Shevale
      changes:
        - dropColumn:
            schemaName: vehicle
            tableName: order_info
            columnName: dealer_number
  - changeSet:
      id: add-vehicle-type-and-manufacturer-to-order-info
      author: Ivan Plese
      changes:
        - addColumn:
            schemaName: vehicle
            tableName: model_info
            columns:
              - column:
                  name: vehicle_type
                  type: varchar
                  constraints:
                    nullable: true
              - column:
                  name: manufacturer
                  type: varchar
                  constraints:
                    nullable: true

  - changeSet:
      id: add-vehicle-flags-to-order-info
      author: Manuel Jany
      changes:
        - addColumn:
            tableName: order_info
            schemaName: vehicle
            columns:
              - column:
                  name: preproduction_vehicle
                  type: boolean
              - column:
                  name: blocked_for_sale
                  type: boolean
              - column:
                  name: scrap_vehicle
                  type: boolean
      rollback:
        - dropColumn:
            columnName: preproduction_vehicle
            schemaName: vehicle
            tableName: order_info
        - dropColumn:
            columnName: blocked_for_sale
            schemaName: vehicle
            tableName: order_info
        - dropColumn:
            columnName: scrap_vehicle
            schemaName: vehicle
            tableName: order_info
  - changeSet:
      id: drop-registration-info
      author: Vineeth Venudasan
      changes:
        - dropTable:
            schemaName: vehicle
            tableName: registration_info
  - changeSet:
      id: add-source-asset-type-and-import-id-to-vehicle-table
      author: Martin Grulich
      changes:
        - addColumn:
            tableName: vehicle
            schemaName: vehicle
            columns:
              - column:
                  name: source
                  type: varchar
                  defaultValue: "UNKNOWN"
              - column:
                  name: asset_type
                  type: varchar
                  constraints:
                    nullable: true
              - column:
                  name: import_id
                  type: varchar
                  constraints:
                    nullable: true
  - changeSet:
      id: rename-import-id-to-reference-id-vehicle-table
      author: Martin Grulich
      changes:
        - renameColumn:
            tableName: vehicle
            schemaName: vehicle
            oldColumnName: import_id
            newColumnName: reference_id
  - changeSet:
      id: migrate-consignee-to-reference
      author: Martin Grulich
      changes:
        - sql:
            sql: |
              UPDATE vehicle.vehicle v
              SET reference_id = (
                SELECT oi.consignee_number
                FROM vehicle.order_info oi
                WHERE oi.id = v.order_info_id
              )
              WHERE EXISTS (
                SELECT 1
                FROM vehicle.order_info oi
                WHERE oi.id = v.order_info_id
              )

  - changeSet:
      id: create-price-info-table
      author: David Swensen & Komal Shevale
      changes:
        - createTable:
            tableName: price_info
            columns:
              - column:
                  name: id
                  type: bigint
                  autoIncrement: true
                  constraints:
                    primaryKey: true
              - column:
                  name: gross_price
                  type: decimal(19, 2)
              - column:
                  name: gross_price_with_extras
                  type: decimal(19, 2)
              - column:
                  name: gross_price_plan
                  type: decimal(19, 2)
              - column:
                  name: gross_price_new_car
                  type: decimal(19, 2)
              - column:
                  name: net_price
                  type: decimal(19, 2)
              - column:
                  name: net_price_with_extras
                  type: decimal(19, 2)
              - column:
                  name: net_price_new_car
                  type: decimal(19, 2)
              - column:
                  name: value_added_tax
                  type: decimal(19, 2)
            schemaName: vehicle
        - addColumn:
            tableName: vehicle
            schemaName: vehicle
            columns:
              - column:
                  name: price_info_id
                  type: bigint
        - addForeignKeyConstraint:
            baseTableName: vehicle
            baseColumnNames: price_info_id
            baseTableSchemaName: vehicle
            referencedTableSchemaName: vehicle
            referencedTableName: price_info
            referencedColumnNames: id
            constraintName: vehicle_price_info_id_fkey
            validate: true
  - changeSet:
      id: create-technical-info-table
      author: David Swensen & Komal Shevale
      changes:
        - createTable:
            schemaName: vehicle
            tableName: technical_info
            columns:
              - column:
                  name: id
                  type: bigint
                  autoIncrement: true
                  constraints:
                    primaryKey: true
              - column:
                  name: amount_seats
                  type: smallint
              - column:
                  name: engine_capacity
                  type: decimal(19, 2)
              - column:
                  name: engine_power
                  type: decimal(19, 2)
        - addColumn:
            tableName: vehicle
            schemaName: vehicle
            columns:
              - column:
                  name: technical_info_id
                  type: bigint
        - addForeignKeyConstraint:
            baseTableName: vehicle
            baseColumnNames: technical_info_id
            baseTableSchemaName: vehicle
            referencedTableSchemaName: vehicle
            referencedTableName: technical_info
            referencedColumnNames: id
            constraintName: vehicle_technical_info_id_fkey
            validate: true

  - changeSet:
      id: add-new-data-fields-for-vehicle
      author: David Swensen & Komal
      changes:
        - addColumn:
            tableName: vehicle
            schemaName: vehicle
            columns:
              - column:
                  name: current_tires
                  type: varchar(16)
              - column:
                  name: fuel_type
                  type: varchar(16)
              - column:
                  name: fuel_type_2
                  type: varchar(16)
              - column:
                  name: financial_asset_type
                  type: varchar(16)
              - column:
                  name: external_lease_start
                  type: timestamp(6)
              - column:
                  name: external_lease_end
                  type: timestamp(6)
              - column:
                  name: external_lease_rate
                  type: decimal(19, 2)
              - column:
                  name: external_lease_lessee
                  type: varchar(32)

  - changeSet:
      id: drop financial_asset_type column if it exists
      author: David Swensen
      failOnError: false
      preConditions:
        - onFail: CONTINUE
        - columnExists:
            columnName: financial_asset_type
            schemaName: vehicle
            tableName: vehicle
      comment: check if the financial asset type exists then drop it
      changes:
        - dropColumn:
            columnName: financial_asset_type
            schemaName: vehicle
            tableName: vehicle

  - changeSet:
      id: add-postgres-view-that-mirrors-dlz-view
      author: David Swensen & Komal Shevale
      runOrder: last
      runAlways: false
      runOnChange: true
      changes:
        - sqlFile:
            relativeToChangelogFile: true
            path: 'vehicledata/setup-postgres-view-for-vehicle-manager.sql'
            stripComments: true

  - changeSet:
      id: add-clean-up-for-deprecated-postgres-views
      author: Komal Shevale
      runAlways: true
      changes:
        - sqlFile:
            relativeToChangelogFile: true
            path: 'vehicledata/clean-up-deprecated-postgres-views.sql'
            splitStatements: false
            endDelimiter: '$$;'

  - changeSet:
      id: add-vehicle-option-tags-table
      author: Komal Shevale
      changes:
        - createTable:
            schemaName: vehicle
            tableName: vehicle_option_tags
            columns:
              - column:
                  name: id
                  type: bigint
                  autoIncrement: true
                  constraints:
                    primaryKey: true
              - column:
                  name: option_id
                  type: VARCHAR(32)
              - column:
                  name: option_description
                  type: TEXT
              - column:
                  name: created_at
                  type: timestamptz
                  constraints:
                    nullable: false
                  defaultValueComputed: CURRENT_TIMESTAMP
        - addUniqueConstraint:
            schemaName: vehicle
            tableName: vehicle_option_tags
            columnNames: option_id, option_description
            constraintName: uq_vehicle_option_tags_id_description

  - changeSet:
      id: populate-vehicle-option-tags-table
      author: Komal Shevale
      changes:
        - sql:
            splitStatements: false
            stripComments: false
            sql: |
              INSERT INTO vehicle.vehicle_option_tags (option_id, option_description)
              SELECT DISTINCT
                NULLIF(jsonb_array_elements(options->'current'->'individualOptions')->>'id','')  AS option_id,
                NULLIF(jsonb_array_elements(options->'current'->'individualOptions')->'optionDescription'->>'description','') AS option_description
              FROM vehicle.vehicle
              WHERE options IS NOT NULL;

  - changeSet:
      id: add-number-of-damages
      author: Alon Cohn
      changes:
        - addColumn:
            tableName: vehicle
            schemaName: vehicle
            columns:
              - column:
                  name: last_updated_number_of_damages
                  type: timestamp
              - column:
                  name: number_of_damages
                  type: int
                  defaultValue: "0"
              - column:
                  name: number_of_open_damages
                  type: int
                  defaultValue: "0"

  - changeSet:
      id: create-jsonb-gin-index-on-vehicle-options
      author: Komal Shevale
      changes:
        - sql:
            dbms: postgresql
            splitStatements: false
            stripComments: true
            sql: "CREATE INDEX idx_vehicle_options_jsonb_gin ON vehicle.vehicle USING GIN (options);"

  - changeSet:
      id: create-fleet-info-table
      author: Karthik Narahari
      changes:
        - createTable:
            tableName: fleet_info
            schemaName: vehicle
            columns:
              - column:
                  name: id
                  type: BIGINT
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: sold_date
                  type: timestamptz
              - column:
                  name: scrapped_date
                  type: timestamptz
              - column:
                  name: stolen_date
                  type: timestamptz
              - column:
                  name: sold_cup_car_date
                  type: timestamptz
              - column:
                  name: approved_for_scrapping_date
                  type: timestamptz
              - column:
                  name: scrapped_vehicle_offered_date
                  type: timestamptz
              - column:
                  name: vehicle_sent_to_sales_date
                  type: timestamptz
              - column:
                  name: cost_estimation_ordered_date
                  type: timestamptz
              - column:
                  name: is_residual_value_market
                  type: BOOLEAN
              - column:
                  name: profitability_audit_date
                  type: timestamptz

  - changeSet:
      id: create-delivery-info-table
      author: Karthik Narahari
      changes:
        - createTable:
            tableName: delivery_info
            schemaName: vehicle
            columns:
              - column:
                  name: id
                  type: BIGINT
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: preparation_done_date
                  type: timestamptz
              - column:
                  name: is_preparation_necessary
                  type: BOOLEAN

  - changeSet:
      id: create-return-info-table
      author: Karthik Narahari
      changes:
        - createTable:
            tableName: return_info
            schemaName: vehicle
            columns:
              - column:
                  name: id
                  type: BIGINT
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: return_type
                  type: TEXT
              - column:
                  name: is_used_car
                  type: BOOLEAN

  - changeSet:
      id: add-fleet-info-id-to-vehicle-table
      author: Karthik Narahari
      changes:
        - addColumn:
            tableName: vehicle
            schemaName: vehicle
            columns:
              - column:
                  name: fleet_info_id
                  type: bigint
        - addForeignKeyConstraint:
            baseTableName: vehicle
            baseColumnNames: fleet_info_id
            baseTableSchemaName: vehicle
            referencedTableSchemaName: vehicle
            referencedTableName: fleet_info
            referencedColumnNames: id
            constraintName: vehicle_fleet_info_id_fkey
            validate: true

  - changeSet:
      id: add-delivery-info-id-to-vehicle-table
      author: Karthik Narahari
      changes:
        - addColumn:
            tableName: vehicle
            schemaName: vehicle
            columns:
              - column:
                  name: delivery_info_id
                  type: bigint
        - addForeignKeyConstraint:
            baseTableName: vehicle
            baseColumnNames: delivery_info_id
            baseTableSchemaName: vehicle
            referencedTableSchemaName: vehicle
            referencedTableName: delivery_info
            referencedColumnNames: id
            constraintName: vehicle_delivery_info_id_fkey
            validate: true

  - changeSet:
      id: add-return-info-id-to-vehicle-table
      author: Karthik Narahari
      changes:
        - addColumn:
            tableName: vehicle
            schemaName: vehicle
            columns:
              - column:
                  name: return_info_id
                  type: bigint
        - addForeignKeyConstraint:
            baseTableName: vehicle
            baseColumnNames: return_info_id
            baseTableSchemaName: vehicle
            referencedTableSchemaName: vehicle
            referencedTableName: return_info
            referencedColumnNames: id
            constraintName: vehicle_return_info_id_fkey
            validate: true

  - changeSet:
      id: remove-vehicle-scrapped-or-sold-date-from-vehicle-table
      author: Karthik Narahari
      changes:
        - dropColumn:
            tableName: vehicle
            schemaName: vehicle
            columnName: vehicle_scrapped_or_sold_date


  - changeSet:
      id: add-tire-set-change-table
      author: Manuel Jany
      changes:
        - createTable:
            schemaName: vehicle
            tableName: tire_set_change
            columns:
              - column:
                  name: id
                  type: bigint
                  autoIncrement: true
                  constraints:
                    primaryKey: true
              - column:
                  name: comment
                  type: VARCHAR
              - column:
                  name: ordered_date
                  type: timestamptz
              - column:
                  name: completed_date
                  type: timestamptz

  - changeSet:
      id: add-tire-set-change-id-to-vehicle
      author: Manuel Jany
      changes:
        - addColumn:
            tableName: vehicle
            schemaName: vehicle
            columns:
              - column:
                  name: tire_set_change_id
                  type: bigint

  - changeSet:
      id: add-tire-set-change-id-fk
      author: Manuel Jany
      changes:
        - addForeignKeyConstraint:
            baseTableName: vehicle
            baseColumnNames: tire_set_change_id
            baseTableSchemaName: vehicle
            referencedTableSchemaName: vehicle
            referencedTableName: tire_set_change
            referencedColumnNames: id
            constraintName: vehicle_tire_set_change_id_fkey
            validate: true

  - changeSet:
      id: add-status-to-vehicle
      author: Manuel Jany
      changes:
        - addColumn:
            schemaName: vehicle
            tableName: vehicle
            columns:
              - column:
                  name: status
                  type: varchar

  - changeSet:
      id: add-key-return-to-vehicle-return-info
      author: Manuel Jany
      changes:
        - addColumn:
            schemaName: vehicle
            tableName: return_info
            columns:
              - column:
                  name: key_returned
                  type: timestamptz

  - changeSet:
      id: set-default-vehicle-status
      author: Manuel Jany
      changes:
        - update:
            tableName: vehicle
            schemaName: vehicle
            columns:
              - column:
                  name: status
                  value: 'E000'
            where: status is null
  - changeSet:
      id: drop-unused-columns
      author: Christoph Portmann (TW)
      changes:
        - dropColumn:
            schemaName: vehicle
            tableName: order_info
            columnName: vehicle_status_oem
        - dropColumn:
            schemaName: vehicle
            tableName: order_info
            columnName: planned_customer_delivery_date
        - dropColumn:
            schemaName: vehicle
            tableName: order_info
            columnName: shipping_code
  - changeSet:
      id: create-wltp-info-table
      author: Martin Grulich
      changes:
        - createTable:
            schemaName: vehicle
            tableName: wltp_info
            columns:
              - column:
                  name: id
                  type: BIGINT
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: typification
                  type: varchar(255)
              - column:
                  name: vehicle_weight
                  type: int
              - column:
                  name: electric_range
                  type: int
              - column:
                  name: co2_combined
                  type: int
              - column:
                  name: electric_range_city
                  type: int
  - changeSet:
      id: add-technical-model-fleet-attributes-to-vehicle
      author: Martin Grulich
      changes:
        - addColumn:
            schemaName: vehicle
            tableName: fleet_info
            columns:
              - column:
                  name: comment
                  type: text
        - addColumn:
            schemaName: vehicle
            tableName: model_info
            columns:
              - column:
                  name: range
                  type: varchar
        - addColumn:
            schemaName: vehicle
            tableName: technical_info
            columns:
              - column:
                  name: range
                  type: int
              - column:
                  name: cargo_volume
                  type: int
              - column:
                  name: vehicle_width_mirrors_extended
                  type: int
              - column:
                  name: maximum_charging_power_dc
                  type: int
              - column:
                  name: gross_vehicle_weight
                  type: int
              - column:
                  name: curb_weight_eu
                  type: int
              - column:
                  name: total_power_kw
                  type: int
              - column:
                  name: curb_weight_din
                  type: int
              - column:
                  name: maximum_payload
                  type: int
              - column:
                  name: charging_time_ac_22_kw
                  type: decimal(19, 2)
              - column:
                  name: charging_time_ac_11_kw_0100
                  type: decimal(19, 2)
              - column:
                  name: charging_time_ac_96_kw_0100
                  type: decimal(19, 2)
              - column:
                  name: net_battery_capacity
                  type: decimal(19, 2)
              - column:
                  name: gross_battery_capacity
                  type: decimal(19, 2)
              - column:
                  name: acceleration_0100_kmh
                  type: decimal(19, 2)
              - column:
                  name: acceleration_0100_kmh_launch_control
                  type: decimal(19, 2)
              - column:
                  name: top_speed
                  type: int
              - column:
                  name: height
                  type: int
              - column:
                  name: width_mirrors_folded
                  type: int
              - column:
                  name: length
                  type: int
              - column:
                  name: acceleration_80_120_kmh
                  type: decimal(19, 2)
              - column:
                  name: max_roof_load_with_porsche_roof_transport_system
                  type: int
              - column:
                  name: charging_time_dc_max_power_580
                  type: decimal(19, 2)
              - column:
                  name: displacement
                  type: int
              - column:
                  name: power_kw
                  type: int
        - addColumn:
            schemaName: vehicle
            tableName: vehicle
            columns:
              - column:
                  name: wltp_info_id
                  type: bigint
        - addForeignKeyConstraint:
            baseTableName: vehicle
            baseColumnNames: wltp_info_id
            baseTableSchemaName: vehicle
            referencedTableSchemaName: vehicle
            referencedTableName: wltp_info
            referencedColumnNames: id
            constraintName: vehicle_wltp_info_id_fkey
            validate: true

  - changeSet:
      id: rename-return-type-to-next-process
      author: Manuel Jany
      changes:
        - renameColumn:
            tableName: return_info
            schemaName: vehicle
            oldColumnName: return_type
            newColumnName: next_process

  - changeSet:
      id: add-repairfix-car-id-to-vehicle
      author: Ivan Plese
      changes:
        - addColumn:
            tableName: vehicle
            schemaName: vehicle
            columns:
              - column:
                  name: repairfix_car_id
                  type: varchar

  - changeSet:
      id: add-factory-car-preparation-order-number-to-vehicle-return-info
      author: Manuel Jany
      changes:
        - addColumn:
            schemaName: vehicle
            tableName: return_info
            columns:
              - column:
                  name: factory_car_preparation_order_number
                  type: varchar

  - changeSet:
      id: update-model-info-range
      author: Karthik Narahari & Anji Yarram Reddy
      changes:
        - update:
            schemaName: vehicle
            tableName: model_info
            columns:
              - column:
                  name: range
                  valueComputed: CASE
                    WHEN model_description ~* '.*Taycan.*' THEN 'Taycan'
                    WHEN model_description ~* '.*Boxster.*' OR model_description ~* '.*Cayman.*' THEN 'Boxster'
                    WHEN model_description ~* '.*911.*' THEN '911'
                    WHEN model_description ~* '.*Macan.*' THEN 'Macan'
                    WHEN model_description ~* '.*Cayenne.*' THEN 'Cayenne'
                    WHEN model_description ~* '.*Panamera.*' THEN 'Panamera'
                    WHEN model_description ~* '.*918.*' THEN 'Super-Sportwagen'
                    WHEN model_description ~* '.*GT.*Cup' THEN 'Kd. Sport-Fzg.'
                    ELSE range
                    END
            where: range IS NULL

  - changeSet:
      id: switch-order-to-update-model-info-range
      author: Karthik Narahari & Anji Yarram Reddy
      changes:
        - update:
            schemaName: vehicle
            tableName: model_info
            columns:
              - column:
                  name: range
                  valueComputed: CASE
                    WHEN model_description ~* '.*GT.*Cup' THEN 'Kd. Sport-Fzg.'
                    WHEN model_description ~* '.*918.*' THEN 'Super-Sportwagen'
                    WHEN model_description ~* '.*Panamera.*' THEN 'Panamera'
                    WHEN model_description ~* '.*Cayenne.*' THEN 'Cayenne'
                    WHEN model_description ~* '.*Macan.*' THEN 'Macan'
                    WHEN model_description ~* '.*911.*' THEN '911'
                    WHEN model_description ~* '.*Boxster.*' OR model_description ~* '.*Cayman.*' THEN 'Boxster'
                    WHEN model_description ~* '.*Taycan.*' THEN 'Taycan'
                    ELSE range
                    END
  - changeSet:
      id: update-manufacturer
      author: Karthik Narahari
      changes:
        - update:
            tableName: model_info
            schemaName: vehicle
            columns:
              - column:
                  name: manufacturer
                  value: 'Porsche'
            where: "id IN (SELECT m.id FROM vehicle.vehicle v INNER JOIN vehicle.model_info m ON m.id = v.model_info_id WHERE (v.source = 'PVH' OR v.source = 'PVH_IMPORT') AND m.manufacturer IS NULL)"

  - changeSet:
      id: update-model-info-range-for-718
      author: Karthik Narahari & Anji Yarram Reddy
      changes:
        - update:
            schemaName: vehicle
            tableName: model_info
            columns:
              - column:
                  name: range
                  valueComputed: CASE
                    WHEN model_description ~* '.*718.*' THEN 'Boxster'
                    ELSE range
                    END
            where: range IS NULL

  - changeSet:
      id: add-version-column-to-vehicle-table
      author: Karthik Narahari & Anji Yarram Reddy
      changes:
        - addColumn:
            tableName: vehicle
            schemaName: vehicle
            columns:
              - column:
                  name: version
                  type: bigint

  - changeSet:
      id: add-migration-and-default-for-version
      author: Karthik Narahari & Anji Yarram Reddy
      changes:
        - update:
            tableName: vehicle
            schemaName: vehicle
            columns:
              - column:
                  name: version
                  valueNumeric: 0
            where: version IS NULL
        - addDefaultValue:
            tableName: vehicle
            schemaName: vehicle
            columnName: version
            columnDataType: bigint
            defaultValueNumeric: 0
        - addNotNullConstraint:
            columnName: version
            constraintName: vehicle_version_key
            schemaName: vehicle
            tableName: vehicle
            validate: true

  - changeSet:
      id: rename-engine-table-and-columns
      author: Karthik Narahari
      changes:
        - renameColumn:
            schemaName: vehicle
            tableName: engine_info
            oldColumnName: engine_type
            newColumnName: drive_type
            columnDataType: VARCHAR(32)
        - renameTable:
            schemaName: vehicle
            oldTableName: engine_info
            newTableName: consumption_info
        - renameColumn:
            schemaName: vehicle
            tableName: vehicle
            oldColumnName: engine_info_id
            newColumnName: consumption_info_id
            columnDataType: bigint

  - changeSet:
      id: move-fuel-type-to-consumption
      author: Karthik Narahari
      changes:
        - addColumn:
            schemaName: vehicle
            tableName: consumption_info
            columns:
              - column:
                  name: primary_fuel_type
                  type: varchar(16)
              - column:
                  name: secondary_fuel_type
                  type: varchar(16)
  - changeSet:
      id: add-scrap-vehicle-field-to-fleet-info
      author: Vineeth Venudasan
      changes:
        - addColumn:
            tableName: fleet_info
            schemaName: vehicle
            columns:
              - column:
                  name: scrap_vehicle
                  type: boolean
        - sql:
            sql: |
              WITH vehicle_ids_to_scrap_vehicle AS (
                  SELECT v."id" AS "vehicle_id", v."fleet_info_id", oi."scrap_vehicle"
                  FROM vehicle."order_info" oi
                  JOIN "vehicle"."vehicle" v ON v.order_info_id = oi.id
                  WHERE scrap_vehicle IS NOT NULL AND fleet_info_id IS NOT NULL
              ) UPDATE "vehicle".fleet_info
                SET "scrap_vehicle" = vehicle_ids_to_scrap_vehicle.scrap_vehicle
                FROM vehicle_ids_to_scrap_vehicle
                WHERE id = fleet_info_id;
        - sql:
            sql: |
              ALTER TABLE "vehicle"."fleet_info" ADD COLUMN vehicle_id UUID;
              
              WITH  vehicle_ids_to_scrap_vehicle AS (
                SELECT v."id" AS "vehicle_id", oi.id AS "order_info_id", oi."scrap_vehicle"
                FROM vehicle."order_info" oi
                JOIN "vehicle"."vehicle" v ON v.order_info_id = oi.id
                WHERE scrap_vehicle IS NOT NULL AND fleet_info_id IS NULL
              ),
                    new_fleet_info_records AS (
                INSERT INTO "vehicle"."fleet_info" ("scrap_vehicle", "vehicle_id")
                  SELECT scrap_vehicle, "vehicle_id"
                  FROM vehicle_ids_to_scrap_vehicle
                RETURNING id, "vehicle_id"
              )
              UPDATE "vehicle"."vehicle" v SET fleet_info_id = nfir.id FROM new_fleet_info_records nfir WHERE nfir.vehicle_id = v.id;
              
              ALTER TABLE "vehicle"."fleet_info" DROP COLUMN vehicle_id;
  - changeSet:
      id: migrate-fuel-type-data-from-vehicle-to-consumption
      author: Karthik Narahari
      changes:
        - sql:
            splitStatements: false
            stripComments: false
            sql: |
              DO
              $func$
              DECLARE r RECORD; cid bigint;
              BEGIN
                  FOR r IN
                      SELECT v.id AS vehicle_id, v.fuel_type, v.fuel_type_2
                      FROM vehicle.vehicle v
                      WHERE v.consumption_info_id IS NULL AND (v.fuel_type IS NOT NULL OR v.fuel_type_2 IS NOT NULL)
                      LOOP
                          INSERT INTO vehicle.consumption_info (primary_fuel_type, secondary_fuel_type)
                          VALUES (r.fuel_type, r.fuel_type_2)
                          RETURNING id INTO cid;

                          UPDATE vehicle.vehicle
                          SET consumption_info_id = cid
                          WHERE id = r.vehicle_id;
                      END LOOP;
              END;
              $func$;
  - changeSet:
      id: drop-fuel-types-from-vehicle
      author: Karthik Narahari
      changes:
        - dropColumn:
            columnName: fuel_type
            schemaName: vehicle
            tableName: vehicle
        - dropColumn:
            columnName: fuel_type_2
            schemaName: vehicle
            tableName: vehicle
  - changeSet:
      id: add-evaluation-info-table
      author: Alon Cohn
      changes:
        - createTable:
            schemaName: vehicle
            tableName: evaluation_info
            columns:
              - column:
                  name: id
                  type: bigint
                  autoIncrement: true
                  constraints:
                    primaryKey: true
              - column:
                  name: vehicle_evaluation_comment
                  type: VARCHAR
              - column:
                  name: pc_complaint_check_comment
                  type: VARCHAR
              - column:
                  name: appraisal_net_price
                  type: decimal(19, 2)
        - addColumn:
            schemaName: vehicle
            tableName: vehicle
            columns:
              - column:
                  name: evaluation_info_id
                  type: bigint
        - addForeignKeyConstraint:
            baseTableName: vehicle
            baseColumnNames: evaluation_info_id
            baseTableSchemaName: vehicle
            referencedTableSchemaName: vehicle
            referencedTableName: evaluation_info
            referencedColumnNames: id
            constraintName: vehicle_evaluation_info_id_fkey
            validate: true

  - changeSet:
      id: add-non-customer-adequate-to-vehicle-option-table
      author: Anji Yarram Reddy
      changes:
        - addColumn:
            tableName: vehicle_option_tags
            schemaName: vehicle
            columns:
              - column:
                  name: non_customer_adequate
                  type: boolean
                  defaultValue: "false"

  - changeSet:
      id: drop-duplicate-option-tag-ids
      author: Anji Yarram Reddy & Christoph Portmann
      changes:
        - sql:
            sql: |
              WITH option_ids_ordered AS (
              SELECT
              id,
              ROW_NUMBER() OVER (PARTITION BY option_id ORDER BY created_at DESC) AS rn
              FROM
              vehicle.vehicle_option_tags
              )
              
              DELETE FROM vehicle.vehicle_option_tags
              WHERE id IN (
              SELECT id
              FROM option_ids_ordered
              WHERE rn > 1
              );

  - changeSet:
      id: add-unique-and-non-null-constraints-on-vehicle-option-tag-id
      author: Anji Yarram Reddy & Christoph Portmann
      changes:
        - addNotNullConstraint:
            columnName: option_id
            constraintName: option_tag_id_not_null
            schemaName: vehicle
            tableName: vehicle_option_tags
            validate: true
            columnDataType: VARCHAR(32)
        - addUniqueConstraint:
            columnNames: option_id
            constraintName: option_tag_id_unique
            schemaName: vehicle
            tableName: vehicle_option_tags
            validate: true
        - dropUniqueConstraint:
            constraintName: uq_vehicle_option_tags_id_description
            schemaName: vehicle
            tableName: vehicle_option_tags
            uniqueColumns: option_id, option_description

  - changeSet:
      id: add-blocked-for-sale-source-to-vehicle-order
      author: Anji Yarram Reddy
      changes:
        - addColumn:
            tableName: order_info
            schemaName: vehicle
            columns:
              - column:
                  name: blocked_for_sale_source
                  type: varchar(255)
                  constraints:
                    nullable: true
        - update:
            tableName: order_info
            schemaName: vehicle
            columns:
              - column:
                  name: blocked_for_sale_source
                  value: 'CONSIGNEE_DATA'
            where: "blocked_for_sale = true"
  - changeSet:
      id: move-displacement-to-engine-capacity-if-required
      author: Vineeth Venudasan
      changes:
        - update:
            tableName: technical_info
            schemaName: vehicle
            columns:
              - column:
                  name: engine_capacity
                  valueComputed: CAST(displacement AS NUMERIC(19,2))
            where: displacement IS NOT NULL AND engine_capacity IS NULL
  - changeSet:
      id: add-is-activated-for-accounting
      author: Ivan Plese
      changes:
        - addColumn:
            schemaName: vehicle
            tableName: vehicle
            columns:
              - column:
                  name: is_activated_for_accounting
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValue: "false"
  - changeSet:
      id: update-is-activated-for-accounting
      author: Ivan Plese
      changes:
        - update:
            schemaName: vehicle
            tableName: vehicle
            columns:
              - column:
                  name: is_activated_for_accounting
                  valueComputed: >
                    CASE
                      WHEN status IN ('SX97', 'SX98', 'SX99', 'SX90', 'SX100') OR asset_type IS NULL THEN false
                      ELSE true
                    END
            where: is_activated_for_accounting IS NULL
  - changeSet:
      id: run-with-no-where-clause-on-is-activated-for-accounting
      author: Ivan Plese
      changes:
        - update:
            schemaName: vehicle
            tableName: vehicle
            columns:
              - column:
                  name: is_activated_for_accounting
                  valueComputed: >
                    CASE
                      WHEN status IN ('SX97', 'SX98', 'SX99', 'SX90', 'SX100') OR asset_type IS NULL THEN false
                      ELSE true
                    END
  - changeSet:
      id: rename-asset-type-column
      author: Alon Cohn
      changes:
        - renameColumn:
            schemaName: vehicle
            tableName: vehicle
            oldColumnName: asset_type
            newColumnName: financial_asset_type
            columnDataType: varchar
  - changeSet:
      id: Update column value to new enum
      author: Alon Cohn
      changes:
        - update:
            schemaName: vehicle
            tableName: vehicle
            columns:
              - column:
                  name: financial_asset_type
                  value: AV
            where: financial_asset_type = 'CAPITAL_ASSET'

        - update:
            schemaName: vehicle
            tableName: vehicle
            columns:
              - column:
                  name: financial_asset_type
                  value: UV
            where: financial_asset_type = 'CURRENT_ASSET'

  - changeSet:
      id: add-factory-net-price-to-price-table
      author: Karthik Narahari
      changes:
        - addColumn:
            tableName: price_info
            schemaName: vehicle
            columns:
              - column:
                  name: factory_net_price_eur
                  type: decimal(19, 2)
  - changeSet:
      id: add-pia-event-to-price-table
      author: Karthik Narahari
      changes:
        - addColumn:
            tableName: price_info
            schemaName: vehicle
            columns:
              - column:
                  name: pia_event
                  type: jsonb

  - changeSet:
      id: add-factory-gross-price-to-price-table
      author: Anji Yarram Reddy
      changes:
        - addColumn:
            tableName: price_info
            schemaName: vehicle
            columns:
              - column:
                  name: factory_gross_price_eur
                  type: decimal(19, 2)

  - changeSet:
      id: add-mileage-reading-table
      author: Manuel Jany
      changes:
        - createTable:
            schemaName: vehicle
            tableName: mileage_reading
            columns:
              - column:
                  name: id
                  type: uuid
                  constraints:
                    primaryKey: true
              - column:
                  name: vehicle_id
                  type: uuid
                  constraints:
                    nullable: false
              - column:
                  name: read_date
                  type: timestamptz
                  constraints:
                    nullable: false
              - column:
                  name: source
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: mileage
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: created_date
                  type: varchar
              - column:
                  name: created_by
                  type: varchar

  - changeSet:
      id: add-vehicle-id-fk-to-mileage-reading
      author: Manuel Jany
      changes:
        - addForeignKeyConstraint:
            baseTableName: mileage_reading
            baseColumnNames: vehicle_id
            baseTableSchemaName: vehicle
            referencedTableSchemaName: vehicle
            referencedTableName: vehicle
            referencedColumnNames: id
            constraintName: mileage_reading_vehicle_id_fkey
            validate: true

  - changeSet:
      id: create-index-mileage-reading-vehicle-id
      author: Manuel Jany
      changes:
        - createIndex:
            tableName: mileage_reading
            schemaName: vehicle
            indexName: idx_mr_v_id
            columns:
              - column:
                  name: vehicle_id
      rollback:
        - dropIndex:
            tableName: mileage_reading
            schemaName: vehicle
            indexName: idx_mr_v_id

  - changeSet:
      id: add-postgres-view-vehicle-current-mileage
      author: Manuel Jany
      runOrder: last
      runAlways: false
      runOnChange: true
      changes:
        - sqlFile:
            relativeToChangelogFile: true
            path: 'vehicledata/setup-postgres-view-for-vehicle-current-mileage.sql'
            stripComments: true

  - changeSet:
      id: remove-vehicle-id-fk-to-mileage-reading
      author: Manuel Jany
      changes:
        - dropForeignKeyConstraint:
            baseTableName: mileage_reading
            baseTableSchemaName: vehicle
            constraintName: mileage_reading_vehicle_id_fkey

  - changeSet:
      id: migrate-vehicle-source-from-fms-to-manual
      author: Anji Yarram Reddy & Karthik Narahari
      changes:
        - update:
            tableName: vehicle
            schemaName: vehicle
            columns:
              - column:
                  name: source
                  value: 'MANUAL'
            where: "source = 'FMS'"

  - changeSet:
      id: update-power-kw-from-engine-power
      author: Karthik Narahari
      changes:
        - sql:
            sql: >
              UPDATE vehicle.technical_info
              SET power_kw = TRUNC(engine_power)
              WHERE engine_power IS NOT NULL AND power_kw IS NULL;

  - changeSet:
      id: drop-engine-power-from-technical-info
      author: Karthik Narahari
      failOnError: false
      preConditions:
        - onFail: CONTINUE
        - columnExists:
            columnName: engine_power
            schemaName: vehicle
            tableName: technical_info
      comment: Remove duplicate column engine_power from technical_info
      changes:
        - dropColumn:
            columnName: engine_power
            schemaName: vehicle
            tableName: technical_info

  - changeSet:
      id: add-is-classic-flage-to-fleet-info
      author: Christoph Portmann (TW)
      changes:
        - addColumn:
            schemaName: vehicle
            tableName: fleet_info
            columns:
              - column:
                  name: is_classic
                  type: boolean
                  defaultValue: "false"

  - changeSet:
      id: add-not-null-constraint-to-fleet-info-scrap-vehicle
      author: Manuel Jany
      changes:
        - addNotNullConstraint:
            columnDataType: boolean
            columnName: scrap_vehicle
            constraintName: nn_scrap_vehicle
            defaultNullValue: "false"
            tableName: fleet_info
            schemaName: vehicle
            validate: true

  - changeSet:
      id: add-not-null-constraint-to-order-info-preproduction-vehicle
      author: Manuel Jany
      changes:
        - addNotNullConstraint:
            columnDataType: boolean
            columnName: preproduction_vehicle
            constraintName: nn_preproduction_vehicle
            defaultNullValue: "false"
            tableName: order_info
            schemaName: vehicle
            validate: true

  - changeSet:
      id: add-not-null-constraint-to-order-info-blocked-for-sale-vehicle
      author: Manuel Jany
      changes:
        - addNotNullConstraint:
            columnDataType: boolean
            columnName: blocked_for_sale
            constraintName: nn_blocked_for_sale
            defaultNullValue: "false"
            tableName: order_info
            schemaName: vehicle
            validate: true

  - changeSet:
      id: add-not-null-constraint-to-fleet-info-residual-value-market
      author: Manuel Jany
      changes:
        - addNotNullConstraint:
            columnDataType: boolean
            columnName: is_residual_value_market
            constraintName: nn_is_residual_value_market
            defaultNullValue: "false"
            tableName: fleet_info
            schemaName: vehicle
            validate: true

  - changeSet:
      id: add-not-null-constraint-to-delivery-info-preparation-necessary
      author: Manuel Jany
      changes:
        - addNotNullConstraint:
            columnDataType: boolean
            columnName: is_preparation_necessary
            constraintName: nn_is_preparation_necessary
            defaultNullValue: "false"
            tableName: delivery_info
            schemaName: vehicle
            validate: true

  - changeSet:
      id: add-not-null-constraint-to-fleet-info-classic
      author: Manuel Jany
      changes:
        - addNotNullConstraint:
            columnDataType: boolean
            columnName: is_classic
            constraintName: nn_is_classic
            defaultNullValue: "false"
            tableName: fleet_info
            schemaName: vehicle
            validate: true

  - changeSet:
      id: add-not-null-constraint-to-return-info-used-car
      author: Manuel Jany
      changes:
        - addNotNullConstraint:
            columnDataType: boolean
            columnName: is_used_car
            constraintName: nn_is_used_car
            defaultNullValue: "false"
            tableName: return_info
            schemaName: vehicle
            validate: true

  - changeSet:
      id: add-not-null-constraint-to-embargo-info-embargo
      author: Manuel Jany
      changes:
        - addNotNullConstraint:
            columnDataType: boolean
            columnName: in_embargo
            constraintName: nn_in_embargo
            defaultNullValue: "false"
            tableName: embargo_info
            schemaName: vehicle
            validate: true

  - changeSet:
      id: add-financial-asset-type-update-table
      author: sonia balodi
      changes:
        - createTable:
            schemaName: vehicle
            tableName: financial_asset_type_update
            columns:
              - column:
                  name: id
                  type: uuid
                  constraints:
                    primaryKey: true
              - column:
                  name: vin
                  type: varchar
                  constraints:
                    nullable: false
                    unique: true
              - column:
                  name: financial_asset_type
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: created_at
                  type: timestamptz

  - changeSet:
      id: make-vguid-nullable
      author: Karthik Narahari
      changes:
        - update:
            tableName: vehicle
            schemaName: vehicle
            columns:
              - column:
                  name: vguid
                  value: null
            where: "vguid = ''"
  - changeSet:
      id: make-vguid-nullable
      author: Vineeth Venudasan & Christoph Portmann
      changes:
        - addColumn:
            tableName: price_info
            schemaName: vehicle
            columns:
              - column:
                  name: new_vehicle_invoice_date
                  type: date
  - changeSet:
      id: create-confidentiality-info-table
      author: Manuel Jany
      changes:
        - createTable:
            tableName: confidentiality_info
            schemaName: vehicle
            columns:
              - column:
                  name: id
                  type: BIGINT
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: subject_to_confidentiality
                  type: boolean
                  constraints:
                    nullable: true
              - column:
                  name: confidentiality_classification
                  type: varchar
              - column:
                  name: subject_to_confidentiality_start_date
                  type: timestamp(6)
              - column:
                  name: subject_to_confidentiality_end_date
                  type: timestamp(6)
              - column:
                  name: record_factory_exit
                  type: boolean
                  constraints:
                    nullable: true
              - column:
                  name: camouflage_required
                  type: boolean
                  constraints:
                    nullable: true

  - changeSet:
      id: add-confidentiality-info-id-to-vehicle
      author: Manuel Jany
      changes:
        - addColumn:
            tableName: vehicle
            schemaName: vehicle
            columns:
              - column:
                  name: confidentiality_info_id
                  type: bigint

  - changeSet:
      id: add-confidentiality-info-id-fk
      author: Manuel Jany
      changes:
        - addForeignKeyConstraint:
            baseTableName: vehicle
            baseColumnNames: confidentiality_info_id
            baseTableSchemaName: vehicle
            referencedTableSchemaName: vehicle
            referencedTableName: confidentiality_info
            referencedColumnNames: id
            constraintName: vehicle_confidentiality_info_id_fkey
            validate: true

  - changeSet:
      id: add-model-description-development-to-model-info-table
      author: Manuel Jany
      changes:
        - addColumn:
            tableName: model_info
            schemaName: vehicle
            columns:
              - column:
                  name: model_description_development
                  type: varchar

  - changeSet:
      id: add-range-development-to-model-info-table
      author: Manuel Jany
      changes:
        - addColumn:
            tableName: model_info
            schemaName: vehicle
            columns:
              - column:
                  name: range_development
                  type: varchar

  - changeSet:
      id: add-tuev-appointment-to-vehicle-table
      author: Manuel Jany
      changes:
        - addColumn:
            tableName: vehicle
            schemaName: vehicle
            columns:
              - column:
                  name: tuev_appointment
                  type: timestamp(6)
  - changeSet:
      id: add-internal-designation-to-vehicle-table
      author: Manuel Jany
      changes:
        - addColumn:
            tableName: vehicle
            schemaName: vehicle
            columns:
              - column:
                  name: internal_designation
                  type: varchar
  - changeSet:
      id: cleanup-invalid-vehicle-type
      author: Ivan Plese
      changes:
        - update:
            schemaName: vehicle
            tableName: model_info
            columns:
              - column:
                  name: vehicle_type
                  value: null
            where: vehicle_type NOT IN ('PKW','TRUCK','TRAILER','MOTORCYCLE','TRANSPORTER')

  - changeSet:
      id: add-race-car-flage-to-fleet-info
      author: Anji Yarram Reddy
      changes:
        - addColumn:
            schemaName: vehicle
            tableName: fleet_info
            columns:
              - column:
                  name: race_car
                  type: boolean
                  defaultValue: "false"

  -  changeSet:
       id: rename-confidentiality-info-to-vtstamm-info
       author:  Manuel Jany
       changes:
         -  renameTable:
              newTableName:  vtstamm_info
              oldTableName:  confidentiality_info
              schemaName:  vehicle

  - changeSet:
      id: rename-confidentiality-info-id-to-vtstamm-info-id
      author: liquibase-docs
      changes:
        - renameColumn:
            columnDataType: varchar
            newColumnName: vtstamm_info_id
            oldColumnName: confidentiality_info_id
            schemaName: vehicle
            tableName: vehicle

  - changeSet:
      id: add-internal-designation-to-vtstamm-table
      author: Manuel Jany
      changes:
        - addColumn:
            tableName: vtstamm_info
            schemaName: vehicle
            columns:
              - column:
                  name: internal_designation
                  type: varchar
  - changeSet:
      id: renames-internal-designation-in-vehicle-table
      author: Manuel Jany
      changes:
        - renameColumn:
            tableName: vehicle
            schemaName: vehicle
            oldColumnName: internal_designation
            newColumnName: unused_internal_designation

  - changeSet:
      id: add-type-of-use-to-vtstamm-table
      author: Manuel Jany
      changes:
        - addColumn:
            tableName: vtstamm_info
            schemaName: vehicle
            columns:
              - column:
                  name: type_of_use_vts
                  type: varchar

  - changeSet:
      id: add-status-of-use-to-vtstamm-table
      author: Manuel Jany
      changes:
        - addColumn:
            tableName: vtstamm_info
            schemaName: vehicle
            columns:
              - column:
                  name: status_vts
                  type: varchar
  - changeSet:
      id: nullify-technical-info-for-non-german-vehicles
      author: Christoph Portmann (TW) and Karthik Narahari
      comment: Set technical info fields to null for vehicles where country info cnr_value is not C00 (Germany)
      changes:
        - sql:
            sql: |
              UPDATE vehicle.technical_info 
              SET 
                cargo_volume = NULL,
                length = NULL,
                width_mirrors_folded = NULL,
                height = NULL,
                top_speed = NULL,
                acceleration_0100_kmh = NULL,
                power_kw = NULL,
                maximum_payload = NULL,
                curb_weight_din = NULL,
                curb_weight_eu = NULL,
                gross_vehicle_weight = NULL,
                engine_capacity = NULL,
                vehicle_width_mirrors_extended = NULL,
                maximum_charging_power_dc = NULL,
                charging_time_ac_22_kw = NULL,
                charging_time_ac_11_kw_0100 = NULL,
                charging_time_ac_96_kw_0100 = NULL,
                net_battery_capacity = NULL,
                gross_battery_capacity = NULL,
                acceleration_0100_kmh_launch_control = NULL,
                acceleration_80_120_kmh = NULL,
                max_roof_load_with_porsche_roof_transport_system = NULL,
                charging_time_dc_max_power_580 = NULL,
                total_power_kw = NULL
              WHERE id IN (
                SELECT ti.id 
                FROM vehicle.technical_info ti
                INNER JOIN vehicle.vehicle v ON v.technical_info_id = ti.id
                INNER JOIN vehicle.country_info ci ON v.country_info_id = ci.id
                WHERE ci.cnr_value != 'C00'
              )

  - changeSet:
      id: nullify-incorrect-factory-price-info
      author: Karthik Narahari
      comment: Remove incorrect factory prices from migration data
      changes:
        - sql:
            sql: |
              UPDATE vehicle.price_info 
              SET 
                factory_net_price_eur = null,
                factory_gross_price_eur = null
              WHERE id IN (
                SELECT pi.id 
                FROM vehicle.price_info pi
                INNER JOIN vehicle.vehicle v ON v.price_info_id = pi.id
                WHERE (pi.factory_net_price_eur = 0 OR pi.factory_gross_price_eur = 0)
              )

  - changeSet:
      id: add-index-vehicle-status-lower
      author: Karthik Narahari
      changes:
        - sql:
            dbms: postgresql
            sql: "CREATE INDEX IF NOT EXISTS idx_vehicle_status_lower ON vehicle.vehicle (LOWER(status));"

  - changeSet:
      id: add-index-vehicle-status
      author: Karthik Narahari
      changes:
        - sql:
            dbms: postgresql
            sql: "CREATE INDEX IF NOT EXISTS idx_vehicle_status ON vehicle.vehicle (status);"
