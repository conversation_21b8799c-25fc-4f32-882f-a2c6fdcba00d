/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

/* cleans up any deprecated view */

DO
$$
    DECLARE
        r RECORD;
    BEGIN
        FOR r IN
            SELECT views.table_schema, table_name
            FROM information_schema.views
            WHERE views.table_schema = 'vehicle'
              AND (table_name LIKE 'vehicle_manager_vehicle_deprecated%' or
                   table_name LIKE 'vehicle_manager_vehicle_current_mileage_deprecated%')
            LOOP
                BEGIN
                    EXECUTE 'DROP VIEW IF EXISTS ' || r.table_schema || '.' || r.table_name;
                EXCEPTION
                    WHEN OTHERS THEN
                        RAISE NOTICE 'Error while dropping view: %, %', r.table_name, SQLERRM;
                END;
            END LOOP;
    END
$$;
