/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
ALTER VIEW IF EXISTS vehicle.vehicle_manager_vehicle_current_mileage RENAME TO vehicle_manager_vehicle_current_mileage_deprecated;

CREATE VIEW vehicle.vehicle_manager_vehicle_current_mileage(vehicle_id, read_date, mileage) AS
SELECT vehicle_id,
       read_date,
       mileage
FROM (SELECT vehicle_id,
             read_date,
             mileage,
             ROW_NUMBER() OVER (PARTITION BY vehicle_id ORDER BY read_date DESC) as rn
      FROM vehicle.mileage_reading) ranked
WHERE rn = 1;
