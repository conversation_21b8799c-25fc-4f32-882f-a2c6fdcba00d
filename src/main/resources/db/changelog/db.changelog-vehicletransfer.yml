databaseChangeLog:
  - changeSet:
      id: create-cost-center-table
      author: manuel jany
      changes:
        - createTable:
            tableName: cost_center
            schemaName: vehicletransfer
            columns:
              - column:
                  name: id
                  type: uuid
                  constraints:
                    primaryKey: true
              - column:
                  name: cost_center_id
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: description
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: version
                  type: int
                  constraints:
                    nullable: false
      rollback:
        - dropTable:
            tableName: cost_center
            schemaName: vehicletransfer

  - changeSet:
      id: create-usage-group-table
      author: manuel jany
      changes:
        - createTable:
            tableName: usage_group
            schemaName: vehicletransfer
            columns:
              - column:
                  name: id
                  type: uuid
                  constraints:
                    primaryKey: true
              - column:
                  name: usage_group_id
                  type: bigint
                  constraints:
                    nullable: false
              - column:
                  name: description
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: version
                  type: int
                  constraints:
                    nullable: false
      rollback:
        - dropTable:
            tableName: usage_group
            schemaName: vehicletransfer

  - changeSet:
      id: create-vehicle-usage-table
      author: manuel jany
      changes:
        - createTable:
            tableName: vehicle_usage
            schemaName: vehicletransfer
            columns:
              - column:
                  name: id
                  type: uuid
                  constraints:
                    primaryKey: true
              - column:
                  name: usage_id
                  type: bigint
                  constraints:
                    nullable: false
              - column:
                  name: usage
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: version
                  type: int
                  constraints:
                    nullable: false
      rollback:
        - dropTable:
            tableName: vehicle_usage
            schemaName: vehicletransfer

  - changeSet:
      id: create-consignee-data-table
      author: manuel jany
      changes:
        - createTable:
            tableName: consignee_data
            schemaName: vehicletransfer
            columns:
              - column:
                  name: id
                  type: uuid
                  constraints:
                    primaryKey: true
              - column:
                  name: consignee
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: description
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: maximum_service_life_months
                  type: int
              - column:
                  name: vehicle_usage_id
                  type: uuid
                  constraints:
                    foreignKeyName: consignee_data_vehicle_usage_id_fkey
                    referencedColumnNames: id
                    referencedTableName: vehicle_usage
                    referencedTableSchemaName: vehicletransfer
              - column:
                  name: vehicle_responsible_person
                  type: varchar
              - column:
                  name: internal_contact_person
                  type: varchar
              - column:
                  name: preproduction_vehicle
                  type: boolean
                  constraints:
                    nullable: false
              - column:
                  name: blocked_for_sale
                  type: boolean
                  constraints:
                    nullable: false
              - column:
                  name: scrapped
                  type: boolean
                  constraints:
                    nullable: false
              - column:
                  name: cost_center_carrier_id
                  type: uuid
                  constraints:
                    foreignKeyName: consignee_data_cost_center_id_fkey
                    referencedColumnNames: id
                    referencedTableName: cost_center
                    referencedTableSchemaName: vehicletransfer
              - column:
                  name: responsible_cost_center_id
                  type: uuid
                  constraints:
                    foreignKeyName: consignee_data_responsible_cost_center_id_fkey
                    referencedColumnNames: id
                    referencedTableName: cost_center
                    referencedTableSchemaName: vehicletransfer
              - column:
                  name: internal_order_number
                  type: varchar
              - column:
                  name: validation_of_leasing_privileges
                  type: varchar
              - column:
                  name: usage_group_id
                  type: uuid
                  constraints:
                    foreignKeyName: consignee_data_usage_group_id_fkey
                    referencedColumnNames: id
                    referencedTableName: usage_group
                    referencedTableSchemaName: vehicletransfer
              - column:
                  name: vehicle_type
                  type: varchar
              - column:
                  name: manufacturer
                  type: varchar
              - column:
                  name: version
                  type: int
                  constraints:
                    nullable: false
      rollback:
        - dropTable:
            tableName: consignee_data
            schemaName: vehicletransfer

  - changeSet:
      id: create-index-consignee-data-vehicle_usage-id
      author: manuel jany
      changes:
        - createIndex:
            tableName: consignee_data
            schemaName: vehicletransfer
            indexName: idx_cd_vh_id
            columns:
              - column:
                  name: vehicle_usage_id
      rollback:
        - dropIndex:
            tableName: consignee_data
            schemaName: vehicletransfer
            indexName: idx_cd_vh_id

  - changeSet:
      id: create-index-consignee-data-cost-center-carrier-id
      author: manuel jany
      changes:
        - createIndex:
            tableName: consignee_data
            schemaName: vehicletransfer
            indexName: idx_cd_ccc_id
            columns:
              - column:
                  name: cost_center_carrier_id
      rollback:
        - dropIndex:
            tableName: consignee_data
            schemaName: vehicletransfer
            indexName: idx_cd_ccc_id

  - changeSet:
      id: create-index-consignee-data-responsible-cost-center-id
      author: manuel jany
      changes:
        - createIndex:
            tableName: consignee_data
            schemaName: vehicletransfer
            indexName: idx_cd_rcc_id
            columns:
              - column:
                  name: responsible_cost_center_id
      rollback:
        - dropIndex:
            tableName: consignee_data
            schemaName: vehicletransfer
            indexName: idx_cd_rcc_id

  - changeSet:
      id: create-index-consignee-data-usage-group-id
      author: manuel jany
      changes:
        - createIndex:
            tableName: consignee_data
            schemaName: vehicletransfer
            indexName: idx_cd_ug_id
            columns:
              - column:
                  name: usage_group_id
      rollback:
        - dropIndex:
            tableName: consignee_data
            schemaName: vehicletransfer
            indexName: idx_cd_ug_id

  - changeSet:
      id: drop-index-consignee-data-responsible-cost-center-id
      author: manuel jany
      changes:
        - dropIndex:
            tableName: consignee_data
            schemaName: vehicletransfer
            indexName: idx_cd_rcc_id
      rollback:
        - createIndex:
            tableName: consignee_data
            schemaName: vehicletransfer
            indexName: idx_cd_rcc_id
            columns:
              - column:
                  name: responsible_cost_center_id

  - changeSet:
      id: drop-responsible-cost-center-id-for-consignee-data
      author: manuel jany
      changes:
        - dropColumn:
            columnName: responsible_cost_center_id
            tableName: consignee_data
            schemaName: vehicletransfer
      rollback:
        - addColumn:
            tableName: consignee_data
            schemaName: vehicletransfer
            columns:
              - column:
                  name: responsible_cost_center_id
                  type: uuid
                  constraints:
                    foreignKeyName: consignee_data_responsible_cost_center_id_fkey
                    referencedColumnNames: id
                    referencedTableName: cost_center
                    referencedTableSchemaName: vehicletransfer

  - changeSet:
      id: add-responsible-cost-center-for-consignee-data
      author: manuel jany
      changes:
        - addColumn:
            tableName: consignee_data
            schemaName: vehicletransfer
            columns:
              - column:
                  name: responsible_cost_center
                  type: varchar
      rollback:
        - dropColumn:
            columnName: responsible_cost_center
            tableName: consignee_data
            schemaName: vehicletransfer
  - changeSet:
      id: create-planned-vehicle-transfer-table
      author: Alon Cohn
      changes:
        - createTable:
            tableName: planned_vehicle_transfer
            schemaName: vehicletransfer
            columns:
              - column:
                  name: id
                  type: uuid
                  constraints:
                    primaryKey: true
              - column:
                  name: vehicle_id
                  type: uuid
                  constraints:
                    nullable: false
              - column:
                  name: consignee
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: business_partner_id
                  type: varchar
              - column:
                  name: vehicle_responsible_person
                  type: varchar
              - column:
                  name: maximum_service_life_in_months
                  type: int
              - column:
                  name: internal_contact_person
                  type: varchar
              - column:
                  name: is_preproduction_vehicle
                  type: boolean
              - column:
                  name: is_blocked_for_sale
                  type: boolean
              - column:
                  name: is_scrapped
                  type: boolean
              - column:
                  name: cost_center_carrier_id
                  type: uuid
                  constraints:
                    foreignKeyName: planned_vehicle_transfer_cost_center_id_fkey
                    referencedColumnNames: id
                    referencedTableName: cost_center
                    referencedTableSchemaName: vehicletransfer
              - column:
                  name: responsible_cost_center
                  type: varchar
              - column:
                  name: internal_order_number
                  type: varchar
              - column:
                  name: vehicle_usage_id
                  type: uuid
                  constraints:
                    foreignKeyName: planned_vehicle_transfer_vehicle_usage_id_fkey
                    referencedColumnNames: id
                    referencedTableName: vehicle_usage
                    referencedTableSchemaName: vehicletransfer
              - column:
                  name: usage_group_id
                  type: uuid
                  constraints:
                    foreignKeyName: planned_vehicle_transfer_usage_group_id_fkey
                    referencedColumnNames: id
                    referencedTableName: usage_group
                    referencedTableSchemaName: vehicletransfer
              - column:
                  name: validation_of_leasing_privileges
                  type: varchar
              - column:
                  name: version
                  type: int
                  constraints:
                    nullable: false
      rollback:
        - dropTable:
            tableName: planned_vehicle_transfer
            schemaName: vehicletransfer
  - changeSet:
      id: update-vehicle-id-as-unique
      author: Alon Cohn
      changes:
        - addUniqueConstraint:
            tableName: planned_vehicle_transfer
            columnNames: vehicle_id
            constraintName: unique_vehicle_id
            schemaName: vehicletransfer
            validate: true
  - changeSet:
      id: make-consignee-nullable
      author: Alon Cohn
      changes:
        - dropNotNullConstraint:
            columnName: consignee
            tableName: planned_vehicle_transfer
            columnDataType: varchar
            schemaName: vehicletransfer
      rollback:
        - addNotNullConstraint:
            columnName: consignee
            tableName: planned_vehicle_transfer
            columnDataType: varchar
            schemaName: vehicletransfer
  - changeSet:
      id: add-initialized-column
      author: Alon Cohn
      changes:
        - addColumn:
            schemaName: vehicletransfer
            tableName: planned_vehicle_transfer
            columns:
              - column:
                  name: initialized
                  type: boolean
                  defaultValueBoolean: false
                  constraints:
                    nullable: false
      rollback:
        - dropColumn:
            columnName: initialized
            schemaName: vehicletransfer
            tableName: planned_vehicle_transfer
  - changeSet:
      id: create-index-initialized
      author: Alon Cohn
      changes:
        - createIndex:
            tableName: planned_vehicle_transfer
            schemaName: vehicletransfer
            indexName: idx_pvt_initialized
            columns:
              - column:
                  name: initialized
      rollback:
        - dropIndex:
            tableName: planned_vehicle_transfer
            schemaName: vehicletransfer
            indexName: idx_pvt_initialized

  - changeSet:
      id: alter-leasing-privilege-column-name-planned-vehicle-transfer-table
      author: Manuel Jany
      changes:
        - renameColumn:
            tableName: planned_vehicle_transfer
            schemaName: vehicletransfer
            oldColumnName: validation_of_leasing_privileges
            newColumnName: required_leasing_privilege
            columnDataType: varchar
      rollback:
        - renameColumn:
            tableName: planned_vehicle_transfer
            schemaName: vehicletransfer
            oldColumnName: required_leasing_privilege
            newColumnName: validation_of_leasing_privileges
            columnDataType: varchar

  - changeSet:
      id: drop-foreignkey-cost-center-id
      author: sonia balodi
      changes:
        - dropForeignKeyConstraint:
            baseTableName: planned_vehicle_transfer
            constraintName: planned_vehicle_transfer_cost_center_id_fkey
            baseTableSchemaName: vehicletransfer

  - changeSet:
      id: drop-foreignkey-usage-group-id
      author: sonia balodi
      changes:
        - dropForeignKeyConstraint:
            baseTableName: planned_vehicle_transfer
            constraintName: planned_vehicle_transfer_usage_group_id_fkey
            baseTableSchemaName: vehicletransfer

  - changeSet:
      id: drop-foreignkey-vehicle-usage-id
      author: sonia balodi
      changes:
        - dropForeignKeyConstraint:
            baseTableName: planned_vehicle_transfer
            constraintName: planned_vehicle_transfer_vehicle_usage_id_fkey
            baseTableSchemaName: vehicletransfer

  - changeSet:
      id: drop-deprecated-planned-vehicle-transfer-view-if-exists
      failOnError: false
      runAlways: true
      author: sonia balodi
      changes:
        - sql:
            sql: DROP VIEW IF EXISTS vehicletransfer.planned_vehicle_transfer_vs_view_deprecated_2024_11_26;

  - changeSet:
      id: drop-table-consignee-data
      author: sonia balodi
      changes:
        - dropTable:
            schemaName: vehicletransfer
            tableName: consignee_data

  - changeSet:
      id: drop-table-cost-center
      author: sonia balodi
      changes:
        - dropTable:
            schemaName: vehicletransfer
            tableName: cost_center

  - changeSet:
      id: drop-table-usage-group
      author: sonia balodi
      changes:
        - dropTable:
            schemaName: vehicletransfer
            tableName: usage_group

  - changeSet:
      id: drop-table-vehicle-usage
      author: sonia balodi
      changes:
        - dropTable:
            schemaName: vehicletransfer
            tableName: vehicle_usage

  - changeSet:
      id: drop-deprecated-planned-vehicle-transfer-view
      failOnError: false
      runAlways: true
      author: sonia balodi
      changes:
        - sql:
            sql: DROP VIEW IF EXISTS vehicletransfer.planned_vehicle_transfer_vs_view_deprecated;
  - changeSet:
      id: create-vehicle-transfer-table
      author: Alon Cohn
      changes:
        - createTable:
            tableName: vehicle_transfer
            schemaName: vehicletransfer
            columns:
              - column:
                  name: id
                  type: UUID
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: transfer_type
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: vehicle_id
                  type: UUID
                  constraints:
                    nullable: false
              - column:
                  name: consignee
                  type: varchar
              - column:
                  name: business_partner_id
                  type: varchar
              - column:
                  name: maximum_service_life_in_months
                  type: int
              - column:
                  name: responsible_cost_center
                  type: varchar
              - column:
                  name: internal_order_number
                  type: varchar
              - column:
                  name: usage_group_id
                  type: UUID
              - column:
                  name: vehicle_usage_id
                  type: UUID
              - column:
                  name: cost_center_carrier_id
                  type: UUID
              - column:
                  name: vehicle_responsible_person
                  type: varchar

              - column:
                  name: internal_contact_person
                  type: varchar
              - column:
                  name: planned_delivery_date
                  type: timestamp
              - column:
                  name: planned_return_date
                  type: timestamp
              - column:
                  name: remark
                  type: varchar
              - column:
                  name: license_plate
                  type: varchar
              - column:
                  name: initialized
                  type: boolean
                  defaultValueBoolean: false
                  constraints:
                    nullable: false
              - column:
                  name: delivery_date
                  type: timestamp
              - column:
                  name: return_date
                  type: timestamp
              - column:
                  name: latest_return_date
                  type: timestamp
              - column:
                  name: mileage_at_delivery
                  type: int
              - column:
                  name: mileage_at_return
                  type: int
              - column:
                  name: status
                  type: varchar
                  constraints:
                    nullable: false
                  defaultValue: 'PLANNED'
              - column:
                  name: version
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: created_by
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: created_date
                  type: timestamp
                  constraints:
                    nullable: false
              - column:
                  name: last_modified_by
                  type: varchar
              - column:
                  name: last_modified_date
                  type: timestamp
              - column:
                  name: vehicle_transfer_key
                  type: varchar
                  constraints:
                    nullable: false
      rollback:
        - dropTable:
            tableName: vehicle_transfer
            schemaName: vehicletransfer
  - changeSet:
      id: vehicle-transfer-key-as-unique-and-in-sequence
      author: Alon Cohn
      changes:
        - addUniqueConstraint:
            tableName: vehicle_transfer
            columnNames: vehicle_transfer_key
            constraintName: uq_vehicle_transfer_key
            schemaName: vehicletransfer
            validate: true
        - createSequence:
            sequenceName: vehicle_transfer_key_seq
            startValue: 1
            incrementBy: 1
            cacheSize: 1
            minValue: 1
            maxValue: 9999999999
            schemaName: vehicletransfer
            cycle: false
            dataType: bigint
      rollback:
        - dropUniqueConstraint:
            constraintName: uq_vehicle_transfer_key
            tableName: vehicle_transfer
            schemaName: vehicletransfer
        - dropSequence:
            sequenceName: vehicle_transfer_key_seq
            schemaName: vehicletransfer
  - changeSet:
      id: create-index
      author: Alon Cohn
      changes:
        - createIndex:
            indexName: index_vehicle_transfer_key
            tableName: vehicle_transfer
            schemaName: vehicletransfer
            columns:
              - column:
                  name: vehicle_transfer_key
        - createIndex:
            indexName: index_transfer_type
            tableName: vehicle_transfer
            schemaName: vehicletransfer
            columns:
              - column:
                  name: transfer_type
        - createIndex:
            indexName: index_status
            tableName: vehicle_transfer
            schemaName: vehicletransfer
            columns:
              - column:
                  name: status
  - changeSet:
      id: break-dependencies-of-existing-planned-vehicle-transfer-vs-view
      runAlways: false
      failOnError: true
      author: sonia balodi
      changes:
        - sql:
           sql: ALTER VIEW IF EXISTS vehicletransfer.planned_vehicle_transfer_vs_view RENAME TO planned_vehicle_transfer_vs_view_deprecated;
        - sql:
            sql: DROP VIEW IF EXISTS vehicletransfer.planned_vehicle_transfer_vs_view;
  - changeSet:
      id: drop-unused-vehicle_responsible_person_vs_view
      runAlways: false
      failOnError: true
      author: sonia balodi
      changes:
        - sql:
            sql: DROP VIEW IF EXISTS vehicletransfer.vehicle_responsible_person_vs_view;

  - changeSet:
      id: add-license-plate-last-update-column
      author: Alon Cohn
      changes:
        - addColumn:
            schemaName: vehicletransfer
            tableName: vehicle_transfer
            columns:
              - column:
                  name: license_plate_last_update
                  type: timestamp

  - changeSet:
      id: add-predecessor-and-latestReturnDateOfPredecessor
      author: sonia balodi
      changes:
        - addColumn:
            tableName: vehicle_transfer
            schemaName: vehicletransfer
            columns:
              - column:
                  name: predecessor_id
                  type: uuid
              - column:
                  name: predecessor_latest_return_date
                  type: timestamp

  - changeSet:
      id: add-predecessor-foreign-key-constraint
      author: sonia balodi
      changes:
        - addForeignKeyConstraint:
            baseTableName: vehicle_transfer
            baseColumnNames: predecessor_id
            baseTableSchemaName: vehicletransfer
            referencedTableSchemaName: vehicletransfer
            referencedTableName: vehicle_transfer
            referencedColumnNames: id
            constraintName: vehicle_transfer_predecessory_key_fkey
  - changeSet:
      id: create-index-predecessor
      author: sonia balodi
      changes:
        - createIndex:
            tableName: vehicle_transfer
            schemaName: vehicletransfer
            indexName: idx_vt_predecessor
            columns:
              - column:
                  name: predecessor_id
  - changeSet:
      id: add-utilizationArea-and-deliveryLeipzig
      author: Alon Cohn
      changes:
        - addColumn:
            tableName: vehicle_transfer
            schemaName: vehicletransfer
            columns:
              - column:
                  name: utilization_area
                  type: varchar
              - column:
                  name: delivery_leipzig
                  type: boolean

  - changeSet:
      id: drop-deprecated-vehicle-transfer-for-vehicle-transfer-manager
      failOnError: false
      runAlways: true
      author: Alon Cohn
      changes:
        - sql:
            sql: DROP VIEW IF EXISTS vehicletransfer.vehicle_transfer_manager_vehicle_transfer_deprecated_2025_02_06;

  - changeSet:
      id: fix-vehicle-transfer-discriminator-value
      author: Manuel Jany
      changes:
        - update:
            tableName: vehicle_transfer
            schemaName: vehicletransfer
            columns:
              - column:
                  name: transfer_type
                  value: 'ACTUAL'
            where: transfer_type='ACUTAL'

  - changeSet:
      id: add-desired-delivery-date-and-provision-for-delivery-comment-and-delivery-comment
      author: sonia balodi
      changes:
        - addColumn:
            tableName: vehicle_transfer
            schemaName: vehicletransfer
            columns:
              - column:
                  name: desired_delivery_date
                  type: timestamp
              - column:
                  name: provision_for_delivery_comment
                  type: varchar
              - column:
                  name: delivery_comment
                  type: varchar

  - changeSet:
      id: add-leasing-privilege-to-vehicle-transfer
      author: Manuel Jany
      changes:
        - addColumn:
            tableName: vehicle_transfer
            schemaName: vehicletransfer
            columns:
              - column:
                  name: leasing_privilege
                  type: varchar

  - changeSet:
      id: add-vehicle-transfer-manager-postgres-view
      author: Ivan Plese
      runOrder: last
      runAlways: false
      runOnChange: true
      changes:
        - sqlFile:
            relativeToChangelogFile: true
            path: 'vehicletransfer/setup-postgres-view-for-vehicle-transfer-manager.sql'
            stripComments: true

  - changeSet:
      id: add-clean-up-for-deprecated-postgres-views
      author: Komal Shevale
      runAlways: true
      changes:
        - sqlFile:
            relativeToChangelogFile: true
            path: 'vehicletransfer/clean-up-deprecated-postgres-views.sql'
            splitStatements: false
            endDelimiter: '$$;'
  - changeSet:
      id: add-tires_comment-and-desired_tire_set
      author: Alon Cohn
      changes:
        - addColumn:
            tableName: vehicle_transfer
            schemaName: vehicletransfer
            columns:
              - column:
                  name: desired_tire_set
                  type: varchar
              - column:
                  name: tires_comment
                  type: varchar
  - changeSet:
      id: add-desired-tire-set-changed-manually
      author: Ivan Plese
      changes:
        - addColumn:
            tableName: vehicle_transfer
            schemaName: vehicletransfer
            columns:
              - column:
                  name: desired_tire_set_changed_manually
                  defaultValue: "false"
                  type: boolean
  - changeSet:
      id: update-vehicle-transfer-key-from-varchar-to-bigint
      author: sonia balodi
      changes:
        - addColumn:
            tableName: vehicle_transfer
            schemaName: vehicletransfer
            columns:
              - column:
                  name: new_vehicle_transfer_key
                  type: BIGINT
        - sql:
            sql: UPDATE vehicletransfer.vehicle_transfer SET new_vehicle_transfer_key = vehicle_transfer_key::BIGINT;
        - dropColumn:
            tableName: vehicle_transfer
            columnName: vehicle_transfer_key
            schemaName: vehicletransfer
        - renameColumn:
            tableName: vehicle_transfer
            oldColumnName: new_vehicle_transfer_key
            newColumnName: vehicle_transfer_key
            schemaName: vehicletransfer
  - changeSet:
      id: vehicle-transfer-key-as-unique-and-add-index
      author: sonia balodi
      changes:
        - addUniqueConstraint:
            tableName: vehicle_transfer
            columnNames: vehicle_transfer_key
            constraintName: uq_vehicle_transfer_key
            schemaName: vehicletransfer
            validate: true
        - createIndex:
            indexName: index_vehicle_transfer_key
            tableName: vehicle_transfer
            schemaName: vehicletransfer
            columns:
              - column:
                  name: vehicle_transfer_key
      rollback:
        - dropUniqueConstraint:
            constraintName: uq_vehicle_transfer_key
            tableName: vehicle_transfer
            schemaName: vehicletransfer
        - dropIndex:
            indexName: index_vehicle_transfer_key
            tableName: vehicle_transfer
            schemaName: vehicletransfer
#  - changeSet:
#      id: recreate-vehicle_transfer_manager_vehicle_transfer-view
#      author: sonia balodi
#      runOnChange: false
#      runAlways: false
#      failOnError: true
#      validCheckSum: 1:any # This is a mitigation to avoid liquibase checking changes in the checksum of the file( changes within the file)
#      changes:
#        - sqlFile:
#            relativeToChangelogFile: true
#            path: 'vehicletransfer/setup-postgres-view-for-vehicle-transfer-manager.sql'
#            stripComments: true


  - changeSet:
      id: add-msbooking-appointment-id
      author: Alon Cohn
      changes:
        - addColumn:
            tableName: vehicle_transfer
            schemaName: vehicletransfer
            columns:
              - column:
                  name: msbooking_appointment_id
                  type: varchar
  - changeSet:
      id: add-leasingPrivilegeValidationSuccessful-column
      author: Alon Cohn
      changes:
        - addColumn:
            tableName: vehicle_transfer
            schemaName: vehicletransfer
            columns:
              - column:
                  name: leasing_privilege_validation_successful
                  type: boolean

  - changeSet:
      id: add-maintenance-order-number-column
      author: Manuel Jany
      changes:
        - addColumn:
            tableName: vehicle_transfer
            schemaName: vehicletransfer
            columns:
              - column:
                  name: maintenance_order_number
                  type: varchar

  - changeSet:
      id: update-vehicle-transfer-marked-for-sale-to-deleted
      author: Manuel Jany
      changes:
        - update:
            tableName: vehicle_transfer
            schemaName: vehicletransfer
            columns:
              - column:
                  name: status
                  value: 'DELETED'
            where: status='MARKED_FOR_SALE'

  - changeSet:
      id: add-successor-key
      author: Manuel Jany
      changes:
        - addColumn:
            tableName: vehicle_transfer
            schemaName: vehicletransfer
            columns:
              - column:
                  name: successor_key
                  type: BIGINT

  - changeSet:
      id: add-successor-order-date
      author: Manuel Jany
      changes:
        - addColumn:
            tableName: vehicle_transfer
            schemaName: vehicletransfer
            columns:
              - column:
                  name: successor_order_date
                  type: timestamptz
  - changeSet:
      id: add-return-comment
      author: Ivan Plese
      changes:
        - addColumn:
            tableName: vehicle_transfer
            schemaName: vehicletransfer
            columns:
              - column:
                  name: return_comment
                  type: varchar
  - changeSet:
      id: add-service-card
      author: Ivan Plese
      changes:
        - addColumn:
            tableName: vehicle_transfer
            schemaName: vehicletransfer
            columns:
              - column:
                  name: service_card
                  type: varchar
  - changeSet:
      id: add-registration-needed
      author: Ivan Plese
      changes:
        - addColumn:
            tableName: vehicle_transfer
            schemaName: vehicletransfer
            columns:
              - column:
                  name: registration_needed
                  type: boolean

  - changeSet:
      id: rename-vehicle-transfer-responsible-cost-center-column-to-using-cost-center
      author: Manuel Jany
      changes:
        - renameColumn:
            tableName: vehicle_transfer
            schemaName: vehicletransfer
            oldColumnName: responsible_cost_center
            newColumnName: using_cost_center
            columnDataType: varchar
      rollback:
        - renameColumn:
            tableName: vehicle_transfer
            schemaName: vehicletransfer
            oldColumnName: using_cost_center
            newColumnName: responsible_cost_center
            columnDataType: varchar

  - changeSet:
      id: rename-vehicle-transfer-cost-center-carrier-id-column-to-depreciation-relevant-cost-center-id
      author: Manuel Jany
      changes:
        - renameColumn:
            tableName: vehicle_transfer
            schemaName: vehicletransfer
            oldColumnName: cost_center_carrier_id
            newColumnName: depreciation_relevant_cost_center_id
            columnDataType: uuid
      rollback:
        - renameColumn:
            tableName: vehicle_transfer
            schemaName: vehicletransfer
            oldColumnName: depreciation_relevant_cost_center_id
            newColumnName: cost_center_carrier_id
            columnDataType: uuid

  - changeSet:
      id: add-is-current-column
      author: Manuel Jany
      changes:
        - addColumn:
            schemaName: vehicletransfer
            tableName: vehicle_transfer
            columns:
              - column:
                  name: is_current
                  type: boolean
                  valueBoolean: "false"
                  constraints:
                    nullable: false
      rollback:
        - dropColumn:
            columnName: is_current
            schemaName: vehicletransfer
            tableName: vehicle_transfer

  - changeSet:
      id: drop-table-planned-vehicle-transfer
      author: Manuel Jany
      preConditions:
        - onFail: MARK_RAN
        - tableExists:
            schemaName: vehicletransfer
            tableName: planned_vehicle_transfer
      changes:
        - dropTable:
            schemaName: vehicletransfer
            tableName: planned_vehicle_transfer

  - changeSet:
      id: add-delivery-index-column
      author: Manuel Jany
      changes:
        - addColumn:
            schemaName: vehicletransfer
            tableName: vehicle_transfer
            columns:
              - column:
                  name: delivery_index
                  type: varchar
      rollback:
        - dropColumn:
            columnName: delivery_index
            schemaName: vehicletransfer
            tableName: vehicle_transfer

  - changeSet:
      id: rename-service-card-column
      author: Ivan Plese
      changes:
        - renameColumn:
            tableName: vehicle_transfer
            schemaName: vehicletransfer
            oldColumnName: service_card
            newColumnName: service_cards

  - changeSet:
      id: set-delivery-leipzig-null-to-false
      author: Manuel Jany
      changes:
        - update:
            tableName: vehicle_transfer
            schemaName: vehicletransfer
            columns:
              - column:
                  name: delivery_leipzig
                  valueBoolean: "false"
            where: delivery_leipzig is null

  - changeSet:
      id: add-not-null-constraint-to-delivery-leipzig
      author: Manuel Jany
      changes:
        - addNotNullConstraint:
            columnDataType: boolean
            columnName: delivery_leipzig
            constraintName: nn_delivery_leipzig
            defaultNullValue: "false"
            tableName: vehicle_transfer
            schemaName: vehicletransfer
            validate: true

  - changeSet:
      id: add-not-null-constraint-to-registration-needed
      author: Manuel Jany
      changes:
        - addNotNullConstraint:
            columnDataType: boolean
            columnName: registration_needed
            constraintName: nn_registration_needed
            defaultNullValue: "false"
            tableName: vehicle_transfer
            schemaName: vehicletransfer
            validate: true

  - changeSet:
      id: add-mhp-vdw-km-columns
      author: Martin Schöffel
      changes:
        - addColumn:
            schemaName: vehicletransfer
            tableName: vehicle_transfer
            columns:
              - column:
                  name: usage_mhp
                  type: boolean
                  valueBoolean: "false"
                  constraints:
                    nullable: false
              - column:
                  name: usage_vdw
                  type: boolean
                  valueBoolean: "false"
                  constraints:
                    nullable: false
              - column:
                  name: private_monthly_kilometers
                  type: int
                  constraints:
                    nullable: true

  - changeSet:
      id: update-vehicle_transfer-using_cost_center-column
      author: sonia balodi
      changes:
        - sql:
            sql: >
              UPDATE vehicletransfer.vehicle_transfer
              SET using_cost_center =
                CASE
                  WHEN using_cost_center ~ '^H001\d{4}$' THEN '00' || using_cost_center
                  WHEN using_cost_center ~ '^00H001\d{4}$' THEN using_cost_center
                  ELSE NULL
                END
              WHERE using_cost_center IS NOT NULL;

  - changeSet:
      id: add-indexes-on-vehicle-transfer-lower
      author: Karthik Narahari
      changes:
        - sql:
            dbms: postgresql
            sql: >
              CREATE INDEX IF NOT EXISTS idx_vehicle_transfer_lower_status ON vehicletransfer.vehicle_transfer (lower(status));
              CREATE INDEX IF NOT EXISTS idx_vehicle_transfer_lower_utilization_area ON vehicletransfer.vehicle_transfer (lower(utilization_area));

  - changeSet:
      id: add-indexes-on-vehicle-transfer-multiple-columns
      author: Karthik Narahari
      changes:
        - sql:
            dbms: postgresql
            sql: >
              CREATE INDEX IF NOT EXISTS idx_vehicle_transfer_usage_group_id ON vehicletransfer.vehicle_transfer (usage_group_id);
              CREATE INDEX IF NOT EXISTS idx_vehicle_transfer_vehicle_usage_id ON vehicletransfer.vehicle_transfer (vehicle_usage_id);
              CREATE INDEX IF NOT EXISTS idx_vehicle_transfer_planned_return_date ON vehicletransfer.vehicle_transfer (planned_return_date);
              CREATE INDEX IF NOT EXISTS idx_vehicle_transfer_successor_order_date ON vehicletransfer.vehicle_transfer (successor_order_date);

  - changeSet:
      id: add-indexes-on-vehicle-transfer-status
      author: Karthik Narahari
      changes:
        - sql:
            dbms: postgresql
            sql: >
              CREATE INDEX IF NOT EXISTS idx_vehicle_transfer_status ON vehicletransfer.vehicle_transfer (status);
