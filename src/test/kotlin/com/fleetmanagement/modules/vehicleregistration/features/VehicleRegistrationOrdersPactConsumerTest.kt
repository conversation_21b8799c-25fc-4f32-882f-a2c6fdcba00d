/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

@file:Suppress("ktlint:standard:no-wildcard-imports")

package com.fleetmanagement.modules.vehicleregistration.features

import au.com.dius.pact.consumer.dsl.LambdaDsl
import au.com.dius.pact.consumer.dsl.PactDslWithProvider
import au.com.dius.pact.consumer.junit5.PactConsumerTest
import au.com.dius.pact.consumer.junit5.PactTestFor
import au.com.dius.pact.core.model.PactSpecVersion
import au.com.dius.pact.core.model.RequestResponsePact
import au.com.dius.pact.core.model.annotations.Pact
import au.com.dius.pact.core.model.annotations.PactDirectory
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.KotlinModule
import com.fleetmanagement.modules.legalhold.service.external.vehicleregistration.VehicleRegistrationLegalHoldPactConsumerTest
import com.fleetmanagement.modules.vehicleregistration.api.dto.VehicleRegistrationAPIResponse
import com.fleetmanagement.modules.vehicleregistration.client.VehicleRegistrationClient
import com.fleetmanagement.modules.vehicleregistration.client.VehicleRegistrationClientConfiguration
import com.fleetmanagement.modules.vehicleregistration.features.ConsumerPactProperties.HOST
import com.fleetmanagement.modules.vehicleregistration.features.ConsumerPactProperties.PORT
import com.fleetmanagement.modules.vehicleregistration.features.VehicleRestrictionService.Companion.ACTIVE_VEHICLE_TRANSFER_FOR_PERSON
import com.fleetmanagement.oidc.client.OIDCTokenClient
import com.fleetmanagement.oidc.client.TokenResponse
import com.fleetmanagement.oidc.service.OIDCTokenService
import com.fleetmanagement.pact.Pacticipants.VEHICLE_REGISTRATION_SERVICE
import com.fleetmanagement.pact.Pacticipants.VEHICLE_SERVICE
import feign.Feign
import feign.Response
import feign.codec.Decoder
import feign.form.spring.SpringFormEncoder
import feign.jackson.JacksonEncoder
import io.mockk.every
import io.mockk.mockk
import jakarta.servlet.http.HttpServletRequest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.cloud.openfeign.support.ResponseEntityDecoder
import org.springframework.cloud.openfeign.support.SpringMvcContract
import org.springframework.http.HttpStatus
import org.springframework.web.context.request.RequestContextHolder
import org.springframework.web.context.request.ServletRequestAttributes
import java.lang.reflect.Type
import java.time.OffsetDateTime
import java.time.ZonedDateTime
import java.util.UUID

object ConsumerPactProperties {
    const val PORT = "8050"
    const val HOST = "http://localhost"
}

@PactConsumerTest
@PactTestFor(
    providerName = VEHICLE_REGISTRATION_SERVICE,
    port = PORT,
    pactVersion = PactSpecVersion.V3,
)
@PactDirectory("pacts/consumer")
class VehicleRegistrationOrdersPactConsumerTest {
    companion object {
        const val TEST_ACCESS_TOKEN =
            "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.***************************.dw1oz77jAV2AUdIv7MAiarGl1EmVM8HGJUPxCaC5GUyD6VLp3c8K58fbgOPnslpgDf8wmgiwr2KzgPPNXCX5ebxz3b_q-09fHirXn_8fhUV2GAbcvgW9aCL8LxmUH-zLbyBYWcdc-GGFucOVNCB7uP-nWHgjim7BLiyUn1XwRuJhZTaZtMGnAgZ8oTw83yznLFdjpZBD9NUGE_m_FlGT_7559ixUk1jQVPkDjRZldQWwjSSzHVwLpQXHgCoHhWhRykmTgyg8KERtwywvBJikQABOEYw592uP2cWl023g3reZu8xl-17ojSFUplz2J4zqMhqZptP3z7kpe0C_SQQlNw"
    }

    private val oidcTokenClient = mockk<OIDCTokenClient>()
    private val servletRequest = mockk<HttpServletRequest>()
    private val client = createClient()

    private val vehicleIdsWithRegistrationData =
        listOf(
            UUID.fromString("12345678-1234-1234-1234-000000000111"),
            UUID.fromString("12345678-1234-1234-1234-000000000222"),
        )

    @BeforeEach
    fun setUp() {
        val tokenResponse =
            TokenResponse(VehicleRegistrationLegalHoldPactConsumerTest.TEST_ACCESS_TOKEN, "Bearer", 1234)
        every { oidcTokenClient.getAccessToken(any()) } returns tokenResponse

        val requestAttributes = mockk<ServletRequestAttributes>()
        every { requestAttributes.request } returns servletRequest
        every { servletRequest.getHeader("Content-Type") } returns "application/json"

        // These are UI specific headers but latestRegistrationDatesLicencePlates endpoint is expected to be called using m2m token,
        // so we don't expect these headers in incoming request
        every { servletRequest.getHeader("X-Amzn-Oidc-Accesstoken") } returns null
        every { servletRequest.getHeader("X-Amzn-Oidc-Data") } returns null
        RequestContextHolder.setRequestAttributes(requestAttributes)
    }

    @Pact(consumer = VEHICLE_SERVICE, provider = VEHICLE_REGISTRATION_SERVICE)
    fun createPactWhenRegistrationDataIsAvailable(builder: PactDslWithProvider): RequestResponsePact =
        builder
            .given(
                "Vehicles with vehicle ids $vehicleIdsWithRegistrationData has registration date,licence plate,sfme and registration type information",
            ).uponReceiving("Get Latest Registration Orders for VehicleIds")
            .matchPath(
                ".*/orders/latest$",
                "/orders/latest",
            ).matchHeader(
                "Authorization",
                "Bearer eyJ[a-zA-Z0-9-_]+\\.[a-zA-Z0-9-_]+\\.[a-zA-Z0-9-_]+",
                "Bearer $TEST_ACCESS_TOKEN",
            ).method("POST")
            .headers(mapOf("Content-Type" to "application/json"))
            .body(
                LambdaDsl
                    .newJsonArray {
                        it.stringMatcher(
                            "^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}",
                            "12345678-1234-1234-1234-000000000111",
                        )
                        it.stringMatcher(
                            "^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}",
                            "12345678-1234-1234-1234-000000000222",
                        )
                    }.build(),
            ).willRespondWith()
            .status(200)
            .headers(mapOf("Content-Type" to "application/json"))
            .body(
                LambdaDsl
                    .newJsonBody { jsonBody ->
                        jsonBody.array("data") { array ->
                            array.`object` {
                                it.stringMatcher(
                                    "vehicleId",
                                    "^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}",
                                    "12345678-1234-1234-1234-000000000111",
                                )
                                it.stringMatcher(
                                    "vin",
                                    "^[A-HJ-NPR-Z0-9]{17}\$",
                                    "WP0AA2A97MS123456",
                                )
                                it.stringMatcher("lastRegistrationDate", ".*", "2024-01-01T00:00:00Z")
                                it.stringMatcher("licencePlate", ".*", "BB-PS 111")
                                it.booleanType("sfme", true)
                                it.integerType("registrationType", 1)
                            }
                            array.`object` {
                                it.stringMatcher(
                                    "vehicleId",
                                    "^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}",
                                    "12345678-1234-1234-1234-000000000222",
                                )
                                it.stringMatcher(
                                    "vin",
                                    "^[A-HJ-NPR-Z0-9]{17}\$",
                                    "WP0AA2A97MS123456",
                                )
                                it.stringMatcher("lastRegistrationDate", ".*", "2024-01-02T00:00:00Z")
                                it.stringMatcher("licencePlate", ".*", "BB-PS 222")
                                it.booleanType("sfme", true)
                                it.integerType("registrationType", 1)
                            }
                        }
                    }.build(),
            ).toPact()

    @Pact(consumer = VEHICLE_SERVICE, provider = VEHICLE_REGISTRATION_SERVICE)
    fun completedOrdersAvailable(builder: PactDslWithProvider): RequestResponsePact =
        builder
            .given("Vehicle has completed registration orders")
            .uponReceiving("Get Completed Registration Orders for VehicleId")
            .matchPath(
                ".*/orders/completed/ba7dab53-f31e-4ef8-b9d7-db131840a336$",
                "/orders/completed/ba7dab53-f31e-4ef8-b9d7-db131840a336",
            ).matchHeader(
                "Authorization",
                "Bearer eyJ[a-zA-Z0-9-_]+\\.[a-zA-Z0-9-_]+\\.[a-zA-Z0-9-_]+",
                "Bearer $TEST_ACCESS_TOKEN",
            ).method("GET")
            .willRespondWith()
            .status(200)
            .headers(mapOf("Content-Type" to "application/json"))
            .body(
                LambdaDsl
                    .newJsonBody { jsonBody ->
                        jsonBody.array("data") { array ->
                            array.`object` {
                                it.stringMatcher(
                                    "vehicleId",
                                    "^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}",
                                    "ba7dab53-f31e-4ef8-b9d7-db131840a336",
                                )
                                it.stringMatcher(
                                    "vin",
                                    "^[A-HJ-NPR-Z0-9]{17}\$",
                                    "WP0AA2A97MS123456",
                                )
                                it.stringMatcher("firstRegistrationDate", ".*", "2024-01-01T00:00:00Z")
                                it.stringMatcher("registrationDate", ".*", "2024-01-01T00:00:00Z")
                                it.stringMatcher("licencePlate", ".*", "BB-PS 111")
                                it.booleanType("sfme", true)
                                it.integerType("registrationType", 1)
                                it.stringType("registrationStatus", "REGISTERED")
                                it.stringType("hsn", "583")
                                it.stringType("tsn", "ANU00171")
                                it.integerType("testNumber", 1)
                            }
                            array.`object` {
                                it.stringMatcher(
                                    "vehicleId",
                                    "^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}",
                                    "ba7dab53-f31e-4ef8-b9d7-db131840a336",
                                )
                                it.stringMatcher(
                                    "vin",
                                    "^[A-HJ-NPR-Z0-9]{17}\$",
                                    "WP0AA2A97MS123456",
                                )
                                it.stringMatcher("firstRegistrationDate", ".*", "2024-01-02T00:00:00Z")
                                it.stringMatcher("registrationDate", ".*", "2024-01-01T00:00:00Z")
                                it.nullValue("licencePlate")
                                it.booleanType("sfme", true)
                                it.integerType("registrationType", 4)
                                it.stringType("registrationStatus", "DE_REGISTERED")
                                it.stringType("hsn", "583")
                                it.stringType("tsn", "ANU00171")
                                it.integerType("testNumber", 2)
                            }
                        }
                    }.build(),
            ).toPact()

    @Pact(consumer = VEHICLE_SERVICE, provider = VEHICLE_REGISTRATION_SERVICE)
    fun registrationsModifiedSince(builder: PactDslWithProvider): RequestResponsePact =
        builder
            .given("Registration exists that has been modified since specified date")
            .uponReceiving("Get Registrations that are modified since specified date")
            .matchPath(
                ".*/orders/registrations",
                "/orders/registrations",
            ).matchQuery("modifiedSince", "\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}(:\\d{2})?Z", "2024-01-01T00:00:00Z")
            .matchHeader(
                "Authorization",
                "Bearer eyJ[a-zA-Z0-9-_]+\\.[a-zA-Z0-9-_]+\\.[a-zA-Z0-9-_]+",
                "Bearer $TEST_ACCESS_TOKEN",
            ).method("GET")
            .willRespondWith()
            .status(200)
            .headers(mapOf("Content-Type" to "application/json"))
            .body(
                LambdaDsl
                    .newJsonBody { jsonBody ->
                        jsonBody.array("data") { array ->
                            array.`object` {
                                it.stringMatcher(
                                    "vehicleId",
                                    "^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}",
                                    "ba7dab53-f31e-4ef8-b9d7-db131840a336",
                                )
                                it.stringMatcher(
                                    "vin",
                                    "^[A-HJ-NPR-Z0-9]{17}\$",
                                    "WP0AA2A97MS123456",
                                )
                                it.stringMatcher("firstRegistrationDate", ".*", "2024-01-01T00:00:00Z")
                                it.stringMatcher("registrationDate", ".*", "2024-01-01T00:00:00Z")
                                it.stringMatcher("licencePlate", ".*", "BB-PS 111")
                                it.booleanType("sfme", true)
                                it.integerType("registrationType", 1)
                                it.stringType("registrationStatus", "REGISTERED")
                                it.stringType("hsn", "583")
                                it.stringType("tsn", "ANU00171")
                                it.integerType("testNumber", 1)
                            }
                            array.`object` {
                                it.stringMatcher(
                                    "vehicleId",
                                    "^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}",
                                    "ba7dab53-f31e-4ef8-b9d7-db131840a336",
                                )
                                it.stringMatcher(
                                    "vin",
                                    "^[A-HJ-NPR-Z0-9]{17}\$",
                                    "WP0AA2A97MS123456",
                                )
                                it.stringMatcher("firstRegistrationDate", ".*", "2024-01-02T00:00:00Z")
                                it.stringMatcher("registrationDate", ".*", "2024-01-01T00:00:00Z")
                                it.nullValue("licencePlate")
                                it.booleanType("sfme", true)
                                it.integerType("registrationType", 4)
                                it.stringType("registrationStatus", "DE_REGISTERED")
                                it.stringType("hsn", "583")
                                it.stringType("tsn", "ANU00171")
                                it.integerType("testNumber", 2)
                            }
                        }
                    }.build(),
            ).toPact()

    @Pact(consumer = VEHICLE_SERVICE, provider = VEHICLE_REGISTRATION_SERVICE)
    fun restrictVehicleForRegistrationUpdates(builder: PactDslWithProvider): RequestResponsePact =
        builder
            .given("vehicle with vehicle-id 06e46b6b-a898-449e-82ca-6a8a5bbb9dfe has no previous restrictions")
            .uponReceiving("a request to restrict registrations")
            .matchPath(
                ".*/vehicle-restrictions/06e46b6b-a898-449e-82ca-6a8a5bbb9dfe$",
                "/vehicle-restrictions/06e46b6b-a898-449e-82ca-6a8a5bbb9dfe",
            ).matchQuery(
                "reasonCode",
                ".*",
                "ACTIVE_VEHICLE_TRANSFER_FOR_PERSON",
            ).matchHeader(
                "Authorization",
                "Bearer eyJ[a-zA-Z0-9-_]+\\.[a-zA-Z0-9-_]+\\.[a-zA-Z0-9-_]+",
                "Bearer $TEST_ACCESS_TOKEN",
            ).method("POST")
            .willRespondWith()
            .status(200)
            .toPact()

    @Pact(consumer = VEHICLE_SERVICE, provider = VEHICLE_REGISTRATION_SERVICE)
    fun deleteVehicleRestriction(builder: PactDslWithProvider): RequestResponsePact =
        builder
            .given("vehicle with vehicle-id 06e46b6b-a898-449e-82ca-6a8a5bbb9dfe has a previous restriction")
            .uponReceiving("a request to remove registration restriction")
            .matchPath(
                ".*/vehicle-restrictions/06e46b6b-a898-449e-82ca-6a8a5bbb9dfe$",
                "/vehicle-restrictions/06e46b6b-a898-449e-82ca-6a8a5bbb9dfe",
            ).matchHeader(
                "Authorization",
                "Bearer eyJ[a-zA-Z0-9-_]+\\.[a-zA-Z0-9-_]+\\.[a-zA-Z0-9-_]+",
                "Bearer $TEST_ACCESS_TOKEN",
            ).method("DELETE")
            .willRespondWith()
            .status(204)
            .toPact()

    @Pact(consumer = VEHICLE_SERVICE, provider = VEHICLE_REGISTRATION_SERVICE)
    fun getRegistrationPeriodsForVehicle(builder: PactDslWithProvider): RequestResponsePact =
        builder
            .given("vehicle has registration periods")
            .uponReceiving("a request to get registration periods")
            .matchPath(
                ".*/orders/registration-period/vehicle/8447bad4-2791-41b9-8a7b-33ae827b933c$",
                "/orders/registration-period/vehicle/8447bad4-2791-41b9-8a7b-33ae827b933c",
            ).matchHeader(
                "Authorization",
                "Bearer eyJ[a-zA-Z0-9-_]+\\.[a-zA-Z0-9-_]+\\.[a-zA-Z0-9-_]+",
                "Bearer $TEST_ACCESS_TOKEN",
            ).matchQuery("activeAfter", "\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}(:\\d{2})?Z", "2024-01-01T00:00:00Z")
            .method("GET")
            .willRespondWith()
            .status(200)
            .headers(mapOf("Content-Type" to "application/json"))
            .body(
                LambdaDsl
                    .newJsonBody
                    { jsonBody ->
                        jsonBody.array("data") { array ->
                            array.`object` {
                                it.stringMatcher(
                                    "vehicleId",
                                    "^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}",
                                    "8447bad4-2791-41b9-8a7b-33ae827b933c",
                                )
                                it.stringMatcher(
                                    "vin",
                                    "^[A-HJ-NPR-Z0-9]{17}\$",
                                    "WP0AA2A97MS123456",
                                )
                                it.stringMatcher("licencePlate", ".*", "BB-PS 111")
                                it.date(
                                    "fromDate",
                                    "yyyy-MM-dd'T'HH:mm:ssXXX",
                                    ZonedDateTime.parse("2024-01-01T00:00:00Z"),
                                )
                                it.date("toDate", "yyyy-MM-dd'T'HH:mm:ssXXX", ZonedDateTime.parse("2024-01-01T00:00:00Z"))
                            }
                            array.`object` {
                                it.stringMatcher(
                                    "vehicleId",
                                    "^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}",
                                    "8447bad4-2791-41b9-8a7b-33ae827b933c",
                                )
                                it.stringMatcher(
                                    "vin",
                                    "^[A-HJ-NPR-Z0-9]{17}\$",
                                    "WP0AA2A97MS123456",
                                )
                                it.stringMatcher("licencePlate", ".*", "BB-PS 111")
                                it.date(
                                    "fromDate",
                                    "yyyy-MM-dd'T'HH:mm:ssXXX",
                                    ZonedDateTime.parse("2024-01-01T00:00:00Z"),
                                )
                                it.nullValue("toDate")
                            }
                        }
                    }.build(),
            ).toPact()

    @Test
    @PactTestFor(pactMethod = "createPactWhenRegistrationDataIsAvailable")
    fun canCreatePactWhenRegistrationDataIsAvailable() {
        val result = client.getLatestOrdersBy(vehicleIdsWithRegistrationData)
        assertNotNull(result.data)
    }

    @Test
    @PactTestFor(pactMethod = "registrationsModifiedSince")
    fun registrationsModifiedSince() {
        val modifiedSince = OffsetDateTime.parse("2024-01-01T00:00:00Z")
        val result = client.getRegistrationsModifiedSince(modifiedSince = modifiedSince.toString())
        assertNotNull(result.data)
    }

    @Test
    @PactTestFor(pactMethod = "completedOrdersAvailable")
    fun completedOrdersAvailable() {
        val vehicleId = UUID.fromString("ba7dab53-f31e-4ef8-b9d7-db131840a336")
        val result = client.getCompletedOrdersBy(vehicleId)
        assertNotNull(result.data)
    }

    @Test
    @PactTestFor(pactMethod = "restrictVehicleForRegistrationUpdates")
    fun restrictVehicleForRegistrationUpdates() {
        val vehicleId = UUID.fromString("06e46b6b-a898-449e-82ca-6a8a5bbb9dfe")
        val result = client.restrictVehicle(vehicleId, ACTIVE_VEHICLE_TRANSFER_FOR_PERSON)
        assertEquals(HttpStatus.OK, result.statusCode)
    }

    @Test
    @PactTestFor(pactMethod = "deleteVehicleRestriction")
    fun deleteVehicleRestriction() {
        val vehicleId = UUID.fromString("06e46b6b-a898-449e-82ca-6a8a5bbb9dfe")
        val result = client.unRestrictVehicle(vehicleId)
        assertEquals(HttpStatus.NO_CONTENT, result.statusCode)
    }

    @Test
    @PactTestFor(pactMethod = "getRegistrationPeriodsForVehicle")
    fun getRegistrationPeriodsForVehicle() {
        val vehicleId = UUID.fromString("8447bad4-2791-41b9-8a7b-33ae827b933c")
        val activeAfter = OffsetDateTime.parse("2024-01-01T00:00:00Z")
        val result = client.getRegistrationPeriodByVehicleId(vehicleId, activeAfter.toString())
        assertNotNull(result.data)
    }

    private fun createClient(): VehicleRegistrationClient {
        val tokenService: OIDCTokenService = mockk()
        every { tokenService.getAccessToken() } returns VehicleRegistrationLegalHoldPactConsumerTest.TEST_ACCESS_TOKEN
        every { tokenService.evictCachedToken() } returns Unit

        val okHttpClient = VehicleRegistrationClientConfiguration().feignClient(tokenService)
        return Feign
            .builder()
            .contract(SpringMvcContract())
            .encoder { obj, bodyType, requestTemplate ->
                SpringFormEncoder(JacksonEncoder()).encode(obj, bodyType, requestTemplate)
            }.client(okHttpClient)
            .decoder(ResponseEntityDecoder(CustomDecoder()))
            .target(VehicleRegistrationClient::class.java, "$HOST:$PORT")
    }
}

class CustomDecoder : Decoder {
    private val objectMapper =
        ObjectMapper().apply {
            configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            registerModule(KotlinModule.Builder().build())
        }

    override fun decode(
        response: Response?,
        type: Type?,
    ): Any? {
        val body = response?.body()?.asInputStream()
        return body?.let {
            objectMapper.readValue(body, VehicleRegistrationAPIResponse::class.java)
        }
    }
}
