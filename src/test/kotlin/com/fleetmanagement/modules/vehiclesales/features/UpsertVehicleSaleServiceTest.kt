package com.fleetmanagement.modules.vehiclesales.features

import com.fleetmanagement.TestcontainersConfiguration
import com.fleetmanagement.modules.vehiclesales.VehicleSalesDBConfiguration
import com.fleetmanagement.modules.vehiclesales.api.dto.VehicleSaleUpdateDto
import com.fleetmanagement.modules.vehiclesales.api.exception.VehicleInvoiceNotFoundException
import com.fleetmanagement.modules.vehiclesales.features.updateInvoice.B2BVehicleInvoiceUpdateService
import com.fleetmanagement.modules.vehiclesales.repository.JPAVehicleSaleRepository
import com.fleetmanagement.modules.vehiclesales.repository.entities.JPAVehicleSaleEntity
import com.ninjasquad.springmockk.MockkBean
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest
import org.springframework.context.annotation.Import
import java.time.OffsetDateTime
import java.util.Optional
import java.util.UUID

@DataJpaTest
@Import(
    TestcontainersConfiguration::class,
    VehicleSalesDBConfiguration::class,
)
class UpsertVehicleSaleServiceTest {
    private lateinit var updateVehicleSalesService: UpsertVehicleSaleService

    @MockkBean
    private lateinit var vehicleSalesChangeCaptureService: VehicleSalesChangeCaptureService

    @Autowired
    private lateinit var repository: JPAVehicleSaleRepository

    @MockkBean
    private lateinit var b2BVehicleInvoiceUpdateService: B2BVehicleInvoiceUpdateService

    @MockkBean
    private lateinit var locationUpdateService: VehicleLocationUpdateService

    @BeforeEach
    fun setUp() {
        every {
            vehicleSalesChangeCaptureService.captureChanges(any(), any())
        } just Runs

        every {
            b2BVehicleInvoiceUpdateService.updateCustomerDeliveryDateForB2B(any(), any())
        } returns mockk { every { vehicleId } returns UUID.randomUUID() }

        every {
            locationUpdateService.handleVehicleLocationUpdate(any(), any())
        } just Runs

        updateVehicleSalesService =
            UpsertVehicleSaleService(
                repository,
                vehicleSalesChangeCaptureService,
                b2BVehicleInvoiceUpdateService = b2BVehicleInvoiceUpdateService,
                locationUpdateService = locationUpdateService,
            )
        repository.deleteAll()
    }

    @Test
    fun `should update all vehicle sale fields correctly`() {
        // GIVEN
        val vehicleId = UUID.randomUUID()
        `vehicle sale for vehicle exists`(vehicleId)

        val updatedComment = Optional.of("Updated comment")
        val reserved = Optional.of(true)
        val contractSigned = Optional.of(false)
        val plannedDate = Optional.of(OffsetDateTime.parse("2025-05-01T00:00:00Z"))
        val loadingCompletedDate = Optional.of(OffsetDateTime.parse("2025-05-01T00:00:00Z"))

        val dto =
            VehicleSaleUpdateDto(
                vehicleId = vehicleId,
                comment = updatedComment,
                reservedForB2C = reserved,
                contractSigned = contractSigned,
                plannedDeliveryDate = plannedDate,
                loadingCompletedDate = loadingCompletedDate,
            )

        // WHEN
        updateVehicleSalesService.upsert(dto, "testUser")

        // THEN
        val updated = repository.findByVehicleId(vehicleId)!!
        assertEquals("Updated comment", updated.comment)
        assertEquals(true, updated.reservedForB2C)
        assertEquals(false, updated.contractSigned)
        assertEquals(plannedDate.get(), updated.plannedDeliveryDate)
        assertEquals(loadingCompletedDate.get(), updated.loadingCompletedDate)
    }

    @Test
    fun `should create a new record if vehicle does not exist already`() {
        // GIVEN
        val nonExistingVehicleId = UUID.randomUUID()
        val dto =
            VehicleSaleUpdateDto(
                vehicleId = nonExistingVehicleId,
                comment = Optional.of("New entry"),
                reservedForB2C = Optional.of(true),
                contractSigned = Optional.of(false),
                plannedDeliveryDate = Optional.empty(),
                loadingCompletedDate = Optional.empty(),
            )

        // WHEN
        updateVehicleSalesService.upsert(dto, "testUser")

        // THEN
        val updated = repository.findByVehicleId(nonExistingVehicleId)!!
        assertEquals("New entry", updated.comment)
        assertEquals(true, updated.reservedForB2C)
        assertEquals(false, updated.contractSigned)
        assertNull(updated.plannedDeliveryDate)
        assertNull(updated.loadingCompletedDate)
    }

    @Test
    fun `should correctly handle partial updates`() {
        // GIVEN
        val vehicleId = UUID.randomUUID()
        val existingEntity = JPAVehicleSaleEntity(vehicleId = vehicleId)
        val loadingCompletedDate = Optional.of(OffsetDateTime.now().plusDays(5))
        existingEntity.updatePrivileged(
            comment = Optional.of("Initial comment"),
            reservedForB2C = Optional.of(true),
            reservedForB2B = null,
            contractSigned = Optional.of(true),
            plannedDeliveryDate = Optional.of(OffsetDateTime.now().plusDays(5)),
            loadingCompletedDate = loadingCompletedDate,
        )
        repository.saveAndFlush(existingEntity)

        val updatedComment = "Updated comment"
        val updatedPlannedDeliveryDate = OffsetDateTime.now().plusDays(20)

        val dto =
            VehicleSaleUpdateDto(
                vehicleId = vehicleId,
                comment = Optional.of(updatedComment), // should update
                reservedForB2C = Optional.empty(), // should set to null
                reservedForB2B = Optional.of(true), // should update
                contractSigned = null, // should NOT change
                plannedDeliveryDate = Optional.of(updatedPlannedDeliveryDate), // should update
                loadingCompletedDate = null, // should NOT change
            )

        // WHEN
        updateVehicleSalesService.upsert(dto, "testUser")

        // THEN
        val updatedEntity = repository.findByVehicleId(vehicleId)
        assertNotNull(updatedEntity)
        assertEquals(updatedComment, updatedEntity!!.comment)
        assertNull(updatedEntity.reservedForB2C)
        assertTrue(updatedEntity.reservedForB2B!!)
        assertTrue(updatedEntity.contractSigned!!)
        assertEquals(updatedPlannedDeliveryDate, updatedEntity.plannedDeliveryDate)
        assertEquals(loadingCompletedDate.get(), updatedEntity.loadingCompletedDate)
    }

    @Test
    fun `should create migration vehicle sales data`() {
        // GIVEN
        val loadingCompletedDate = OffsetDateTime.now()

        val vehicleId = UUID.randomUUID()
        val dto =
            VehicleSaleUpdateDto(
                vehicleId = vehicleId,
                loadingCompletedDate = Optional.of(loadingCompletedDate),
                reservedForB2C = null,
                reservedForB2B = null,
                comment = null,
                contractSigned = null,
                plannedDeliveryDate = null,
            )

        // WHEN
        updateVehicleSalesService.upsertMigrationVehicleSalesData(dto)

        // THEN
        val salesEntity = repository.findByVehicleId(vehicleId)
        assertNotNull(salesEntity)
        assertEquals(loadingCompletedDate, salesEntity!!.loadingCompletedDate)
        verify(exactly = 0) { b2BVehicleInvoiceUpdateService.updateCustomerDeliveryDateForB2B(any(), any()) }
    }

    @Test
    fun `should update B2B vehicle invoice when loading completed date is set`() {
        // GIVEN
        val vehicleId = UUID.randomUUID()
        `vehicle sale for vehicle exists`(vehicleId)
        val loadingCompletedDate = OffsetDateTime.now()
        val dto =
            VehicleSaleUpdateDto(
                vehicleId = vehicleId,
                loadingCompletedDate = Optional.of(loadingCompletedDate),
                reservedForB2C = null,
                reservedForB2B = Optional.of(true),
                comment = null,
                contractSigned = null,
                plannedDeliveryDate = null,
            )
        // WHEN
        updateVehicleSalesService.upsert(dto, "testUser")
        val updated = repository.findByVehicleId(vehicleId)!!
        assertEquals(loadingCompletedDate, updated.loadingCompletedDate)
        verify(exactly = 1) {
            b2BVehicleInvoiceUpdateService.updateCustomerDeliveryDateForB2B(
                vehicleId,
                loadingCompletedDate,
            )
        }
    }

    @Test
    fun `should not fail to update B2B vehicle sales when no invoice found for vehicle`() {
        // GIVEN
        val vehicleId = UUID.randomUUID()
        `vehicle sale for vehicle exists`(vehicleId)
        val loadingCompletedDate = OffsetDateTime.now()
        val dto =
            VehicleSaleUpdateDto(
                vehicleId = vehicleId,
                loadingCompletedDate = Optional.of(loadingCompletedDate),
                reservedForB2C = null,
                reservedForB2B = Optional.of(true),
                comment = null,
                contractSigned = null,
                plannedDeliveryDate = null,
            )

        every { b2BVehicleInvoiceUpdateService.updateCustomerDeliveryDateForB2B(any(), any()) } throws
            VehicleInvoiceNotFoundException("No invoice found for vehicle $vehicleId")
        // WHEN
        updateVehicleSalesService.upsert(dto, "testUser")
        val updated = repository.findByVehicleId(vehicleId)!!
        assertEquals(loadingCompletedDate, updated.loadingCompletedDate)
        verify(exactly = 1) {
            b2BVehicleInvoiceUpdateService.updateCustomerDeliveryDateForB2B(
                vehicleId,
                loadingCompletedDate,
            )
        }
    }

    @Test
    fun `should handle location update when loading completed date is set`() {
        // GIVEN
        val vehicleId = UUID.randomUUID()
        `vehicle sale for vehicle exists`(vehicleId)
        val loadingCompletedDate = OffsetDateTime.now()
        val dto =
            VehicleSaleUpdateDto(
                vehicleId = vehicleId,
                loadingCompletedDate = Optional.of(loadingCompletedDate),
                reservedForB2C = null,
                reservedForB2B = null,
                comment = null,
                contractSigned = null,
                plannedDeliveryDate = null,
            )
        // WHEN
        updateVehicleSalesService.upsert(dto, "testUser")
        val updated = repository.findByVehicleId(vehicleId)!!
        assertEquals(loadingCompletedDate, updated.loadingCompletedDate)
        verify(exactly = 1) { locationUpdateService.handleVehicleLocationUpdate(vehicleId, loadingCompletedDate) }
    }

    @Test
    fun `should reset vehicle sale data when upserting with null values`() {
        // GIVEN
        val vehicleId = UUID.randomUUID()
        val vehicleSaleEntity =
            JPAVehicleSaleEntity(vehicleId = vehicleId).apply {
                this.updatePrivileged(
                    comment = Optional.of("Initial comment"),
                    reservedForB2C = Optional.of(true),
                    reservedForB2B = Optional.of(false),
                    contractSigned = Optional.of(true),
                    plannedDeliveryDate = Optional.of(OffsetDateTime.now().plusDays(10)),
                    loadingCompletedDate = null,
                )
            }
        repository.saveAndFlush(vehicleSaleEntity)
        assertNotNull(repository.findByVehicleId(vehicleId))

        // WHEN
        updateVehicleSalesService.resetVehicleSales(vehicleId, "testUser")

        // THEN
        with(repository.findByVehicleId(vehicleId)!!) {
            assertNull(comment)
            assertNull(reservedForB2C)
            assertNull(contractSigned)
            assertNull(plannedDeliveryDate)
        }
    }

    private fun `vehicle sale for vehicle exists`(vehicleId: UUID): JPAVehicleSaleEntity {
        val existingVehicleSale = JPAVehicleSaleEntity(vehicleId)
        return repository.saveAndFlush(existingVehicleSale)
    }
}
