/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehiclesales.integrations.service

import com.fleetmanagement.TestcontainersConfiguration
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByVehicleId
import com.fleetmanagement.modules.vehicledata.api.UpdateVehicleSoldDate
import com.fleetmanagement.modules.vehicledata.objectmothers.VehicleDataDTOObjectMother
import com.fleetmanagement.modules.vehiclesales.VehicleSalesDBConfiguration
import com.fleetmanagement.modules.vehiclesales.api.domain.InvoiceStatus
import com.fleetmanagement.modules.vehiclesales.api.domain.PaymentType
import com.fleetmanagement.modules.vehiclesales.api.domain.SalesDiscountType
import com.fleetmanagement.modules.vehiclesales.api.domain.TransactionType
import com.fleetmanagement.modules.vehiclesales.features.UpsertVehicleSaleService
import com.fleetmanagement.modules.vehiclesales.features.VehicleLocationUpdateService
import com.fleetmanagement.modules.vehiclesales.features.VehicleSalesChangeCaptureService
import com.fleetmanagement.modules.vehiclesales.features.createinvoice.CurrentVehicleInvoiceService
import com.fleetmanagement.modules.vehiclesales.features.createinvoice.TransactionIdGenerator
import com.fleetmanagement.modules.vehiclesales.features.createinvoice.VehicleInvoiceCreateService
import com.fleetmanagement.modules.vehiclesales.features.updateInvoice.B2BVehicleInvoiceUpdateService
import com.fleetmanagement.modules.vehiclesales.repository.JPAVehicleInvoiceRepository
import com.fleetmanagement.modules.vehiclesales.repository.JPAVehicleSaleRepository
import com.ninjasquad.springmockk.MockkBean
import com.porsche.b2b.entities.Customer
import com.porsche.b2b.entities.KafkaCompletedAuction
import io.mockk.every
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest
import org.springframework.context.annotation.Import
import org.springframework.transaction.annotation.EnableTransactionManagement
import java.math.BigDecimal
import java.time.LocalDate
import java.time.OffsetDateTime
import java.time.Year
import java.util.UUID

@DataJpaTest
@Import(
    value = [
        VehicleSalesDBConfiguration::class,
        TestcontainersConfiguration::class,
        VehicleInvoiceCreateService::class,
        TransactionIdGenerator::class,
        CurrentVehicleInvoiceService::class,
        VehicleSalesB2BInvoiceCreateService::class,
        UpsertVehicleSaleService::class,
    ],
)
@EnableTransactionManagement(proxyTargetClass = true)
class VehicleSalesB2BInvoiceCreateServiceTest {
    @Autowired
    private lateinit var jpaVehicleSaleRepository: JPAVehicleSaleRepository

    @Autowired
    private lateinit var jpaVehicleInvoiceRepository: JPAVehicleInvoiceRepository

    @MockkBean
    private lateinit var readVehicleService: ReadVehicleByVehicleId

    @MockkBean(relaxed = true)
    private lateinit var updateVehicleSoldService: UpdateVehicleSoldDate

    @MockkBean(relaxed = true)
    private lateinit var vehicleSalesChangeCaptureService: VehicleSalesChangeCaptureService

    @MockkBean(relaxed = true)
    private lateinit var vehicleInvoiceUpdateService: B2BVehicleInvoiceUpdateService

    @MockkBean(relaxed = true)
    private lateinit var locationUpdateService: VehicleLocationUpdateService

    @Autowired
    private lateinit var createVehicleInvoiceService: VehicleInvoiceCreateService

    @Autowired
    private lateinit var upsertVehicleSaleService: UpsertVehicleSaleService

    private lateinit var vehicleSalesB2BInvoiceCreateService: VehicleSalesB2BInvoiceCreateService

    @BeforeEach
    fun setUp() {
        jpaVehicleSaleRepository.deleteAll()
        jpaVehicleInvoiceRepository.deleteAll()
        vehicleSalesB2BInvoiceCreateService =
            VehicleSalesB2BInvoiceCreateService(
                createVehicleInvoice = createVehicleInvoiceService,
                upsertVehicleSalesService = upsertVehicleSaleService,
            )
    }

    @Test
    fun `should create invoice for a vehicle that is auctioned in B2B`() {
        val vehicleId = UUID.randomUUID()
        every { readVehicleService.readVehicleById(vehicleId) } returns
            VehicleDataDTOObjectMother.vehicleDTOWithAllFieldsPopulated(
                id = vehicleId,
                vin = "WP0CB2A92FS143285",
            )
        val completedAuction =
            KafkaCompletedAuction
                .newBuilder()
                .setVin("WP0CB2A92FS143285")
                .setAuctionId("auction123")
                .setCustomerVehiclePrice(100000.0)
                .setCustomer(Customer("John", "Doe", "Porsche Dealer", "12345"))
                .setPurchaseContractDate(LocalDate.parse("2025-10-01"))
                .setSaleDate(LocalDate.parse("2025-10-02"))
                .setInvoiceDate(LocalDate.parse("2025-10-03"))
                .setSellerId("P332392")
                .build()

        val createdInvoice = vehicleSalesB2BInvoiceCreateService.createInvoiceForAuction(vehicleId, completedAuction)
        checkNotNull(createdInvoice)
        assertTrue(createdInvoice.isCurrent)
        assertFalse(createdInvoice.customerInvoiceRecipient)
        assertEquals(vehicleId, createdInvoice.vehicleId)
        assertEquals("auction123", createdInvoice.auctionId)
        assertEquals("WP0CB2A92FS143285_${Year.now().value}_1", createdInvoice.transactionId)
        assertEquals(PaymentType.DIRECT_DEBIT, createdInvoice.paymentType)
        assertEquals("0000012345", createdInvoice.customerPartnerNumber)
        assertEquals("0000012345", createdInvoice.invoiceRecipientNumber)
        assertEquals("00332392", createdInvoice.salesPersonNumber)
        assertEquals(SalesDiscountType.NO_DISCOUNT, createdInvoice.salesDiscountType)
        assertEquals(BigDecimal("100000.0"), createdInvoice.salesNetPriceEUR)
        assertEquals(BigDecimal("100000.00"), createdInvoice.salesNetPriceAfterDiscountEUR)
        assertEquals(TransactionType.INVOICE, createdInvoice.transactionType)
        assertEquals(InvoiceStatus.INVOICE_SENT_TO_CUSTOMER, createdInvoice.invoiceStatus)
        assertEquals(OffsetDateTime.parse("2025-10-02T00:00Z"), createdInvoice.customerDeliveryDate)
        assertEquals(OffsetDateTime.parse("2025-10-03T00:00Z"), createdInvoice.invoiceDate)
        assertNull(createdInvoice.receiptNumber)
        assertNull(createdInvoice.invoiceNumber)
        assertNull(createdInvoice.finalInvoiceNumber)

        val createdSales = jpaVehicleSaleRepository.findByVehicleId(vehicleId)
        checkNotNull(createdSales)
        assertTrue(createdSales.contractSigned!!) // Since purchaseContractDate is not null
        assertTrue(createdSales.reservedForB2B!!)
        assertEquals("Porsche Dealer", createdSales.comment)

        verify(exactly = 1) {
            updateVehicleSoldService.updateVehicleSoldDate(
                vehicleId = vehicleId,
                soldDate = OffsetDateTime.parse("2025-10-03T00:00Z"),
                modifier = "B2BKafkaEvent",
            )
        }
    }
}
