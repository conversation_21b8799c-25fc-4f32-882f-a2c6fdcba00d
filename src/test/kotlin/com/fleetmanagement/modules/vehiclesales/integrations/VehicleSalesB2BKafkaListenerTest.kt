/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehiclesales.integrations

import com.fasterxml.jackson.databind.ObjectMapper
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByVIN
import com.fleetmanagement.modules.vehicledata.api.VehicleNotFoundInFVM
import com.fleetmanagement.modules.vehicledata.objectmothers.VehicleDataDTOObjectMother
import com.fleetmanagement.modules.vehiclesales.api.domain.InvoiceStatus
import com.fleetmanagement.modules.vehiclesales.api.domain.PaymentType
import com.fleetmanagement.modules.vehiclesales.api.domain.SalesDiscountType
import com.fleetmanagement.modules.vehiclesales.api.domain.TransactionType
import com.fleetmanagement.modules.vehiclesales.api.dto.VehicleInvoice
import com.fleetmanagement.modules.vehiclesales.integrations.service.VehicleSalesB2BInvoiceCreateService
import com.porsche.b2b.entities.Customer
import com.porsche.b2b.entities.KafkaCompletedAuction
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.apache.avro.Schema
import org.apache.avro.generic.GenericData
import org.apache.avro.generic.GenericRecord
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.json.JsonTest
import org.springframework.core.io.ClassPathResource
import java.io.InputStream
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID

@JsonTest
class VehicleSalesB2BKafkaListenerTest {
    @Autowired
    private lateinit var objectMapper: ObjectMapper
    private lateinit var readVehicleByVinService: ReadVehicleByVIN
    private lateinit var vehicleSalesB2BInvoiceCreateService: VehicleSalesB2BInvoiceCreateService
    private lateinit var listener: VehicleSalesB2BKafkaListener

    companion object {
        val schema: Schema

        init {
            val schemaStream: InputStream = ClassPathResource("avro/b2b/b2b.avsc").inputStream
            schema = Schema.Parser().parse(schemaStream)
        }
    }

    @BeforeEach
    fun setUp() {
        readVehicleByVinService = mockk()
        vehicleSalesB2BInvoiceCreateService = mockk()
        listener =
            VehicleSalesB2BKafkaListener(objectMapper, readVehicleByVinService, vehicleSalesB2BInvoiceCreateService)
    }

    @Test
    fun `should ignore message with blank VIN`() {
        val record: GenericRecord = GenericData.Record(schema).apply { put("vin", "") }
        listener.listenCompletedAuction(record)

        verify(exactly = 0) { vehicleSalesB2BInvoiceCreateService.createInvoiceForAuction(any(), any()) }
    }

    @Test
    fun `should not process when vehicle does not exist`() {
        val record: GenericRecord = GenericData.Record(schema).apply { put("vin", "12345") }
        every { readVehicleByVinService.readVehicleByVIN("12345") } throws VehicleNotFoundInFVM("vin", "12345")

        listener.listenCompletedAuction(record)

        verify(exactly = 0) { vehicleSalesB2BInvoiceCreateService.createInvoiceForAuction(any(), any()) }
    }

    @Test
    fun `should process valid message and create invoice`() {
        val record: GenericRecord =
            GenericData.Record(schema).apply {
                put("vin", "WP0CB2A92FS143285")
                put("auctionId", "auction123")
                put("customerVehiclePrice", 100000.0)
                put("customer", Customer("John", "Doe", "Porsche Dealer", "12345"))
                put("purchaseContractDate", "2025-10-01")
                put("saleDate", "2025-10-02")
                put("invoiceDate", "2025-10-03")
                put("sellerId", "P000123")
            }
        val completedAuction =
            KafkaCompletedAuction
                .newBuilder()
                .setVin("WP0CB2A92FS143285")
                .setAuctionId("auction123")
                .setCustomerVehiclePrice(100000.0)
                .setCustomer(Customer("John", "Doe", "Porsche Dealer", "12345"))
                .setPurchaseContractDate(LocalDate.parse("2025-10-01"))
                .setSaleDate(LocalDate.parse("2025-10-02"))
                .setInvoiceDate(LocalDate.parse("2025-10-03"))
                .setSellerId("P000123")
                .build()
        val vehicleId = UUID.randomUUID()
        val vehicle = VehicleDataDTOObjectMother.vehicleDTOWithAllFieldsPopulated(id = vehicleId)
        every { readVehicleByVinService.readVehicleByVIN("WP0CB2A92FS143285") } returns vehicle
        every { vehicleSalesB2BInvoiceCreateService.createInvoiceForAuction(vehicleId, any()) } returns
            VehicleInvoice(
                vehicleId = vehicleId,
                invoiceId = UUID.randomUUID(),
                invoiceStatus = InvoiceStatus.INVOICE_SENT_TO_CUSTOMER,
                salesNetPriceEUR = BigDecimal("100000.00"),
                salesNetPriceAfterDiscountEUR = BigDecimal("100000.00"),
                salesDiscountType = SalesDiscountType.NO_DISCOUNT,
                customerPartnerNumber = "123456",
                customerInvoiceRecipient = true,
                paymentType = PaymentType.CASH,
                invoiceRecipientNumber = "987654",
                paymentReceived = false,
                transactionId = "transactionId",
                transactionType = TransactionType.INVOICE,
                salesPersonNumber = "000123",
                salesDiscountPercentage = 0.0f,
                isCurrent = true,
                winterTiresDiscountPercentage = 0.0f,
            )

        listener.listenCompletedAuction(record)

        verify(exactly = 1) { vehicleSalesB2BInvoiceCreateService.createInvoiceForAuction(vehicleId, completedAuction) }
    }
}
