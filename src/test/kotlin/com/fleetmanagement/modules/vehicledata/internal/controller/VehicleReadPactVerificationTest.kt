/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.internal.controller

import au.com.dius.pact.provider.junit5.PactVerificationContext
import au.com.dius.pact.provider.junit5.PactVerificationInvocationContextProvider
import au.com.dius.pact.provider.junitsupport.Consumer
import au.com.dius.pact.provider.junitsupport.Provider
import au.com.dius.pact.provider.junitsupport.State
import au.com.dius.pact.provider.junitsupport.loader.PactFolder
import au.com.dius.pact.provider.spring.junit5.MockMvcTestTarget
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByAnyIdentifier
import com.fleetmanagement.modules.vehicledata.api.dtos.VehicleDTO
import com.fleetmanagement.modules.vehicledata.features.readvehicle.DeprecatedVehicleDataReadService
import com.fleetmanagement.modules.vehicledata.features.readvehicle.VehicleReadController
import com.fleetmanagement.modules.vehicledata.internal.repository.objectmothers.JPAVehicleEntityObjectMother
import com.fleetmanagement.pact.Pacticipants.VEHICLE_REGISTRATION_SERVICE
import com.fleetmanagement.pact.Pacticipants.VEHICLE_SERVICE
import com.fleetmanagement.security.configurations.SecurityConfigurationForLocalDevelopment
import com.ninjasquad.springmockk.MockkBean
import io.mockk.clearAllMocks
import io.mockk.every
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.TestTemplate
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.context.annotation.Import
import org.springframework.test.web.servlet.MockMvc

@WebMvcTest(
    controllers = [VehicleReadController::class],
    properties = ["security.enabled=false"],
)
@Import(SecurityConfigurationForLocalDevelopment::class)
@AutoConfigureMockMvc
@Provider(VEHICLE_SERVICE)
@Consumer(VEHICLE_REGISTRATION_SERVICE)
@PactFolder("pacts/provider")
class VehicleReadPactVerificationTest {
    @MockkBean
    private lateinit var mockVehicleReadService: DeprecatedVehicleDataReadService

    @MockkBean
    private lateinit var readVehicleByAnyIdentifier: ReadVehicleByAnyIdentifier

    @Autowired
    private lateinit var mockMvc: MockMvc

    @BeforeEach
    fun before(context: PactVerificationContext) {
        context.target = MockMvcTestTarget(mockMvc)
    }

    @AfterEach
    fun afterEach() {
        clearAllMocks()
    }

    @State("a vehicle with VIN test-vin is already loaded")
    fun setupReadServiceForTestVin() {
        val vin = "test-vin"
        every {
            mockVehicleReadService.readVehicleDataByVINOrCreateIfMissing(vin)
        } returns JPAVehicleEntityObjectMother.persistedVehicleEntityWithAllFieldsPopulated(vin = vin)
    }

    @State("a vehicle with equiId EQUI ID 111 222 is already loaded")
    fun setupReadServiceForEquiId() {
        val testEquiId = "EQUI ID 111 222"
        every {
            readVehicleByAnyIdentifier.readVehicle(
                vehicleId = null,
                vin = null,
                vguid = null,
                equipmentNumber = null,
                equiId = testEquiId,
            )
        } returns
            VehicleDTO.from(
                JPAVehicleEntityObjectMother.persistedVehicleEntityWithAllFieldsPopulated().apply {
                    equiId = testEquiId
                    equipmentNumber = 1234569
                },
                jacksonObjectMapper(),
            )
    }

    @TestTemplate
    @ExtendWith(PactVerificationInvocationContextProvider::class)
    fun pactVerificationTestTemplate(context: PactVerificationContext) {
        context.verifyInteraction()
    }
}
