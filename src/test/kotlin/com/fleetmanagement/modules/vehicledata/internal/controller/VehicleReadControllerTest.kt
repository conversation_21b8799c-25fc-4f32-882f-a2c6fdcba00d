/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

@file:Suppress("ktlint:standard:no-wildcard-imports")

package com.fleetmanagement.modules.vehicledata.internal.controller

import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByAnyIdentifier
import com.fleetmanagement.modules.vehicledata.api.dtos.VehicleDTO
import com.fleetmanagement.modules.vehicledata.api.exceptions.VehicleNotFoundException
import com.fleetmanagement.modules.vehicledata.features.readvehicle.DeprecatedVehicleDataReadService
import com.fleetmanagement.modules.vehicledata.features.readvehicle.VehicleReadController
import com.fleetmanagement.modules.vehicledata.internal.repository.objectmothers.JPAVehicleEntityObjectMother
import com.fleetmanagement.modules.vehicledata.objectmothers.VehicleDataDTOObjectMother.vehicleDTOWithAllFieldsPopulated
import com.fleetmanagement.security.configurations.SecurityConfiguration
import com.fleetmanagement.security.features.tokenvalidation.alb.ALBTokenValidator
import com.fleetmanagement.security.features.tokenvalidation.apigateway.APIGatewayTokenValidator
import com.fleetmanagement.security.features.tokenvalidation.apigateway.JwtTokenValidationException
import com.fleetmanagement.security.utils.WithMockALBUser
import com.github.dockerjava.api.exception.UnauthorizedException
import com.ninjasquad.springmockk.MockkBean
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.verify
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.context.annotation.Import
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.MediaType.APPLICATION_PROBLEM_JSON
import org.springframework.test.context.TestPropertySource
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.content
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import java.net.URLEncoder
import java.nio.charset.StandardCharsets
import java.util.UUID

@WebMvcTest(
    controllers = [VehicleReadController::class],
)
@TestPropertySource(
    properties = [
        "security.enabled=true",
    ],
)
@Import(SecurityConfiguration::class)
@AutoConfigureMockMvc
class VehicleReadControllerTest {
    companion object {
        const val TEST_VIN = "WP0CB2A92FS143285"
        const val TEST_VGUID = "13e4a669-2ba8-4a7e-be98-27173133b6f0"
        const val TEST_EQUI_ID = "PO 992 S SEU123BA04"
        const val TEST_EQUIPMENT_NUMBER = 123456L
        const val TEST_VEHICLE_ID = "fde5feee-dc55-4d93-b47b-777f5d601673"
    }

    @MockkBean
    private lateinit var mockVehicleReadService: DeprecatedVehicleDataReadService

    @Autowired
    private lateinit var mockMvc: MockMvc

    @MockkBean(name = "apiGatewayTokenValidator")
    private lateinit var apiGatewayTokenValidator: APIGatewayTokenValidator

    @MockkBean(name = "albTokenValidator")
    private lateinit var albTokenValidator: ALBTokenValidator

    @MockkBean
    private lateinit var readVehicleByAnyIdentifier: ReadVehicleByAnyIdentifier

    private lateinit var mockVehicleDTO: VehicleDTO

    @BeforeEach
    fun setup() {
        every { apiGatewayTokenValidator.validate(any()) } returns Unit
        every { apiGatewayTokenValidator.canValidate(any()) } returns true
        every { albTokenValidator.validate(any()) } returns Unit
        every { albTokenValidator.canValidate(any()) } returns true

        mockVehicleDTO = vehicleDTOWithAllFieldsPopulated(id = UUID.fromString(TEST_VEHICLE_ID), vin = TEST_VIN, vguid = TEST_VGUID)
    }

    @AfterEach
    fun afterEach() {
        clearAllMocks()
    }

    @Test
    fun `can get vehicle details by VIN from PVH`() {
        val testVehicle = JPAVehicleEntityObjectMother.persistedVehicleEntityWithAllFieldsPopulated(vin = TEST_VIN)
        every { mockVehicleReadService.readVehicleDataByVINOrCreateIfMissing(TEST_VIN) } returns testVehicle

        mockMvc
            .perform(
                get("/vehicles/vin/${TEST_VIN}")
                    .header("Authorization", "Bearer testAccessToken"),
            ).andExpectAll(
                status().is2xxSuccessful,
                content().json(
                    """
                    {
                      "vin": "${testVehicle.vin}",
                      "vguid": "${testVehicle.vguid}",
                      "model": {
                        "description": "${testVehicle.modelInfo?.modelDescription}",
                        "orderType": "${testVehicle.modelInfo?.orderType}"
                      },
                      "order": { 
                        "department": "BRZ",
                        "leasingType": "AF",
                        "tradingPartnerNumber": "1003000",
                        "purposeOrderType": "LE",
                        "importerShortName": "PIS",
                        "commissionNumber": "I47699",
                        "invoiceNumber": "478922",
                        "deliveryType": "A",
                        "primaryStatus": "FD95"
                      }
                    }
                    """.trimIndent(),
                ),
            )
        verify(exactly = 1) { mockVehicleReadService.readVehicleDataByVINOrCreateIfMissing(TEST_VIN) }
    }

    @Test
    fun `can get vehicle details by Vguid from PVH`() {
        val encodedVguid = URLEncoder.encode(TEST_VGUID, StandardCharsets.UTF_8.toString())
        val url = "/vehicles/vguid/$encodedVguid"

        val testVehicle = JPAVehicleEntityObjectMother.persistedVehicleEntityWithAllFieldsPopulated()
        every { mockVehicleReadService.readVehicleDataByVGUID(TEST_VGUID) } returns testVehicle

        mockMvc
            .perform(
                get(url)
                    .header("Authorization", "Bearer testAccessToken"),
            ).andExpectAll(
                status().is2xxSuccessful,
                content().json(
                    """
                    {
                      "vin": "${testVehicle.vin}",
                      "vguid": "${testVehicle.vguid}",
                      "model": {
                        "description": "${testVehicle.modelInfo?.modelDescription}",
                        "orderType": "${testVehicle.modelInfo?.orderType}"
                      },
                      "order": { 
                        "department": "BRZ",
                        "leasingType": "AF"
                      }
                    }
                    """.trimIndent(),
                ),
            )
        verify(exactly = 1) { mockVehicleReadService.readVehicleDataByVGUID(TEST_VGUID) }
    }

    @Test
    fun `can get vehicle details by Vguids from database`() {
        val body =
            """["13e4a669-2ba8-4a7e-be98-27173133b6f0", "abe4a669-2ba8-7e4a-98be-sda7173133b"]""".trimIndent()
        val vguids = listOf("13e4a669-2ba8-4a7e-be98-27173133b6f0", "abe4a669-2ba8-7e4a-98be-sda7173133b")
        val testVehicles =
            vguids.map {
                JPAVehicleEntityObjectMother.persistedVehicleEntityWithAllFieldsPopulated(vguid = it)
            }
        every { mockVehicleReadService.readVehicleDataByVGUIDs(vguids) } returns testVehicles

        mockMvc
            .perform(
                post("/vehicles/vguid")
                    .header("Authorization", "Bearer testAccessToken")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(body),
            ).andExpectAll(
                status().is2xxSuccessful,
                content().json(
                    """
                    [
                      {
                        "vin": "${testVehicles.first().vin}",
                        "vguid": "${testVehicles.first().vguid}",
                        "model": {
                          "description": "${testVehicles.first().modelInfo?.modelDescription}",
                          "orderType": "${testVehicles.first().modelInfo?.orderType}"
                        },
                        "order": {
                          "department": "BRZ",
                          "leasingType": "AF"
                        }
                      },
                      {
                        "vin": "${testVehicles.last().vin}",
                        "vguid": "${testVehicles.last().vguid}",
                        "model": {
                          "description": "${testVehicles.last().modelInfo?.modelDescription}",
                          "orderType": "${testVehicles.last().modelInfo?.orderType}"
                        },
                        "order": {
                          "department": "BRZ",
                          "leasingType": "AF"
                        }
                      }
                    ]
                    """.trimIndent(),
                ),
            )
        verify(exactly = 1) { mockVehicleReadService.readVehicleDataByVGUIDs(vguids) }
    }

    @Test
    fun `can get vehicle details by equiId from EQUI`() {
        val testVin = "WP000000000000001"

        val testVehicle = JPAVehicleEntityObjectMother.persistedVehicleEntityWithAllFieldsPopulated(vin = testVin)

        every { mockVehicleReadService.readVehicleDataByEquiIdOrCreateIfMissing(testVehicle.equiId!!) } returns testVehicle

        mockMvc
            .perform(
                get("/vehicles/equiId/${testVehicle.equiId}")
                    .header("Authorization", "Bearer testAccessToken"),
            ).andExpectAll(
                status().is2xxSuccessful,
                content().json(
                    """
                    {
                      "vin": "${testVehicle.vin}",
                      "vguid": "${testVehicle.vguid}",
                      "model": {
                        "description": "${testVehicle.modelInfo?.modelDescription}",
                        "orderType": "${testVehicle.modelInfo?.orderType}"
                      },
                      "order": {
                        "department": "BRZ",
                        "leasingType": "AF"
                      }
                    }
                    """.trimIndent(),
                ),
            )
        verify(exactly = 1) { mockVehicleReadService.readVehicleDataByEquiIdOrCreateIfMissing(testVehicle.equiId!!) }
    }

    @Test
    fun `can get vehicle details by equipmentNumber from EQUI`() {
        val testVin = "WP000000000000001"
        val testVehicle = JPAVehicleEntityObjectMother.persistedVehicleEntityWithAllFieldsPopulated(vin = testVin)

        every { mockVehicleReadService.readVehicleDataByEquipmentNumberOrCreateIfMissing(testVehicle.equipmentNumber!!) } returns
            testVehicle

        mockMvc
            .perform(
                get("/vehicles/equipmentNumber/${testVehicle.equipmentNumber}")
                    .header("Authorization", "Bearer testAccessToken"),
            ).andExpectAll(
                status().is2xxSuccessful,
                content().json(
                    """
                    {
                      "vin": "${testVehicle.vin}",
                      "vguid": "${testVehicle.vguid}",
                      "model": {
                        "description": "${testVehicle.modelInfo?.modelDescription}",
                        "orderType": "${testVehicle.modelInfo?.orderType}"
                      },
                      "order": {
                        "department": "BRZ",
                        "leasingType": "AF"
                      }
                    }
                    """.trimIndent(),
                ),
            )
        verify(exactly = 1) { mockVehicleReadService.readVehicleDataByEquipmentNumberOrCreateIfMissing(testVehicle.equipmentNumber!!) }
    }

    @Test
    fun `can handle failures when there are any unexpected errors`() {
        every {
            mockVehicleReadService.readVehicleDataByVINOrCreateIfMissing(TEST_VIN)
        } throws RuntimeException("Something went wrong")

        mockMvc
            .perform(
                get("/vehicles/vin/${TEST_VIN}")
                    .header("Authorization", "Bearer testAccessToken")
                    .header("Content-Type", MediaType.APPLICATION_JSON),
            ).andExpectAll(
                status().`is`(500),
                content().contentType(APPLICATION_PROBLEM_JSON),
                jsonPath("$.detail").value("Something went wrong. The development team has been notified."),
            )
    }

    @Test
    fun `should deny access when token is not provided`() {
        val testVehicle = JPAVehicleEntityObjectMother.persistedVehicleEntityWithAllFieldsPopulated(vin = TEST_VIN)
        every { mockVehicleReadService.readVehicleDataByVINOrCreateIfMissing(TEST_VIN) } returns testVehicle
        every { apiGatewayTokenValidator.validate(any()) } throws
            JwtTokenValidationException(
                UnauthorizedException("No Token Provided"),
                HttpStatus.UNAUTHORIZED,
            )

        mockMvc
            .perform(
                get("/vehicles/vin/${TEST_VIN}"),
            ).andExpectAll(
                status().isUnauthorized,
            )
        verify(exactly = 0) { mockVehicleReadService.readVehicleDataByVINOrCreateIfMissing(TEST_VIN) }
    }

    @Test
    @WithMockALBUser
    fun `can find vehicle by vehicleId`() {
        every {
            readVehicleByAnyIdentifier.readVehicle(
                UUID.fromString(TEST_VEHICLE_ID),
                null,
                null,
                null,
                null,
            )
        } returns mockVehicleDTO

        mockMvc
            .perform(
                get("/vehicles/find")
                    .param("vehicleId", TEST_VEHICLE_ID)
                    .header("Authorization", "Bearer testAccessToken"),
            ).andExpectAll(
                status().isOk,
                content().contentType("application/json"),
                jsonPath("$.vin").value(TEST_VIN),
                jsonPath("$.vguid").value(TEST_VGUID),
            )

        verify(exactly = 1) { readVehicleByAnyIdentifier.readVehicle(UUID.fromString(TEST_VEHICLE_ID), null, null, null, null) }
    }

    @Test
    fun `can find vehicle by VIN`() {
        every { readVehicleByAnyIdentifier.readVehicle(null, TEST_VIN, null, null, null) } returns mockVehicleDTO

        mockMvc
            .perform(
                get("/vehicles/find")
                    .param("vin", TEST_VIN)
                    .header("Authorization", "Bearer testAccessToken"),
            ).andExpectAll(
                status().isOk,
                content().contentType("application/json"),
                jsonPath("$.vin").value(TEST_VIN),
                jsonPath("$.vguid").value(TEST_VGUID),
            )

        verify(exactly = 1) {
            readVehicleByAnyIdentifier.readVehicle(
                null,
                TEST_VIN,
                null,
                null,
                null,
            )
        }
    }

    @Test
    fun `can find vehicle by VGUID`() {
        every { readVehicleByAnyIdentifier.readVehicle(null, null, TEST_VGUID, null, null) } returns
            mockVehicleDTO

        mockMvc
            .perform(
                get("/vehicles/find")
                    .param("vguid", TEST_VGUID)
                    .header("Authorization", "Bearer testAccessToken"),
            ).andExpectAll(
                status().isOk,
                content().contentType("application/json"),
                jsonPath("$.vin").value(TEST_VIN),
                jsonPath("$.vguid").value(TEST_VGUID),
            )

        verify(exactly = 1) {
            readVehicleByAnyIdentifier.readVehicle(
                null,
                null,
                TEST_VGUID,
                null,
                null,
            )
        }
    }

    @Test
    fun `can find vehicle by equipmentNumber`() {
        every { readVehicleByAnyIdentifier.readVehicle(null, null, null, TEST_EQUIPMENT_NUMBER, null) } returns mockVehicleDTO

        mockMvc
            .perform(
                get("/vehicles/find")
                    .param("equipmentNumber", "$TEST_EQUIPMENT_NUMBER")
                    .header("Authorization", "Bearer testAccessToken"),
            ).andExpectAll(
                status().isOk,
                content().contentType("application/json"),
                jsonPath("$.vin").value(TEST_VIN),
                jsonPath("$.vguid").value(TEST_VGUID),
            )

        verify(exactly = 1) { readVehicleByAnyIdentifier.readVehicle(null, null, null, TEST_EQUIPMENT_NUMBER, null) }
    }

    @Test
    @WithMockALBUser
    fun `can find vehicle by equiId`() {
        every { readVehicleByAnyIdentifier.readVehicle(null, null, null, null, TEST_EQUI_ID) } returns mockVehicleDTO

        mockMvc
            .perform(
                get("/vehicles/find")
                    .param("equiId", TEST_EQUI_ID)
                    .header("Authorization", "Bearer testAccessToken"),
            ).andExpectAll(
                status().isOk,
                content().contentType("application/json"),
                jsonPath("$.vin").value(TEST_VIN),
                jsonPath("$.vguid").value(TEST_VGUID),
            )

        verify(exactly = 1) { readVehicleByAnyIdentifier.readVehicle(null, null, null, null, TEST_EQUI_ID) }
    }

    @Test
    @WithMockALBUser
    fun `findVehicle should handle multiple parameters and pass them all to finder`() {
        every {
            readVehicleByAnyIdentifier.readVehicle(
                UUID.fromString(TEST_VEHICLE_ID),
                TEST_VIN,
                TEST_VGUID,
                TEST_EQUIPMENT_NUMBER,
                TEST_EQUI_ID,
            )
        } returns mockVehicleDTO

        mockMvc
            .perform(
                get("/vehicles/find")
                    .param("vehicleId", TEST_VEHICLE_ID)
                    .param("vin", TEST_VIN)
                    .param("vguid", TEST_VGUID)
                    .param("equipmentNumber", "$TEST_EQUIPMENT_NUMBER")
                    .param("equiId", TEST_EQUI_ID)
                    .header("Authorization", "Bearer testAccessToken"),
            ).andExpectAll(
                status().isOk,
                content().contentType("application/json"),
                jsonPath("$.vin").value(TEST_VIN),
                jsonPath("$.vguid").value(TEST_VGUID),
            )

        verify(exactly = 1) {
            readVehicleByAnyIdentifier.readVehicle(
                UUID.fromString(TEST_VEHICLE_ID),
                TEST_VIN,
                TEST_VGUID,
                TEST_EQUIPMENT_NUMBER,
                TEST_EQUI_ID,
            )
        }
    }

    @Test
    @WithMockALBUser
    fun `findVehicle should return 404 when VehicleNotFoundException is thrown`() {
        every { readVehicleByAnyIdentifier.readVehicle(null, TEST_VIN, null, null, null) } throws
            VehicleNotFoundException("No vehicle found for any of the provided parameters.")

        mockMvc
            .perform(
                get("/vehicles/find")
                    .param("vin", TEST_VIN)
                    .header("Authorization", "Bearer testAccessToken"),
            ).andExpect(status().isNotFound)

        verify(exactly = 1) {
            readVehicleByAnyIdentifier.readVehicle(
                null,
                TEST_VIN,
                null,
                null,
                null,
            )
        }
    }

    @Test
    @WithMockALBUser
    fun `findVehicle should return 400 when IllegalArgumentException is thrown for no parameters`() {
        every { readVehicleByAnyIdentifier.readVehicle(null, null, null, null, null) } throws
            IllegalArgumentException("At least one identifying parameter must be provided to find a vehicle.")

        mockMvc
            .perform(
                get("/vehicles/find")
                    .header("Authorization", "Bearer testAccessToken"),
            ).andExpect(status().isBadRequest)

        verify(exactly = 1) { readVehicleByAnyIdentifier.readVehicle(null, null, null, null, null) }
    }

    @Test
    @WithMockALBUser
    fun `findVehicle should handle empty string parameters correctly`() {
        every { readVehicleByAnyIdentifier.readVehicle(null, "", "", null, "") } returns mockVehicleDTO

        mockMvc
            .perform(
                get("/vehicles/find")
                    .param("vin", "")
                    .param("vguid", "")
                    .param("equiId", "")
                    .header("Authorization", "Bearer testAccessToken"),
            ).andExpectAll(
                status().isOk,
                content().contentType("application/json"),
                jsonPath("$.vin").value(TEST_VIN),
                jsonPath("$.vguid").value(TEST_VGUID),
            )

        verify(exactly = 1) { readVehicleByAnyIdentifier.readVehicle(null, "", "", null, "") }
    }

    @Test
    @WithMockALBUser
    fun `findVehicle should handle fallback scenario when first identifier fails`() {
        // Simulate the fallback behavior where VehicleFinder handles the fallback internally
        every {
            readVehicleByAnyIdentifier.readVehicle(
                UUID.fromString(TEST_VEHICLE_ID),
                TEST_VIN,
                null,
                null,
                null,
            )
        } returns mockVehicleDTO

        mockMvc
            .perform(
                get("/vehicles/find")
                    .param("vehicleId", TEST_VEHICLE_ID)
                    .param("vin", TEST_VIN)
                    .header("Authorization", "Bearer testAccessToken"),
            ).andExpectAll(
                status().isOk,
                content().contentType("application/json"),
                jsonPath("$.vin").value(TEST_VIN),
                jsonPath("$.vguid").value(TEST_VGUID),
            )

        verify(exactly = 1) {
            readVehicleByAnyIdentifier.readVehicle(
                UUID.fromString(TEST_VEHICLE_ID),
                TEST_VIN,
                null,
                null,
                null,
            )
        }
    }
}
