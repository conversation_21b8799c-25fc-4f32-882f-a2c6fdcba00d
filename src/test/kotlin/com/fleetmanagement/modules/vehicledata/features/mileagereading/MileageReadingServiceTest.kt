/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehicledata.features.mileagereading

import com.fleetmanagement.TestcontainersConfiguration
import com.fleetmanagement.modules.vehicledata.internal.repository.objectmothers.JPAVehicleEntityObjectMother.newVehicleEntityWithAllFieldsPopulated
import com.fleetmanagement.modules.vehicledata.repository.JPAMileageReadingRepository
import com.fleetmanagement.modules.vehicledata.repository.JPAVehicleRepository
import com.fleetmanagement.modules.vehicledata.repository.entities.JPAMileageReadingEntity
import com.fleetmanagement.modules.vehicledata.repository.entities.MileageReadingId
import com.fleetmanagement.modules.vehicledata.repository.entities.MileageReadingSource
import com.fleetmanagement.modules.vehicletransfer.VehicleTransferBuilder
import com.fleetmanagement.modules.vehicletransfer.domain.VehicleTransferDeliveredEvent
import com.fleetmanagement.modules.vehicletransfer.domain.VehicleTransferRepository
import com.fleetmanagement.modules.vehicletransfer.domain.VehicleTransferReturnedEvent
import jakarta.persistence.EntityManager
import org.awaitility.Durations
import org.awaitility.kotlin.atMost
import org.awaitility.kotlin.await
import org.awaitility.kotlin.untilAsserted
import org.awaitility.kotlin.withPollDelay
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.ApplicationEventPublisher
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Import
import org.springframework.context.annotation.Primary
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.support.JpaRepositoryFactory
import org.springframework.stereotype.Repository
import org.springframework.test.context.transaction.TestTransaction
import org.springframework.transaction.annotation.Transactional
import java.time.OffsetDateTime

@SpringBootTest
@Transactional
@Import(TestcontainersConfiguration::class)
class MileageReadingServiceTest {
    @Autowired
    private lateinit var applicationEventPublisher: ApplicationEventPublisher

    @Autowired
    private lateinit var jpaVehicleRepository: JPAVehicleRepository

    @Autowired
    private lateinit var jpaMileageReadingRepository: JPAMileageReadingRepository

    @Autowired
    private lateinit var vehicleTransferRepository: VehicleTransferRepository

    @Autowired
    private lateinit var entityManager: EntityManager

    @Autowired
    private lateinit var testJpaMileageReadingRepository: TestJpaMileageReadingRepository

    @Autowired
    private lateinit var mileageReadingService: MileageReadingService

    @AfterEach
    fun cleanup() {
        // delete mileage afterward, to reduce the chance of test failures in other tests,
        // that try to delete all vehicles and might cause fk constraint errors
        testJpaMileageReadingRepository.deleteAll()
    }

    @Test
    fun `should create mileage reading `() {
        val vehicle = jpaVehicleRepository.save(newVehicleEntityWithAllFieldsPopulated(vin = "deliveryVIN"))
        entityManager.flush()
        val readDate = OffsetDateTime.now().minusHours(4)

        mileageReadingService.addCurrentMileageReading(
            vehicleId = vehicle.id!!,
            readDate = readDate,
            mileage = 235,
            source = com.fleetmanagement.modules.vehicledata.api.dtos.MileageReadingSource.CAR_SYNC,
        )

        val mileageReadingFromRepository =
            jpaMileageReadingRepository.findAllByVehicleId(vehicleId = vehicle.id!!).single()
        assertEquals(MileageReadingSource.CAR_SYNC, mileageReadingFromRepository.source)
        assertEquals(235, mileageReadingFromRepository.mileage)
        assertEquals(readDate, mileageReadingFromRepository.readDate)
    }

    @Test
    fun `should create mileage reading on delivered event`() {
        val vehicle = jpaVehicleRepository.save(newVehicleEntityWithAllFieldsPopulated(vin = "deliveryVIN"))
        entityManager.flush()
        val vehicleTransfer =
            VehicleTransferBuilder().vehicleId(vehicle.id!!).mileageAtDelivery(235).build()
        vehicleTransferRepository.saveAndFlush(vehicleTransfer)

        applicationEventPublisher.publishEvent(
            VehicleTransferDeliveredEvent(vehicleId = vehicle.id!!, vehicleTransferKey = vehicleTransfer.key),
        )
        TestTransaction.flagForCommit()
        TestTransaction.end()

        TestTransaction.start()
        await atMost
            Durations.ONE_SECOND withPollDelay
            Durations.TWO_HUNDRED_MILLISECONDS untilAsserted
            {
                val deliveryMileageReading =
                    jpaMileageReadingRepository.findAllByVehicleId(vehicleId = vehicle.id!!).single()
                assertEquals(MileageReadingSource.DELIVERY, deliveryMileageReading.source)
                assertEquals(235, deliveryMileageReading.mileage)
            }
    }

    @Test
    fun `should not create mileage reading on delivered event if mileageAtDelivery is not maintained`() {
        val vehicle = jpaVehicleRepository.save(newVehicleEntityWithAllFieldsPopulated(vin = "noDeliveryVIN"))
        entityManager.flush()
        val vehicleTransfer =
            VehicleTransferBuilder().vehicleId(vehicle.id!!).mileageAtDelivery(null).build()
        vehicleTransferRepository.saveAndFlush(vehicleTransfer)

        applicationEventPublisher.publishEvent(
            VehicleTransferDeliveredEvent(vehicleId = vehicle.id!!, vehicleTransferKey = vehicleTransfer.key),
        )
        TestTransaction.flagForCommit()
        TestTransaction.end()

        TestTransaction.start()
        await atMost
            Durations.ONE_SECOND withPollDelay
            Durations.TWO_HUNDRED_MILLISECONDS untilAsserted
            {
                val deliveryMileageReading =
                    jpaMileageReadingRepository.findAllByVehicleId(vehicleId = vehicle.id!!)
                assertTrue(deliveryMileageReading.isEmpty())
            }
    }

    @Test
    fun `should create mileage reading on returned event`() {
        val vehicle = jpaVehicleRepository.save(newVehicleEntityWithAllFieldsPopulated(vin = "returnVIN"))
        entityManager.flush()
        val vehicleTransfer =
            VehicleTransferBuilder().vehicleId(vehicle.id!!).returned(mileageAtReturn = 9001).build()
        vehicleTransferRepository.saveAndFlush(vehicleTransfer)

        applicationEventPublisher.publishEvent(
            VehicleTransferReturnedEvent(vehicleId = vehicle.id!!, vehicleTransferKey = vehicleTransfer.key),
        )
        TestTransaction.flagForCommit()
        TestTransaction.end()

        TestTransaction.start()
        await atMost
            Durations.ONE_SECOND withPollDelay
            Durations.TWO_HUNDRED_MILLISECONDS untilAsserted
            {
                val returnMileageReading =
                    jpaMileageReadingRepository.findAllByVehicleId(vehicleId = vehicle.id!!).single()
                assertEquals(MileageReadingSource.RETURN, returnMileageReading.source)
                assertEquals(9001, returnMileageReading.mileage)
            }
    }

    @Test
    fun `should not create mileage reading on returned event if mileageAtReturn is not maintained`() {
        val vehicle = jpaVehicleRepository.save(newVehicleEntityWithAllFieldsPopulated(vin = "noReturnVIN"))
        entityManager.flush()
        val vehicleTransfer =
            VehicleTransferBuilder().vehicleId(vehicle.id!!).mileageAtReturn(null).build()
        vehicleTransferRepository.saveAndFlush(vehicleTransfer)

        applicationEventPublisher.publishEvent(
            VehicleTransferReturnedEvent(vehicleId = vehicle.id!!, vehicleTransferKey = vehicleTransfer.key),
        )
        TestTransaction.flagForCommit()
        TestTransaction.end()

        TestTransaction.start()
        await atMost
            Durations.ONE_SECOND withPollDelay
            Durations.TWO_HUNDRED_MILLISECONDS untilAsserted
            {
                val returnMileageReading =
                    jpaMileageReadingRepository.findAllByVehicleId(vehicleId = vehicle.id!!)
                assertTrue(returnMileageReading.isEmpty())
            }
    }

    @Test
    fun `should return latest mileage reading `() {
        val vehicle = jpaVehicleRepository.save(newVehicleEntityWithAllFieldsPopulated(vin = "mileageVIN"))
        entityManager.flush()
        (1..5).map {
            val readDate = OffsetDateTime.now().minusHours(it.toLong())
            mileageReadingService.addCurrentMileageReading(
                vehicleId = vehicle.id!!,
                readDate = readDate,
                mileage = 100 + it,
                source = com.fleetmanagement.modules.vehicledata.api.dtos.MileageReadingSource.CAR_SYNC,
            )
        }

        val latestMileageReading =
            requireNotNull(mileageReadingService.getLatestMileageReading(vehicleId = vehicle.id!!))

        assertEquals(101, latestMileageReading.mileage)
    }
}

@Repository
interface TestJpaMileageReadingRepository : JpaRepository<JPAMileageReadingEntity, MileageReadingId>

@TestConfiguration
class TestMileageReadingCleanupProvider {
    @Bean
    @Primary
    fun testJpaMileageReadingRepository(entityManager: EntityManager): TestJpaMileageReadingRepository =
        JpaRepositoryFactory(entityManager).getRepository(TestJpaMileageReadingRepository::class.java)
}
