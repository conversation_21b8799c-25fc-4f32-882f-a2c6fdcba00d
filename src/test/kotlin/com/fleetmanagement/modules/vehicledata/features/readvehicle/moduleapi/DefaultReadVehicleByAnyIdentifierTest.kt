/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.features.readvehicle.moduleapi

import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByEquiId
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByEquipmentNumber
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByVGUID
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByVIN
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByVehicleId
import com.fleetmanagement.modules.vehicledata.api.VehicleNotFoundInFVM
import com.fleetmanagement.modules.vehicledata.api.dtos.VehicleDTO
import com.fleetmanagement.modules.vehicledata.api.exceptions.VehicleNotFoundException
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertSame
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.util.UUID

class DefaultReadVehicleByAnyIdentifierTest {
    private val readVehicleByVehicleId: ReadVehicleByVehicleId = mockk()
    private val readVehicleByVIN: ReadVehicleByVIN = mockk()
    private val readVehicleByVGUID: ReadVehicleByVGUID = mockk()
    private val readVehicleByEquiId: ReadVehicleByEquiId = mockk()
    private val readVehicleByEquipmentNumber: ReadVehicleByEquipmentNumber = mockk()

    private lateinit var defaultReadVehicleByAnyIdentifier: DefaultReadVehicleByAnyIdentifier
    private lateinit var mockVehicleDTO: VehicleDTO

    @BeforeEach
    fun setUp() {
        defaultReadVehicleByAnyIdentifier =
            DefaultReadVehicleByAnyIdentifier(
                readVehicleByVehicleId,
                readVehicleByVIN,
                readVehicleByVGUID,
                readVehicleByEquiId,
                readVehicleByEquipmentNumber,
            )
        mockVehicleDTO = mockk<VehicleDTO>()
    }

    @Test
    fun `should throw IllegalArgumentException when all parameters are null`() {
        val exception =
            assertThrows<IllegalArgumentException> {
                defaultReadVehicleByAnyIdentifier.readVehicle(
                    vehicleId = null,
                    vin = null,
                    vguid = null,
                    equipmentNumber = null,
                    equiId = null,
                )
            }
        assertEquals("At least one identifying parameter must be provided to find a vehicle.", exception.message)
    }

    @Test
    fun `should throw IllegalArgumentException when all parameters are empty or blank`() {
        val exception =
            assertThrows<IllegalArgumentException> {
                defaultReadVehicleByAnyIdentifier.readVehicle(
                    vehicleId = null,
                    vin = "",
                    vguid = "   ",
                    equipmentNumber = null,
                    equiId = "",
                )
            }
        assertEquals("At least one identifying parameter must be provided to find a vehicle.", exception.message)
    }

    @Test
    fun `should find vehicle by vehicleId when provided and vehicle exists`() {
        val vehicleId = UUID.randomUUID()
        every { readVehicleByVehicleId.readVehicleById(vehicleId) } returns mockVehicleDTO

        val result =
            defaultReadVehicleByAnyIdentifier.readVehicle(
                vehicleId = vehicleId,
                vin = "someVin",
                vguid = "someVguid",
                equipmentNumber = 12345,
                equiId = "someEquiId",
            )

        assertSame(mockVehicleDTO, result)
        verify { readVehicleByVehicleId.readVehicleById(vehicleId) }
        verify(exactly = 0) { readVehicleByVIN.readVehicleByVIN(any()) }
        verify(exactly = 0) { readVehicleByVGUID.readVehicleByVGUID(any()) }
        verify(exactly = 0) { readVehicleByEquipmentNumber.readVehicleByEquipmentNumber(any()) }
        verify(exactly = 0) { readVehicleByEquiId.readVehicleByEquiId(any()) }
    }

    @Test
    fun `should fallback to VIN when vehicleId fails`() {
        val vehicleId = UUID.randomUUID()
        val vin = "WP0ZZZ99ZTS392124"

        every { readVehicleByVehicleId.readVehicleById(vehicleId) } throws VehicleNotFoundInFVM("vehicleId", vehicleId)
        every { readVehicleByVIN.readVehicleByVIN(vin) } returns mockVehicleDTO

        val result =
            defaultReadVehicleByAnyIdentifier.readVehicle(
                vehicleId = vehicleId,
                vin = vin,
                vguid = "someVguid",
                equipmentNumber = 12345,
                equiId = "someEquiId",
            )

        assertSame(mockVehicleDTO, result)
        verify { readVehicleByVehicleId.readVehicleById(vehicleId) }
        verify { readVehicleByVIN.readVehicleByVIN(vin) }
        verify(exactly = 0) { readVehicleByVGUID.readVehicleByVGUID(any()) }
    }

    @Test
    fun `should fallback to VGUID when vehicleId and VIN fail`() {
        val vehicleId = UUID.randomUUID()
        val vin = "WP0ZZZ99ZTS392124"
        val vguid = "some-vguid-123"

        every { readVehicleByVehicleId.readVehicleById(vehicleId) } throws VehicleNotFoundInFVM("vehicleId", vehicleId)
        every { readVehicleByVIN.readVehicleByVIN(vin) } throws VehicleNotFoundInFVM("vin", vin)
        every { readVehicleByVGUID.readVehicleByVGUID(vguid) } returns mockVehicleDTO

        val result =
            defaultReadVehicleByAnyIdentifier.readVehicle(
                vehicleId = vehicleId,
                vin = vin,
                vguid = vguid,
                equipmentNumber = 12345,
                equiId = "someEquiId",
            )

        assertSame(mockVehicleDTO, result)
        verify { readVehicleByVehicleId.readVehicleById(vehicleId) }
        verify { readVehicleByVIN.readVehicleByVIN(vin) }
        verify { readVehicleByVGUID.readVehicleByVGUID(vguid) }
        verify(exactly = 0) { readVehicleByEquipmentNumber.readVehicleByEquipmentNumber(any()) }
    }

    @Test
    fun `should fallback to equipmentNumber when previous identifiers fail`() {
        val vehicleId = UUID.randomUUID()
        val vin = "WP0ZZZ99ZTS392124"
        val vguid = "some-vguid-123"
        val equipmentNumber = 123456L

        every { readVehicleByVehicleId.readVehicleById(vehicleId) } throws VehicleNotFoundInFVM("vehicleId", vehicleId)
        every { readVehicleByVIN.readVehicleByVIN(vin) } throws VehicleNotFoundInFVM("vin", vin)
        every { readVehicleByVGUID.readVehicleByVGUID(vguid) } throws VehicleNotFoundInFVM("vguid", vguid)
        every { readVehicleByEquipmentNumber.readVehicleByEquipmentNumber(equipmentNumber) } returns mockVehicleDTO

        val result =
            defaultReadVehicleByAnyIdentifier.readVehicle(
                vehicleId = vehicleId,
                vin = vin,
                vguid = vguid,
                equipmentNumber = equipmentNumber,
                equiId = "someEquiId",
            )

        assertSame(mockVehicleDTO, result)
        verify { readVehicleByVehicleId.readVehicleById(vehicleId) }
        verify { readVehicleByVIN.readVehicleByVIN(vin) }
        verify { readVehicleByVGUID.readVehicleByVGUID(vguid) }
        verify { readVehicleByEquipmentNumber.readVehicleByEquipmentNumber(equipmentNumber) }
        verify(exactly = 0) { readVehicleByEquiId.readVehicleByEquiId(any()) }
    }

    @Test
    fun `should fallback to equiId when all other identifiers fail`() {
        val vehicleId = UUID.randomUUID()
        val vin = "WP0ZZZ99ZTS392124"
        val vguid = "some-vguid-123"
        val equipmentNumber = 123456L
        val equiId = "equiId123"

        every { readVehicleByVehicleId.readVehicleById(vehicleId) } throws VehicleNotFoundInFVM("vehicleId", vehicleId)
        every { readVehicleByVIN.readVehicleByVIN(vin) } throws VehicleNotFoundInFVM("vin", vin)
        every { readVehicleByVGUID.readVehicleByVGUID(vguid) } throws VehicleNotFoundInFVM("vguid", vguid)
        every { readVehicleByEquipmentNumber.readVehicleByEquipmentNumber(equipmentNumber) } throws
            VehicleNotFoundInFVM("equipmentNumber", equipmentNumber)
        every { readVehicleByEquiId.readVehicleByEquiId(equiId) } returns mockVehicleDTO

        val result =
            defaultReadVehicleByAnyIdentifier.readVehicle(
                vehicleId = vehicleId,
                vin = vin,
                vguid = vguid,
                equipmentNumber = equipmentNumber,
                equiId = equiId,
            )

        assertSame(mockVehicleDTO, result)
        verify { readVehicleByVehicleId.readVehicleById(vehicleId) }
        verify { readVehicleByVIN.readVehicleByVIN(vin) }
        verify { readVehicleByVGUID.readVehicleByVGUID(vguid) }
        verify { readVehicleByEquipmentNumber.readVehicleByEquipmentNumber(equipmentNumber) }
        verify { readVehicleByEquiId.readVehicleByEquiId(equiId) }
    }

    @Test
    fun `should throw VehicleNotFoundException when all identifiers fail`() {
        val vehicleId = UUID.randomUUID()
        val vin = "WP0ZZZ99ZTS392124"
        val vguid = "some-vguid-123"
        val equipmentNumber = 123456L
        val equiId = "equiId123"

        every { readVehicleByVehicleId.readVehicleById(vehicleId) } throws VehicleNotFoundInFVM("vehicleId", vehicleId)
        every { readVehicleByVIN.readVehicleByVIN(vin) } throws VehicleNotFoundInFVM("vin", vin)
        every { readVehicleByVGUID.readVehicleByVGUID(vguid) } throws VehicleNotFoundInFVM("vguid", vguid)
        every { readVehicleByEquipmentNumber.readVehicleByEquipmentNumber(equipmentNumber) } throws
            VehicleNotFoundInFVM("equipmentNumber", equipmentNumber)
        every { readVehicleByEquiId.readVehicleByEquiId(equiId) } throws VehicleNotFoundInFVM("equiId", equiId)

        val exception =
            assertThrows<VehicleNotFoundException> {
                defaultReadVehicleByAnyIdentifier.readVehicle(
                    vehicleId = vehicleId,
                    vin = vin,
                    vguid = vguid,
                    equipmentNumber = equipmentNumber,
                    equiId = equiId,
                )
            }

        assertEquals("No vehicle found for any of the provided parameters.", exception.message)
        verify { readVehicleByVehicleId.readVehicleById(vehicleId) }
        verify { readVehicleByVIN.readVehicleByVIN(vin) }
        verify { readVehicleByVGUID.readVehicleByVGUID(vguid) }
        verify { readVehicleByEquipmentNumber.readVehicleByEquipmentNumber(equipmentNumber) }
        verify { readVehicleByEquiId.readVehicleByEquiId(equiId) }
    }

    @Test
    fun `should skip null vehicleId and find by VIN`() {
        val vin = "WP0ZZZ99ZTS392124"

        every { readVehicleByVIN.readVehicleByVIN(vin) } returns mockVehicleDTO

        val result =
            defaultReadVehicleByAnyIdentifier.readVehicle(
                vehicleId = null,
                vin = vin,
                vguid = "someVguid",
                equipmentNumber = 12345,
                equiId = "someEquiId",
            )

        assertSame(mockVehicleDTO, result)
        verify(exactly = 0) { readVehicleByVehicleId.readVehicleById(any()) }
        verify { readVehicleByVIN.readVehicleByVIN(vin) }
    }

    @Test
    fun `should skip blank VIN and find by VGUID`() {
        val vguid = "some-vguid-123"

        every { readVehicleByVGUID.readVehicleByVGUID(vguid) } returns mockVehicleDTO

        val result =
            defaultReadVehicleByAnyIdentifier.readVehicle(
                vehicleId = null,
                vin = "   ",
                vguid = vguid,
                equipmentNumber = 12345,
                equiId = "someEquiId",
            )

        assertSame(mockVehicleDTO, result)
        verify(exactly = 0) { readVehicleByVehicleId.readVehicleById(any()) }
        verify(exactly = 0) { readVehicleByVIN.readVehicleByVIN(any()) }
        verify { readVehicleByVGUID.readVehicleByVGUID(vguid) }
    }

    @Test
    fun `should skip blank VGUID and find by equipmentNumber`() {
        val equipmentNumber = 123456L

        every { readVehicleByEquipmentNumber.readVehicleByEquipmentNumber(equipmentNumber) } returns mockVehicleDTO

        val result =
            defaultReadVehicleByAnyIdentifier.readVehicle(
                vehicleId = null,
                vin = null,
                vguid = "",
                equipmentNumber = equipmentNumber,
                equiId = "someEquiId",
            )

        assertSame(mockVehicleDTO, result)
        verify(exactly = 0) { readVehicleByVehicleId.readVehicleById(any()) }
        verify(exactly = 0) { readVehicleByVIN.readVehicleByVIN(any()) }
        verify(exactly = 0) { readVehicleByVGUID.readVehicleByVGUID(any()) }
        verify { readVehicleByEquipmentNumber.readVehicleByEquipmentNumber(equipmentNumber) }
    }

    @Test
    fun `should skip null equipmentNumber and find by equiId`() {
        val equiId = "equiId123"

        every { readVehicleByEquiId.readVehicleByEquiId(equiId) } returns mockVehicleDTO

        val result =
            defaultReadVehicleByAnyIdentifier.readVehicle(
                vehicleId = null,
                vin = null,
                vguid = null,
                equipmentNumber = null,
                equiId = equiId,
            )

        assertSame(mockVehicleDTO, result)
        verify(exactly = 0) { readVehicleByVehicleId.readVehicleById(any()) }
        verify(exactly = 0) { readVehicleByVIN.readVehicleByVIN(any()) }
        verify(exactly = 0) { readVehicleByVGUID.readVehicleByVGUID(any()) }
        verify(exactly = 0) { readVehicleByEquipmentNumber.readVehicleByEquipmentNumber(any()) }
        verify { readVehicleByEquiId.readVehicleByEquiId(equiId) }
    }
}
