/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.fvm.features.predeliveryinspection

import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.ObjectMapper
import com.fleetmanagement.modules.fvm.dto.accesscontrol.ApiLabelConstant
import com.fleetmanagement.modules.fvm.dto.accesscontrol.Resource
import com.fleetmanagement.modules.fvm.dto.api.APIResponse
import com.fleetmanagement.modules.predeliveryinspection.application.PDIOrderingEmailService
import com.fleetmanagement.modules.predeliveryinspection.application.PdiValidationError
import com.fleetmanagement.security.api.dto.PrivilegeFilter
import com.fleetmanagement.security.api.dto.PrivilegePermission.READ
import com.fleetmanagement.security.api.dto.PrivilegePermission.WRITE
import com.fleetmanagement.security.configurations.SecurityConfiguration
import com.fleetmanagement.security.features.accesscontrol.domain.Privilege
import com.fleetmanagement.security.features.accesscontrol.services.PrivilegeService
import com.fleetmanagement.security.features.tokenvalidation.JwtValidator
import com.fleetmanagement.security.utils.WithMockALBUser
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.slot
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertIterableEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.context.annotation.Import
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import java.nio.charset.StandardCharsets
import java.util.*

@WebMvcTest(
    controllers = [FVMPdiOrderingController::class],
)
@Import(SecurityConfiguration::class, ApiLabelConstant::class)
class FVMPdiOrderingControllerTest {
    @Autowired
    private lateinit var objectMapper: ObjectMapper

    @MockkBean
    private lateinit var pdiOrderingEmailService: PDIOrderingEmailService

    @Autowired
    private lateinit var mockMvc: MockMvc

    @MockkBean
    private lateinit var privilegeService: PrivilegeService

    @MockkBean(name = "albTokenValidator")
    private lateinit var albTokenValidator: JwtValidator

    @MockkBean(name = "apiGatewayTokenValidator")
    private lateinit var apiGatewayTokenValidator: JwtValidator

    @BeforeEach
    fun setupPrivileges() {
        every { albTokenValidator.validate(any()) } returns Unit
        every { albTokenValidator.canValidate(any()) } returns true

        every { apiGatewayTokenValidator.validate(any()) } returns Unit
        every { apiGatewayTokenValidator.canValidate(any()) } returns true

        every { privilegeService.findAllByGroupIdWithFilters(any()) } returns
            listOf(
                Privilege(
                    id = 1000,
                    resource = "preDeliveryInspection",
                    permission = READ,
                    filter = null,
                ),
                Privilege(
                    id = 1001,
                    resource = "preDeliveryInspection",
                    permission = WRITE,
                    filter = null,
                ),
                Privilege(
                    id = 1002,
                    resource = Resource.APIS.label,
                    permission = READ,
                    filter = PrivilegeFilter(listOf(Resource.PRE_DELIVERY_INSPECTION.label)),
                ),
                Privilege(
                    id = 1003,
                    resource = Resource.APIS.label,
                    permission = WRITE,
                    filter = PrivilegeFilter(listOf(Resource.PRE_DELIVERY_INSPECTION.label)),
                ),
            )
    }

    @Test
    @WithMockALBUser(authorities = [ApiLabelConstant.API_PRE_DELIVERY_INSPECTION_WRITE])
    fun `should return warnings when pdi ordering partially fails with validation errors`() {
        val validationErrors =
            listOf(
                PdiValidationError(
                    identifier = "someId",
                    type = PdiValidationError.ValidationErrorType.PDI_MISSING_PLANNED_DATE,
                    detail = "someDetail",
                ),
            )
        val vehicleIdsSlot = slot<List<UUID>>()
        every { pdiOrderingEmailService.createAndSendPdiEmailsAsync(capture(vehicleIdsSlot)) } returns validationErrors

        val payload = OrderingEmailPayload((1..5).map { UUID.randomUUID() })
        val result =
            mockMvc
                .perform(
                    MockMvcRequestBuilders
                        .post("/ui/predelivery-inspection/ordering-email")
                        .header("Authorization", "Bearer testAccessToken")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(payload)),
                ).andExpect(status().isConflict)
                .andReturn()

        val resultPayload =
            objectMapper.readValue(
                result.response.getContentAsString(StandardCharsets.UTF_8),
                object : TypeReference<APIResponse<Nothing?>>() {},
            )

        assertIterableEquals(payload.vehicleIds, vehicleIdsSlot.captured)
        assertEquals(validationErrors.single().detail, resultPayload.warnings!!.single().detail)
        assertEquals(
            "error.fvm.pdi.invalidPlannedDate",
            resultPayload.warnings
                .single()
                .type
                .toString(),
        )
    }
}
