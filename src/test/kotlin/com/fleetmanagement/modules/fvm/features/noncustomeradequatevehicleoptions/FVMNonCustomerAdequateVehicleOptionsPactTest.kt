package com.fleetmanagement.modules.fvm.features.noncustomeradequatevehicleoptions

import au.com.dius.pact.provider.junit5.PactVerificationContext
import au.com.dius.pact.provider.junit5.PactVerificationInvocationContextProvider
import au.com.dius.pact.provider.junitsupport.Consumer
import au.com.dius.pact.provider.junitsupport.Provider
import au.com.dius.pact.provider.junitsupport.State
import au.com.dius.pact.provider.junitsupport.loader.PactFolder
import au.com.dius.pact.provider.spring.junit5.MockMvcTestTarget
import com.fleetmanagement.modules.fvm.dto.accesscontrol.ApiLabelConstant
import com.fleetmanagement.modules.fvm.dto.noncustomeradequatevehicleoptions.FVMCreateVehicleOptionTag
import com.fleetmanagement.modules.fvm.dto.vehicledata.FVMVehicleOptionTag
import com.fleetmanagement.pact.Pacticipants.FLEET_MANAGEMENT_UI
import com.fleetmanagement.pact.Pacticipants.NON_CUSTOMER_ADEQUATE_VEHICLES
import com.fleetmanagement.security.configurations.SecurityConfiguration
import com.fleetmanagement.security.features.accesscontrol.services.PrivilegeService
import com.fleetmanagement.security.features.tokenvalidation.JwtValidator
import com.fleetmanagement.security.utils.WithMockALBUser
import com.ninjasquad.springmockk.MockkBean
import io.mockk.clearAllMocks
import io.mockk.every
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.TestTemplate
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.autoconfigure.data.jpa.JpaRepositoriesAutoConfiguration
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.context.annotation.Import
import org.springframework.http.converter.HttpMessageConverter
import org.springframework.test.web.servlet.MockMvc

@WebMvcTest(
    controllers = [FVMNonCustomerAdequateVehicleOptionsController::class],
    excludeAutoConfiguration = [
        JpaRepositoriesAutoConfiguration::class,
    ],
    properties = [
        "security.enabled=true",
        "pact_do_not_track=true",
    ],
)
@Import(SecurityConfiguration::class, ApiLabelConstant::class)
@AutoConfigureMockMvc
@Provider(NON_CUSTOMER_ADEQUATE_VEHICLES)
@Consumer(FLEET_MANAGEMENT_UI)
@PactFolder("pacts/provider")
class FVMNonCustomerAdequateVehicleOptionsPactTest {
    @Autowired
    private lateinit var mockMvc: MockMvc

    @MockkBean
    private lateinit var fvmNonCustomerAdequateVehicleOptionsService: FVMNonCustomerAdequateVehicleOptionsService

    @MockkBean(name = "albTokenValidator")
    private lateinit var albTokenValidator: JwtValidator

    @MockkBean(name = "apiGatewayTokenValidator")
    private lateinit var apiGatewayTokenValidator: JwtValidator

    @MockkBean
    private lateinit var privilegeService: PrivilegeService

    @Autowired
    private lateinit var controller: FVMNonCustomerAdequateVehicleOptionsController

    @Autowired
    private lateinit var httpMessageConverters: List<HttpMessageConverter<*>>

    @BeforeEach
    fun before(context: PactVerificationContext) {
        context.target =
            MockMvcTestTarget(
                controllers = listOf(controller),
                messageConverters = httpMessageConverters,
                servletPath = "/vs",
            )

        every { albTokenValidator.validate(any()) } returns Unit
        every { albTokenValidator.canValidate(any()) } returns true

        every { apiGatewayTokenValidator.validate(any()) } returns Unit
        every { apiGatewayTokenValidator.canValidate(any()) } returns true

        every { privilegeService.findAllByGroupIdWithFilters(any()) } returns
            emptyList()
    }

    @AfterEach
    fun afterEach() {
        clearAllMocks()
    }

    @State("FVM has multiple non-customer adequate option tags")
    fun fvmHasMultipleNonCustomerAdequateVehicleOptionTags() {
        every { fvmNonCustomerAdequateVehicleOptionsService.getAllNonCustomerAdequateOptions() } returns
            listOf(
                FVMVehicleOptionTag("9WT", "Apple® CarPlay inkl. Sprachsteuerung Siri®"),
                FVMVehicleOptionTag("7V0", "Zentraler analoger Drehzahlmesser mit schwarzem Ziffernblatt"),
            )
    }

    @State("FVM has an option tags - AB0")
    fun fvmHasAnOptionTag() {
        every { fvmNonCustomerAdequateVehicleOptionsService.createNonCustomerAdequateOption(any<FVMCreateVehicleOptionTag>()) } returns
            FVMVehicleOptionTag("AB0", "Apple® CarPlay inkl. Sprachsteuerung Siri®")
    }

    @State("FVM has a non-customer adequate option tags - AB0")
    fun fvmHasANonCustomerAdequateVehicleOptionTag() {
        every { fvmNonCustomerAdequateVehicleOptionsService.deleteNonCustomerAdequateOption("AB0") } returns Unit
    }

    @TestTemplate
    @WithMockALBUser
    @ExtendWith(PactVerificationInvocationContextProvider::class)
    fun pactVerificationTestTemplate(context: PactVerificationContext) {
        context.verifyInteraction()
    }
}
