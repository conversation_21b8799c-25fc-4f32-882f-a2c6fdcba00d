/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.features.admin.accesscontrol.privilege

import com.fleetmanagement.modules.fvm.dto.accesscontrol.FieldLabel
import com.fleetmanagement.modules.fvm.dto.accesscontrol.PagesWithActions
import com.fleetmanagement.modules.fvm.dto.accesscontrol.Resource
import com.fleetmanagement.modules.fvm.features.admin.accesscontrol.exception.AccessControlExportException
import com.fleetmanagement.security.api.ReadGroupPrivileges
import com.fleetmanagement.security.api.dto.PrivilegeFilter
import com.fleetmanagement.security.api.dto.PrivilegePermission
import com.fleetmanagement.security.features.accesscontrol.domain.Privilege
import io.mockk.every
import io.mockk.mockk
import io.mockk.unmockkAll
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertAll
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.util.UUID

class AccessControlPrivilegeServiceTest {
    private lateinit var privilegeService: AccessControlPrivilegeService
    private lateinit var readGroupPrivileges: ReadGroupPrivileges

    @BeforeEach
    fun setUp() {
        readGroupPrivileges = mockk()
        privilegeService = AccessControlPrivilegeService(readGroupPrivileges)
    }

    @AfterEach
    fun tearDown() {
        unmockkAll()
    }

    /**
     * Every field annotated with @AccessControlledField and part of a class annotated
     * with @AccessControlledResource will be exported here.
     * Adjust the expected row count when new fields are added or something is removed from such classes
     */
    @Test
    fun `should return default list of privileges for any user`() {
        // GIVEN
        val groupId = UUID.randomUUID()
        every { readGroupPrivileges.findAllByGroupIdWithFilters(listOf(groupId)) } returns emptyList()

        // WHEN
        val privileges = privilegeService.getDefaultPrivileges()

        // THEN
        assertEquals(14, privileges.size)

        val pagePrivilege = privileges.filter { it.resource == Resource.PAGES }
        val actionPrivilege = privileges.filter { it.resource == Resource.ACTIONS }
        val fieldPrivilege = privileges.filterNot { it.resource == Resource.PAGES || it.resource == Resource.ACTIONS }
        assertAll(
            // validate pages
            { assertEquals(1, pagePrivilege.size) },
            { assertEquals(57, pagePrivilege.first().fields.size) },
            { assertNull(pagePrivilege.first().permission) },
            { assertTrue(PagesWithActions.getAllPageKeys().containsAll(pagePrivilege.first().fields)) },
            // validate actions
            { assertEquals(1, actionPrivilege.size) },
            { assertEquals(40, actionPrivilege.first().fields.size) },
            { assertNull(actionPrivilege.first().permission) },
            { assertTrue(PagesWithActions.getAllActionKeys().containsAll(actionPrivilege.first().fields)) },
            // validate actions
            { assertEquals(12, fieldPrivilege.size) },
            { assertEquals(340, fieldPrivilege.sumOf { it.fields.size }) },
        )
    }

    /**
     * Every field annotated with @AccessControlledField and part of a class annotated
     * with @AccessControlledResource will be exported here.
     * Adjust the expected row count when new fields are added or something is removed from such classes
     */
    @Test
    fun `should return current set of privileges from database for given user`() {
        // GIVEN
        val groupId = UUID.randomUUID()
        every { readGroupPrivileges.findAllByGroupIdWithFilters(listOf(groupId)) } returns testPrivileges()

        // WHEN
        val privileges = privilegeService.getCurrentPrivileges(groupId)

        // THEN
        assertEquals(19, privileges.size)

        val allPages = privileges.filter { it.resource == Resource.PAGES }
        val pagesWithReadPermissions = allPages.filter { it.permission == PrivilegePermission.READ }
        val pagesWithNoPermissions = allPages.filterNot { it in pagesWithReadPermissions }

        val allActions = privileges.filter { it.resource == Resource.ACTIONS }
        val actionsWithExecutePermissions = allActions.filter { it.permission == PrivilegePermission.EXECUTE }
        val actionsWithNoPermissions = allActions.filterNot { it in actionsWithExecutePermissions }

        val allFields =
            privileges.filter {
                it.resource != Resource.PAGES &&
                    it.resource != Resource.ACTIONS &&
                    it.resource != Resource.APIS
            }
        val fieldsWithReadPermissions = allFields.filter { it.permission == PrivilegePermission.READ }
        val fieldsWithWritePermissions = allFields.filter { it.permission == PrivilegePermission.WRITE }
        val fieldsWithNoPermissions =
            allFields.filterNot { it.permission == PrivilegePermission.READ || it.permission == PrivilegePermission.WRITE }

        assertAll(
            // validate pages
            { assertEquals(1, pagesWithNoPermissions.size) },
            { assertEquals(55, pagesWithNoPermissions.first().fields.size) },
            { assertEquals(1, pagesWithReadPermissions.size) },
            { assertEquals(2, pagesWithReadPermissions.first().fields.size) },
            {
                assertTrue(
                    PagesWithActions
                        .getAllPageKeys()
                        .containsAll(allPages.flatMap { it.fields }),
                )
            },
            // validate actions
            { assertEquals(1, actionsWithNoPermissions.size) },
            { assertEquals(38, actionsWithNoPermissions.first().fields.size) },
            { assertEquals(1, actionsWithExecutePermissions.size) },
            { assertEquals(2, actionsWithExecutePermissions.first().fields.size) },
            {
                assertTrue(
                    PagesWithActions
                        .getAllActionKeys()
                        .containsAll(allActions.flatMap { it.fields }),
                )
            },
            // validate fields
            { assertEquals(340, allFields.sumOf { it.fields.size }) },
            { assertEquals(11, fieldsWithNoPermissions.size) },
            { assertEquals(327, fieldsWithNoPermissions.sumOf { it.fields.size }) },
            { assertEquals(2, fieldsWithReadPermissions.size) },
            { assertEquals(10, fieldsWithReadPermissions.sumOf { it.fields.size }) },
            { assertEquals(2, fieldsWithWritePermissions.size) },
            { assertEquals(3, fieldsWithWritePermissions.sumOf { it.fields.size }) },
        )
    }

    private fun testPrivileges() =
        listOf(
            Privilege(
                1,
                Resource.PAGES.label,
                PrivilegePermission.READ,
                PrivilegeFilter(
                    listOf(
                        "page.vehicleDetails.vehicleHistory",
                        "page.vehicleRegistration",
                    ),
                ),
            ),
            Privilege(
                2,
                Resource.ACTIONS.label,
                PrivilegePermission.EXECUTE,
                PrivilegeFilter(
                    listOf(
                        "page.vehicleDetails.vehicleHistory.action.exportRows",
                        "page.vehicleRegistration.action.addVehicle",
                    ),
                ),
            ),
            Privilege(
                2,
                Resource.VEHICLE.label,
                PrivilegePermission.WRITE,
                PrivilegeFilter(
                    listOf(
                        FieldLabel.VIN.label,
                        FieldLabel.MODEL_DESCRIPTION.label,
                    ),
                ),
            ),
            Privilege(
                3,
                Resource.VEHICLE_REGISTRATION.label,
                PrivilegePermission.READ,
                PrivilegeFilter(
                    listOf(
                        FieldLabel.VEHICLE_REGISTRATION_LICENCE_PLATE.label,
                        FieldLabel.VEHICLE_REGISTRATION_TSN.label,
                    ),
                ),
            ),
            Privilege(
                4,
                Resource.VEHICLE_REGISTRATION.label,
                PrivilegePermission.WRITE,
                PrivilegeFilter(
                    listOf(
                        FieldLabel.VEHICLE_REGISTRATION_BRIEF_NUMBER.label,
                        "RANDOM-FIELD-NOT-EXISTING-IN-DB",
                    ),
                ),
            ),
            Privilege(5, Resource.VEHICLE_LOCATION.label, PrivilegePermission.READ, null),
        )

    @Test
    fun `should throw relevant exception when failed to generate permissions excel`() {
        // GIVEN
        val groupId = UUID.randomUUID()
        every { readGroupPrivileges.findAllByGroupIdWithFilters(listOf(groupId)) } throws RuntimeException("Something went wrong")

        // WHEN
        val exception =
            assertThrows(AccessControlExportException::class.java) {
                privilegeService.getCurrentPrivileges(groupId)
            }

        // THEN
        assert(exception.message == "Error while generating privileges")
    }
}
