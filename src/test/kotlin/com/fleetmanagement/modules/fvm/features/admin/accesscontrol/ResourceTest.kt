/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.features.admin.accesscontrol

import com.fleetmanagement.modules.fvm.dto.accesscontrol.PagesWithActions
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test

class ResourceTest {
    @Test
    fun `test getAllPageKeys generates correct keys`() {
        val allPageKeys = PagesWithActions.getAllPageKeys()

        val expectedPageKeys =
            listOf(
                "page.admin.derivationTables.consignee",
                "page.admin.derivationTables.depreciationRelevantCostCenter",
                "page.admin.derivationTables.usageGroup",
                "page.admin.derivationTables.vehicleUsage",
                "page.admin.roleManagement.roles",
                "page.admin.roleManagement.history",
                "page.admin.vehicleImport",
                "page.clientManagement.createVehicle",
                "page.clientManagement.manualVehicleCreation",
                "page.clientManagement.campaigns",
                "page.clientManagement.terminating",
                "page.clientManagement.vehicleReturn",
                "page.clientManagement.vehicleDelivery",
                "page.createVehicle",
                "page.people",
                "page.peopleDetails",
                "page.peopleDetails.general",
                "page.peopleDetails.roles",
                "page.peopleDetails.history",
                "page.peopleDetails.vehicles",
                "page.settings",
                "page.vehicleDetails.documents",
                "page.vehicleDetails.lifecycle",
                "page.vehicleDetails.location",
                "page.vehicleDetails.overview",
                "page.vehicleDetails.usage",
                "page.vehicleDetails.vehicle",
                "page.vehicleDetails.vehicleHistory",
                "page.vehicleManagement",
                "page.vehicleRegistration",
                "page.vehicleRegistration.printing",
                "page.vehicleRegistration.registered",
                "page.vehicleRegistration.testNumberRenewal",
                "page.vehicleRegistration.unregistered",
                "page.vehicleTransferManagement",
                "page.welcome",
                "page.logistics",
                "page.scrapping",
                "page.scrapping.monitoring",
                "page.scrapping.identification",
                "page.scrapping.offeredForReuse",
                "page.scrapping.approvedForScrapping",
                "page.evaluation",
                "page.evaluation.tuevCommissioning",
                "page.evaluation.pcComplaintChecks",
                "page.evaluation.residualValueMarket",
                "page.enquiryManagement",
                "page.nonCustomerAdequateVehicles",
                "page.nonCustomerAdequateVehicles.monitoring",
                "page.nonCustomerAdequateVehicles.identification",
                "page.sales",
                "page.sales.vehicleSearch",
                "page.sales.soldVehicles",
                "page.sales.createInvoice",
                "page.sales.invoicedVehiclesTransactions",
                "page.sales.customerDeliveredVehicles",
                "page.damageManagement",
            )

        val expectedCounts = expectedPageKeys.groupingBy { it }.eachCount()
        val actualCounts = allPageKeys.groupingBy { it }.eachCount()

        val missing = expectedCounts.filter { (k, v) -> v > (actualCounts[k] ?: 0) }
        val extra = actualCounts.filter { (k, v) -> v > (expectedCounts[k] ?: 0) }

        assertTrue(missing.isEmpty() && extra.isEmpty()) {
            "Missing: $missing, Extra: $extra"
        }
    }

    @Test
    fun `test getAllActionKeys generates correct keys`() {
        val allActionKeys = PagesWithActions.getAllActionKeys()

        val expectedActionKeys =
            listOf(
                "page.admin.derivationTables.consignee.action.addNewConsignee",
                "page.admin.derivationTables.consignee.action.deleteConsignee",
                "page.admin.derivationTables.depreciationRelevantCostCenter.action.addNewCostCenter",
                "page.admin.derivationTables.depreciationRelevantCostCenter.action.deleteCostCenter",
                "page.admin.derivationTables.usageGroup.action.addNewUsageGroup",
                "page.admin.derivationTables.usageGroup.action.deleteUsageGroup",
                "page.admin.derivationTables.vehicleUsage.action.addNewVehicleUsage",
                "page.admin.derivationTables.vehicleUsage.action.deleteVehicleUsage",
                "page.admin.vehicleImport.action.importVehicle",
                "page.createVehicle.action.createVehicle",
                "page.peopleDetails.history.action.exportRows",
                "page.vehicleDetails.location.action.exportRows",
                "page.vehicleDetails.vehicleHistory.action.exportRows",
                "page.vehicleManagement.action.changeLocation",
                "page.vehicleManagement.action.exportRows",
                "page.vehicleManagement.action.viewDocuments",
                "page.vehicleManagement.action.evaluateVehicles",
                "page.vehicleRegistration.action.addVehicle",
                "page.vehicleRegistration.action.deleteRegistrationOrder",
                "page.vehicleRegistration.registered.action.importRegistrations",
                "page.vehicleRegistration.testNumberRenewal.action.renewTestNumbers",
                "page.vehicleRegistration.unregistered.action.exportSelection",
                "page.vehicleRegistration.unregistered.action.importPsoList",
                "page.vehicleTransferManagement.action.addNewPlannedVehicleTransfer",
                "page.vehicleTransferManagement.action.delete",
                "page.vehicleTransferManagement.action.cancel",
                "page.vehicleTransferManagement.action.updatePreDeliveryInspection",
                "page.vehicleTransferManagement.action.updateVehicleTransfer",
                "page.vehicleTransferManagement.action.exportRows",
                "page.sales.soldVehicles.action.createInvoice",
                "page.sales.invoicedVehiclesTransactions.action.approveInvoice",
                "page.sales.invoicedVehiclesTransactions.action.rejectInvoice",
                "page.sales.invoicedVehiclesTransactions.action.cancelInvoice",
                "page.common.action.createVehicleTransfer",
                "page.common.action.printGreenInsuranceCard",
                "page.common.action.printPowerOfAttorney",
                "page.settings.gridViewConfig.action.deletePublicView",
                "page.settings.gridViewConfig.action.editPublicView",
                "page.common.action.printProvisionOfDelivery",
                "page.common.action.pdiOrdering",
            )

        val expectedCounts = expectedActionKeys.groupingBy { it }.eachCount()
        val actualCounts = allActionKeys.groupingBy { it }.eachCount()

        val missing = expectedCounts.filter { (k, v) -> v > (actualCounts[k] ?: 0) }
        val extra = actualCounts.filter { (k, v) -> v > (expectedCounts[k] ?: 0) }

        assertTrue(missing.isEmpty() && extra.isEmpty()) {
            "Missing: $missing, Extra: $extra"
        }
    }
}
