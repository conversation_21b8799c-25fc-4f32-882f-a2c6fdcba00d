/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

@file:Suppress("ktlint:standard:no-wildcard-imports")

package com.fleetmanagement.modules.fvm.features.passthrough

import com.fleetmanagement.TestcontainersConfiguration
import com.fleetmanagement.modules.fvm.dto.accesscontrol.ApiLabelConstant
import com.fleetmanagement.modules.fvm.dto.accesscontrol.FieldLabel
import com.fleetmanagement.modules.fvm.dto.accesscontrol.Resource
import com.fleetmanagement.modules.vehiclehistory.service.VehicleChangesService
import com.fleetmanagement.security.api.dto.PrivilegeFilter
import com.fleetmanagement.security.api.dto.PrivilegePermission.READ
import com.fleetmanagement.security.api.dto.PrivilegePermission.WRITE
import com.fleetmanagement.security.features.accesscontrol.domain.Privilege
import com.fleetmanagement.security.features.accesscontrol.services.PrivilegeService
import com.fleetmanagement.security.features.tokenvalidation.JwtValidator
import com.fleetmanagement.security.features.tokenvalidation.alb.entraid.FixedGroupIds
import com.fleetmanagement.security.utils.TestObjectMothers.testJWTToken
import com.fleetmanagement.security.utils.WithMockALBUser
import com.github.tomakehurst.wiremock.client.WireMock.aMultipart
import com.github.tomakehurst.wiremock.client.WireMock.aResponse
import com.github.tomakehurst.wiremock.client.WireMock.delete
import com.github.tomakehurst.wiremock.client.WireMock.deleteRequestedFor
import com.github.tomakehurst.wiremock.client.WireMock.equalTo
import com.github.tomakehurst.wiremock.client.WireMock.equalToJson
import com.github.tomakehurst.wiremock.client.WireMock.exactly
import com.github.tomakehurst.wiremock.client.WireMock.get
import com.github.tomakehurst.wiremock.client.WireMock.getRequestedFor
import com.github.tomakehurst.wiremock.client.WireMock.jsonResponse
import com.github.tomakehurst.wiremock.client.WireMock.notFound
import com.github.tomakehurst.wiremock.client.WireMock.post
import com.github.tomakehurst.wiremock.client.WireMock.postRequestedFor
import com.github.tomakehurst.wiremock.client.WireMock.put
import com.github.tomakehurst.wiremock.client.WireMock.putRequestedFor
import com.github.tomakehurst.wiremock.client.WireMock.stubFor
import com.github.tomakehurst.wiremock.client.WireMock.urlEqualTo
import com.github.tomakehurst.wiremock.client.WireMock.verify
import com.github.tomakehurst.wiremock.core.WireMockConfiguration.wireMockConfig
import com.github.tomakehurst.wiremock.http.Fault
import com.github.tomakehurst.wiremock.junit5.WireMockExtension
import com.github.tomakehurst.wiremock.junit5.WireMockRuntimeInfo
import com.github.tomakehurst.wiremock.junit5.WireMockTest
import com.ninjasquad.springmockk.MockkBean
import com.ninjasquad.springmockk.SpykBean
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.verify
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.RegisterExtension
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.annotation.Import
import org.springframework.core.io.ClassPathResource
import org.springframework.http.MediaType
import org.springframework.mock.web.MockMultipartFile
import org.springframework.test.context.DynamicPropertyRegistry
import org.springframework.test.context.DynamicPropertySource
import org.springframework.test.context.TestPropertySource
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders
import org.springframework.test.web.servlet.result.MockMvcResultMatchers
import java.util.UUID

@SpringBootTest
@TestPropertySource(
    properties = [
        "security.enabled=true",
    ],
)
@AutoConfigureMockMvc
@WireMockTest
@Import(TestcontainersConfiguration::class, ApiLabelConstant::class)
class FVMVehicleRegistrationControllerTest {
    companion object {
        @RegisterExtension
        var wireMockExtension: WireMockExtension =
            WireMockExtension
                .newInstance()
                .options(wireMockConfig().dynamicPort().dynamicPort())
                .configureStaticDsl(true)
                .build()

        @JvmStatic
        @DynamicPropertySource
        fun properties(registry: DynamicPropertyRegistry) {
            registry.add("fvm.vehicle-registration-base-uri", wireMockExtension::baseUrl)
        }
    }

    @Autowired
    private lateinit var mockMvc: MockMvc

    @SpykBean
    private lateinit var vehicleChanges: VehicleChangesService

    @MockkBean
    private lateinit var privilegeService: PrivilegeService

    @MockkBean(name = "apiGatewayTokenValidator")
    private lateinit var apiGatewayTokenValidator: JwtValidator

    @MockkBean(name = "albTokenValidator")
    private lateinit var albTokenValidator: JwtValidator

    @BeforeEach
    fun beforeEach() {
        listOf(apiGatewayTokenValidator, albTokenValidator).forEach {
            every { it.validate(any()) } returns Unit
            every { it.canValidate(any()) } returns true
        }
    }

    @AfterEach
    fun afterEach() {
        clearAllMocks()
    }

    @Test
    @WithMockALBUser(groups = [FixedGroupIds.payroll], authorities = [ApiLabelConstant.API_VEHICLE_REGISTRATION_READ])
    fun `vehicle registration orders contains only fields that user is allowed to access`() {
        setupPermissionsForDifferentGroups()

        //  Given a response from the vehicle-registration
        //  service containing orders with all fields
        stubFor(
            get("/api/vr/orders/uncompleted").willReturn(
                jsonResponse(
                    """
                    {
                      "data": [
                        {
                          "vin": "WP0ZZZ975RL118042",
                          "vehicleId": "fa0e5e5a-0b4a-4125-81d0-488b0532d7c0",
                          "briefNumber": "HA055316",
                          "driveType": "OVC_HEV",
                          "leasingType": "DLF",
                          "modelType": "Panamera 4S E-Hybrid Sport Turismo",
                          "department": "DLF",
                          "registrationOffice": "",
                          "plannedLicencePlate": null,
                          "plannedRegistrationDate": null,
                          "commenter": "",
                          "orderStatus": "OPEN",
                          "storageLocation": null,
                          "tsn": null,
                          "hsn": null,
                          "registrationType": null,
                          "registrationDate": null,
                          "firstRegistrationDate": null,
                          "lastRegistrationDate": null,
                          "lastDeRegistrationDate": null,
                          "licencePlate": null,
                          "remark": null,
                          "sfme": null,
                          "testVehicle": null,
                          "registrationStatus": null,
                          "deleted": false,
                          "createdAt": "2024-04-30T07:38:45.953165Z",
                          "updatedAt": "2024-04-30T07:38:52.811217Z",
                          "version": 100
                        }
                      ]
                    }
                    """.trimIndent(),
                    200,
                ),
            ),
        )

        // Perform test to verify expectations
        mockMvc
            .perform(
                MockMvcRequestBuilders
                    .get("/ui/vehicleregistration/orders")
                    .header("Authorization", "Bearer testAccessToken")
                    .header("X-Amzn-Oidc-Accesstoken", testJWTToken())
                    .header("X-Amzn-Oidc-Data", "data-token"),
            ).andExpectAll(
                MockMvcResultMatchers.status().is2xxSuccessful,
                MockMvcResultMatchers.jsonPath("$.data[0].vin").exists(),
                MockMvcResultMatchers.jsonPath("$.data[0].vehicleId").exists(),
                MockMvcResultMatchers.jsonPath("$.data[0].version").value(100),
                MockMvcResultMatchers.jsonPath("$.data[0].briefNumber").exists(),
                MockMvcResultMatchers.jsonPath("$.data[0].modelType").doesNotExist(),
            )

        verify(exactly = 1) {
            privilegeService.findAllByGroupIdWithFilters(
                listOf(UUID.fromString(FixedGroupIds.payroll)),
            )
        }
        verify(
            exactly(1),
            getRequestedFor(urlEqualTo("/api/vr/orders/uncompleted"))
                .withHeader("X-Amzn-Oidc-Accesstoken", equalTo(testJWTToken()))
                .withHeader("X-Amzn-Oidc-Data", equalTo("data-token")),
        )
    }

    @Test
    @WithMockALBUser(
        groups = [FixedGroupIds.administrators],
        authorities = [ApiLabelConstant.API_VEHICLE_REGISTRATION_WRITE],
    )
    fun `vehicle registration orders can only be updated with all fields if user is not limited to specific fields`() {
        setupPermissionsForDifferentGroups()

        //  Mock response from vehicle-registration service
        stubFor(
            put("/api/vr/orders")
                .willReturn(
                    jsonResponse(
                        """
                        {
                          "data": [
                            {
                              "id": 1,
                              "vin": "WP0ZZZ975RL118042",
                              "vehicleId": "fa0e5e5a-0b4a-4125-81d0-488b0532d7c0",
                              "equiId": null,
                              "testNumber": null,
                              "equipmentNumber": null,
                              "orderType": 1,
                              "briefNumber": "HA055316",
                              "driveType": "OVC_HEV",
                              "leasingType": "DLF",
                              "modelType": "Panamera 4S E-Hybrid Sport Turismo",
                              "department": "DLF",
                              "registrationOffice": "",
                              "plannedLicencePlate": null,
                              "plannedRegistrationDate": null,
                              "commenter": "",
                              "orderStatus": "OPEN",
                              "storageLocation": null,
                              "tsn": null,
                              "hsn": null,
                              "registrationType": null,
                              "registrationDate": null,
                              "firstRegistrationDate": null,
                              "lastRegistrationDate": null,
                              "lastDeRegistrationDate": null,
                              "licencePlate": null,
                              "remark": null,
                              "sfme": null,
                              "testVehicle": null,
                              "registrationStatus": null,
                              "deleted": false,
                              "createdAt": "2024-04-30T07:38:45.953165Z",
                              "updatedAt": "2024-04-30T07:38:52.811217Z",
                              "version": 100
                            }
                          ],
                          "errors": [
                            {
                              "type": "error.registration.invalidField.orderType",
                              "detail": "orderType: Value must be between 1 and 4"
                            }
                          ]
                        }
                        """.trimIndent(),
                        200,
                    ),
                ),
        )

        // Perform test to verify expectations
        mockMvc
            .perform(
                MockMvcRequestBuilders
                    .put("/ui/vehicleregistration/orders")
                    .header("Authorization", "Bearer testAccessToken")
                    .header("X-Amzn-Oidc-Accesstoken", testJWTToken())
                    .header("X-Amzn-Oidc-Data", "data-token")
                    .header("X-Trace-Id", "my-test-id")
                    .header("Content-Type", "application/json")
                    .content(
                        """
                    {
                      "data": [
                        {
                          "id": 1,
                          "vin": "WP0ZZZ975RL118042",
                          "vehicleId": "fa0e5e5a-0b4a-4125-81d0-488b0532d7c0",
                          "equiId": null,
                          "testNumber": null,
                          "equipmentNumber": null,
                          "orderType": 1,
                          "briefNumber": "HA055316",
                          "driveType": null,
                          "leasingType": null,
                          "modelType": null,
                          "department": null,
                          "registrationOffice": null,
                          "plannedLicencePlate": null,
                          "plannedRegistrationDate": null,
                          "commenter": null,
                          "orderStatus": null,
                          "storageLocation": null,
                          "tsn": null,
                          "hsn": null,
                          "registrationType": null,
                          "registrationDate": null,
                          "firstRegistrationDate": null,
                          "lastRegistrationDate": null,
                          "lastDeRegistrationDate": null,
                          "licencePlate": null,
                          "remark": null,
                          "sfme": null,
                          "testVehicle": null,
                          "registrationStatus": null,
                          "deleted": false,
                          "createdAt": null,
                          "updatedAt": null,
                          "version": 100
                        }
                      ]
                    }
                """,
                    ),
            ).andExpectAll(
                MockMvcResultMatchers.status().is2xxSuccessful,
                MockMvcResultMatchers.content().json(
                    """
                    {
                      "data": [
                        {
                          "id": 1,
                          "vin": "WP0ZZZ975RL118042",
                          "vehicleId": "fa0e5e5a-0b4a-4125-81d0-488b0532d7c0",
                          "equiId": null,
                          "testNumber": null,
                          "equipmentNumber": null,
                          "orderType": 1,
                          "briefNumber": "HA055316",
                          "driveType": "OVC_HEV",
                          "leasingType": "DLF",
                          "modelType": "Panamera 4S E-Hybrid Sport Turismo",
                          "department": "DLF",
                          "registrationOffice": "",
                          "plannedLicencePlate": null,
                          "plannedRegistrationDate": null,
                          "commenter": "",
                          "orderStatus": "OPEN",
                          "storageLocation": null,
                          "tsn": null,
                          "hsn": null,
                          "registrationType": null,
                          "registrationDate": null,
                          "firstRegistrationDate": null,
                          "lastRegistrationDate": null,
                          "lastDeRegistrationDate": null,
                          "licencePlate": null,
                          "remark": null,
                          "sfme": null,
                          "testVehicle": null,
                          "registrationStatus": null,
                          "deleted": false,
                          "createdAt": "2024-04-30T07:38:45.953165Z",
                          "updatedAt": "2024-04-30T07:38:52.811217Z",
                          "version": 100
                        }
                      ],
                    "errors": [
                        {
                          "type": "error.registration.invalidField.orderType",
                          "detail": "orderType: Value must be between 1 and 4"
                        }
                      ]
                    }
                    """.trimIndent(),
                ),
            )

        verify(exactly = 2) {
            privilegeService.findAllByGroupIdWithFilters(
                listOf(UUID.fromString(FixedGroupIds.administrators)),
            ) // MockALBUser is setup this way
        }

        verify(
            exactly(1),
            putRequestedFor(urlEqualTo("/api/vr/orders"))
                .withHeader("X-Amzn-Oidc-Accesstoken", equalTo(testJWTToken()))
                .withHeader("X-Amzn-Oidc-Data", equalTo("data-token"))
                .withoutHeader("X-Trace-Id") // X-Trace-Id is not propagated to the vehicle-registration service
                .withHeader("Content-Type", equalTo("application/json;charset=UTF-8"))
                .withRequestBody(
                    equalToJson(
                        // value =
                        """
                        {
                          "data": [
                            {
                              "id": 1,
                              "vin": "WP0ZZZ975RL118042",
                              "vehicleId": "fa0e5e5a-0b4a-4125-81d0-488b0532d7c0",
                              "equiId": null,
                              "testNumber": null,
                              "equipmentNumber": null,
                              "orderType": 1,
                              "briefNumber": "HA055316",
                              "driveType": null,
                              "leasingType": null,
                              "modelType": null,
                              "department": null,
                              "registrationOffice": null,
                              "plannedLicencePlate": null,
                              "plannedRegistrationDate": null,
                              "commenter": null,
                              "orderStatus": null,
                              "storageLocation": null,
                              "tsn": null,
                              "hsn": null,
                              "registrationType": null,
                              "registrationArea": null,
                              "registrationDate": null,
                              "firstRegistrationDate": null,
                              "lastRegistrationDate": null,
                              "lastDeRegistrationDate": null,
                              "licencePlate": null,
                              "remark": null,
                              "sfme": null,
                              "testVehicle": null,
                              "registrationStatus": null,
                              "deleted": false,
                              "createdAt": null,
                              "updatedAt": null,
                              "version": 100
                            }
                          ]
                        }
                        """.trimIndent(),
                    ),
                ),
        )

        verify { vehicleChanges.addVehicleChange(match { it.vehicleId == UUID.fromString("fa0e5e5a-0b4a-4125-81d0-488b0532d7c0") }) }
    }

    @WithMockALBUser(
        groups = [FixedGroupIds.administrators],
        authorities = [ApiLabelConstant.API_VEHICLE_REGISTRATION_READ],
    )
    @Test
    fun `a 4xx error with a problem+json api response is passed through`(wmRuntimeInfo: WireMockRuntimeInfo) {
        //  Given a response from the vehicle-registration
        //  service containing orders.
        stubFor(
            get("/api/vr/orders/uncompleted").willReturn(
                aResponse()
                    .withStatus(409)
                    .withBody(
                        """{
              "type": "remote.error.type",
              "status": 409,
              "title": "Remote System Error",
              "detail": "Remote System Error Detail"
            }""",
                    ).withHeader("Content-Type", "application/problem+json"),
            ),
        )

        // Perform test to verify expectations
        mockMvc
            .perform(
                MockMvcRequestBuilders
                    .get("/ui/vehicleregistration/orders")
                    .header("Authorization", "Bearer testAccessToken")
                    .header("X-Amzn-Oidc-Accesstoken", testJWTToken())
                    .header("X-Amzn-Oidc-Data", "data-token"),
            ).andExpectAll(
                MockMvcResultMatchers.status().`is`(409),
                MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_PROBLEM_JSON),
                MockMvcResultMatchers.jsonPath("$.type").value("remote.error.type"),
                MockMvcResultMatchers.jsonPath("$.status").value(409),
                MockMvcResultMatchers.jsonPath("$.title").value("Remote System Error"),
                MockMvcResultMatchers
                    .jsonPath("$.detail")
                    .value("Remote System Error Detail"),
            )

        verify(
            exactly(1),
            getRequestedFor(urlEqualTo("/api/vr/orders/uncompleted"))
                .withHeader("X-Amzn-Oidc-Accesstoken", equalTo(testJWTToken()))
                .withHeader("X-Amzn-Oidc-Data", equalTo("data-token")),
        )
    }

    @WithMockALBUser(
        groups = [FixedGroupIds.administrators],
        authorities = [ApiLabelConstant.API_VEHICLE_REGISTRATION_READ],
    )
    @Test
    fun `a 4xx error from the vehicle-registration service creates an understandable problem+json api response`() {
        //  Given a response from the vehicle-registration
        //  service containing orders.
        stubFor(get("/api/vr/orders/uncompleted").willReturn(notFound()))

        // Perform test to verify expectations
        mockMvc
            .perform(
                MockMvcRequestBuilders
                    .get("/ui/vehicleregistration/orders")
                    .header("Authorization", "Bearer testAccessToken")
                    .header("X-Amzn-Oidc-Accesstoken", testJWTToken())
                    .header("X-Amzn-Oidc-Data", "data-token"),
            ).andExpectAll(
                MockMvcResultMatchers.status().`is`(502),
                MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_PROBLEM_JSON),
                MockMvcResultMatchers.jsonPath("$.type").value("error.system.remoteSystemProblem"),
                MockMvcResultMatchers.jsonPath("$.status").value(502),
                MockMvcResultMatchers.jsonPath("$.title").value("Unexpected response from remote system"),
                MockMvcResultMatchers
                    .jsonPath("$.detail")
                    .value("The current operation could not be completed. Please retry again after sometime"),
            )

        verify(
            exactly(1),
            getRequestedFor(urlEqualTo("/api/vr/orders/uncompleted"))
                .withHeader("X-Amzn-Oidc-Accesstoken", equalTo(testJWTToken()))
                .withHeader("X-Amzn-Oidc-Data", equalTo("data-token")),
        )
    }

    @Test
    @WithMockALBUser(
        groups = [FixedGroupIds.administrators],
        authorities = [ApiLabelConstant.API_VEHICLE_REGISTRATION_READ],
    )
    fun `a network error from the vehicle-registration service forces a retry of the call and then fails`() {
        //  Given a response from the vehicle-registration
        //  service containing orders.
        stubFor(get("/api/vr/orders/uncompleted").willReturn(aResponse().withFault(Fault.CONNECTION_RESET_BY_PEER)))

        // Perform test to verify expectations
        mockMvc
            .perform(
                MockMvcRequestBuilders
                    .get("/ui/vehicleregistration/orders")
                    .header("Authorization", "Bearer testAccessToken")
                    .header("X-Amzn-Oidc-Accesstoken", testJWTToken())
                    .header("X-Amzn-Oidc-Data", "data-token"),
            ).andExpectAll(
                MockMvcResultMatchers.status().`is`(502),
                MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_PROBLEM_JSON),
                MockMvcResultMatchers.jsonPath("$.type").value("error.system.remoteSystemProblem"),
                MockMvcResultMatchers.jsonPath("$.status").value(502),
                MockMvcResultMatchers.jsonPath("$.title").value("Unexpected response from remote system"),
                MockMvcResultMatchers
                    .jsonPath("$.detail")
                    .value("The current operation could not be completed. Please retry again after sometime"),
            )

        verify(
            // TODO: Identify why the calls are not correct on pipeline
            // exactly(2), // due to the retry (default feign client behavior)
            getRequestedFor(urlEqualTo("/api/vr/orders/uncompleted"))
                .withHeader("X-Amzn-Oidc-Accesstoken", equalTo(testJWTToken()))
                .withHeader("X-Amzn-Oidc-Data", equalTo("data-token")),
        )
    }

    @Test
    @WithMockALBUser(groups = [], authorities = []) // no permission for user to endpoint
    fun `a user without access to the vehicle-registration resource cannot access the bff endpoint`(wmRuntimeInfo: WireMockRuntimeInfo) {
        mockMvc
            .perform(
                MockMvcRequestBuilders
                    .get("/ui/vehicleregistration/orders")
                    .header("Authorization", "Bearer testAccessToken")
                    .header("X-Amzn-Oidc-Accesstoken", testJWTToken())
                    .header("X-Amzn-Oidc-Data", "data-token"),
            ).andExpectAll(
                MockMvcResultMatchers.status().`is`(403),
                MockMvcResultMatchers.jsonPath("$.errors[0].type").value("error.fvm.accessDenied"), // default
                MockMvcResultMatchers.jsonPath("$.errors[0].status").value(403),
                MockMvcResultMatchers.jsonPath("$.errors[0].title").value("Forbidden"),
                MockMvcResultMatchers.jsonPath("$.errors[0].detail").value("Access Denied"),
            )
    }

    @Test
    @WithMockALBUser(
        groups = [FixedGroupIds.administrators],
        authorities = [ApiLabelConstant.API_VEHICLE_REGISTRATION_WRITE],
    )
    fun `vehicle orders can be created via passthrough request when user has right permissions`(wmRuntimeInfo: WireMockRuntimeInfo) {
        setupPermissionsForDifferentGroups()

        stubFor(
            post("/api/vr/brieflist")
                .withRequestBody(
                    equalToJson(
                        // value =
                        """[{"vin": "WP0ZZZ979PL122551", "briefNumber":  "ABC123"}]""",
                        // ignoreArrayOrder =
                        false,
                        // ignoreExtraElements =
                        true,
                    ),
                ).willReturn(
                    jsonResponse(
                        """
                        {
                          "data": [
                            {
                              "id": 2,
                              "vin": "WP0ZZZ979PL122551",
                              "equiId": null,
                              "testNumber": null,
                              "equipmentNumber": null,
                              "vehicleId": "aaaaaaaa-bbbb-cccc-dddd-000000000000",
                              "briefNumber": null,
                              "driveType": "ICE",
                              "leasingType": "test",
                              "modelType": "Taycan",
                              "department": "Z01",
                              "registrationOffice": null,
                              "plannedLicencePlate": null,
                              "plannedRegistrationDate": null,
                              "commenter": null,
                              "orderType": 1,
                              "orderStatus": "CREATED",
                              "storageLocation": null,
                              "tsn": null,
                              "hsn": null,
                              "registrationType": null,
                              "registrationDate": null,
                              "firstRegistrationDate": null,
                              "lastRegistrationDate": null,
                              "lastDeRegistrationDate": null,
                              "licencePlate": null,
                              "remark": null,
                              "sfme": null,
                              "testVehicle": null,
                              "registrationStatus": null,
                              "deleted": false,
                              "createdAt": "2024-02-05T00:00:00Z",
                              "updatedAt": "2024-02-05T00:00:00Z",
                              "version": 2
                            }
                          ],
                          "errors": null
                        }
                        """.trimIndent(),
                        201,
                    ),
                ),
        )

        // Perform test to verify expectations
        mockMvc
            .perform(
                MockMvcRequestBuilders
                    .post("/ui/vehicleregistration/brieflist")
                    .content("""[{"vin": "WP0ZZZ979PL122551", "briefNumber":  "ABC123"}]""")
                    .header("Authorization", "Bearer testAccessToken")
                    .header("X-Amzn-Oidc-Accesstoken", testJWTToken())
                    .header("X-Amzn-Oidc-Data", "data-token")
                    .header("Content-Type", MediaType.APPLICATION_JSON),
            ).andExpectAll(
                MockMvcResultMatchers.status().is2xxSuccessful,
                MockMvcResultMatchers.content().json(
                    """
                    {
                      "data": [
                        {
                          "id": 2,
                          "vin": "WP0ZZZ979PL122551",
                          "equiId": null,
                          "testNumber": null,
                          "equipmentNumber": null,
                          "vehicleId": "aaaaaaaa-bbbb-cccc-dddd-000000000000",
                          "briefNumber": null,
                          "driveType": "ICE",
                          "leasingType": "test",
                          "modelType": "Taycan",
                          "department": "Z01",
                          "registrationOffice": null,
                          "plannedLicencePlate": null,
                          "plannedRegistrationDate": null,
                          "commenter": null,
                          "orderType": 1,
                          "orderStatus": "CREATED",
                          "storageLocation": null,
                          "tsn": null,
                          "hsn": null,
                          "registrationType": null,
                          "registrationDate": null,
                          "firstRegistrationDate": null,
                          "lastRegistrationDate": null,
                          "lastDeRegistrationDate": null,
                          "licencePlate": null,
                          "remark": null,
                          "sfme": null,
                          "testVehicle": null,
                          "registrationStatus": null,
                          "deleted": false,
                          "createdAt": "2024-02-05T00:00:00Z",
                          "updatedAt": "2024-02-05T00:00:00Z",
                          "version": 2
                        }
                      ],
                      "errors": null
                    }
                    """.trimIndent(),
                ),
            )

        verify(exactly = 1) {
            privilegeService.findAllByGroupIdWithFilters(
                listOf(UUID.fromString(FixedGroupIds.administrators)),
            )
        }
        verify(
            exactly(1),
            postRequestedFor(urlEqualTo("/api/vr/brieflist"))
                .withHeader("X-Amzn-Oidc-Accesstoken", equalTo(testJWTToken()))
                .withHeader("X-Amzn-Oidc-Data", equalTo("data-token"))
                .withRequestBody(
                    equalToJson(
                        // value =
                        """[{"vin": "WP0ZZZ979PL122551", "briefNumber":  "ABC123"}]""",
                        // ignoreArrayOrder =
                        false,
                        // ignoreExtraElements =
                        true,
                    ),
                ),
        )

        verify { vehicleChanges.addVehicleChange(match { it.vehicleId == UUID.fromString("aaaaaaaa-bbbb-cccc-dddd-000000000000") }) }
    }

    @Test
    @WithMockALBUser(
        groups = [FixedGroupIds.administrators],
        authorities = [ApiLabelConstant.API_VEHICLE_REGISTRATION_WRITE],
    )
    fun `vehicle orders can be created by uploading file via passthrough request when user has right permissions`() {
        setupPermissionsForDifferentGroups()

        stubFor(
            post("/api/vr/brieflist")
                .withMultipartRequestBody(
                    aMultipart()
                        .withBody(equalTo(String(validExcelFile().bytes))),
                ).willReturn(
                    jsonResponse(
                        """
                        {
                          "data": [
                            {
                              "id": 1,
                              "vin": "WP0ZZZ979PL122551",
                              "equiId": null,
                              "testNumber": null,
                              "equipmentNumber": null,
                              "vehicleId": "aaaaaaaa-bbbb-cccc-dddd-000000000001",
                              "briefNumber": "testBriefNr",
                              "driveType": "",
                              "leasingType": "",
                              "modelType": "",
                              "department": "",
                              "registrationOffice": "",
                              "plannedLicencePlate": "",
                              "plannedRegistrationDate": null,
                              "commenter": "",
                              "orderType": 1,
                              "orderStatus": "CREATED",
                              "storageLocation": null,
                              "tsn": null,
                              "hsn": null,
                              "registrationType": null,
                              "registrationDate": null,
                              "firstRegistrationDate": null,
                              "lastRegistrationDate": null,
                              "lastDeRegistrationDate": null,
                              "licencePlate": null,
                              "remark": null,
                              "sfme": null,
                              "testVehicle": null,
                              "registrationStatus": null,
                              "deleted": false,
                              "createdAt": "2024-04-30T07:38:45.953165Z",
                              "updatedAt": "2024-04-30T07:38:45.953165Z",
                              "version": 0
                            },
                            {
                              "id": 2,
                              "vin": "WP0ZZZ979PL122552",
                              "equiId": null,
                              "testNumber": null,
                              "equipmentNumber": null,
                              "vehicleId": "aaaaaaaa-bbbb-cccc-dddd-000000000002",
                              "briefNumber": "1234",
                              "driveType": "",
                              "leasingType": "",
                              "modelType": "",
                              "department": "",
                              "registrationOffice": "",
                              "plannedLicencePlate": "",
                              "plannedRegistrationDate": null,
                              "commenter": "",
                              "orderType": 1,
                              "orderStatus": "CREATED",
                              "storageLocation": null,
                              "tsn": null,
                              "hsn": null,
                              "registrationType": null,
                              "registrationDate": null,
                              "firstRegistrationDate": null,
                              "lastRegistrationDate": null,
                              "lastDeRegistrationDate": null,
                              "licencePlate": null,
                              "remark": null,
                              "sfme": null,
                              "testVehicle": null,
                              "registrationStatus": null,
                              "deleted": false,
                              "createdAt": "2024-04-30T07:38:45.953165Z",
                              "updatedAt": "2024-04-30T07:38:45.953165Z",
                              "version": 0
                            }
                          ],
                          "errors": null
                        }
                        """.trimIndent(),
                        201,
                    ),
                ),
        )

        // Perform test to verify expectations
        mockMvc
            .perform(
                MockMvcRequestBuilders
                    .multipart("/ui/vehicleregistration/brieflist")
                    .file(validExcelFile())
                    .contentType(MediaType.MULTIPART_FORM_DATA)
                    .header("Authorization", "Bearer testAccessToken")
                    .header("X-Amzn-Oidc-Accesstoken", testJWTToken())
                    .header("X-Amzn-Oidc-Data", "data-token"),
            ).andExpectAll(
                MockMvcResultMatchers.status().is2xxSuccessful,
                MockMvcResultMatchers.content().json(
                    """
                    {
                      "data": [
                        {
                          "id": 1,
                          "vin": "WP0ZZZ979PL122551",
                          "equiId": null,
                          "testNumber": null,
                          "equipmentNumber": null,
                          "vehicleId": "aaaaaaaa-bbbb-cccc-dddd-000000000001",
                          "briefNumber": "testBriefNr",
                          "driveType": "",
                          "leasingType": "",
                          "modelType": "",
                          "department": "",
                          "registrationOffice": "",
                          "plannedLicencePlate": "",
                          "plannedRegistrationDate": null,
                          "commenter": "",
                          "orderType": 1,
                          "orderStatus": "CREATED",
                          "storageLocation": null,
                          "tsn": null,
                          "hsn": null,
                          "registrationType": null,
                          "registrationDate": null,
                          "firstRegistrationDate": null,
                          "lastRegistrationDate": null,
                          "lastDeRegistrationDate": null,
                          "licencePlate": null,
                          "remark": null,
                          "sfme": null,
                          "testVehicle": null,
                          "registrationStatus": null,
                          "deleted": false,
                          "createdAt": "2024-04-30T07:38:45.953165Z",
                          "updatedAt": "2024-04-30T07:38:45.953165Z",
                          "version": 0
                        },
                        {
                          "id": 2,
                          "vin": "WP0ZZZ979PL122552",
                          "equiId": null,
                          "testNumber": null,
                          "equipmentNumber": null,
                          "vehicleId": "aaaaaaaa-bbbb-cccc-dddd-000000000002",
                          "briefNumber": "1234",
                          "driveType": "",
                          "leasingType": "",
                          "modelType": "",
                          "department": "",
                          "registrationOffice": "",
                          "plannedLicencePlate": "",
                          "plannedRegistrationDate": null,
                          "commenter": "",
                          "orderType": 1,
                          "orderStatus": "CREATED",
                          "storageLocation": null,
                          "tsn": null,
                          "hsn": null,
                          "registrationType": null,
                          "registrationDate": null,
                          "firstRegistrationDate": null,
                          "lastRegistrationDate": null,
                          "lastDeRegistrationDate": null,
                          "licencePlate": null,
                          "remark": null,
                          "sfme": null,
                          "testVehicle": null,
                          "registrationStatus": null,
                          "deleted": false,
                          "createdAt": "2024-04-30T07:38:45.953165Z",
                          "updatedAt": "2024-04-30T07:38:45.953165Z",
                          "version": 0
                        }
                      ],
                      "errors": null
                    }
                    """.trimIndent(),
                ),
            )

        verify(exactly = 1) {
            privilegeService.findAllByGroupIdWithFilters(
                listOf(UUID.fromString(FixedGroupIds.administrators)),
            )
        }
        verify(
            exactly(1),
            postRequestedFor(urlEqualTo("/api/vr/brieflist"))
                .withHeader("X-Amzn-Oidc-Accesstoken", equalTo(testJWTToken()))
                .withHeader("X-Amzn-Oidc-Data", equalTo("data-token"))
                .withRequestBodyPart(
                    aMultipart()
                        .withBody(equalTo(String(validExcelFile().bytes)))
                        .build(),
                ),
        )

        verify(exactly = 1) {
            vehicleChanges.addVehicleChange(
                match {
                    it.vehicleId ==
                        UUID.fromString("aaaaaaaa-bbbb-cccc-dddd-000000000001")
                },
            )
        }
        verify(exactly = 1) {
            vehicleChanges.addVehicleChange(
                match {
                    it.vehicleId ==
                        UUID.fromString("aaaaaaaa-bbbb-cccc-dddd-000000000002")
                },
            )
        }
    }

    @Test
    @WithMockALBUser(
        groups = [FixedGroupIds.administrators],
        authorities = [ApiLabelConstant.API_VEHICLE_REGISTRATION_WRITE],
    )
    fun `vehicle registration orders can be created via passthrough request when user has right permissions`() {
        setupPermissionsForDifferentGroups()

        stubFor(
            post("/api/vr/orders")
                .withRequestBody(
                    equalToJson(
                        // value =
                        """[{"vin": "WP0ZZZ979PL122551", "licencePlate": "BB-PS 1111", "registrationType": 1}]""",
                        // ignoreArrayOrder =
                        false,
                        // ignoreExtraElements =
                        true,
                    ),
                ).willReturn(
                    jsonResponse(
                        """
                        {
                          "data": [
                            {
                              "id": 2,
                              "vin": "WP0ZZZ979PL122551",
                              "equiId": null,
                              "testNumber": null,
                              "equipmentNumber": null,
                              "vehicleId": "aaaaaaaa-bbbb-cccc-dddd-000000000003",
                              "briefNumber": null,
                              "driveType": "ICE",
                              "leasingType": "test",
                              "modelType": "Taycan",
                              "department": "Z01",
                              "registrationOffice": null,
                              "plannedLicencePlate": null,
                              "plannedRegistrationDate": null,
                              "commenter": null,
                              "orderType": 1,
                              "orderStatus": "COMPLETED",
                              "storageLocation": null,
                              "tsn": null,
                              "hsn": null,
                              "registrationType": 1,
                              "registrationDate": null,
                              "firstRegistrationDate": null,
                              "lastRegistrationDate": null,
                              "lastDeRegistrationDate": null,
                              "licencePlate": "BB-PS 1111",
                              "remark": null,
                              "sfme": null,
                              "testVehicle": null,
                              "registrationStatus": null,
                              "deleted": false,
                              "createdAt": "2024-04-30T07:38:45.953165Z",
                              "updatedAt": "2024-04-30T07:38:45.953165Z",
                              "version": 2
                            }
                          ],
                          "errors": null
                        }
                        """.trimIndent(),
                        201,
                    ),
                ),
        )

        // Perform test to verify expectations
        mockMvc
            .perform(
                MockMvcRequestBuilders
                    .post("/ui/vehicleregistration/orders")
                    .content("""[{"vin": "WP0ZZZ979PL122551", "licencePlate": "BB-PS 1111", "registrationType": 1}]""")
                    .header("Authorization", "Bearer testAccessToken")
                    .header("X-Amzn-Oidc-Accesstoken", testJWTToken())
                    .header("X-Amzn-Oidc-Data", "data-token")
                    .header("Content-Type", MediaType.APPLICATION_JSON),
            ).andExpectAll(
                MockMvcResultMatchers.status().is2xxSuccessful,
                MockMvcResultMatchers.content().json(
                    """
                    {
                      "data": [
                        {
                          "id": 2,
                          "vin": "WP0ZZZ979PL122551",
                          "equiId": null,
                          "testNumber": null,
                          "equipmentNumber": null,
                          "vehicleId": "aaaaaaaa-bbbb-cccc-dddd-000000000003",
                          "briefNumber": null,
                          "driveType": "ICE",
                          "leasingType": "test",
                          "modelType": "Taycan",
                          "department": "Z01",
                          "registrationOffice": null,
                          "plannedLicencePlate": null,
                          "plannedRegistrationDate": null,
                          "commenter": null,
                          "orderType": 1,
                          "orderStatus": "COMPLETED",
                          "storageLocation": null,
                          "tsn": null,
                          "hsn": null,
                          "registrationType": 1,
                          "registrationDate": null,
                          "firstRegistrationDate": null,
                          "lastRegistrationDate": null,
                          "lastDeRegistrationDate": null,
                          "licencePlate": "BB-PS 1111",
                          "remark": null,
                          "sfme": null,
                          "testVehicle": null,
                          "registrationStatus": null,
                          "deleted": false,
                          "createdAt": "2024-04-30T07:38:45.953165Z",
                          "updatedAt": "2024-04-30T07:38:45.953165Z",
                          "version": 2
                        }
                      ],
                      "errors": null
                    }
                    """.trimIndent(),
                ),
            )

        verify(exactly = 1) {
            privilegeService.findAllByGroupIdWithFilters(
                listOf(UUID.fromString(FixedGroupIds.administrators)),
            )
        }
        verify(
            exactly(1),
            postRequestedFor(urlEqualTo("/api/vr/orders"))
                .withHeader("X-Amzn-Oidc-Accesstoken", equalTo(testJWTToken()))
                .withHeader("X-Amzn-Oidc-Data", equalTo("data-token"))
                .withRequestBody(
                    equalToJson(
                        // value =
                        """[{"vin": "WP0ZZZ979PL122551", "licencePlate": "BB-PS 1111", "registrationType": 1}]""",
                        // ignoreArrayOrder =
                        false,
                        // ignoreExtraElements =
                        true,
                    ),
                ),
        )

        verify(exactly = 1) {
            vehicleChanges.addVehicleChange(
                match {
                    it.vehicleId ==
                        UUID.fromString("aaaaaaaa-bbbb-cccc-dddd-000000000003")
                },
            )
        }
    }

    @Test
    @WithMockALBUser(
        groups = [FixedGroupIds.administrators],
        authorities = [ApiLabelConstant.API_VEHICLE_REGISTRATION_WRITE],
    )
    fun `vehicle registration orders can be created by uploading a csv via passthrough request when user has right permissions`() {
        setupPermissionsForDifferentGroups()

        stubFor(
            post("/api/vr/orders")
                .withMultipartRequestBody(
                    aMultipart()
                        .withBody(equalTo(String(validCsvFile().bytes))),
                ).willReturn(
                    jsonResponse(
                        """
                        {
                          "data": [
                            {
                              "id": 1,
                              "vin": "WP1ZZZ9Y0RDA64165",
                              "equiId": null,
                              "testNumber": null,
                              "equipmentNumber": null,
                              "vehicleId": "aaaaaaaa-bbbb-cccc-dddd-000000000004",
                              "briefNumber": "HA060585",
                              "driveType": "",
                              "leasingType": "",
                              "modelType": "",
                              "department": "",
                              "registrationOffice": "",
                              "plannedLicencePlate": "",
                              "plannedRegistrationDate": null,
                              "commenter": "",
                              "orderType": 1,
                              "orderStatus": "COMPLETED",
                              "storageLocation": "ZF01",
                              "tsn": "ANU00171",
                              "hsn": "583",
                              "registrationType": 1,
                              "registrationDate": "2024-02-15T00:00:00Z",
                              "firstRegistrationDate": null,
                              "lastRegistrationDate": null,
                              "lastDeRegistrationDate": null,
                              "licencePlate": "BB-PS 946",
                              "remark": "E",
                              "sfme": false,
                              "testVehicle": false,
                              "registrationStatus": "REGISTERED",
                              "deleted": false,
                              "createdAt": "2024-02-05T00:00:00Z",
                              "updatedAt": "2024-02-05T00:00:00Z",
                              "version": 0
                            },
                            {
                              "id": 2,
                              "vin": "WP1ZZZ9Y0RDA64263",
                              "equiId": null,
                              "testNumber": null,
                              "equipmentNumber": null,
                              "vehicleId": "aaaaaaaa-bbbb-cccc-dddd-000000000005",
                              "briefNumber": "HA060587",
                              "driveType": "",
                              "leasingType": "",
                              "modelType": "",
                              "department": "",
                              "registrationOffice": "",
                              "plannedLicencePlate": "",
                              "plannedRegistrationDate": null,
                              "commenter": "",
                              "orderType": 1,
                              "orderStatus": "COMPLETED",
                              "storageLocation": "ZF01",
                              "tsn": "ANU00183",
                              "hsn": "583",
                              "registrationType": 1,
                              "registrationDate": "2024-02-15T00:00:00Z",
                              "firstRegistrationDate": null,
                              "lastRegistrationDate": null,
                              "lastDeRegistrationDate": null,
                              "licencePlate": "BB-PS 948",
                              "remark": "E",
                              "sfme": true,
                              "testVehicle": false,
                              "registrationStatus": "REGISTERED",
                              "deleted": false,
                              "createdAt": "2024-02-05T00:00:00Z",
                              "updatedAt": "2024-02-05T00:00:00Z",
                              "version": 0
                            }
                          ],
                          "errors": null
                        }
                        """.trimIndent(),
                        201,
                    ),
                ),
        )

        // Perform test to verify expectations
        mockMvc
            .perform(
                MockMvcRequestBuilders
                    .multipart("/ui/vehicleregistration/orders")
                    .file(validCsvFile())
                    .contentType(MediaType.MULTIPART_FORM_DATA)
                    .header("Authorization", "Bearer testAccessToken")
                    .header("X-Amzn-Oidc-Accesstoken", testJWTToken())
                    .header("X-Amzn-Oidc-Data", "data-token"),
            ).andExpectAll(
                MockMvcResultMatchers.status().is2xxSuccessful,
                MockMvcResultMatchers.content().json(
                    """
                    {
                      "data": [
                        {
                          "id": 1,
                          "vin": "WP1ZZZ9Y0RDA64165",
                          "equiId": null,
                          "testNumber": null,
                          "equipmentNumber": null,
                          "vehicleId": "aaaaaaaa-bbbb-cccc-dddd-000000000004",
                          "briefNumber": "HA060585",
                          "driveType": "",
                          "leasingType": "",
                          "modelType": "",
                          "department": "",
                          "registrationOffice": "",
                          "plannedLicencePlate": "",
                          "plannedRegistrationDate": null,
                          "commenter": "",
                          "orderType": 1,
                          "orderStatus": "COMPLETED",
                          "storageLocation": "ZF01",
                          "tsn": "ANU00171",
                          "hsn": "583",
                          "registrationType": 1,
                          "registrationDate": "2024-02-15T00:00:00Z",
                          "firstRegistrationDate": null,
                          "lastRegistrationDate": null,
                          "lastDeRegistrationDate": null,
                          "licencePlate": "BB-PS 946",
                          "remark": "E",
                          "sfme": false,
                          "testVehicle": false,
                          "registrationStatus": "REGISTERED",
                          "deleted": false,
                          "createdAt": "2024-02-05T00:00:00Z",
                          "updatedAt": "2024-02-05T00:00:00Z",
                          "version": 0
                        },
                        {
                          "id": 2,
                          "vin": "WP1ZZZ9Y0RDA64263",
                          "equiId": null,
                          "testNumber": null,
                          "equipmentNumber": null,
                          "vehicleId": "aaaaaaaa-bbbb-cccc-dddd-000000000005",
                          "briefNumber": "HA060587",
                          "driveType": "",
                          "leasingType": "",
                          "modelType": "",
                          "department": "",
                          "registrationOffice": "",
                          "plannedLicencePlate": "",
                          "plannedRegistrationDate": null,
                          "commenter": "",
                          "orderType": 1,
                          "orderStatus": "COMPLETED",
                          "storageLocation": "ZF01",
                          "tsn": "ANU00183",
                          "hsn": "583",
                          "registrationType": 1,
                          "registrationDate": "2024-02-15T00:00:00Z",
                          "firstRegistrationDate": null,
                          "lastRegistrationDate": null,
                          "lastDeRegistrationDate": null,
                          "licencePlate": "BB-PS 948",
                          "remark": "E",
                          "sfme": true,
                          "testVehicle": false,
                          "registrationStatus": "REGISTERED",
                          "deleted": false,
                          "createdAt": "2024-02-05T00:00:00Z",
                          "updatedAt": "2024-02-05T00:00:00Z",
                          "version": 0
                        }
                      ],
                      "errors": null
                    }
                    """.trimIndent(),
                ),
            )

        verify(exactly = 1) {
            privilegeService.findAllByGroupIdWithFilters(
                listOf(UUID.fromString(FixedGroupIds.administrators)),
            )
        }
        verify(
            exactly(1),
            postRequestedFor(urlEqualTo("/api/vr/orders"))
                .withHeader("X-Amzn-Oidc-Accesstoken", equalTo(testJWTToken()))
                .withHeader("X-Amzn-Oidc-Data", equalTo("data-token"))
                .withRequestBodyPart(
                    aMultipart()
                        .withBody(equalTo(String(validCsvFile().bytes)))
                        .build(),
                ),
        )

        verify(exactly = 1) {
            vehicleChanges.addVehicleChange(
                match {
                    it.vehicleId ==
                        UUID.fromString("aaaaaaaa-bbbb-cccc-dddd-000000000004")
                },
            )
        }
        verify(exactly = 1) {
            vehicleChanges.addVehicleChange(
                match {
                    it.vehicleId ==
                        UUID.fromString("aaaaaaaa-bbbb-cccc-dddd-000000000005")
                },
            )
        }
    }

    @Test
    @WithMockALBUser(
        groups = [FixedGroupIds.administrators],
        authorities = [ApiLabelConstant.API_VEHICLE_REGISTRATION_WRITE],
    )
    fun `can successfully renew test numbers (KBA) for registration orders via passthrough when user has right permissions`() {
        setupPermissionsForDifferentGroups()

        stubFor(
            post("/api/vr/orders/renew-test-numbers")
                .willReturn(
                    jsonResponse(
                        """
                        {
                          "data": [
                            {
                              "id": 1,
                              "vin": "WP0ZZZ979PL122551",
                              "equiId": "EQUI ID 123",
                              "testNumber": 1,
                              "equipmentNumber": 111222,
                              "vehicleId": "aaaaaaaa-bbbb-cccc-dddd-000000000006",
                              "briefNumber": "briefNr",
                              "driveType": "ICE",
                              "leasingType": "AF",
                              "modelType": "Taycan",
                              "department": "D01",
                              "registrationOffice": "BB",
                              "plannedLicencePlate": "GO ????",
                              "plannedRegistrationDate": "2024-02-15T00:00:00Z",
                              "commenter": "User",
                              "orderType": 1,
                              "orderStatus": "COMPLETED",
                              "storageLocation": "ZF01",
                              "tsn": "ANU00183",
                              "hsn": "0183",
                              "registrationType": 1,
                              "registrationDate": "2024-02-15T00:00:00Z",
                              "firstRegistrationDate": null,
                              "lastRegistrationDate": null,
                              "lastDeRegistrationDate": null,
                              "licencePlate": "BB-GO ????",
                              "remark": "E",
                              "sfme": false,
                              "testVehicle": true,
                              "registrationStatus": "REGISTERED",
                              "deleted": false,
                              "createdAt": "2024-02-05T00:00:00Z",
                              "updatedAt": "2024-02-05T00:00:00Z",
                              "version": 0
                            }
                          ],
                          "errors": null
                        }
                        """.trimIndent(),
                        200,
                    ),
                ),
        )

        // Perform test to verify expectations
        mockMvc
            .perform(
                MockMvcRequestBuilders
                    .post("/ui/vehicleregistration/orders/renew-test-numbers")
                    .header("Authorization", "Bearer testAccessToken")
                    .header("X-Amzn-Oidc-Accesstoken", testJWTToken())
                    .header("X-Amzn-Oidc-Data", "data-token"),
            ).andExpectAll(
                MockMvcResultMatchers.status().is2xxSuccessful,
                MockMvcResultMatchers.content().json(
                    """
                    {
                      "data": [
                        {
                          "id": 1,
                          "vin": "WP0ZZZ979PL122551",
                          "equiId": "EQUI ID 123",
                          "testNumber": 1,
                          "equipmentNumber": 111222,
                          "vehicleId": "aaaaaaaa-bbbb-cccc-dddd-000000000006",
                          "briefNumber": "briefNr",
                          "driveType": "ICE",
                          "leasingType": "AF",
                          "modelType": "Taycan",
                          "department": "D01",
                          "registrationOffice": "BB",
                          "plannedLicencePlate": "GO ????",
                          "plannedRegistrationDate": "2024-02-15T00:00:00Z",
                          "commenter": "User",
                          "orderType": 1,
                          "orderStatus": "COMPLETED",
                          "storageLocation": "ZF01",
                          "tsn": "ANU00183",
                          "hsn": "0183",
                          "registrationType": 1,
                          "registrationDate": "2024-02-15T00:00:00Z",
                          "firstRegistrationDate": null,
                          "lastRegistrationDate": null,
                          "lastDeRegistrationDate": null,
                          "licencePlate": "BB-GO ????",
                          "remark": "E",
                          "sfme": false,
                          "testVehicle": true,
                          "registrationStatus": "REGISTERED",
                          "deleted": false,
                          "version": 0
                        }
                      ],
                      "errors": null
                    }
                    """.trimIndent(),
                ),
            )

        verify(exactly = 1) {
            privilegeService.findAllByGroupIdWithFilters(
                listOf(UUID.fromString(FixedGroupIds.administrators)),
            )
        }
        verify(
            exactly(1),
            postRequestedFor(urlEqualTo("/api/vr/orders/renew-test-numbers"))
                .withHeader("X-Amzn-Oidc-Accesstoken", equalTo(testJWTToken()))
                .withHeader("X-Amzn-Oidc-Data", equalTo("data-token")),
        )

        verify(exactly = 1) {
            vehicleChanges.addVehicleChange(
                match {
                    it.vehicleId ==
                        UUID.fromString("aaaaaaaa-bbbb-cccc-dddd-000000000006")
                },
            )
        }
    }

    @Test
    @WithMockALBUser(
        groups = [FixedGroupIds.administrators],
        authorities = [ApiLabelConstant.API_VEHICLE_REGISTRATION_DELETE],
    )
    fun `vehicle registration order can be deleted via passthrough request when user has right permissions`() {
        val orderId = 1L
        setupPermissionsForDifferentGroups()

        stubFor(
            delete("/api/vr/orders/$orderId")
                .willReturn(
                    jsonResponse(
                        """
                        {
                          "data": {
                              "id": $orderId,
                              "vin": "WP0ZZZ979PL122551",
                              "equiId": null,
                              "testNumber": null,
                              "equipmentNumber": null,
                              "vehicleId": "aaaaaaaa-bbbb-cccc-dddd-000000000003",
                              "briefNumber": null,
                              "driveType": "ICE",
                              "leasingType": "test",
                              "modelType": "Taycan",
                              "department": "Z01",
                              "registrationOffice": null,
                              "plannedLicencePlate": null,
                              "plannedRegistrationDate": null,
                              "commenter": null,
                              "orderType": 1,
                              "orderStatus": "COMPLETED",
                              "storageLocation": null,
                              "tsn": null,
                              "hsn": null,
                              "registrationType": null,
                              "registrationDate": null,
                              "firstRegistrationDate": null,
                              "lastRegistrationDate": null,
                              "lastDeRegistrationDate": null,
                              "licencePlate": null,
                              "remark": null,
                              "sfme": null,
                              "testVehicle": null,
                              "registrationStatus": null,
                              "deleted": true,
                              "createdAt": "2024-04-30T07:38:45.953165Z",
                              "updatedAt": "2024-04-30T07:38:45.953165Z",
                              "version": 2
                            },
                          "errors": null
                        }
                        """.trimIndent(),
                        200,
                    ),
                ),
        )

        // Perform test to verify expectations
        mockMvc
            .perform(
                MockMvcRequestBuilders
                    .delete("/ui/vehicleregistration/orders/$orderId")
                    .header("Authorization", "Bearer testAccessToken")
                    .header("X-Amzn-Oidc-Accesstoken", testJWTToken())
                    .header("X-Amzn-Oidc-Data", "data-token")
                    .header("Content-Type", MediaType.APPLICATION_JSON),
            ).andExpectAll(
                MockMvcResultMatchers.status().is2xxSuccessful,
                MockMvcResultMatchers.content().json(
                    """
                    {
                      "data": {
                          "id": $orderId,
                          "vin": "WP0ZZZ979PL122551",
                          "equiId": null,
                          "testNumber": null,
                          "equipmentNumber": null,
                          "vehicleId": "aaaaaaaa-bbbb-cccc-dddd-000000000003",
                          "briefNumber": null,
                          "driveType": "ICE",
                          "leasingType": "test",
                          "modelType": "Taycan",
                          "department": "Z01",
                          "registrationOffice": null,
                          "plannedLicencePlate": null,
                          "plannedRegistrationDate": null,
                          "commenter": null,
                          "orderType": 1,
                          "orderStatus": "COMPLETED",
                          "storageLocation": null,
                          "tsn": null,
                          "hsn": null,
                          "registrationType": null,
                          "registrationDate": null,
                          "firstRegistrationDate": null,
                          "lastRegistrationDate": null,
                          "lastDeRegistrationDate": null,
                          "licencePlate": null,
                          "remark": null,
                          "sfme": null,
                          "testVehicle": null,
                          "registrationStatus": null,
                          "deleted": true,
                          "createdAt": "2024-04-30T07:38:45.953165Z",
                          "updatedAt": "2024-04-30T07:38:45.953165Z",
                          "version": 2
                        },
                      "errors": null
                    }
                    """.trimIndent(),
                ),
            )

        verify(exactly = 1) {
            privilegeService.findAllByGroupIdWithFilters(
                listOf(UUID.fromString(FixedGroupIds.administrators)),
            )
        }
        verify(
            exactly(1),
            deleteRequestedFor(urlEqualTo("/api/vr/orders/$orderId"))
                .withHeader("X-Amzn-Oidc-Accesstoken", equalTo(testJWTToken()))
                .withHeader("X-Amzn-Oidc-Data", equalTo("data-token")),
        )

        verify(exactly = 1) {
            vehicleChanges.addVehicleChange(
                match {
                    it.vehicleId ==
                        UUID.fromString("aaaaaaaa-bbbb-cccc-dddd-000000000003")
                },
            )
        }
    }

    @Test
    @WithMockALBUser(
        groups = [FixedGroupIds.administrators],
        authorities = [ApiLabelConstant.API_VEHICLE_REGISTRATION_READ],
    )
    fun `should be able to search vehicle registration orders`(wmRuntimeInfo: WireMockRuntimeInfo) {
        setupPermissionsForDifferentGroups()

        val uiSearchRequest =
            """
            {
              "startRow": 0,
              "endRow": 5,
              "rowGroupCols": [],
              "valueCols": [],
              "pivotCols": [],
              "pivotMode": false,
              "groupKeys": [],
              "filterModel": {
                "licencePlate": {
                  "filterType": "text",
                  "type": "contains",
                  "filter": "BB-PS 1111"
                }
              },
              "sortModel": [],
              "columns": []
            }
            """.trimIndent()

        val mappedSearchRequest =
            """
            {
              "startRow": 0,
              "endRow": 5,
              "filterModel": {
                "licencePlate": {
                  "filterType": "text",
                  "type": "contains",
                  "filter": "BB-PS 1111"
                }
              },
              "sortModel": [],
              "columns": []
            }
            """.trimIndent()

        stubFor(
            post("/api/vr/orders/search")
                .withRequestBody(
                    equalToJson(
                        // value =
                        mappedSearchRequest,
                        // ignoreArrayOrder =
                        false,
                        // ignoreExtraElements =
                        true,
                    ),
                ).willReturn(
                    jsonResponse(
                        """
                        {
                          "data": [
                            {
                              "id": 2,
                              "vin": "WP0ZZZ979PL122551",
                              "equiId": null,
                              "testNumber": null,
                              "equipmentNumber": null,
                              "vehicleId": "aaaaaaaa-bbbb-cccc-dddd-000000000003",
                              "briefNumber": null,
                              "driveType": "ICE",
                              "leasingType": "test",
                              "modelType": "Taycan",
                              "department": "Z01",
                              "registrationOffice": null,
                              "plannedLicencePlate": null,
                              "plannedRegistrationDate": null,
                              "commenter": null,
                              "orderType": 1,
                              "orderStatus": "COMPLETED",
                              "storageLocation": null,
                              "tsn": null,
                              "hsn": null,
                              "registrationType": 1,
                              "registrationDate": null,
                              "firstRegistrationDate": null,
                              "lastRegistrationDate": null,
                              "lastDeRegistrationDate": null,
                              "licencePlate": "BB-PS 1111",
                              "remark": null,
                              "sfme": null,
                              "testVehicle": null,
                              "registrationStatus": null,
                              "deleted": false,
                              "createdAt": "2024-04-30T07:38:45.953165Z",
                              "updatedAt": "2024-04-30T07:38:45.953165Z",
                              "version": 2
                            }
                          ],
                          "errors": null
                        }
                        """.trimIndent(),
                        200,
                    ),
                ),
        )

        // Perform test to verify expectations
        mockMvc
            .perform(
                MockMvcRequestBuilders
                    .post("/ui/vehicleregistration/orders/search")
                    .header("Content-Type", "application/json")
                    .content(uiSearchRequest)
                    .header("Authorization", "Bearer testAccessToken")
                    .header("X-Amzn-Oidc-Accesstoken", testJWTToken())
                    .header("X-Amzn-Oidc-Data", "data-token"),
            ).andExpectAll(
                MockMvcResultMatchers.status().is2xxSuccessful,
                MockMvcResultMatchers.content().json(
                    """
                    {
                      "data": [
                        {
                          "id": 2,
                          "vin": "WP0ZZZ979PL122551",
                          "equiId": null,
                          "testNumber": null,
                          "equipmentNumber": null,
                          "vehicleId": "aaaaaaaa-bbbb-cccc-dddd-000000000003",
                          "briefNumber": null,
                          "driveType": "ICE",
                          "leasingType": "test",
                          "modelType": "Taycan",
                          "department": "Z01",
                          "registrationOffice": null,
                          "plannedLicencePlate": null,
                          "plannedRegistrationDate": null,
                          "commenter": null,
                          "orderType": 1,
                          "orderStatus": "COMPLETED",
                          "storageLocation": null,
                          "tsn": null,
                          "hsn": null,
                          "registrationType": 1,
                          "registrationDate": null,
                          "firstRegistrationDate": null,
                          "lastRegistrationDate": null,
                          "lastDeRegistrationDate": null,
                          "licencePlate": "BB-PS 1111",
                          "remark": null,
                          "sfme": null,
                          "testVehicle": null,
                          "registrationStatus": null,
                          "deleted": false,
                          "createdAt": "2024-04-30T07:38:45.953165Z",
                          "updatedAt": "2024-04-30T07:38:45.953165Z",
                          "version": 2
                        }
                      ],
                      "errors": null
                    }
                    """.trimIndent(),
                ),
            )

        verify(exactly = 1) {
            privilegeService.findAllByGroupIdWithFilters(
                listOf(UUID.fromString(FixedGroupIds.administrators)),
            )
        }
        verify(
            exactly(1),
            postRequestedFor(urlEqualTo("/api/vr/orders/search"))
                .withHeader("X-Amzn-Oidc-Accesstoken", equalTo(testJWTToken()))
                .withHeader("X-Amzn-Oidc-Data", equalTo("data-token"))
                .withRequestBody(
                    equalToJson(
                        // value =
                        mappedSearchRequest,
                        // ignoreArrayOrder =
                        false,
                        // ignoreExtraElements =
                        true,
                    ),
                ),
        )
    }

    private fun setupPermissionsForDifferentGroups() {
        val adminGroup = listOf(UUID.fromString(FixedGroupIds.administrators))
        every {
            privilegeService.findAllByGroupIdWithFilters(adminGroup)
        } returns
            listOf(
                Privilege(1, Resource.VEHICLE_REGISTRATION.label, READ, PrivilegeFilter(fields = null)),
                Privilege(2, Resource.VEHICLE_REGISTRATION.label, WRITE, PrivilegeFilter(fields = null)),
            )

        val payrollGroup = listOf(UUID.fromString(FixedGroupIds.payroll))
        every {
            privilegeService.findAllByGroupIdWithFilters(payrollGroup)
        } returns
            listOf(
                Privilege(
                    1,
                    Resource.VEHICLE_REGISTRATION.label,
                    READ,
                    PrivilegeFilter(fields = listOf(FieldLabel.VEHICLE_REGISTRATION_BRIEF_NUMBER.label)),
                ),
                Privilege(
                    2,
                    Resource.VEHICLE_REGISTRATION.label,
                    WRITE,
                    PrivilegeFilter(fields = listOf(FieldLabel.VEHICLE_REGISTRATION_COMMENTER.label)),
                ),
            )
    }

    private fun validExcelFile(): MockMultipartFile =
        MockMultipartFile(
            "file",
            "validBrieflist.xlsx",
            "application/vnd.ms-excel",
            ClassPathResource("vehicleregistration/testdata/validBrieflist.xlsx").inputStream,
        )

    private fun validCsvFile(): MockMultipartFile =
        MockMultipartFile(
            "file",
            "validRegistrations.csv",
            "application/vnd.ms-excel",
            ClassPathResource("vehicleregistration/testdata/validRegistrations.csv").inputStream,
        )
}
