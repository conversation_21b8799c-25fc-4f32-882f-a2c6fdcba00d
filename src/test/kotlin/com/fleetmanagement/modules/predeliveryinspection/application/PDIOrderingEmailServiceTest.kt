/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

@file:Suppress("ktlint:standard:no-wildcard-imports")

package com.fleetmanagement.modules.predeliveryinspection.application

import com.aspose.email.MailAddress
import com.fleetmanagement.TestcontainersConfiguration
import com.fleetmanagement.emhshared.addWorkingDays
import com.fleetmanagement.integrations.mailclient.application.port.EmailDto
import com.fleetmanagement.integrations.mailclient.application.port.EmailException
import com.fleetmanagement.integrations.mailclient.application.port.EmailOutPort
import com.fleetmanagement.modules.predeliveryinspection.PreDeliveryInspectionBuilder
import com.fleetmanagement.modules.predeliveryinspection.application.port.UpdatePreDeliveryInspectionUseCase
import com.fleetmanagement.modules.predeliveryinspection.domain.PreDeliveryInspection
import com.fleetmanagement.modules.predeliveryinspection.domain.PreDeliveryInspectionId
import com.fleetmanagement.modules.predeliveryinspection.domain.PreDeliveryInspectionRepository
import com.fleetmanagement.modules.predeliveryinspection.domain.service.PreDeliveryInspectionUpdateService
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByVehicleId
import com.fleetmanagement.modules.vehicledata.api.domain.TireSet
import com.fleetmanagement.modules.vehicledata.api.domain.VehicleSource
import com.fleetmanagement.modules.vehicledata.api.dtos.ModelDTO
import com.fleetmanagement.modules.vehicledata.api.dtos.OrderDTO
import com.fleetmanagement.modules.vehicledata.api.dtos.TechnicalInfoDTO
import com.fleetmanagement.modules.vehicledata.api.dtos.VehicleDTO
import com.fleetmanagement.modules.vehicledata.features.updatevehicle.UpdateVehicleStatusService
import com.fleetmanagement.modules.vehiclelocation.api.LastKnownLocation
import com.fleetmanagement.modules.vehiclelocation.features.updatelastknownlocation.objectmothers.createVehicleLocation
import com.fleetmanagement.modules.vehicleregistration.api.ReadRegistrationOrder
import com.fleetmanagement.modules.vehicleregistration.api.dto.LatestRegistrationOrder
import com.fleetmanagement.modules.vehicleregistration.api.dto.VehicleRegistrationAPIResponse
import com.ninjasquad.springmockk.MockkBean
import com.ninjasquad.springmockk.SpykBean
import com.opencsv.CSVParserBuilder
import com.opencsv.CSVReaderBuilder
import io.mockk.Runs
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.slot
import io.mockk.verify
import org.awaitility.Durations
import org.awaitility.kotlin.atMost
import org.awaitility.kotlin.await
import org.awaitility.kotlin.untilAsserted
import org.awaitility.kotlin.withPollDelay
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertDoesNotThrow
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertIterableEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.annotation.Import
import org.springframework.transaction.annotation.Transactional
import java.io.StringReader
import java.time.OffsetDateTime
import java.util.Optional
import java.util.Random
import java.util.UUID

@SpringBootTest
@Transactional
@Import(TestcontainersConfiguration::class)
class PDIOrderingEmailServiceTest(
    @Value("\${pre-delivery-inspection.pdi-lead-time}") val pdiLeadTime: Long,
) {
    @Autowired
    private lateinit var pdiOrderingEmailService: PDIOrderingEmailService

    @SpykBean
    private lateinit var preDeliveryInspectionFinder: PreDeliveryInspectionFinder

    @SpykBean
    private lateinit var preDeliveryInspectionUpdateService: PreDeliveryInspectionUpdateService

    @Autowired
    private lateinit var preDeliveryInspectionRepository: PreDeliveryInspectionRepository

    @MockkBean
    private lateinit var readRegistrationOrder: ReadRegistrationOrder

    @MockkBean(relaxed = true)
    private lateinit var pdiEmailAsyncService: PDIEmailAsyncService

    @MockkBean
    private lateinit var readVehicleByVehicleId: ReadVehicleByVehicleId

    @MockkBean
    private lateinit var lastKnownLocation: LastKnownLocation

    @MockkBean
    private lateinit var emailOutPort: EmailOutPort

    @MockkBean
    private lateinit var updateVehicleStatusService: UpdateVehicleStatusService

    @BeforeEach
    fun before() {
        mockkStatic(OffsetDateTime::addWorkingDays)
        every { updateVehicleStatusService.handlePreDeliveryInspectionUpdatedEvent(any()) } just Runs
    }

    @AfterEach
    fun afterEach() {
        clearAllMocks()
    }

    @Test
    fun `should create and send emails for pdi ordering`() {
        val vehicleIds = (1..5).map { UUID.randomUUID() }
        val preDeliveryInspections =
            vehicleIds.map { PreDeliveryInspectionBuilder().vehicleId(it).build() }.map {
                preDeliveryInspectionRepository.save(it)
            }
        val (listOfPreDeliveryInspection, anotherListOfPreDeliveryInspection) = preDeliveryInspections.chunked(3)
        val plannedPDIDate = OffsetDateTime.parse("2025-02-24T14:00:00Z")
        val plannedPDIDateWithDifferentTime = OffsetDateTime.parse("2025-02-24T16:00:00Z")
        val plannedPDIDateLessthanExpected = OffsetDateTime.parse("2025-02-23T14:00:00Z")
        listOfPreDeliveryInspection.forEach {
            it.update(
                tireSet = null,
                isRelevant = null,
                foiling = Optional.of(true),
                refuel = Optional.of(false),
                charge = Optional.of(true),
                digitalLogbook = Optional.of(false),
                licencePlateMounting = Optional.of(true),
                orderedDate = Optional.empty(),
                completedDate = null,
                plannedDate =
                    if (Random().nextBoolean()) {
                        Optional.of(plannedPDIDate)
                    } else {
                        Optional.of(
                            plannedPDIDateWithDifferentTime,
                        )
                    },
                comment = Optional.of("pdiComment"),
            )
        }

        anotherListOfPreDeliveryInspection.forEach {
            it.update(
                tireSet = null,
                isRelevant = null,
                foiling = Optional.of(false),
                refuel = Optional.of(false),
                charge = Optional.of(true),
                digitalLogbook = Optional.of(true),
                licencePlateMounting = Optional.of(true),
                orderedDate = Optional.empty(),
                completedDate = null,
                plannedDate = Optional.of(plannedPDIDateLessthanExpected),
                comment = Optional.of("anotherPdiComment"),
            )
        }

        every { any<OffsetDateTime>().addWorkingDays(pdiLeadTime) } returns plannedPDIDate

        val vehicleIdsSlot = mutableListOf<List<UUID>>()
        every { readRegistrationOrder.getLatestOrdersBy(capture(vehicleIdsSlot)) } returns
            VehicleRegistrationAPIResponse(
                preDeliveryInspections
                    .map { it.vehicleId }
                    .map { latestRegistrationOrder(it) },
            )

        val vehicleData = vehicleDTO()
        val registrationVehicleIdSlot = mutableListOf<UUID>()
        every { readVehicleByVehicleId.readVehicleById(capture(registrationVehicleIdSlot)) } returns vehicleData

        val vehicleLocation = createVehicleLocation()
        val locationVehicleIdSlot = mutableListOf<UUID>()
        every { lastKnownLocation.findLastKnownVehicleLocationBy(capture(locationVehicleIdSlot)) } returns vehicleLocation

        val emailDtosSlot = mutableListOf<EmailDto>()
        every { emailOutPort.sendEmail(capture(emailDtosSlot)) } just Runs

        pdiOrderingEmailService.createAndSendPDIOrderingEmail()

        val thresholdSlot = slot<OffsetDateTime>()
        verify(exactly = 1) {
            preDeliveryInspectionFinder.findPreDeliveryInspectionForPDIOrderingEmail(capture(thresholdSlot))
        }

        /**
         * Email should only be sent for listOfPreDeliveryInspection
         */
        verify(exactly = 1) {
            readRegistrationOrder.getLatestOrdersBy(any())
            emailOutPort.sendEmail(any())
        }

        val pdiSlot = mutableListOf<PreDeliveryInspection>()
        val dateTime = mutableListOf<OffsetDateTime>()

        /**
         * Email should only be sent for listOfPreDeliveryInspection, which contains 3 vehicles
         */
        verify(exactly = 3) {
            readVehicleByVehicleId.readVehicleById(any())
            lastKnownLocation.findLastKnownVehicleLocationBy(any())
            preDeliveryInspectionUpdateService.updateOrderedDate(capture(pdiSlot), capture(dateTime))
        }

        /**
         * only one csv is rendered
         */
        assertEquals(plannedPDIDate, thresholdSlot.captured)
        assertEquals(1, vehicleIdsSlot.size)
        assertTrue(
            vehicleIdsSlot.any { pdi ->
                pdi == listOfPreDeliveryInspection.map { it.vehicleId }
            },
        )
        assertEquals(listOfPreDeliveryInspection.map { it.vehicleId }.toSet(), registrationVehicleIdSlot.toSet())
        assertEquals(listOfPreDeliveryInspection.map { it.vehicleId }.toSet(), locationVehicleIdSlot.toSet())
        assertEquals(1, emailDtosSlot.size)
        assertTrue(
            emailDtosSlot.any {
                it.subject == "Fahrzeugabruf für die Auslieferung"
            },
        )
        assertTrue(
            emailDtosSlot.any {
                it.attachment[0].name == "Übersicht Abruf.csv"
            },
        )

        assertTrue(
            emailDtosSlot.all {
                it.senderMailAddress == MailAddress("<EMAIL>") &&
                    it.recipientsMailAddressInTo ==
                    listOf(
                        MailAddress("<EMAIL>"),
                        MailAddress("<EMAIL>"),
                    ) &&
                    it.recipientsMailAddressInCC ==
                    listOf(
                        MailAddress("<EMAIL>"),
                        MailAddress("<EMAIL>"),
                    ) &&
                    it.htmlBody.contains("<html>") &&
                    it.htmlBody.contains("<body") &&
                    it.htmlBody.contains("PDI Dienstleister")
            },
        )

        val expectedCSVDataRowList =
            listOfPreDeliveryInspection.map {
                CSVRow(
                    vin = "1HGCM82633A123456",
                    model = "Macan",
                    licencePlate = "SW 2035",
                    compoundName = "test-compound",
                    eventType = "DIRECT",
                    foiling = "Ja",
                    refuel = "Nein",
                    charge = "Ja",
                    digitalLogbook = "Nein",
                    licencePlateMounting = "Ja",
                    comment = "pdiComment",
                )
            }

        val attachmentList =
            emailDtosSlot.map {
                CSVReaderBuilder(
                    StringReader(
                        it.attachment[0]
                            .contentStream
                            ?.bufferedReader()
                            ?.readText()!!,
                    ),
                ).withCSVParser(CSVParserBuilder().withSeparator(';').build()).build()
            }

        val listOfAttachments = attachmentList.map { attachment -> attachment.readAll() }
        val listOfCsvHeaders = listOfAttachments.map { it.firstOrNull() }

        val actualListOfCSVDateRowList =
            listOfAttachments.map { allRows ->
                val dataRows = allRows.drop(1)
                dataRows.map { row ->
                    CSVRow(
                        vin = row[0],
                        model = row[1],
                        licencePlate = row[2],
                        compoundName = row[3],
                        eventType = row[4],
                        foiling = row[5],
                        refuel = row[6],
                        charge = row[7],
                        digitalLogbook = row[8],
                        licencePlateMounting = row[9],
                        comment = row[10],
                    )
                }
            }

        assertTrue(
            listOfCsvHeaders.all {
                it.contentEquals(
                    arrayOf(
                        "FIN",
                        "Modell",
                        "KFZ-Kennzeichen",
                        "Compound",
                        "Location Event",
                        "Folierung",
                        "Tanken",
                        "Laden",
                        "dFB",
                        "Kennzeichenmontage",
                        "PDI Kommentar",
                    ),
                )
            },
        )
        // check csv data rows.
        assertTrue(
            actualListOfCSVDateRowList.any {
                it.size == expectedCSVDataRowList.size
            },
        )
        assertEquals(listOfPreDeliveryInspection.map { it.id }, pdiSlot.map { it.id })
        dateTime.forEach {
            assertNotNull(it)
        }
        listOfPreDeliveryInspection.forEach {
            val preDeliveryFromFinder = preDeliveryInspectionFinder.getPreDeliveryInspection(it.id)
            assertNotNull(preDeliveryFromFinder.orderedDate)
        }
    }

    @Test
    fun `should not send PDI Ordering Email when it is already send for that day`() {
        val vehicleIds = (1..5).map { UUID.randomUUID() }
        val preDeliveryInspections =
            vehicleIds.map { PreDeliveryInspectionBuilder().vehicleId(it).build() }.map {
                preDeliveryInspectionRepository.save(it)
            }
        val (listOfPreDeliveryInspection, anotherListOfPreDeliveryInspection) = preDeliveryInspections.chunked(3)
        val plannedPDIDate = OffsetDateTime.parse("2025-02-24T14:00:00Z")
        listOfPreDeliveryInspection.forEach {
            it.update(
                tireSet = null,
                isRelevant = null,
                foiling = Optional.of(true),
                refuel = Optional.of(false),
                charge = Optional.of(true),
                digitalLogbook = Optional.of(false),
                licencePlateMounting = Optional.of(true),
                orderedDate = Optional.empty(),
                completedDate = null,
                plannedDate = Optional.of(plannedPDIDate),
                comment = Optional.of("pdiComment"),
            )
        }

        anotherListOfPreDeliveryInspection.forEach {
            it.update(
                tireSet = null,
                isRelevant = null,
                foiling = Optional.of(true),
                refuel = Optional.of(false),
                charge = Optional.of(true),
                digitalLogbook = Optional.of(false),
                licencePlateMounting = Optional.of(true),
                orderedDate = Optional.of(plannedPDIDate),
                completedDate = null,
                plannedDate = Optional.of(plannedPDIDate),
                comment = Optional.of("pdiComment"),
            )
        }

        every { any<OffsetDateTime>().addWorkingDays(pdiLeadTime) } returns plannedPDIDate

        pdiOrderingEmailService.createAndSendPDIOrderingEmail()

        verify(exactly = 0) {
            preDeliveryInspectionFinder.findPreDeliveryInspectionForPDIOrderingEmail(any())
            emailOutPort.sendEmail(any())
        }
    }

    @Test
    fun `should handle EmailException when there is an error sending email`() {
        val preDeliveryInspection =
            preDeliveryInspectionRepository.save(
                PreDeliveryInspectionBuilder().vehicleId(UUID.randomUUID()).build(),
            )
        val plannedPDIDate = OffsetDateTime.parse("2025-02-24T14:00:00Z")
        preDeliveryInspection.update(
            tireSet = null,
            isRelevant = null,
            foiling = Optional.of(true),
            refuel = Optional.of(false),
            charge = Optional.of(true),
            digitalLogbook = Optional.of(false),
            licencePlateMounting = Optional.of(true),
            orderedDate = Optional.empty(),
            completedDate = null,
            plannedDate = Optional.of(plannedPDIDate),
            comment = Optional.of("pdiComment"),
        )

        every { any<OffsetDateTime>().addWorkingDays(pdiLeadTime) } returns plannedPDIDate

        every { readRegistrationOrder.getLatestOrdersBy(any()) } returns
            VehicleRegistrationAPIResponse(data = listOf(latestRegistrationOrder(UUID.randomUUID())))

        every { readVehicleByVehicleId.readVehicleById(any()) } returns vehicleDTO()

        val vehicleLocation = createVehicleLocation()
        every { lastKnownLocation.findLastKnownVehicleLocationBy(any()) } returns vehicleLocation

        val message = "invalid password"

        every { emailOutPort.sendEmail(any()) } throws EmailException(message)

        assertDoesNotThrow { pdiOrderingEmailService.createAndSendPDIOrderingEmail() }
        verify(exactly = 1) {
            readRegistrationOrder.getLatestOrdersBy(any())
            lastKnownLocation.findLastKnownVehicleLocationBy(any())
            readVehicleByVehicleId.readVehicleById(any())
            emailOutPort.sendEmail(any())
        }
    }

    private fun latestRegistrationOrder(vehicleId: UUID) =
        LatestRegistrationOrder(
            vin = "1HGCM82633A123456",
            vehicleId = vehicleId,
            lastRegistrationDate = null,
            licencePlate = "SW 2035",
            registrationType = 1,
            sfme = Random().nextBoolean(),
        )

    private fun vehicleDTO() =
        VehicleDTO(
            id = UUID.randomUUID(),
            vguid = "13e4a669-2ba8-4a7e-be98-27173133b6f0",
            vin = "1HGCM82633A123456",
            order = OrderDTO(department = null, leasingType = null, tradingPartnerNumber = "1234567"),
            equipmentNumber = null,
            equiId = null,
            model =
                ModelDTO(
                    description = "Macan",
                    year = null,
                    productId = null,
                    productCode = null,
                    orderType = null,
                    vehicleType = null,
                    manufacturer = null,
                    range = null,
                    rangeDevelopment = null,
                    modelDescriptionDevelopment = null,
                ),
            options = null,
            consumption = null,
            production = null,
            country = null,
            pmp = null,
            embargo = null,
            color = null,
            source = VehicleSource.UNKNOWN,
            currentTires = TireSet.WR,
            referenceId = null,
            createdAt = null,
            price = null,
            technical = TechnicalInfoDTO(amountSeats = 5, engineCapacity = 1600f),
            fleet = null,
            delivery = null,
            returnInfo = null,
            tireSetChange = null,
            wltpInfo = null,
            evaluation = null,
            repairfixCarId = null,
            tuevAppointment = null,
            currentMileage = null,
            vtstamm = null,
        )

    @Test
    fun `should create and send emails for pdi ordering asynchronously  `() {
        val vehicleIds = (1..5).map { UUID.randomUUID() }
        val preDeliveryInspections =
            vehicleIds.map { PreDeliveryInspectionBuilder().vehicleId(it).build() }.map {
                preDeliveryInspectionRepository.save(it)
            }
        val (listOfPreDeliveryInspection, anotherListOfPreDeliveryInspection) = preDeliveryInspections.chunked(3)
        val plannedPDIDate = OffsetDateTime.parse("2025-02-24T14:00:00Z")
        val plannedPDIDateWithDifferentTime = OffsetDateTime.parse("2025-02-24T16:00:00Z")
        OffsetDateTime.parse("2025-02-23T14:00:00Z")
        listOfPreDeliveryInspection.forEach {
            it.update(
                tireSet = null,
                isRelevant = null,
                foiling = Optional.of(true),
                refuel = Optional.of(false),
                charge = Optional.of(true),
                digitalLogbook = Optional.of(false),
                licencePlateMounting = Optional.of(true),
                orderedDate = Optional.empty(),
                completedDate = null,
                plannedDate =
                    if (Random().nextBoolean()) {
                        Optional.of(plannedPDIDate)
                    } else {
                        Optional.of(
                            plannedPDIDateWithDifferentTime,
                        )
                    },
                comment = Optional.of("pdiComment"),
            )
        }
        anotherListOfPreDeliveryInspection.forEach {
            it.update(
                tireSet = null,
                isRelevant = null,
                foiling = Optional.of(false),
                refuel = Optional.of(false),
                charge = Optional.of(true),
                digitalLogbook = Optional.of(true),
                licencePlateMounting = Optional.of(true),
                orderedDate = Optional.empty(),
                completedDate = null,
                plannedDate = Optional.empty(),
                comment = Optional.of("anotherPdiComment"),
            )
        }
        every { any<OffsetDateTime>().addWorkingDays(pdiLeadTime) } returns plannedPDIDate
        every { readRegistrationOrder.getLatestOrdersBy(any()) } returns
            VehicleRegistrationAPIResponse(
                preDeliveryInspections
                    .map { it.vehicleId }
                    .map { latestRegistrationOrder(it) },
            )
        val vehicleData = vehicleDTO()
        every { readVehicleByVehicleId.readVehicleById(any()) } returns vehicleData
        val vehicleLocation = createVehicleLocation()
        every { lastKnownLocation.findLastKnownVehicleLocationBy(any()) } returns vehicleLocation
        every { emailOutPort.sendEmail(any()) } just Runs

        val validationErrors = pdiOrderingEmailService.createAndSendPdiEmailsAsync(vehicleIds = vehicleIds)
        assertTrue(
            validationErrors
                .map { it.identifier }
                .containsAll(anotherListOfPreDeliveryInspection.map { it.id.value.toString() }),
        )
        assertEquals(PdiValidationError.ValidationErrorType.PDI_MISSING_PLANNED_DATE, validationErrors[0].type)
        assertEquals(PdiValidationError.ValidationErrorType.PDI_MISSING_PLANNED_DATE, validationErrors[1].type)

        await atMost
            Durations.FIVE_SECONDS withPollDelay
            Durations.TWO_HUNDRED_MILLISECONDS untilAsserted
            {
                /**
                 * Email should only be sent for listOfPreDeliveryInspection
                 */
                verify(exactly = 1) {
                    readRegistrationOrder.getLatestOrdersBy(any())
                }
                val pdiAndEmailSlot = slot<PDIEmailAsyncService.PdiAndEmail>()
                verify { pdiEmailAsyncService.sendPdiEmailAndUpdateOrderedDate(capture(pdiAndEmailSlot)) }

                assertTrue(pdiAndEmailSlot.captured.pdiIds.containsAll(listOfPreDeliveryInspection.map { it.id }))
                /**
                 * Email should only be sent for listOfPreDeliveryInspection, which contains 3 vehicles
                 */
                verify(exactly = 3) {
                    lastKnownLocation.findLastKnownVehicleLocationBy(any())
                }
                verify(exactly = 5) {
                    // 3 + 2 for error
                    readVehicleByVehicleId.readVehicleById(any())
                }
            }
    }
}

class PDIEmailAsyncServiceTest {
    private val updatePreDeliveryInspectionUseCase = mockk<UpdatePreDeliveryInspectionUseCase>()
    private val emailOutPort = mockk<EmailOutPort>()

    private val pdiEmailAsyncService =
        PDIEmailAsyncService(
            updatePreDeliveryInspectionUseCase = updatePreDeliveryInspectionUseCase,
            emailOutPort = emailOutPort,
        )

    @BeforeEach
    fun setup() {
        every { emailOutPort.sendEmail(any()) } just Runs
        every { updatePreDeliveryInspectionUseCase.updatePreDeliveryInspectionOrderedDate(any(), any()) } just Runs
    }

    @Test
    fun `should call emailOutPort and update PDIs`() {
        val pdiAndEmail =
            PDIEmailAsyncService.PdiAndEmail(
                vehicleIds = emptyList(),
                email = mockk<EmailDto>(),
                pdiIds = (1..3).map { PreDeliveryInspectionId() },
            )

        pdiEmailAsyncService.sendPdiEmailAndUpdateOrderedDate(pdiAndEmail)

        verify(exactly = 1) { emailOutPort.sendEmail(any()) }
        val pdiUpdateSlot =
            mutableListOf<com.fleetmanagement.modules.predeliveryinspection.application.port.PreDeliveryInspectionId>()
        verify {
            updatePreDeliveryInspectionUseCase.updatePreDeliveryInspectionOrderedDate(
                id = capture(pdiUpdateSlot),
                orderedDate = any(),
            )
        }
        assertIterableEquals(pdiAndEmail.pdiIds.map { it.value }, pdiUpdateSlot.map { it.value })
    }
}
