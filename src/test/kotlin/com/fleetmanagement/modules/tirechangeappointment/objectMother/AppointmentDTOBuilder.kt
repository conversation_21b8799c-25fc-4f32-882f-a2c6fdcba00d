package com.fleetmanagement.modules.tirechangeappointment.objectMother

import com.fleetmanagement.modules.tirechangeappointment.application.AppointmentDto
import com.fleetmanagement.modules.tirechangeappointment.application.AppointmentType
import java.time.OffsetDateTime
import kotlin.random.Random

class AppointmentDTOBuilder {
    private var id: String = Random.nextInt(1000, 9999).toString()
    private var firstName: String = "John"
    private var lastName: String = "Doe"
    private var customerEmailAddress: String = "<EMAIL>"
    private var startDateTime: OffsetDateTime = OffsetDateTime.now()
    private var endDateTime: OffsetDateTime = OffsetDateTime.now()
    private var lastUpdatedDateTime: OffsetDateTime = OffsetDateTime.now()
    private var appointmentType: AppointmentType = AppointmentType.entries.random()
    private var licensePlate: String = "S-PL 1234"

    fun id(id: String) = apply { this.id = id }

    fun build(): AppointmentDto =
        AppointmentDto(
            appointmentId = id,
            startTime = startDateTime,
            endTime = endDateTime,
            customerFirstName = firstName,
            customerLastName = lastName,
            customerEmailAddress = customerEmailAddress,
            licensePlate = licensePlate,
            appointmentType = appointmentType,
            lastUpdatedDateTime = lastUpdatedDateTime,
        )

    companion object {
        fun buildSingle() = AppointmentDTOBuilder().build()

        fun buildMultiple(count: Int): Set<AppointmentDto> = (1..count).map { AppointmentDTOBuilder().build() }.toSet()
    }
}
