package com.fleetmanagement.modules.tirechangeappointment.objectMother

import com.fleetmanagement.modules.rela.application.port.RelaAppointmentRequest
import java.time.OffsetDateTime
import kotlin.random.Random

class RelaAppointmentRequestBuilder {
    private var appointment: OffsetDateTime = OffsetDateTime.now()
    private var vehicleLicensePlate: String = createRandomString(Random.Default.nextInt(4, 10))
    private var vehicleVin: String = createRandomString(Random.Default.nextInt(10, 17))
    private var customerLastName: String = createRandomString(Random.Default.nextInt(4, 15))
    private var customerFirstName: String = createRandomString(Random.Default.nextInt(4, 15))
    private var serviceBayNumber: Int = 1
    private var serviceTypeId: Int = 1
    private var vehicleTypeCode: String = "AB12"
    private var vehicleTypeDescription: String = "Porsche"
    private var pccbCode: String? = null
    private var wheelCode: String = "CB34"
    private var cwlCode: String? = null
    private var rasCode: String? = null
    private var pccbDescription: String? = null
    private var orderedByEmail: String = "<EMAIL>"
    private var orderDate: OffsetDateTime = OffsetDateTime.now()

    fun build(): RelaAppointmentRequest =
        RelaAppointmentRequest(
            appointment = appointment,
            vehicleLicensePlate = vehicleLicensePlate,
            vehicleVin = vehicleVin,
            customerLastName = customerLastName,
            customerFirstName = customerFirstName,
            serviceBayNumber = serviceBayNumber,
            serviceTypeId = serviceTypeId,
            vehicleTypeCode = vehicleTypeCode,
            vehicleTypeDescription = vehicleTypeDescription,
            pccbCode = pccbCode,
            wheelCode = wheelCode,
            cwlCode = cwlCode,
            rasCode = rasCode,
            pccbDescription = pccbDescription,
            orderedByEmail = orderedByEmail,
            orderDate = orderDate,
        )

    companion object {
        fun buildSingle() = RelaAppointmentRequestBuilder().build()
    }
}
