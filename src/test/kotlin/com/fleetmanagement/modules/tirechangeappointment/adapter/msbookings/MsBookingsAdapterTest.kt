/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.tirechangeappointment.adapter.msbookings

import com.fleetmanagement.modules.tirechangeappointment.application.AppointmentType
import com.fleetmanagement.modules.tirechangeappointment.application.AppointmentUpdateRequest
import com.fleetmanagement.modules.tirechangeappointment.application.port.CancelAppointmentRequestDTO
import com.fleetmanagement.modules.tirechangeappointment.objectMother.MsBookingsDTOBuilder
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.springframework.http.ResponseEntity

class MsBookingsAdapterTest {
    private val msBookingsWebClient: MsBookingsWebClient = mockk()
    private val bookingsId = "test-booking-id"
    val msBookingsAdapter =
        MsBookingsAdapter(
            bookingsId = bookingsId,
            msBookingsWebClient = msBookingsWebClient,
        )

    @Test
    fun `should return list of appointments`() {
        val msBookingsDto =
            MsBookingsDTOBuilder().serviceName("Winterreifenwechsel").build()

        val msBookingResponse = MsBookingsResponse(value = listOf(msBookingsDto))
        val readFrom = "2025-03-10T00:00:00"
        val readTo = "2025-03-10T23:59:59"

        val startTimeCaptor = slot<String>()
        val endTimeCaptor = slot<String>()

        every {
            msBookingsWebClient.getCalendarView(bookingsId, capture(startTimeCaptor), capture(endTimeCaptor))
        } returns msBookingResponse

        val result = msBookingsAdapter.readCalendar(readFrom, readTo)

        assertEquals(1, result.size)
        assertEquals(msBookingsDto.id, result[0].appointmentId)
        assertEquals(msBookingsDto.customerEmailAddress, result[0].customerEmailAddress)
        assertEquals(msBookingsDto.customerName, result[0].customerFirstName)
        assertEquals(msBookingsDto.customerName, result[0].customerLastName)
        assertEquals("", result[0].licensePlate)
        assertEquals(AppointmentType.WINTER_TIRE_CHANGE, result[0].appointmentType)
        assertEquals(readFrom, startTimeCaptor.captured)
        assertEquals(readTo, endTimeCaptor.captured)

        verify(exactly = 1) { msBookingsWebClient.getCalendarView(bookingsId, any(), any()) }
    }

    @Test
    fun `should ignore invalid service name`() {
        val invalidServiceNameMsBookingsDto =
            MsBookingsDTOBuilder().serviceName("invalid").build()
        val validServiceNameMsBookingsDto =
            MsBookingsDTOBuilder().serviceName("Winterreifenwechsel").build()

        val msBookingResponse =
            MsBookingsResponse(value = listOf(invalidServiceNameMsBookingsDto, validServiceNameMsBookingsDto))
        val readFrom = "2025-03-10T00:00:00"
        val readTo = "2025-03-10T23:59:59"

        val startTimeCaptor = slot<String>()
        val endTimeCaptor = slot<String>()

        every {
            msBookingsWebClient.getCalendarView(bookingsId, capture(startTimeCaptor), capture(endTimeCaptor))
        } returns msBookingResponse

        val result = msBookingsAdapter.readCalendar(readFrom, readTo)

        assertEquals(1, result.size)
        assertEquals(validServiceNameMsBookingsDto.id, result[0].appointmentId)
        assertEquals(validServiceNameMsBookingsDto.customerEmailAddress, result[0].customerEmailAddress)
        assertEquals(validServiceNameMsBookingsDto.customerName, result[0].customerFirstName)
        assertEquals(validServiceNameMsBookingsDto.customerName, result[0].customerLastName)
        assertEquals("", result[0].licensePlate)
        assertEquals(AppointmentType.WINTER_TIRE_CHANGE, result[0].appointmentType)
        assertEquals(readFrom, startTimeCaptor.captured)
        assertEquals(readTo, endTimeCaptor.captured)

        verify(exactly = 1) { msBookingsWebClient.getCalendarView(bookingsId, any(), any()) }
    }

    @Test
    fun `should call updateAppointment with correct parameters`() {
        val updateRequest =
            AppointmentUpdateRequest(
                "",
                "name",
                "<EMAIL>",
                emptyList(),
            )
        val appointmentId = "appointmentId"
        every { msBookingsWebClient.updateAppointment(any(), any(), any()) } returns ResponseEntity.noContent().build()

        msBookingsAdapter.updateAppointment(appointmentId, updateRequest)

        verify(exactly = 1) {
            msBookingsWebClient.updateAppointment(
                bookingsId = bookingsId,
                appointmentId = appointmentId,
                updateRequest = updateRequest,
            )
        }
    }

    @Test
    fun `should call cancel Appointment with correct parameters`() {
        val cancelRequest =
            CancelAppointmentRequestDTO(
                cancellationMessage = "Test message",
            )
        val appointmentId = "appointmentId"
        every { msBookingsWebClient.cancelAppointment(any(), any(), any()) } returns ResponseEntity.noContent().build()

        msBookingsAdapter.cancelAppointment(appointmentId, cancelRequest)

        // Assert
        verify(exactly = 1) {
            msBookingsWebClient.cancelAppointment(
                bookingsId = bookingsId,
                appointmentId = appointmentId,
                cancelAppointmentRequestDTO = cancelRequest,
            )
        }
    }
}
