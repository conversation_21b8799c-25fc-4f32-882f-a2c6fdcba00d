/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
@file:Suppress("PropertyName")

package com.fleetmanagement.modules.tirechangeappointment.architecture

import com.tngtech.archunit.core.domain.JavaModifier
import com.tngtech.archunit.core.importer.ImportOption
import com.tngtech.archunit.junit.AnalyzeClasses
import com.tngtech.archunit.junit.ArchTest
import com.tngtech.archunit.lang.ArchRule
import com.tngtech.archunit.lang.syntax.ArchRuleDefinition.classes
import com.tngtech.archunit.lang.syntax.ArchRuleDefinition.noClasses
import org.springframework.stereotype.Component
import org.springframework.stereotype.Service

@AnalyzeClasses(packages = [TIRE_CHANGE_APPOINTMENT_DEFAULT_PACKAGE], importOptions = [ImportOption.DoNotIncludeTests::class])
class ApplicationRulesTest {
    @ArchTest
    val `finder has no access to service`: ArchRule =
        noClasses()
            .that()
            .haveSimpleNameEndingWith(FINDER_SUFFIX)
            .should()
            .dependOnClassesThat()
            .haveSimpleNameEndingWith(SERVICE_SUFFIX)

    @ArchTest
    val `finder should have annotation @Component`: ArchRule =
        classes()
            .that()
            .haveSimpleNameEndingWith(FINDER_SUFFIX)
            .should()
            .beAnnotatedWith(Component::class.java)

    @ArchTest
    val `application has no access to adapter`: ArchRule =
        classes()
            .that()
            .resideInAPackage(APPLICATION_PACKAGE)
            .should()
            .onlyDependOnClassesThat()
            .resideOutsideOfPackage(ADAPTER_PACKAGE)

    @ArchTest
    val `application services should have annotation @Component`: ArchRule =
        classes()
            .that()
            .haveSimpleNameEndingWith(SERVICE_SUFFIX)
            .and()
            .resideInAPackage(APPLICATION_PACKAGE)
            .and()
            .doNotHaveModifier(JavaModifier.ABSTRACT)
            .should()
            .beAnnotatedWith(Component::class.java)
            .orShould()
            .notBeAnnotatedWith(Service::class.java)

    @ArchTest
    val `domain services should have annotation @Service`: ArchRule =
        classes()
            .that()
            .haveSimpleNameEndingWith(SERVICE_SUFFIX)
            .and()
            .resideInAPackage(DOMAIN_PACKAGE)
            .and()
            .doNotHaveModifier(JavaModifier.ABSTRACT)
            .should()
            .beAnnotatedWith(Service::class.java)
            .orShould()
            .notBeAnnotatedWith(Component::class.java)
}

// Suffixes
const val SERVICE_SUFFIX = "Service"
const val FINDER_SUFFIX = "Finder"
