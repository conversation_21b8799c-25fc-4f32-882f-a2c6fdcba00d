/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
@file:Suppress("PropertyName")

package com.fleetmanagement.modules.tirechangeappointment.architecture

import com.tngtech.archunit.core.importer.ImportOption
import com.tngtech.archunit.junit.AnalyzeClasses
import com.tngtech.archunit.junit.ArchTest
import com.tngtech.archunit.lang.ArchRule
import com.tngtech.archunit.library.Architectures.onionArchitecture

@AnalyzeClasses(packages = [TIRE_CHANGE_APPOINTMENT_DEFAULT_PACKAGE], importOptions = [ImportOption.DoNotIncludeTests::class])
class ArchitectureRulesTest {
    @ArchTest
    val `should respect onion architecture`: ArchRule =
        onionArchitecture()
            .domainModels(DOMAIN_PACKAGE)
            .domainServices("..domain.service..")
            .adapter("all", ADAPTER_PACKAGE)
            .applicationServices(APPLICATION_PACKAGE)
            .allowEmptyShould(true)
}

// Packages
const val TIRE_CHANGE_APPOINTMENT_DEFAULT_PACKAGE = "com.fleetmanagement.modules.tirechangeappointment"
const val ADAPTER_PACKAGE = "..adapter.."
const val APPLICATION_PACKAGE = "..application.."
const val DOMAIN_PACKAGE = "..domain.."
