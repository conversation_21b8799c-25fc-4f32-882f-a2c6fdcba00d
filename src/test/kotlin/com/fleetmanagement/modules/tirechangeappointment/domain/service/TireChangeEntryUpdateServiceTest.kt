/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.tirechangeappointment.domain.service

import com.fleetmanagement.TestcontainersConfiguration
import com.fleetmanagement.modules.tirechangeappointment.domain.TireChangeEntryRepository
import com.fleetmanagement.modules.tirechangeappointment.objectMother.TireChangeEntryBuilder
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.annotation.Import
import org.springframework.transaction.annotation.Transactional
import java.time.OffsetDateTime

@SpringBootTest
@Transactional
@Import(TestcontainersConfiguration::class)
class TireChangeEntryUpdateServiceTest {
    @Autowired
    private lateinit var tireChangeEntryRepository: TireChangeEntryRepository

    @Autowired
    private lateinit var tireChangeEntryUpdateService: TireChangeEntryUpdateService

    @Test
    fun `should update appointment date`() {
        val tireChangeEntry = TireChangeEntryBuilder.buildSingle()

        val saved = tireChangeEntryRepository.save(tireChangeEntry)
        val msBookingsAppointmentDate = OffsetDateTime.now().plusDays(1)

        tireChangeEntryUpdateService.updateAppointmentDate(saved, msBookingsAppointmentDate)

        val tireChangeEntryFromRepo = tireChangeEntryRepository.findById(tireChangeEntry.id)!!

        assertEquals(tireChangeEntry.id, tireChangeEntryFromRepo.id)
        assertEquals(msBookingsAppointmentDate, tireChangeEntryFromRepo.msBookingsAppointmentDate)
    }

    @Test
    fun `should update rela order number`() {
        val tireChangeEntry = TireChangeEntryBuilder.buildSingle()

        val saved = tireChangeEntryRepository.save(tireChangeEntry)
        val relaOrderNumber = "Rela123"

        tireChangeEntryUpdateService.updateRelaOrderNumber(saved, relaOrderNumber)

        val tireChangeEntryFromRepo = tireChangeEntryRepository.findById(tireChangeEntry.id)!!

        assertEquals(tireChangeEntry.id, tireChangeEntryFromRepo.id)
        assertEquals(relaOrderNumber, tireChangeEntryFromRepo.relaOrderNumber)
    }

    @Test
    fun `should update rela update flag`() {
        val tireChangeEntry = TireChangeEntryBuilder.buildSingle()

        val saved = tireChangeEntryRepository.save(tireChangeEntry)

        tireChangeEntryUpdateService.markAsSyncedWithRela(saved)

        val tireChangeEntryFromRepo = tireChangeEntryRepository.findById(tireChangeEntry.id)!!

        assertEquals(tireChangeEntry.id, tireChangeEntryFromRepo.id)
        assertEquals(false, tireChangeEntryFromRepo.shouldUpdateRela)
    }
}
