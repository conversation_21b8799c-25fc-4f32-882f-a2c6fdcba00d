/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.tirechangeappointment.domain.service

import com.fleetmanagement.TestcontainersConfiguration
import com.fleetmanagement.modules.tirechangeappointment.domain.TireChangeEntryRepository
import com.fleetmanagement.modules.tirechangeappointment.objectMother.TireChangeEntryBuilder
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertNull
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.annotation.Import
import org.springframework.transaction.annotation.Transactional

@SpringBootTest
@Transactional
@Import(TestcontainersConfiguration::class)
class TireChangeEntryCreateServiceTest {
    @Autowired
    private lateinit var tireChangeEntryCreateService: TireChangeEntryCreateService

    @Autowired
    private lateinit var tireChangeEntryRepository: TireChangeEntryRepository

    @Test
    fun `should create tire change entry`() {
        val tireChangeEntry = TireChangeEntryBuilder.buildSingle()

        tireChangeEntryCreateService.createTireChangeEntry(tireChangeEntry)

        val tireChangeEntryFromRepo = tireChangeEntryRepository.findById(tireChangeEntry.id)!!

        assertEquals(tireChangeEntry.id, tireChangeEntryFromRepo.id)
        assertEquals(tireChangeEntry.licensePlate, tireChangeEntryFromRepo.licensePlate)
        assertEquals(tireChangeEntry.msBookingsAppointmentId, tireChangeEntryFromRepo.msBookingsAppointmentId)
        assertEquals(tireChangeEntry.msBookingsAppointmentDate, tireChangeEntryFromRepo.msBookingsAppointmentDate)
        assertEquals(tireChangeEntry.firstName, tireChangeEntryFromRepo.firstName)
        assertEquals(tireChangeEntry.lastName, tireChangeEntryFromRepo.lastName)
        assertEquals(tireChangeEntry.vin, tireChangeEntryFromRepo.vin)
        assertTrue(tireChangeEntryFromRepo.shouldUpdateRela)
        assertNull(tireChangeEntryFromRepo.relaOrderNumber)
    }
}
