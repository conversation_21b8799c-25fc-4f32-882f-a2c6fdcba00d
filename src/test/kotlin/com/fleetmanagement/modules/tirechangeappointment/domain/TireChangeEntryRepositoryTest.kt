/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.tirechangeappointment.domain

import com.fleetmanagement.TestcontainersConfiguration
import com.fleetmanagement.modules.consigneedatasheet.ConsigneeDatasheetDBConfiguration
import com.fleetmanagement.modules.fvm.FVMDBConfiguration
import com.fleetmanagement.modules.noncustomeradequate.NonCustomerAdequateVehiclesDBConfiguration
import com.fleetmanagement.modules.predeliveryinspection.PreDeliveryDBConfiguration
import com.fleetmanagement.modules.tirechangeappointment.TireChangeAppointmentDBConfiguration
import com.fleetmanagement.modules.tirechangeappointment.objectMother.TireChangeEntryBuilder
import com.fleetmanagement.modules.vehiclecampaigns.VehicleCampaignsDBConfiguration
import com.fleetmanagement.modules.vehicledata.VehicleDataDBConfiguration
import com.fleetmanagement.modules.vehiclelocation.VehicleLocationDBConfiguration
import com.fleetmanagement.modules.vehiclesales.VehicleSalesDBConfiguration
import com.fleetmanagement.modules.vehicletransfer.VehicleTransferDBConfiguration
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.hasItem
import org.hamcrest.Matchers.`is`
import org.hamcrest.Matchers.not
import org.hamcrest.Matchers.samePropertyValuesAs
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest
import org.springframework.context.annotation.Import

@DataJpaTest
@Import(
    TestcontainersConfiguration::class,
    FVMDBConfiguration::class,
    VehicleDataDBConfiguration::class,
    VehicleLocationDBConfiguration::class,
    VehicleTransferDBConfiguration::class,
    PreDeliveryDBConfiguration::class,
    ConsigneeDatasheetDBConfiguration::class,
    VehicleCampaignsDBConfiguration::class,
    NonCustomerAdequateVehiclesDBConfiguration::class,
    VehicleSalesDBConfiguration::class,
    TireChangeAppointmentDBConfiguration::class,
)
class TireChangeEntryRepositoryTest {
    @Autowired
    private lateinit var repository: TireChangeEntryRepository

    @Test
    fun `should save new TireChangeEntry and then find by id`() {
        val tireChangeEntry = TireChangeEntryBuilder.buildSingle()
        val stored = repository.save(tireChangeEntry)
        val tireChangeEntryFromRepo = repository.findById(tireChangeEntry.id)
        assertThat(tireChangeEntryFromRepo, `is`(samePropertyValuesAs(stored)))
    }

    @Test
    fun `should find tire change entry by msBookingsAppointmentId`() {
        val tireChangeEntry = TireChangeEntryBuilder.buildSingle()
        val stored = repository.save(tireChangeEntry)
        val tireChangeEntryFromRepo = repository.findByMsBookingsAppointmentId(stored.msBookingsAppointmentId)
        assertThat(tireChangeEntryFromRepo, `is`(samePropertyValuesAs(stored)))
    }

    @Test
    fun `should find tire change entries without rela order number`() {
        val tireChangeEntry1 = TireChangeEntryBuilder.withoutRelaOrderNumber()
        val tireChangeEntry2 = TireChangeEntryBuilder.withRelaOrderNumber("Rela123")

        val storedWithoutRelaOrderNumber = repository.save(tireChangeEntry1)
        val storedRelaOrderNumber = repository.save(tireChangeEntry2)

        val tireChangeEntriesFromRepo =
            repository.findAllByRelaOrderNumberIsNull()
        assertThat(
            tireChangeEntriesFromRepo,
            hasItem(samePropertyValuesAs(storedWithoutRelaOrderNumber)),
        )

        assertThat(
            tireChangeEntriesFromRepo,
            not(hasItem(samePropertyValuesAs(storedRelaOrderNumber))),
        )
    }

    @Test
    fun `should find tire change entries to be updated in rela`() {
        val tireChangeEntry1 = TireChangeEntryBuilder.buildSingle()
        val tireChangeEntry2 = TireChangeEntryBuilder.withRelaUpdateFlagFalse()

        val storedWithUpdateNeeded = repository.save(tireChangeEntry1)
        val storedWithoutUpdateNeeded = repository.save(tireChangeEntry2)

        val tireChangeEntriesFromRepo =
            repository.findAllByShouldUpdateRelaIsTrue()
        assertThat(
            tireChangeEntriesFromRepo,
            hasItem(samePropertyValuesAs(storedWithUpdateNeeded)),
        )

        assertThat(
            tireChangeEntriesFromRepo,
            not(hasItem(samePropertyValuesAs(storedWithoutUpdateNeeded))),
        )
    }
}
