/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.tirechangeappointment.domain

import com.fleetmanagement.modules.tirechangeappointment.objectMother.TireChangeEntryBuilder
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertNotNull
import org.junit.jupiter.api.assertNull
import java.time.OffsetDateTime
import java.time.ZoneOffset

class TireChangeEntryTest {
    @Test
    fun `should initialise TireChangeEntry`() {
        val msBookingsAppointmentId = "1234"
        val msBookingsAppointmentDate = OffsetDateTime.now()
        val licensePlate = "BBN1234"
        val firstName = "John"
        val lastName = "Doe"
        val vin = "sample-vin"
        val email = "email"

        val tireChangeEntry =
            TireChangeEntry(
                msBookingsAppointmentId = msBookingsAppointmentId,
                msBookingsAppointmentDate = msBookingsAppointmentDate,
                licensePlate = licensePlate,
                firstName = firstName,
                lastName = lastName,
                vin = vin,
                email = email,
            )

        assertEquals(msBookingsAppointmentId, tireChangeEntry.msBookingsAppointmentId)
        assertEquals(msBookingsAppointmentDate, tireChangeEntry.msBookingsAppointmentDate)
        assertEquals(licensePlate, tireChangeEntry.licensePlate)
        assertEquals(firstName, tireChangeEntry.firstName)
        assertEquals(lastName, tireChangeEntry.lastName)
        assertEquals(vin, tireChangeEntry.vin)
        assertEquals(email, tireChangeEntry.email)
        assertNotNull(tireChangeEntry.created)
        assertEquals("", tireChangeEntry.createdBy)
        assertNotNull(tireChangeEntry.lastModified)
        assertEquals("", tireChangeEntry.lastModifiedBy)
        assertNull(tireChangeEntry.relaOrderNumber)
        assertTrue(tireChangeEntry.shouldUpdateRela)
    }

    @Test
    fun `should update appointment date`() {
        val tireChangeEntry = TireChangeEntryBuilder.buildSingle()
        val newMsBookingsAppointmentDate = OffsetDateTime.of(2020, 1, 1, 1, 0, 0, 0, ZoneOffset.UTC)
        tireChangeEntry.updateAppointmentDate(msBookingsAppointmentDate = newMsBookingsAppointmentDate)

        assertEquals(newMsBookingsAppointmentDate, tireChangeEntry.msBookingsAppointmentDate)
        assertTrue(tireChangeEntry.shouldUpdateRela)
    }

    @Test
    fun `should update rela order number`() {
        val tireChangeEntry = TireChangeEntryBuilder.buildSingle()
        val relaOrderNumber = "Rela123"
        tireChangeEntry.updateRelaOrderNumber(relaOrderNumber)

        assertEquals(relaOrderNumber, tireChangeEntry.relaOrderNumber)
    }

    @Test
    fun `should update rela update flag`() {
        val tireChangeEntry = TireChangeEntryBuilder.buildSingle()
        tireChangeEntry.markAsSyncedWithRela()

        assertFalse(tireChangeEntry.shouldUpdateRela)
    }
}
