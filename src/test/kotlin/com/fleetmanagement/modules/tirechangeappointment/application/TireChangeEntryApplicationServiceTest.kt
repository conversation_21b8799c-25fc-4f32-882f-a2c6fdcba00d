/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.tirechangeappointment.application

import com.fleetmanagement.modules.tirechangeappointment.application.port.CancelAppointmentRequestDTO
import com.fleetmanagement.modules.tirechangeappointment.application.port.TireChangeAppointmentCancelUseCase
import com.fleetmanagement.modules.tirechangeappointment.application.port.TireChangeAppointmentReadUseCase
import com.fleetmanagement.modules.tirechangeappointment.application.port.TireChangeAppointmentUpdateUseCase
import com.fleetmanagement.modules.tirechangeappointment.domain.TireChangeEntry
import com.fleetmanagement.modules.tirechangeappointment.domain.service.TireChangeEntryCreateService
import com.fleetmanagement.modules.tirechangeappointment.domain.service.TireChangeEntryUpdateService
import com.fleetmanagement.modules.tirechangeappointment.objectMother.AppointmentDTOBuilder
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByVehicleId
import com.fleetmanagement.modules.vehicledata.api.domain.TireSet
import com.fleetmanagement.modules.vehicledata.api.domain.VehicleSource
import com.fleetmanagement.modules.vehicledata.api.dtos.VehicleDTO
import com.fleetmanagement.modules.vehicleperson.api.dtos.VehiclePersonDetail
import com.fleetmanagement.modules.vehicleperson.api.readvehicleperson.ReadVehiclePersonDetailByEmployeeNumber
import com.fleetmanagement.modules.vehicleperson.api.readvehicleperson.ReadVehiclePersonException
import com.fleetmanagement.modules.vehicletransfer.VehicleTransferBuilder
import com.fleetmanagement.modules.vehicletransfer.application.port.ReadVehicleTransferUseCase
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.`is`
import org.hamcrest.Matchers.samePropertyValuesAs
import org.junit.jupiter.api.Test
import java.time.OffsetDateTime
import java.util.UUID

class TireChangeEntryApplicationServiceTest {
    private val tireChangeEntryFinder = mockk<TireChangeEntryFinder>()
    private val tireChangeEntryUpdateService = mockk<TireChangeEntryUpdateService>()
    private val tireChangeCreateService = mockk<TireChangeEntryCreateService>()
    private val readVehicleTransferUseCase = mockk<ReadVehicleTransferUseCase>()
    private val readVehiclePersonDetailByEmployeeNumber = mockk<ReadVehiclePersonDetailByEmployeeNumber>()
    private val tireChangeAppointmentReadUseCase = mockk<TireChangeAppointmentReadUseCase>()
    private val tireChangeAppointmentUpdateUseCase = mockk<TireChangeAppointmentUpdateUseCase>()
    private val tireChangeAppointmentCancelUseCase = mockk<TireChangeAppointmentCancelUseCase>()
    private val readVehicleByVehicleId = mockk<ReadVehicleByVehicleId>()

    private val tireChangeEntryApplicationService =
        TireChangeEntryApplicationService(
            tireChangeEntryFinder,
            tireChangeEntryUpdateService,
            tireChangeCreateService,
            readVehicleTransferUseCase,
            readVehiclePersonDetailByEmployeeNumber,
            tireChangeAppointmentReadUseCase,
            tireChangeAppointmentUpdateUseCase,
            tireChangeAppointmentCancelUseCase,
            readVehicleByVehicleId,
        )

    @Test
    fun `should create new tire change entry`() {
        val appointmentDTO = AppointmentDTOBuilder().build()
        every { tireChangeAppointmentReadUseCase.readCalendar(any(), any()) } returns
            listOf(
                appointmentDTO,
            )

        val appointmentIdArgumentCaptor = slot<String>()
        every { tireChangeEntryFinder.getTireChangeEntryByMsBookingsAppointmentId(capture(appointmentIdArgumentCaptor)) } returns null

        val licensePlateArgumentCaptor = slot<String>()
        val activeVT = VehicleTransferBuilder.buildSingle()
        every { readVehicleTransferUseCase.findActiveVehicleTransferByLicensePlate(capture(licensePlateArgumentCaptor)) } returns
            listOf(
                activeVT,
            )

        val employeeNumberArgumentCaptor = slot<String>()
        val vehiclePersonDetail =
            VehiclePersonDetail(
                employeeNumber = activeVT.vehicleResponsiblePerson!!.value,
                firstName = "John",
                lastName = "Doe",
                companyEmail = appointmentDTO.customerEmailAddress!!,
                accountingArea = "test",
                businessPartnerId = "test",
            )
        every {
            readVehiclePersonDetailByEmployeeNumber.readVehiclePersonDetailByEmployeeNumber(
                capture(
                    employeeNumberArgumentCaptor,
                ),
            )
        } returns vehiclePersonDetail

        val vehicleDTO = vehicleDTO()
        val vehicleIdArgumentCaptor = slot<UUID>()
        every { readVehicleByVehicleId.readVehicleById(capture(vehicleIdArgumentCaptor)) } returns vehicleDTO

        val tireChangeEntryArgumentCaptor = slot<TireChangeEntry>()
        every { tireChangeCreateService.createTireChangeEntry(capture(tireChangeEntryArgumentCaptor)) } just Runs

        tireChangeEntryApplicationService.syncTireChangeAppointmentsFromMsBookings()

        val expectedTireChangeEntry =
            TireChangeEntry(
                msBookingsAppointmentId = appointmentDTO.appointmentId,
                msBookingsAppointmentDate = appointmentDTO.startTime,
                licensePlate = appointmentDTO.licensePlate,
                firstName = appointmentDTO.customerFirstName,
                lastName = appointmentDTO.customerLastName,
                vin = vehicleDTO.vin!!,
                email = appointmentDTO.customerEmailAddress,
            )
        verify(exactly = 1) { tireChangeAppointmentReadUseCase.readCalendar(any(), any()) }
        assertThat(appointmentIdArgumentCaptor.captured, `is`(appointmentDTO.appointmentId))
        assertThat(licensePlateArgumentCaptor.captured, `is`(appointmentDTO.licensePlate))
        assertThat(employeeNumberArgumentCaptor.captured, `is`(activeVT.vehicleResponsiblePerson!!.value))
        assertThat(
            tireChangeEntryArgumentCaptor.captured,
            `is`(samePropertyValuesAs(expectedTireChangeEntry, "id", "created", "lastModified")),
        )
    }

    @Test
    fun `should cancel msBookings appointment when license plate does not match`() {
        val appointmentDTO = AppointmentDTOBuilder().build()
        every { tireChangeAppointmentReadUseCase.readCalendar(any(), any()) } returns
            listOf(
                appointmentDTO,
            )

        every { tireChangeEntryFinder.getTireChangeEntryByMsBookingsAppointmentId(any()) } returns null

        every { readVehicleTransferUseCase.findActiveVehicleTransferByLicensePlate(any()) } returns
            emptyList()

        every {
            readVehiclePersonDetailByEmployeeNumber.readVehiclePersonDetailByEmployeeNumber(
                any(),
            )
        } returns null

        every { tireChangeCreateService.createTireChangeEntry(any()) } just Runs

        val appointmentIdArgumentCaptor = slot<String>()
        val cancellationArgumentCaptor = slot<CancelAppointmentRequestDTO>()
        every {
            tireChangeAppointmentCancelUseCase.cancelAppointment(
                capture(appointmentIdArgumentCaptor),
                capture(cancellationArgumentCaptor),
            )
        } just Runs

        tireChangeEntryApplicationService.syncTireChangeAppointmentsFromMsBookings()

        verify(exactly = 0) {
            tireChangeCreateService.createTireChangeEntry(any())
        }

        assertThat(appointmentIdArgumentCaptor.captured, `is`(appointmentDTO.appointmentId))
        assertThat(
            cancellationArgumentCaptor.captured,
            `is`(
                samePropertyValuesAs<CancelAppointmentRequestDTO>(
                    CancelAppointmentRequestDTO(
                        cancellationMessage = "Invalid license plate",
                    ),
                ),
            ),
        )
    }

    @Test
    fun `should cancel msBookings appointment when vehicle transfer does not have vehicle responsible person `() {
        val appointmentDTO = AppointmentDTOBuilder().build()
        every { tireChangeAppointmentReadUseCase.readCalendar(any(), any()) } returns
            listOf(
                appointmentDTO,
            )

        every { tireChangeEntryFinder.getTireChangeEntryByMsBookingsAppointmentId(any()) } returns null

        val activeVT = VehicleTransferBuilder().vehicleResponsiblePerson(null).build()
        every { readVehicleTransferUseCase.findActiveVehicleTransferByLicensePlate(any()) } returns
            listOf(activeVT)

        every {
            readVehiclePersonDetailByEmployeeNumber.readVehiclePersonDetailByEmployeeNumber(
                any(),
            )
        } returns null

        every { tireChangeCreateService.createTireChangeEntry(any()) } just Runs

        val appointmentIdArgumentCaptor = slot<String>()
        val cancellationArgumentCaptor = slot<CancelAppointmentRequestDTO>()
        every {
            tireChangeAppointmentCancelUseCase.cancelAppointment(
                capture(appointmentIdArgumentCaptor),
                capture(cancellationArgumentCaptor),
            )
        } just Runs

        tireChangeEntryApplicationService.syncTireChangeAppointmentsFromMsBookings()

        verify(exactly = 0) {
            readVehiclePersonDetailByEmployeeNumber.readVehiclePersonDetailByEmployeeNumber(
                any(),
            )
            tireChangeCreateService.createTireChangeEntry(any())
        }

        assertThat(appointmentIdArgumentCaptor.captured, `is`(appointmentDTO.appointmentId))
        assertThat(
            cancellationArgumentCaptor.captured,
            `is`(
                samePropertyValuesAs(
                    CancelAppointmentRequestDTO(
                        cancellationMessage = "Missing vehicle responsible person details",
                    ),
                ),
            ),
        )
    }

    @Test
    fun `should cancel msBookings appointment when vehicle responsible person details could not be fetched `() {
        val appointmentDTO = AppointmentDTOBuilder().build()
        every { tireChangeAppointmentReadUseCase.readCalendar(any(), any()) } returns
            listOf(
                appointmentDTO,
            )

        every { tireChangeEntryFinder.getTireChangeEntryByMsBookingsAppointmentId(any()) } returns null

        val activeVT = VehicleTransferBuilder().build()
        every { readVehicleTransferUseCase.findActiveVehicleTransferByLicensePlate(any()) } returns
            listOf(activeVT)

        every {
            readVehiclePersonDetailByEmployeeNumber.readVehiclePersonDetailByEmployeeNumber(
                any(),
            )
        } throws ReadVehiclePersonException("Cannot connect", null)

        every { tireChangeCreateService.createTireChangeEntry(any()) } just Runs

        val appointmentIdArgumentCaptor = slot<String>()
        val cancellationArgumentCaptor = slot<CancelAppointmentRequestDTO>()
        every {
            tireChangeAppointmentCancelUseCase.cancelAppointment(
                capture(appointmentIdArgumentCaptor),
                capture(cancellationArgumentCaptor),
            )
        } just Runs

        tireChangeEntryApplicationService.syncTireChangeAppointmentsFromMsBookings()

        verify(exactly = 0) {
            tireChangeCreateService.createTireChangeEntry(any())
        }

        assertThat(appointmentIdArgumentCaptor.captured, `is`(appointmentDTO.appointmentId))
        assertThat(
            cancellationArgumentCaptor.captured,
            `is`(
                samePropertyValuesAs(
                    CancelAppointmentRequestDTO(
                        cancellationMessage = "Missing vehicle responsible person details",
                    ),
                ),
            ),
        )
    }

    @Test
    fun `should update msBookings appointment when vehicle responsible person company email does not match `() {
        val appointmentDTO = AppointmentDTOBuilder().build()
        every { tireChangeAppointmentReadUseCase.readCalendar(any(), any()) } returns
            listOf(
                appointmentDTO,
            )

        every { tireChangeEntryFinder.getTireChangeEntryByMsBookingsAppointmentId(any()) } returns null

        val activeVT = VehicleTransferBuilder().build()
        every { readVehicleTransferUseCase.findActiveVehicleTransferByLicensePlate(any()) } returns
            listOf(activeVT)

        val employeeNumberArgumentCaptor = slot<String>()
        val vehiclePersonDetail =
            VehiclePersonDetail(
                employeeNumber = activeVT.vehicleResponsiblePerson!!.value,
                firstName = "John",
                lastName = "Doe",
                companyEmail = "companyEmail",
                accountingArea = "test",
                businessPartnerId = "test",
            )
        every {
            readVehiclePersonDetailByEmployeeNumber.readVehiclePersonDetailByEmployeeNumber(
                capture(
                    employeeNumberArgumentCaptor,
                ),
            )
        } returns vehiclePersonDetail
        val vehicleDTO = vehicleDTO()
        every { readVehicleByVehicleId.readVehicleById(any()) } returns vehicleDTO

        val tireChangeEntryArgumentCaptor = slot<TireChangeEntry>()
        every { tireChangeCreateService.createTireChangeEntry(capture(tireChangeEntryArgumentCaptor)) } just Runs

        val appointmentIdArgumentCaptor = slot<String>()
        val updateArgumentCaptor = slot<AppointmentUpdateRequest>()
        every {
            tireChangeAppointmentUpdateUseCase.updateAppointment(
                capture(appointmentIdArgumentCaptor),
                capture(updateArgumentCaptor),
            )
        } just Runs

        tireChangeEntryApplicationService.syncTireChangeAppointmentsFromMsBookings()

        assertThat(appointmentIdArgumentCaptor.captured, `is`(appointmentDTO.appointmentId))
        assertThat(
            updateArgumentCaptor.captured,
            `is`(
                samePropertyValuesAs(
                    AppointmentUpdateRequest(
                        odataType = "",
                        customerName = appointmentDTO.customerFirstName,
                        customerEmailAddress = vehiclePersonDetail.companyEmail,
                        customers = emptyList(),
                    ),
                ),
            ),
        )
        val expectedTireChangeEntry =
            TireChangeEntry(
                msBookingsAppointmentId = appointmentDTO.appointmentId,
                msBookingsAppointmentDate = appointmentDTO.startTime,
                licensePlate = appointmentDTO.licensePlate,
                firstName = appointmentDTO.customerFirstName,
                lastName = appointmentDTO.customerLastName,
                vin = vehicleDTO.vin!!,
                email = vehiclePersonDetail.companyEmail,
            )
        assertThat(
            tireChangeEntryArgumentCaptor.captured,
            `is`(samePropertyValuesAs(expectedTireChangeEntry, "id", "created", "lastModified")),
        )
    }

    @Test
    fun `should update tire change entry`() {
        val appointmentDTO = AppointmentDTOBuilder().build()
        every { tireChangeAppointmentReadUseCase.readCalendar(any(), any()) } returns
            listOf(
                appointmentDTO,
            )

        val existingTireChangeEntry =
            TireChangeEntry(
                msBookingsAppointmentId = appointmentDTO.appointmentId,
                msBookingsAppointmentDate = appointmentDTO.startTime.plusDays(1),
                licensePlate = appointmentDTO.licensePlate,
                firstName = appointmentDTO.customerFirstName,
                lastName = appointmentDTO.customerLastName,
                vin = "vin",
                email = appointmentDTO.customerEmailAddress!!,
            )
        every { tireChangeEntryFinder.getTireChangeEntryByMsBookingsAppointmentId(any()) } returns existingTireChangeEntry

        val tireChangeEntryArgumentCaptor = slot<TireChangeEntry>()
        val appointmentDateArgumentCaptor = slot<OffsetDateTime>()
        every {
            tireChangeEntryUpdateService.updateAppointmentDate(
                capture(tireChangeEntryArgumentCaptor),
                capture(appointmentDateArgumentCaptor),
            )
        } just Runs

        tireChangeEntryApplicationService.syncTireChangeAppointmentsFromMsBookings()

        assertThat(
            tireChangeEntryArgumentCaptor.captured,
            `is`(samePropertyValuesAs(existingTireChangeEntry)),
        )
        assertThat(appointmentDateArgumentCaptor.captured, `is`(appointmentDTO.startTime))
    }

    private fun vehicleDTO() =
        VehicleDTO(
            id = UUID.randomUUID(),
            vguid = "vguid",
            vin = "vin",
            order = null,
            equipmentNumber = null,
            equiId = null,
            model = null,
            options = null,
            production = null,
            country = null,
            pmp = null,
            embargo = null,
            color = null,
            source = VehicleSource.UNKNOWN,
            currentTires = TireSet.WR,
            referenceId = null,
            createdAt = null,
            status = null,
            price = null,
            technical = null,
            consumption = null,
            fleet = null,
            delivery = null,
            returnInfo = null,
            tireSetChange = null,
            wltpInfo = null,
            evaluation = null,
            repairfixCarId = null,
            currentMileage = null,
            vtstamm = null,
        )
}
