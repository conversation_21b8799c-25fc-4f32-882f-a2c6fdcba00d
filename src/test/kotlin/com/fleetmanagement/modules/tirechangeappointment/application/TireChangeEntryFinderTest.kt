/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.tirechangeappointment.application

import com.fleetmanagement.TestcontainersConfiguration
import com.fleetmanagement.modules.tirechangeappointment.domain.TireChangeEntryRepository
import com.fleetmanagement.modules.tirechangeappointment.objectMother.TireChangeEntryBuilder
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.hasItem
import org.hamcrest.Matchers.`is`
import org.hamcrest.Matchers.not
import org.hamcrest.Matchers.samePropertyValuesAs
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.annotation.Import
import org.springframework.transaction.annotation.Transactional

@SpringBootTest
@Transactional
@Import(TestcontainersConfiguration::class)
class TireChangeEntryFinderTest {
    @Autowired
    private lateinit var tireChangeEntryFinder: TireChangeEntryFinder

    @Autowired
    private lateinit var tireChangeEntryRepository: TireChangeEntryRepository

    @Test
    fun `should store tire change entry and find it`() {
        val tireChangeEntry = TireChangeEntryBuilder.buildSingle()
        val saved = tireChangeEntryRepository.save(tireChangeEntry)
        val tireChangeEntryFromFinder = tireChangeEntryFinder.getTireChangeEntryById(tireChangeEntry.id)

        assertThat(tireChangeEntryFromFinder, `is`(samePropertyValuesAs(saved)))
    }

    @Test
    fun `should find tire change entry by appointmentId`() {
        val tireChangeEntry = TireChangeEntryBuilder.buildSingle()
        val saved = tireChangeEntryRepository.save(tireChangeEntry)
        val tireChangeEntryFromFinder =
            tireChangeEntryFinder.getTireChangeEntryByMsBookingsAppointmentId(tireChangeEntry.msBookingsAppointmentId)
        assertThat(tireChangeEntryFromFinder, `is`(samePropertyValuesAs(saved)))
    }

    @Test
    fun `should find tire change entries without rela order number`() {
        val tireChangeEntry1 = TireChangeEntryBuilder.withoutRelaOrderNumber()
        val tireChangeEntry2 = TireChangeEntryBuilder.withRelaOrderNumber("Rela123")

        val savedWithoutRelaOrderNumber = tireChangeEntryRepository.save(tireChangeEntry1)
        val savedRelaOrderNumber = tireChangeEntryRepository.save(tireChangeEntry2)

        val tireChangeEntriesFromFinder =
            tireChangeEntryFinder.findAllEntriesWithNullRelaOrderNumber()
        assertThat(
            tireChangeEntriesFromFinder,
            hasItem(samePropertyValuesAs(savedWithoutRelaOrderNumber)),
        )

        assertThat(
            tireChangeEntriesFromFinder,
            not(hasItem(samePropertyValuesAs(savedRelaOrderNumber))),
        )
    }

    @Test
    fun `should find tire change entries to be updated in rela`() {
        val tireChangeEntry1 = TireChangeEntryBuilder.buildSingle()
        val tireChangeEntry2 = TireChangeEntryBuilder.withRelaUpdateFlagFalse()

        val savedWithUpdateNeeded = tireChangeEntryRepository.save(tireChangeEntry1)
        val savedWithoutUpdateNeeded = tireChangeEntryRepository.save(tireChangeEntry2)

        val tireChangeEntriesFromFinder =
            tireChangeEntryFinder.findAllEntriesWithPendingRelaUpdate()
        assertThat(
            tireChangeEntriesFromFinder,
            hasItem(samePropertyValuesAs(savedWithUpdateNeeded)),
        )

        assertThat(
            tireChangeEntriesFromFinder,
            not(hasItem(samePropertyValuesAs(savedWithoutUpdateNeeded))),
        )
    }
}
