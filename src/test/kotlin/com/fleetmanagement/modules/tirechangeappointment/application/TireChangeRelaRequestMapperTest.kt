/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.tirechangeappointment.application

import com.fleetmanagement.modules.tirechangeappointment.objectMother.TireChangeEntryBuilder
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class TireChangeRelaRequestMapperTest {
    private val mapper = TireChangeRelaRequestMapper()

    @Test
    fun `should successfully map TireChangeEntry to RelaAppointmentRequest`() {
        val tireChangeEntry = TireChangeEntryBuilder.buildSingle()

        val result = mapper.mapToRelaAppointmentRequest(tireChangeEntry)

        assertEquals(tireChangeEntry.msBookingsAppointmentDate, result.appointment)
        assertEquals(tireChangeEntry.licensePlate, result.vehicleLicensePlate)
        assertEquals(tireChangeEntry.vin, result.vehicleVin)
        assertEquals(tireChangeEntry.firstName, result.customerFirstName)
        assertEquals(tireChangeEntry.lastName, result.customerLastName)
    }
}
