package com.fleetmanagement.modules.tirechangeappointment.application.job

import com.fleetmanagement.modules.tirechangeappointment.application.TireChangeRelaSyncCreateService
import com.fleetmanagement.modules.tirechangeappointment.application.TireChangeRelaSyncUpdateService
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Test

class TireChangeRelaSyncJobTest {
    private val tireChangeRelaSyncCreateService = mockk<TireChangeRelaSyncCreateService>()
    private val tireChangeRelaSyncUpdateService = mockk<TireChangeRelaSyncUpdateService>()

    private val tireChangeRelaSyncJob: TireChangeRelaSyncJob =
        TireChangeRelaSyncJob(tireChangeRelaSyncCreateService, tireChangeRelaSyncUpdateService)

    @Test
    fun `should call create and update for tire change appointments when job is called`() {
        justRun { tireChangeRelaSyncCreateService.syncCreateRelaAppointments() }
        justRun { tireChangeRelaSyncUpdateService.syncUpdateRelaAppointments() }

        tireChangeRelaSyncJob.execute(null)
        verify {
            tireChangeRelaSyncCreateService.syncCreateRelaAppointments()
            tireChangeRelaSyncUpdateService.syncUpdateRelaAppointments()
        }
    }
}
