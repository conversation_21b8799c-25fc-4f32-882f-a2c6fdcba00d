/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.tirechangeappointment.application

import com.fleetmanagement.modules.rela.application.RelaAppointmentCreationException
import com.fleetmanagement.modules.rela.application.port.CreateRelaAppointment
import com.fleetmanagement.modules.rela.application.port.RelaAppointmentResponse
import com.fleetmanagement.modules.tirechangeappointment.domain.service.TireChangeEntryUpdateService
import com.fleetmanagement.modules.tirechangeappointment.objectMother.RelaAppointmentRequestBuilder
import com.fleetmanagement.modules.tirechangeappointment.objectMother.TireChangeEntryBuilder
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.verify
import org.junit.jupiter.api.Test

class TireChangeRelaSyncCreateServiceTest {
    private val createRelaAppointment: CreateRelaAppointment = mockk()
    private val tireChangeEntryFinder: TireChangeEntryFinder = mockk()
    private val tireChangeEntryUpdateService: TireChangeEntryUpdateService = mockk()
    private val tireChangeRelaRequestMapper: TireChangeRelaRequestMapper = mockk()

    private val service =
        TireChangeRelaSyncCreateService(
            createRelaAppointment,
            tireChangeEntryFinder,
            tireChangeEntryUpdateService,
            tireChangeRelaRequestMapper,
        )

    @Test
    fun `should successfully create RELA appointments for entries without order number`() {
        val tireChangeEntry1 = TireChangeEntryBuilder.withoutRelaOrderNumber()
        val tireChangeEntry2 = TireChangeEntryBuilder.withoutRelaOrderNumber()
        val entriesWithoutOrderNumber = listOf(tireChangeEntry1, tireChangeEntry2)

        val relaRequest1 = RelaAppointmentRequestBuilder.buildSingle()
        val relaRequest2 = RelaAppointmentRequestBuilder.buildSingle()
        val relaResponse1 = RelaAppointmentResponse("ORDER123")
        val relaResponse2 = RelaAppointmentResponse("ORDER456")

        every { tireChangeEntryFinder.findAllEntriesWithNullRelaOrderNumber() } returns entriesWithoutOrderNumber
        every { tireChangeRelaRequestMapper.mapToRelaAppointmentRequest(tireChangeEntry1) } returns relaRequest1
        every { tireChangeRelaRequestMapper.mapToRelaAppointmentRequest(tireChangeEntry2) } returns relaRequest2
        every { createRelaAppointment.createAppointment(relaRequest1) } returns relaResponse1
        every { createRelaAppointment.createAppointment(relaRequest2) } returns relaResponse2
        every { tireChangeEntryUpdateService.updateRelaOrderNumber(any(), any()) } just runs
        every { tireChangeEntryUpdateService.markAsSyncedWithRela(any()) } just runs

        service.syncCreateRelaAppointments()

        verify(exactly = 1) { tireChangeEntryFinder.findAllEntriesWithNullRelaOrderNumber() }
        verify(exactly = 1) { tireChangeRelaRequestMapper.mapToRelaAppointmentRequest(tireChangeEntry1) }
        verify(exactly = 1) { tireChangeRelaRequestMapper.mapToRelaAppointmentRequest(tireChangeEntry2) }
        verify(exactly = 1) { createRelaAppointment.createAppointment(relaRequest1) }
        verify(exactly = 1) { createRelaAppointment.createAppointment(relaRequest2) }
        verify(exactly = 1) { tireChangeEntryUpdateService.updateRelaOrderNumber(tireChangeEntry1, "ORDER123") }
        verify(exactly = 1) { tireChangeEntryUpdateService.updateRelaOrderNumber(tireChangeEntry2, "ORDER456") }
        verify(exactly = 1) { tireChangeEntryUpdateService.markAsSyncedWithRela(tireChangeEntry1) }
        verify(exactly = 1) { tireChangeEntryUpdateService.markAsSyncedWithRela(tireChangeEntry2) }
    }

    @Test
    fun `should handle empty list of entries without order number`() {
        every { tireChangeEntryFinder.findAllEntriesWithNullRelaOrderNumber() } returns emptyList()

        service.syncCreateRelaAppointments()

        verify(exactly = 1) { tireChangeEntryFinder.findAllEntriesWithNullRelaOrderNumber() }
        verify(exactly = 0) { tireChangeRelaRequestMapper.mapToRelaAppointmentRequest(any()) }
        verify(exactly = 0) { createRelaAppointment.createAppointment(any()) }
        verify(exactly = 0) { tireChangeEntryUpdateService.updateRelaOrderNumber(any(), any()) }
        verify(exactly = 0) { tireChangeEntryUpdateService.markAsSyncedWithRela(any()) }
    }

    @Test
    fun `should continue processing other entries when one fails`() {
        val tireChangeEntry1 = TireChangeEntryBuilder.withoutRelaOrderNumber()
        val tireChangeEntry2 = TireChangeEntryBuilder.withoutRelaOrderNumber()
        val entriesWithoutOrderNumber = listOf(tireChangeEntry1, tireChangeEntry2)

        val relaRequest1 = RelaAppointmentRequestBuilder.buildSingle()
        val relaRequest2 = RelaAppointmentRequestBuilder.buildSingle()
        val relaResponse2 = RelaAppointmentResponse("ORDER456")

        every { tireChangeEntryFinder.findAllEntriesWithNullRelaOrderNumber() } returns entriesWithoutOrderNumber
        every { tireChangeRelaRequestMapper.mapToRelaAppointmentRequest(tireChangeEntry1) } returns relaRequest1
        every { tireChangeRelaRequestMapper.mapToRelaAppointmentRequest(tireChangeEntry2) } returns relaRequest2
        every { createRelaAppointment.createAppointment(relaRequest1) } throws RelaAppointmentCreationException("RELA API Error")
        every { createRelaAppointment.createAppointment(relaRequest2) } returns relaResponse2
        every { tireChangeEntryUpdateService.updateRelaOrderNumber(tireChangeEntry2, "ORDER456") } just runs
        every { tireChangeEntryUpdateService.markAsSyncedWithRela(tireChangeEntry2) } just runs

        service.syncCreateRelaAppointments()

        verify(exactly = 1) { tireChangeEntryFinder.findAllEntriesWithNullRelaOrderNumber() }
        verify(exactly = 1) { createRelaAppointment.createAppointment(relaRequest1) }
        verify(exactly = 1) { createRelaAppointment.createAppointment(relaRequest2) }
        verify(exactly = 0) { tireChangeEntryUpdateService.updateRelaOrderNumber(tireChangeEntry1, any()) }
        verify(exactly = 0) { tireChangeEntryUpdateService.markAsSyncedWithRela(tireChangeEntry1) }
        verify(exactly = 1) { tireChangeEntryUpdateService.updateRelaOrderNumber(tireChangeEntry2, "ORDER456") }
        verify(exactly = 1) { tireChangeEntryUpdateService.markAsSyncedWithRela(tireChangeEntry2) }
    }
}
