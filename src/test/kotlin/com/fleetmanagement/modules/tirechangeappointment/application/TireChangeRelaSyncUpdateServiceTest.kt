/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.tirechangeappointment.application

import com.fleetmanagement.modules.rela.application.RelaAppointmentCancellationException
import com.fleetmanagement.modules.rela.application.RelaAppointmentCreationException
import com.fleetmanagement.modules.rela.application.port.CancelRelaAppointment
import com.fleetmanagement.modules.rela.application.port.CreateRelaAppointment
import com.fleetmanagement.modules.rela.application.port.RelaAppointmentResponse
import com.fleetmanagement.modules.tirechangeappointment.domain.service.TireChangeEntryUpdateService
import com.fleetmanagement.modules.tirechangeappointment.objectMother.RelaAppointmentRequestBuilder
import com.fleetmanagement.modules.tirechangeappointment.objectMother.TireChangeEntryBuilder
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.verify
import org.junit.jupiter.api.Test

class TireChangeRelaSyncUpdateServiceTest {
    private val createRelaAppointment: CreateRelaAppointment = mockk()
    private val cancelRelaAppointment: CancelRelaAppointment = mockk()
    private val tireChangeEntryFinder: TireChangeEntryFinder = mockk()
    private val tireChangeEntryUpdateService: TireChangeEntryUpdateService = mockk()
    private val tireChangeRelaRequestMapper: TireChangeRelaRequestMapper = mockk()

    private val service =
        TireChangeRelaSyncUpdateService(
            createRelaAppointment,
            cancelRelaAppointment,
            tireChangeEntryFinder,
            tireChangeEntryUpdateService,
            tireChangeRelaRequestMapper,
        )

    @Test
    fun `should successfully update RELA appointments for entries with pending updates`() {
        val tireChangeEntry1 = TireChangeEntryBuilder.withRelaOrderNumber("OLD_ORDER123")
        val tireChangeEntry2 = TireChangeEntryBuilder.withRelaOrderNumber("OLD_ORDER456")
        val entriesWithPendingUpdates = listOf(tireChangeEntry1, tireChangeEntry2)

        val relaRequest1 = RelaAppointmentRequestBuilder.buildSingle()
        val relaRequest2 = RelaAppointmentRequestBuilder.buildSingle()
        val relaResponse1 = RelaAppointmentResponse("NEW_ORDER123")
        val relaResponse2 = RelaAppointmentResponse("NEW_ORDER456")

        every { tireChangeEntryFinder.findAllEntriesWithPendingRelaUpdate() } returns entriesWithPendingUpdates
        every { cancelRelaAppointment.cancelAppointment("OLD_ORDER123") } just runs
        every { cancelRelaAppointment.cancelAppointment("OLD_ORDER456") } just runs
        every { tireChangeRelaRequestMapper.mapToRelaAppointmentRequest(tireChangeEntry1) } returns relaRequest1
        every { tireChangeRelaRequestMapper.mapToRelaAppointmentRequest(tireChangeEntry2) } returns relaRequest2
        every { createRelaAppointment.createAppointment(relaRequest1) } returns relaResponse1
        every { createRelaAppointment.createAppointment(relaRequest2) } returns relaResponse2
        every { tireChangeEntryUpdateService.updateRelaOrderNumber(any(), any()) } just runs
        every { tireChangeEntryUpdateService.markAsSyncedWithRela(any()) } just runs

        service.syncUpdateRelaAppointments()

        verify(exactly = 1) { tireChangeEntryFinder.findAllEntriesWithPendingRelaUpdate() }
        verify(exactly = 1) { cancelRelaAppointment.cancelAppointment("OLD_ORDER123") }
        verify(exactly = 1) { cancelRelaAppointment.cancelAppointment("OLD_ORDER456") }
        verify(exactly = 1) { tireChangeRelaRequestMapper.mapToRelaAppointmentRequest(tireChangeEntry1) }
        verify(exactly = 1) { tireChangeRelaRequestMapper.mapToRelaAppointmentRequest(tireChangeEntry2) }
        verify(exactly = 1) { createRelaAppointment.createAppointment(relaRequest1) }
        verify(exactly = 1) { createRelaAppointment.createAppointment(relaRequest2) }
        verify(exactly = 1) { tireChangeEntryUpdateService.updateRelaOrderNumber(tireChangeEntry1, "NEW_ORDER123") }
        verify(exactly = 1) { tireChangeEntryUpdateService.updateRelaOrderNumber(tireChangeEntry2, "NEW_ORDER456") }
    }

    @Test
    fun `should handle empty list of entries with pending updates`() {
        every { tireChangeEntryFinder.findAllEntriesWithPendingRelaUpdate() } returns emptyList()

        service.syncUpdateRelaAppointments()

        verify(exactly = 1) { tireChangeEntryFinder.findAllEntriesWithPendingRelaUpdate() }
        verify(exactly = 0) { cancelRelaAppointment.cancelAppointment(any()) }
        verify(exactly = 0) { tireChangeRelaRequestMapper.mapToRelaAppointmentRequest(any()) }
        verify(exactly = 0) { createRelaAppointment.createAppointment(any()) }
        verify(exactly = 0) { tireChangeEntryUpdateService.updateRelaOrderNumber(any(), any()) }
    }

    @Test
    fun `should skip entries with missing relaOrderNumber`() {
        val tireChangeEntryWithoutOrderNumber = TireChangeEntryBuilder.withoutRelaOrderNumber()
        val tireChangeEntryWithOrderNumber = TireChangeEntryBuilder.withRelaOrderNumber("ORDER123")
        val entriesWithPendingUpdates = listOf(tireChangeEntryWithoutOrderNumber, tireChangeEntryWithOrderNumber)

        val relaRequest = RelaAppointmentRequestBuilder.buildSingle()
        val relaResponse = RelaAppointmentResponse("NEW_ORDER123")

        every { tireChangeEntryFinder.findAllEntriesWithPendingRelaUpdate() } returns entriesWithPendingUpdates
        every { cancelRelaAppointment.cancelAppointment("ORDER123") } just runs
        every { tireChangeRelaRequestMapper.mapToRelaAppointmentRequest(tireChangeEntryWithOrderNumber) } returns relaRequest
        every { createRelaAppointment.createAppointment(relaRequest) } returns relaResponse
        every { tireChangeEntryUpdateService.updateRelaOrderNumber(tireChangeEntryWithOrderNumber, "NEW_ORDER123") } just runs
        every { tireChangeEntryUpdateService.markAsSyncedWithRela(tireChangeEntryWithOrderNumber) } just runs

        service.syncUpdateRelaAppointments()

        verify(exactly = 1) { tireChangeEntryFinder.findAllEntriesWithPendingRelaUpdate() }
        verify(exactly = 0) { tireChangeRelaRequestMapper.mapToRelaAppointmentRequest(tireChangeEntryWithoutOrderNumber) }
        verify(exactly = 1) { cancelRelaAppointment.cancelAppointment("ORDER123") }
        verify(exactly = 1) { tireChangeRelaRequestMapper.mapToRelaAppointmentRequest(tireChangeEntryWithOrderNumber) }
        verify(exactly = 1) { createRelaAppointment.createAppointment(relaRequest) }
        verify(exactly = 1) { tireChangeEntryUpdateService.updateRelaOrderNumber(tireChangeEntryWithOrderNumber, "NEW_ORDER123") }
    }

    @Test
    fun `should continue processing other entries when cancellation fails`() {
        val tireChangeEntry1 = TireChangeEntryBuilder.withRelaOrderNumber("ORDER123")
        val tireChangeEntry2 = TireChangeEntryBuilder.withRelaOrderNumber("ORDER456")
        val entriesWithPendingUpdates = listOf(tireChangeEntry1, tireChangeEntry2)

        val relaRequest2 = RelaAppointmentRequestBuilder.buildSingle()
        val relaResponse2 = RelaAppointmentResponse("NEW_ORDER456")

        every { tireChangeEntryFinder.findAllEntriesWithPendingRelaUpdate() } returns entriesWithPendingUpdates
        every { cancelRelaAppointment.cancelAppointment("ORDER123") } throws RelaAppointmentCancellationException("Cancel failed")
        every { cancelRelaAppointment.cancelAppointment("ORDER456") } just runs
        every { tireChangeRelaRequestMapper.mapToRelaAppointmentRequest(tireChangeEntry2) } returns relaRequest2
        every { createRelaAppointment.createAppointment(relaRequest2) } returns relaResponse2
        every { tireChangeEntryUpdateService.updateRelaOrderNumber(tireChangeEntry2, "NEW_ORDER456") } just runs
        every { tireChangeEntryUpdateService.markAsSyncedWithRela(tireChangeEntry2) } just runs

        service.syncUpdateRelaAppointments()

        verify(exactly = 1) { tireChangeEntryFinder.findAllEntriesWithPendingRelaUpdate() }
        verify(exactly = 1) { cancelRelaAppointment.cancelAppointment("ORDER123") }
        verify(exactly = 1) { cancelRelaAppointment.cancelAppointment("ORDER456") }
        verify(exactly = 0) { tireChangeRelaRequestMapper.mapToRelaAppointmentRequest(tireChangeEntry1) }
        verify(exactly = 0) { tireChangeEntryUpdateService.updateRelaOrderNumber(tireChangeEntry1, any()) }
        verify(exactly = 1) { tireChangeRelaRequestMapper.mapToRelaAppointmentRequest(tireChangeEntry2) }
        verify(exactly = 1) { createRelaAppointment.createAppointment(relaRequest2) }
        verify(exactly = 1) { tireChangeEntryUpdateService.updateRelaOrderNumber(tireChangeEntry2, "NEW_ORDER456") }
    }

    @Test
    fun `should continue processing other entries when creation fails`() {
        val tireChangeEntry1 = TireChangeEntryBuilder.withRelaOrderNumber("ORDER123")
        val tireChangeEntry2 = TireChangeEntryBuilder.withRelaOrderNumber("ORDER456")
        val entriesWithPendingUpdates = listOf(tireChangeEntry1, tireChangeEntry2)

        val relaRequest1 = RelaAppointmentRequestBuilder.buildSingle()
        val relaRequest2 = RelaAppointmentRequestBuilder.buildSingle()
        val relaResponse2 = RelaAppointmentResponse("NEW_ORDER456")

        every { tireChangeEntryFinder.findAllEntriesWithPendingRelaUpdate() } returns entriesWithPendingUpdates
        every { cancelRelaAppointment.cancelAppointment("ORDER123") } just runs
        every { cancelRelaAppointment.cancelAppointment("ORDER456") } just runs
        every { tireChangeRelaRequestMapper.mapToRelaAppointmentRequest(tireChangeEntry1) } returns relaRequest1
        every { tireChangeRelaRequestMapper.mapToRelaAppointmentRequest(tireChangeEntry2) } returns relaRequest2
        every { createRelaAppointment.createAppointment(relaRequest1) } throws RelaAppointmentCreationException("Creation failed")
        every { createRelaAppointment.createAppointment(relaRequest2) } returns relaResponse2
        every { tireChangeEntryUpdateService.updateRelaOrderNumber(tireChangeEntry2, "NEW_ORDER456") } just runs
        every { tireChangeEntryUpdateService.markAsSyncedWithRela(tireChangeEntry2) } just runs

        service.syncUpdateRelaAppointments()

        verify(exactly = 1) { tireChangeEntryFinder.findAllEntriesWithPendingRelaUpdate() }
        verify(exactly = 1) { cancelRelaAppointment.cancelAppointment("ORDER123") }
        verify(exactly = 1) { cancelRelaAppointment.cancelAppointment("ORDER456") }
        verify(exactly = 1) { createRelaAppointment.createAppointment(relaRequest1) }
        verify(exactly = 1) { createRelaAppointment.createAppointment(relaRequest2) }
        verify(exactly = 0) { tireChangeEntryUpdateService.updateRelaOrderNumber(tireChangeEntry1, any()) }
        verify(exactly = 1) { tireChangeEntryUpdateService.updateRelaOrderNumber(tireChangeEntry2, "NEW_ORDER456") }
    }
}
