package com.fleetmanagement.modules.vehicleperson.objectmothers

import com.fleetmanagement.modules.vehicleperson.api.dtos.VehiclePersonDetail
import kotlin.random.Random

class VehiclePersonDetailsBuilder {
    private var firstName = createRandomString(10)
    private var lastName = createRandomString(10)
    private var street = createRandomString(10)
    private var postalCode = createRandomString(10)
    private var city = createRandomString(10)
    private var country = createRandomString(10)
    private var companyEmail = createRandomString(10)
    private var privateEmail = createRandomString(10)
    private var accountingArea = createRandomString(10)
    private var businessPartnerId = createRandomString(10)
    private var employeeNumber = createRandomString(10)
    private var department = createRandomString(10)
    private var costCenter = createRandomString(10)
    private var leasingPrivilegeLeasing: String? = null

    fun employeeNumber(employeeNumber: String): VehiclePersonDetailsBuilder {
        this.employeeNumber = employeeNumber
        return this
    }

    fun lastName(lastName: String): VehiclePersonDetailsBuilder {
        this.lastName = lastName
        return this
    }

    fun firstName(firstName: String): VehiclePersonDetailsBuilder {
        this.firstName = firstName
        return this
    }

    fun companyEmail(companyEmail: String): VehiclePersonDetailsBuilder {
        this.companyEmail = companyEmail
        return this
    }

    fun privateEmail(privateEmail: String): VehiclePersonDetailsBuilder {
        this.privateEmail = privateEmail
        return this
    }

    fun department(department: String): VehiclePersonDetailsBuilder {
        this.department = department
        return this
    }

    fun costCenter(costCenter: String): VehiclePersonDetailsBuilder {
        this.costCenter = costCenter
        return this
    }

    fun leasingPrivilegeLeasing(leasingPrivilegeLeasing: String?) =
        apply {
            this.leasingPrivilegeLeasing = leasingPrivilegeLeasing
        }

    fun build(): VehiclePersonDetail =
        VehiclePersonDetail(
            firstName = firstName,
            lastName = lastName,
            street = street,
            postalCode = postalCode,
            city = city,
            country = country,
            companyEmail = companyEmail,
            privateEmail = privateEmail,
            accountingArea = accountingArea,
            businessPartnerId = businessPartnerId,
            employeeNumber = employeeNumber,
            department = department,
            costCenter = costCenter,
            leasingPrivilegeLeasing = leasingPrivilegeLeasing,
        )
}

private val charPool = ('a'..'z') + ('A'..'Z') + ('0'..'9')

private fun createRandomString(length: Int): String =
    (1..length)
        .map {
            Random.nextInt(0, charPool.size)
        }.map(charPool::get)
        .joinToString("")

object VehiclePersonDetailsObjectMother {
    fun vehiclePersonDetails(
        firstName: String,
        lastName: String,
        street: String? = null,
        postalCode: String? = null,
        city: String? = null,
        country: String? = null,
    ) = VehiclePersonDetail(
        firstName = firstName,
        lastName = lastName,
        street = street,
        postalCode = postalCode,
        city = city,
        country = country,
        companyEmail = "someemail",
        accountingArea = "accounting-area",
        businessPartnerId = "12345",
        employeeNumber = "451234",
    )
}
