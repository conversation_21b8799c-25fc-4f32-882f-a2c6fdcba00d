package com.fleetmanagement.modules.tiremanagement

import com.fleetmanagement.integrations.mailclient.application.port.EmailDto
import com.fleetmanagement.integrations.mailclient.application.port.EmailOutPort
import com.fleetmanagement.modules.tiremanagement.features.dataexport.csv.TireManagementDataExportCSV
import com.fleetmanagement.modules.tiremanagement.features.dataexport.csv.TireManagementDataExportCSVRow
import com.fleetmanagement.modules.tiremanagement.features.dataexport.mailclient.TireManagementDataExportMailClient
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test

class TireManagementDataExportMailClientTest {
    @Test
    fun `should send encrypted email with csv attachment`() {
        // Given
        val emailOutPort = mockk<EmailOutPort>(relaxed = true)
        val emailConfiguration =
            TireManagementEmailConfigurationProperties(
                recipient = "<EMAIL>",
                sender = "<EMAIL>",
            )
        val service = TireManagementDataExportMailClient(emailOutPort, emailConfiguration)

        val csvData =
            TireManagementDataExportCSV().also {
                it.addRow(
                    TireManagementDataExportCSVRow(
                        "vin",
                        "licencePlate",
                        "personLastName",
                        "personFirstName",
                        "personBusinessEmail",
                        "personPrivateEmail",
                        "personDepartment",
                        "costCenterCarrier",
                        "vehicleDataOrderType",
                        "vehicleDataModel",
                        "optionCodeRims",
                        "optionDescriptionRims",
                        "optionCodeCentralLocking",
                        "optionDescriptionCentralLocking",
                        "optionCodeBrakingDiscsFront",
                        "optionDescriptionBrakingDiscsFront",
                    ),
                )
            }

        // When
        service.sendEmail(csvData)

        // Then
        val emailSlot = slot<EmailDto>()
        verify { emailOutPort.sendEmailEncrypted(capture(emailSlot)) }

        val capturedEmail = emailSlot.captured
        assertTrue(capturedEmail.subject.endsWith("_Stammdaten_Porsche_VIW.csv"))
        assertEquals("Anbei erhalten Sie den Export der Daten für das Reifenmanagement.", capturedEmail.htmlBody)
        assertEquals(emailConfiguration.sender, capturedEmail.senderMailAddress.address)
        assertEquals(emailConfiguration.recipient, capturedEmail.recipientsMailAddressInTo[0].address)
        assertEquals(1, capturedEmail.attachment.size)
        assertTrue(capturedEmail.attachment[0].name.endsWith("_Stammdaten_Porsche_VIW.csv"))
    }
}
