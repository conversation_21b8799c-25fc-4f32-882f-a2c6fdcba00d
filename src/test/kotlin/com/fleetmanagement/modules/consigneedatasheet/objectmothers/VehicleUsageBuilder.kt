/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
@file:Suppress("ktlint:standard:no-wildcard-imports")

package com.fleetmanagement.modules.consigneedatasheet.objectmothers

import com.fleetmanagement.modules.consigneedatasheet.application.VehicleUsageDto
import com.fleetmanagement.modules.consigneedatasheet.application.VehicleUsageNewOrUpdate
import com.fleetmanagement.modules.consigneedatasheet.domain.VehicleUsage
import java.util.*
import kotlin.random.Random

class VehicleUsageBuilder {
    fun defaults() =
        setOf(
            VehicleUsage(1, "A"),
            VehicleUsage(2, "B"),
            VehicleUsage(3, "C"),
            VehicleUsage(4, "D"),
            VehicleUsage(5, "E"),
            VehicleUsage(6, "F"),
        )

    private var usage: String = createRandomString(Random.nextInt(4, 23))

    fun usage(usage: String) = apply { this.usage = usage }

    fun build(): VehicleUsage =
        VehicleUsage(
            usage = usage,
            usageId = Random.nextLong(),
        )

    companion object {
        fun buildSingle() = VehicleUsageBuilder().build()

        fun buildMultiple(count: Int): Set<VehicleUsage> = (1..count).map { VehicleUsageBuilder().build() }.toSet()
    }
}

class VehicleUsageNewBuilder {
    fun build(): VehicleUsageNewOrUpdate =
        VehicleUsageNewOrUpdate(
            usage = createRandomString(Random.nextInt(4, 23)),
            usageId = Random.nextLong(),
        )

    companion object {
        fun buildSingle() = VehicleUsageNewBuilder().build()

        fun buildMultiple(count: Int): Set<VehicleUsageNewOrUpdate> = (1..count).map { VehicleUsageNewBuilder().build() }.toSet()
    }
}

class VehicleUsageDtoBuilder {
    private var usage: String = createRandomString(Random.nextInt(4, 23))

    fun usage(usage: String) = apply { this.usage = usage }

    fun build(): VehicleUsageDto =
        VehicleUsageDto(
            id = UUID.randomUUID(),
            version = Random.nextInt(1, 10),
            usage = usage,
            usageId = Random.nextLong(),
        )

    companion object {
        fun buildSingle() = VehicleUsageDtoBuilder().build()

        fun buildMultiple(count: Int): Set<VehicleUsageDto> = (1..count).map { VehicleUsageDtoBuilder().build() }.toSet()
    }
}

val charPool = ('a'..'z') + ('A'..'Z') + ('0'..'9')

fun createRandomString(length: Int): String = (1..length).map { Random.nextInt(0, charPool.size) }.map(charPool::get).joinToString("")
