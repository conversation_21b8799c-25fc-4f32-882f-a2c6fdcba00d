/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehicletransfer.adapter.incoming.rest

import ActiveVehicleTransferNotFoundException
import com.fleetmanagement.modules.consigneedatasheet.application.CostCenterFinder
import com.fleetmanagement.modules.consigneedatasheet.application.port.vehicleusage.ReadVehicleUsageUseCase
import com.fleetmanagement.modules.consigneedatasheet.domain.EmployeeNumber
import com.fleetmanagement.modules.consigneedatasheet.objectmothers.CostCenterBuilder
import com.fleetmanagement.modules.consigneedatasheet.objectmothers.VehicleUsageBuilder
import com.fleetmanagement.modules.consigneedatasheet.objectmothers.VehicleUsageDtoBuilder
import com.fleetmanagement.modules.vehicledata.api.ReadMileageReadings
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByVIN
import com.fleetmanagement.modules.vehicledata.api.domain.VehicleSource
import com.fleetmanagement.modules.vehicledata.builders.vehicledto.VehicleDTOBuilder
import com.fleetmanagement.modules.vehicletransfer.VehicleTransferBuilder
import com.fleetmanagement.modules.vehicletransfer.VehicleTransferChangeVehicleResponsiblePersonDtoBuilder
import com.fleetmanagement.modules.vehicletransfer.VehicleTransferCreateAndDeliverDtoBuilder
import com.fleetmanagement.modules.vehicletransfer.VehicleTransferReturnDtoBuilder
import com.fleetmanagement.modules.vehicletransfer.application.VehicleTransferFinder
import com.fleetmanagement.modules.vehicletransfer.application.port.*
import com.fleetmanagement.modules.vehicletransfer.domain.entities.CostCenterId
import com.fleetmanagement.modules.vehicletransfer.domain.entities.VehicleUsageId
import com.fleetmanagement.modules.vtstamm.adapter.incoming.rest.model.VehicleUsageDto
import io.mockk.*
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.time.OffsetDateTime
import java.util.*
import kotlin.jvm.optionals.getOrNull

class VTStammVehicleTransferRestAdapterTest {
    private val createAndDeliverPlannedVehicleTransferUseCase: CreateAndDeliverPlannedVehicleTransferUseCase =
        mockk<CreateAndDeliverPlannedVehicleTransferUseCase>()
    private val returnVehicleTransferUseCase: ReturnVehicleTransferUseCase = mockk<ReturnVehicleTransferUseCase>()
    private val changeVehicleResponsiblePersonUseCase: ChangeVehicleResponsiblePersonUseCase =
        mockk<ChangeVehicleResponsiblePersonUseCase>()
    private val vehicleTransferFinder: VehicleTransferFinder = mockk<VehicleTransferFinder>()
    private val readVehicleByVIN: ReadVehicleByVIN = mockk<ReadVehicleByVIN>()
    private val readVehicleUsageUseCase: ReadVehicleUsageUseCase = mockk<ReadVehicleUsageUseCase>()
    private val costCenterFinder: CostCenterFinder = mockk<CostCenterFinder>()
    private val readMileageReadings: ReadMileageReadings = mockk<ReadMileageReadings>()

    private val vtStammVehicleTransferRestAdapter: VTStammVehicleTransferRestAdapter =
        VTStammVehicleTransferRestAdapter(
            createAndDeliverPlannedVehicleTransferUseCase = createAndDeliverPlannedVehicleTransferUseCase,
            returnVehicleTransferUseCase = returnVehicleTransferUseCase,
            changeVehicleResponsiblePersonUseCase = changeVehicleResponsiblePersonUseCase,
            vehicleTransferFinder = vehicleTransferFinder,
            readVehicleByVIN = readVehicleByVIN,
            readVehicleUsageUseCase = readVehicleUsageUseCase,
            costCenterFinder = costCenterFinder,
            readMileageReadings = readMileageReadings,
        )

    val vin = "vtstammVin"
    val vehicle = VehicleDTOBuilder().source(VehicleSource.VTSTAMM).vin(vin).build()
    val activeVehicleTransfer = VehicleTransferBuilder().build()
    val depreciationRelevantCostCenter = CostCenterBuilder.buildSingle()
    val vehicleUsage = VehicleUsageBuilder().usage(VehicleUsageDto.entries.random().value).build()
    val vehicleUsages = VehicleUsageDto.entries.map { VehicleUsageDtoBuilder().usage(it.value).build() }

    @BeforeEach
    fun setup() {
        every { readVehicleByVIN.readVehicleByVIN(any()) } returns vehicle
        every { vehicleTransferFinder.findActiveVehicleTransferForVehicle(any()) } returns activeVehicleTransfer
        every { costCenterFinder.getCostCenter(any()) } returns depreciationRelevantCostCenter
        every { readVehicleUsageUseCase.findVehicleUsageById(any()) } returns vehicleUsage
        every { readVehicleUsageUseCase.readAllVehicleUsage() } returns vehicleUsages
    }

    @Test
    fun `should call changeVehicleResponsiblePersonUseCase when changing vehicle responsible person`() {
        val vehicleTransferChangeVehicleResponsiblePersonDto =
            VehicleTransferChangeVehicleResponsiblePersonDtoBuilder.buildSingle()
        val vehicleIdSlot = slot<UUID>()
        val vinSlot = slot<String>()
        val returnCommentSlot = slot<String>()
        val deliveryCommentSlot = slot<String>()
        val mileageAtReturnSlot = slot<Int>()
        val vehicleResponsiblePersonSlot = slot<EmployeeNumber>()
        every {
            changeVehicleResponsiblePersonUseCase.changeResponsiblePerson(
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
            )
        } returns
            VehicleTransferBuilder()
                .vehicleUsageId(VehicleUsageId(vehicleUsage.id.value))
                .depreciationRelevantCostCenterId(CostCenterId(depreciationRelevantCostCenter.id.value))
                .build()
        val deliveredVehicleTransferDto =
            vtStammVehicleTransferRestAdapter.changeVehicleResponsiblePerson(
                vin = vin,
                vehicleTransferChangeVehicleResponsiblePersonDto = vehicleTransferChangeVehicleResponsiblePersonDto,
            )

        verify(exactly = 1) {
            changeVehicleResponsiblePersonUseCase.changeResponsiblePerson(
                vehicleId = capture(vehicleIdSlot),
                vin = capture(vinSlot),
                returnComment = capture(returnCommentSlot),
                latestMileage = capture(mileageAtReturnSlot),
                nextProcess = any(),
                deliveryComment = capture(deliveryCommentSlot),
                vehicleResponsiblePerson = capture(vehicleResponsiblePersonSlot),
            )
        }
        assertEquals(vehicle.id, vehicleIdSlot.captured)
        assertEquals(vin, vinSlot.captured)
        assertEquals(
            vehicleTransferChangeVehicleResponsiblePersonDto.returnComment?.getOrNull(),
            returnCommentSlot.captured,
        )
        assertEquals(
            vehicleTransferChangeVehicleResponsiblePersonDto.mileageAtReturn?.getOrNull(),
            mileageAtReturnSlot.captured,
        )
        assertEquals(
            vehicleTransferChangeVehicleResponsiblePersonDto.deliveryComment?.getOrNull(),
            deliveryCommentSlot.captured,
        )
        assertEquals(
            vehicleTransferChangeVehicleResponsiblePersonDto.vehicleResponsiblePerson,
            vehicleResponsiblePersonSlot.captured.value,
        )
        assertEquals(vehicleUsage.usage, deliveredVehicleTransferDto.vehicleUsage?.getOrNull()?.value)
        assertEquals(
            depreciationRelevantCostCenter.description.value,
            deliveredVehicleTransferDto.depreciationRelevantCostCenter?.getOrNull(),
        )
    }

    @Suppress("ktlint:standard:max-line-length")
    @Test
    fun `should throw MileageAtReturnMissing when no mileage is provided and none is maintained when changing vehicle responsible person`() {
        val vehicleTransferChangeVehicleResponsiblePersonDto =
            VehicleTransferChangeVehicleResponsiblePersonDtoBuilder().mileageAtReturn(null).build()
        every { readMileageReadings.getLatestMileageReading(any()) } returns null

        assertThrows<MileageAtReturnMissing> {
            vtStammVehicleTransferRestAdapter.changeVehicleResponsiblePerson(
                vin = vin,
                vehicleTransferChangeVehicleResponsiblePersonDto = vehicleTransferChangeVehicleResponsiblePersonDto,
            )
        }
    }

    @Test
    fun `should call createAndDeliverPlannedVehicleTransfer when creating and delivering vehicle transfer`() {
        val vehicleTransferCreateAndDeliverDto = VehicleTransferCreateAndDeliverDtoBuilder.buildSingle()
        val createAndDeliverPlannedVehicleTransferDtoSlot = slot<CreateAndDeliverPlannedVehicleTransferDto>()
        every {
            createAndDeliverPlannedVehicleTransferUseCase.createAndDeliverPlannedVehicleTransfer(
                any(),
            )
        } returns
            VehicleTransferBuilder()
                .vehicleUsageId(VehicleUsageId(vehicleUsage.id.value))
                .depreciationRelevantCostCenterId(CostCenterId(depreciationRelevantCostCenter.id.value))
                .build()

        val deliveredVehicleTransferDto =
            vtStammVehicleTransferRestAdapter.createAndDeliverVehicleTransfer(
                vehicleTransferCreateAndDeliverDto = vehicleTransferCreateAndDeliverDto,
            )

        verify(exactly = 1) {
            createAndDeliverPlannedVehicleTransferUseCase.createAndDeliverPlannedVehicleTransfer(
                createAndDeliverPlannedVehicleTransferDto = capture(createAndDeliverPlannedVehicleTransferDtoSlot),
            )
        }
        val capturedDto = createAndDeliverPlannedVehicleTransferDtoSlot.captured
        assertEquals(vehicle.id, capturedDto.createPlannedVehicleTransferDto.vehicleId)
        assertEquals(
            vehicleTransferCreateAndDeliverDto.deliveryComment?.getOrNull(),
            capturedDto.deliverPlannedVehicleTransferDto.deliveryComment,
        )
        assertEquals(
            vehicleTransferCreateAndDeliverDto.deliveryDate,
            capturedDto.deliverPlannedVehicleTransferDto.deliveryDate,
        )
        assertEquals(
            vehicleTransferCreateAndDeliverDto.mileageAtDelivery,
            capturedDto.deliverPlannedVehicleTransferDto.mileageAtDelivery,
        )
        assertEquals(
            vehicleTransferCreateAndDeliverDto.vehicleResponsiblePerson,
            capturedDto.deliverPlannedVehicleTransferDto.vehicleResponsiblePerson,
        )
        assertEquals(vehicleUsage.usage, deliveredVehicleTransferDto.vehicleUsage?.getOrNull()?.value)
        assertEquals(
            depreciationRelevantCostCenter.description.value,
            deliveredVehicleTransferDto.depreciationRelevantCostCenter?.getOrNull(),
        )
    }

    @Test
    fun `should call returnVehicleTransfer when returning vehicle transfer`() {
        val vehicleTransferReturnDto = VehicleTransferReturnDtoBuilder.buildSingle()
        val mileageAtReturnSlot = slot<Int>()
        val vehicleTransferKeySlot =
            slot<VehicleTransferKey>()
        val returnCommentSlot = slot<String>()
        val returnDateSlot = slot<OffsetDateTime>()
        every {
            returnVehicleTransferUseCase.returnVehicleTransfer(
                vehicleTransferKey = any(),
                mileageAtReturn = any(),
                returnComment = any(),
                returnDate = any(),
            )
        } just Runs
        every { vehicleTransferFinder.getVehicleTransfer(any()) } returns
            VehicleTransferBuilder()
                .returned()
                .vehicleUsageId(VehicleUsageId(vehicleUsage.id.value))
                .depreciationRelevantCostCenterId(CostCenterId(depreciationRelevantCostCenter.id.value))
                .build()

        val returnedVehicleTransfer =
            vtStammVehicleTransferRestAdapter.returnVehicleTransfer(
                vin = "someVin",
                vehicleTransferReturnDto = vehicleTransferReturnDto,
            )

        verify(exactly = 1) {
            returnVehicleTransferUseCase.returnVehicleTransfer(
                vehicleTransferKey = capture(vehicleTransferKeySlot),
                mileageAtReturn = capture(mileageAtReturnSlot),
                returnComment = capture(returnCommentSlot),
                returnDate = capture(returnDateSlot),
            )
        }
        assertEquals(activeVehicleTransfer.key.value, vehicleTransferKeySlot.captured.value)
        assertEquals(vehicleTransferReturnDto.mileageAtReturn?.getOrNull(), mileageAtReturnSlot.captured)
        assertEquals(vehicleTransferReturnDto.returnComment?.getOrNull(), returnCommentSlot.captured)
        assertEquals(vehicleTransferReturnDto.returnDate, returnDateSlot.captured)
        assertEquals(vehicleUsage.usage, returnedVehicleTransfer.vehicleUsage?.getOrNull()?.value)
        assertEquals(
            depreciationRelevantCostCenter.description.value,
            returnedVehicleTransfer.depreciationRelevantCostCenter?.getOrNull(),
        )
    }

    @Suppress("ktlint:standard:max-line-length")
    @Test
    fun `should throw ActiveVehicleTransferNotFoundException when no active vehicle transfer can be found when returning vehicle transfer`() {
        val vehicleTransferReturnDto = VehicleTransferReturnDtoBuilder.buildSingle()
        every { vehicleTransferFinder.findActiveVehicleTransferForVehicle(any()) } returns null

        assertThrows<ActiveVehicleTransferNotFoundException> {
            vtStammVehicleTransferRestAdapter.returnVehicleTransfer(
                vin = "someVin",
                vehicleTransferReturnDto = vehicleTransferReturnDto,
            )
        }
    }

    @Suppress("ktlint:standard:max-line-length")
    @Test
    fun `should throw MileageAtReturnMissing when no mileage is provided and none is maintained when returning vehicle transfer`() {
        val vehicleTransferReturnDto = VehicleTransferReturnDtoBuilder().mileageAtReturn(null).build()
        every { readMileageReadings.getLatestMileageReading(any()) } returns null

        assertThrows<MileageAtReturnMissing> {
            vtStammVehicleTransferRestAdapter.returnVehicleTransfer(
                vin = "someVin",
                vehicleTransferReturnDto = vehicleTransferReturnDto,
            )
        }
    }
}
