/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehicletransfer.adapter.incoming.rest

import ActiveVehicleTransferNotFoundException
import CurrentVehicleTransferNotFoundException
import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.ObjectMapper
import com.fleetmanagement.emhshared.domain.ViolationType
import com.fleetmanagement.modules.vehicledata.api.VehicleNotFoundInFVM
import com.fleetmanagement.modules.vehicledata.api.exceptions.VehicleUpdateException
import com.fleetmanagement.modules.vehicledata.repository.entities.VehicleReturnInfoUpdateException
import com.fleetmanagement.modules.vehicletransfer.VehicleTransferChangeVehicleResponsiblePersonDtoBuilder
import com.fleetmanagement.modules.vehicletransfer.VehicleTransferCreateAndDeliverDtoBuilder
import com.fleetmanagement.modules.vehicletransfer.VehicleTransferDtoBuilder
import com.fleetmanagement.modules.vehicletransfer.VehicleTransferReturnDtoBuilder
import com.fleetmanagement.modules.vehicletransfer.domain.VehicleTransferReturnException
import com.fleetmanagement.modules.vtstamm.adapter.incoming.rest.VtstammVehicleTransferController
import com.fleetmanagement.modules.vtstamm.adapter.incoming.rest.model.ErrorDto
import com.fleetmanagement.modules.vtstamm.adapter.incoming.rest.model.ErrorTypeDto
import com.fleetmanagement.security.configurations.SecurityConfiguration
import com.fleetmanagement.security.features.accesscontrol.services.PrivilegeService
import com.fleetmanagement.security.features.tokenvalidation.JwtValidator
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.context.annotation.Import
import org.springframework.http.MediaType
import org.springframework.test.context.TestPropertySource
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders
import org.springframework.test.web.servlet.result.MockMvcResultMatchers
import java.nio.charset.StandardCharsets

@WebMvcTest(controllers = [VtstammVehicleTransferController::class])
@TestPropertySource(properties = ["security.enabled=true"])
@Import(SecurityConfiguration::class)
class VTStammVehicleTransferResourceTest {
    @MockkBean
    private lateinit var vtStammVehicleTransferRestAdapter: VTStammVehicleTransferRestAdapter

    @Autowired
    private lateinit var objectMapper: ObjectMapper

    @Autowired
    private lateinit var mockMvc: MockMvc

    @MockkBean(name = "apiGatewayTokenValidator")
    private lateinit var apiGatewayTokenValidator: JwtValidator

    @MockkBean
    private lateinit var privilegeService: PrivilegeService

    @MockkBean(name = "albTokenValidator")
    private lateinit var albTokenValidator: JwtValidator

    @BeforeEach
    fun setupPrivileges() {
        every { apiGatewayTokenValidator.validate(any()) } returns Unit
        every { apiGatewayTokenValidator.canValidate(any()) } returns true
    }

    @Test
    fun `should return 200 when successfully changing vehicle responsible person`() {
        val vehicleTransfer = VehicleTransferDtoBuilder.buildSingle()
        every { vtStammVehicleTransferRestAdapter.changeVehicleResponsiblePerson(any(), any()) } returns vehicleTransfer
        val payload = VehicleTransferChangeVehicleResponsiblePersonDtoBuilder.buildSingle()

        mockMvc
            .perform(
                MockMvcRequestBuilders
                    .post("/vtstamm/vehicletransfer/change-vehicle-responsible-person/{vin}", "someVin")
                    .content(objectMapper.writeValueAsString(payload))
                    .contentType(MediaType.APPLICATION_JSON),
            ).andExpect(MockMvcResultMatchers.status().`is`(200))
    }

    @Test
    fun `should return 200 when successfully rerturning vehicle transfer`() {
        val vehicleTransfer = VehicleTransferDtoBuilder.buildSingle()
        every { vtStammVehicleTransferRestAdapter.returnVehicleTransfer(any(), any()) } returns vehicleTransfer
        val payload = VehicleTransferReturnDtoBuilder.buildSingle()

        mockMvc
            .perform(
                MockMvcRequestBuilders
                    .post("/vtstamm/vehicletransfer/return/{vin}", "someVin")
                    .content(objectMapper.writeValueAsString(payload))
                    .contentType(MediaType.APPLICATION_JSON),
            ).andExpect(MockMvcResultMatchers.status().`is`(200))
    }

    @Test
    fun `should return 201 when successfully creating and delivering new vehicle transfer`() {
        val vehicleTransfer = VehicleTransferDtoBuilder.buildSingle()
        every { vtStammVehicleTransferRestAdapter.createAndDeliverVehicleTransfer(any()) } returns vehicleTransfer
        val payload = VehicleTransferCreateAndDeliverDtoBuilder.buildSingle()

        mockMvc
            .perform(
                MockMvcRequestBuilders
                    .post("/vtstamm/vehicletransfer")
                    .content(objectMapper.writeValueAsString(payload))
                    .contentType(MediaType.APPLICATION_JSON),
            ).andExpect(MockMvcResultMatchers.status().`is`(201))
    }

    @Test
    fun `should return error dto on ActiveVehicleTransferNotFoundException`() {
        every {
            vtStammVehicleTransferRestAdapter.changeVehicleResponsiblePerson(any(), any())
        } throws ActiveVehicleTransferNotFoundException("someVin")
        val payload = VehicleTransferChangeVehicleResponsiblePersonDtoBuilder.buildSingle()

        val response =
            mockMvc
                .perform(
                    MockMvcRequestBuilders
                        .post("/vtstamm/vehicletransfer/change-vehicle-responsible-person/{vin}", "someVin")
                        .content(objectMapper.writeValueAsString(payload))
                        .contentType(MediaType.APPLICATION_JSON),
                ).andExpect(MockMvcResultMatchers.status().isNotFound)
                .andReturn()
                .response

        val errorDto =
            objectMapper.readValue(
                response.getContentAsString(StandardCharsets.UTF_8),
                object : TypeReference<ErrorDto>() {},
            )

        assertEquals(ErrorTypeDto.BUSINESS, errorDto.type)
        assertEquals("Could not find ACTIVE VehicleTransfer for vehicle with VIN [someVin].", errorDto.message)
    }

    @Test
    fun `should return error dto on CurrentVehicleTransferNotFoundException`() {
        every {
            vtStammVehicleTransferRestAdapter.changeVehicleResponsiblePerson(any(), any())
        } throws CurrentVehicleTransferNotFoundException("someVin")
        val payload = VehicleTransferChangeVehicleResponsiblePersonDtoBuilder.buildSingle()

        val response =
            mockMvc
                .perform(
                    MockMvcRequestBuilders
                        .post("/vtstamm/vehicletransfer/change-vehicle-responsible-person/{vin}", "someVin")
                        .content(objectMapper.writeValueAsString(payload))
                        .contentType(MediaType.APPLICATION_JSON),
                ).andExpect(MockMvcResultMatchers.status().isNotFound)
                .andReturn()
                .response

        val errorDto =
            objectMapper.readValue(
                response.getContentAsString(StandardCharsets.UTF_8),
                object : TypeReference<ErrorDto>() {},
            )

        assertEquals(ErrorTypeDto.BUSINESS, errorDto.type)
        assertEquals("Could not determine current VehicleTransfer for vehicle with VIN [someVin].", errorDto.message)
    }

    @Test
    fun `should return error dto on MileageAtReturnMissing`() {
        every {
            vtStammVehicleTransferRestAdapter.changeVehicleResponsiblePerson(any(), any())
        } throws MileageAtReturnMissing("someVin")
        val payload = VehicleTransferChangeVehicleResponsiblePersonDtoBuilder.buildSingle()

        val response =
            mockMvc
                .perform(
                    MockMvcRequestBuilders
                        .post("/vtstamm/vehicletransfer/change-vehicle-responsible-person/{vin}", "someVin")
                        .content(objectMapper.writeValueAsString(payload))
                        .contentType(MediaType.APPLICATION_JSON),
                ).andExpect(MockMvcResultMatchers.status().isConflict)
                .andReturn()
                .response

        val errorDto =
            objectMapper.readValue(
                response.getContentAsString(StandardCharsets.UTF_8),
                object : TypeReference<ErrorDto>() {},
            )

        assertEquals(ErrorTypeDto.BUSINESS, errorDto.type)
        assertEquals(
            "Could not perform return for vehicle with VIN [someVin]. " +
                "MileageAtReturn is either not provided or latest mileage could not be obtained by the system.",
            errorDto.message,
        )
    }

    @Test
    fun `should return error dto on VehicleNotFoundInFVM`() {
        every {
            vtStammVehicleTransferRestAdapter.changeVehicleResponsiblePerson(any(), any())
        } throws VehicleNotFoundInFVM(identifierType = "someVin", identifier = "something")
        val payload = VehicleTransferChangeVehicleResponsiblePersonDtoBuilder.buildSingle()

        val response =
            mockMvc
                .perform(
                    MockMvcRequestBuilders
                        .post("/vtstamm/vehicletransfer/change-vehicle-responsible-person/{vin}", "someVin")
                        .content(objectMapper.writeValueAsString(payload))
                        .contentType(MediaType.APPLICATION_JSON),
                ).andExpect(MockMvcResultMatchers.status().isNotFound)
                .andReturn()
                .response

        val errorDto =
            objectMapper.readValue(
                response.getContentAsString(StandardCharsets.UTF_8),
                object : TypeReference<ErrorDto>() {},
            )

        assertEquals(ErrorTypeDto.BUSINESS, errorDto.type)
        assertEquals(
            "Vehicle with someVin=something not found in FVM",
            errorDto.message,
        )
    }

    @Test
    fun `should return error dto on IllegalArgumentException`() {
        every {
            vtStammVehicleTransferRestAdapter.changeVehicleResponsiblePerson(any(), any())
        } throws IllegalArgumentException()
        val payload = VehicleTransferChangeVehicleResponsiblePersonDtoBuilder.buildSingle()

        val response =
            mockMvc
                .perform(
                    MockMvcRequestBuilders
                        .post("/vtstamm/vehicletransfer/change-vehicle-responsible-person/{vin}", "someVin")
                        .content(objectMapper.writeValueAsString(payload))
                        .contentType(MediaType.APPLICATION_JSON),
                ).andExpect(MockMvcResultMatchers.status().isBadRequest)
                .andReturn()
                .response

        val errorDto =
            objectMapper.readValue(
                response.getContentAsString(StandardCharsets.UTF_8),
                object : TypeReference<ErrorDto>() {},
            )

        assertEquals(ErrorTypeDto.BUSINESS, errorDto.type)
        assertEquals(
            "Error while trying to service request. Illegal argument provided.",
            errorDto.message,
        )
    }

    @Test
    fun `should return error dto on VehicleTransferReturnException`() {
        every {
            vtStammVehicleTransferRestAdapter.changeVehicleResponsiblePerson(any(), any())
        } throws VehicleTransferReturnException(vehicleTransferKey = 23, missingProperties = emptyList())
        val payload = VehicleTransferChangeVehicleResponsiblePersonDtoBuilder.buildSingle()

        val response =
            mockMvc
                .perform(
                    MockMvcRequestBuilders
                        .post("/vtstamm/vehicletransfer/change-vehicle-responsible-person/{vin}", "someVin")
                        .content(objectMapper.writeValueAsString(payload))
                        .contentType(MediaType.APPLICATION_JSON),
                ).andExpect(MockMvcResultMatchers.status().isBadRequest)
                .andReturn()
                .response

        val errorDto =
            objectMapper.readValue(
                response.getContentAsString(StandardCharsets.UTF_8),
                object : TypeReference<ErrorDto>() {},
            )

        assertEquals(ErrorTypeDto.BUSINESS, errorDto.type)
        assertEquals(
            "VehicleTransfer with key [23] missing required properties [].",
            errorDto.message,
        )
    }

    @Test
    fun `should return error dto on VehicleUpdateException`() {
        every {
            vtStammVehicleTransferRestAdapter.changeVehicleResponsiblePerson(any(), any())
        } throws VehicleUpdateException(message = "you broke it", constraintViolation = null)
        val payload = VehicleTransferChangeVehicleResponsiblePersonDtoBuilder.buildSingle()

        val response =
            mockMvc
                .perform(
                    MockMvcRequestBuilders
                        .post("/vtstamm/vehicletransfer/change-vehicle-responsible-person/{vin}", "someVin")
                        .content(objectMapper.writeValueAsString(payload))
                        .contentType(MediaType.APPLICATION_JSON),
                ).andExpect(MockMvcResultMatchers.status().isConflict)
                .andReturn()
                .response

        val errorDto =
            objectMapper.readValue(
                response.getContentAsString(StandardCharsets.UTF_8),
                object : TypeReference<ErrorDto>() {},
            )

        assertEquals(ErrorTypeDto.BUSINESS, errorDto.type)
        assertEquals(
            "you broke it",
            errorDto.message,
        )
    }

    @Test
    fun `should return error dto on VehicleReturnInfoUpdateException`() {
        every {
            vtStammVehicleTransferRestAdapter.changeVehicleResponsiblePerson(any(), any())
        } throws
            VehicleReturnInfoUpdateException(
                message = "blocked for sale",
                violationType = ViolationType.VEHICLE_BLOCKED_FOR_SALE,
            )
        val payload = VehicleTransferChangeVehicleResponsiblePersonDtoBuilder.buildSingle()

        val response =
            mockMvc
                .perform(
                    MockMvcRequestBuilders
                        .post("/vtstamm/vehicletransfer/change-vehicle-responsible-person/{vin}", "someVin")
                        .content(objectMapper.writeValueAsString(payload))
                        .contentType(MediaType.APPLICATION_JSON),
                ).andExpect(MockMvcResultMatchers.status().isConflict)
                .andReturn()
                .response

        val errorDto =
            objectMapper.readValue(
                response.getContentAsString(StandardCharsets.UTF_8),
                object : TypeReference<ErrorDto>() {},
            )

        assertEquals(ErrorTypeDto.BUSINESS, errorDto.type)
        assertEquals(
            "blocked for sale",
            errorDto.message,
        )
    }
}
