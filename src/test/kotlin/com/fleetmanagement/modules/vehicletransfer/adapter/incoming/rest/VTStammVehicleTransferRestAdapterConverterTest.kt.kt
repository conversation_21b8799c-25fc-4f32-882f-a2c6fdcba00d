/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehicletransfer.adapter.incoming.rest

import com.fleetmanagement.modules.vehicletransfer.VehicleTransferBuilder
import com.fleetmanagement.modules.vehicletransfer.VehicleTransferCreateAndDeliverDtoBuilder
import com.fleetmanagement.modules.vehicletransfer.createRandomString
import com.fleetmanagement.modules.vehicletransfer.domain.entities.UtilizationArea
import com.fleetmanagement.modules.vehicletransfer.domain.entities.VehicleTransfer
import com.fleetmanagement.modules.vehicletransfer.domain.entities.VehicleTransferStatus
import com.fleetmanagement.modules.vtstamm.adapter.incoming.rest.model.NextProcessWithDefaultScrappingDto
import com.fleetmanagement.modules.vtstamm.adapter.incoming.rest.model.VehicleTransferCreateAndDeliverDto
import com.fleetmanagement.modules.vtstamm.adapter.incoming.rest.model.VehicleUsageDto
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import java.util.*
import java.util.stream.Stream
import kotlin.jvm.optionals.getOrNull

class VTStammVehicleTransferRestAdapterConverterTest {
    @ParameterizedTest
    @MethodSource("vehicleTransferProvider")
    fun `should convert VehicleTransfer to VehicleTransferDto`(vehicleTransfer: VehicleTransfer) {
        val vehicleUsage = VehicleUsageDto.entries.random().value
        val vin = createRandomString()
        val depreciationRelevantCostCenter = createRandomString()
        val vehicleTransferDto =
            vehicleTransfer.toVehicleTransferDto(
                vin = vin,
                vehicleUsage = vehicleUsage,
                depreciationRelevantCostCenter = depreciationRelevantCostCenter,
            )

        assertEquals(vehicleTransfer.key.value.toString(), vehicleTransferDto.key)
        assertEquals(vin, vehicleTransferDto.vin)
        assertEquals(vehicleTransfer.status.toString(), vehicleTransferDto.status.toString())
        assertEquals(vehicleUsage, vehicleTransferDto.vehicleUsage?.get()?.value)
        assertEquals(vehicleTransfer.internalContactPerson?.value, vehicleTransferDto.internalContactPerson?.get())
        assertEquals(
            vehicleTransfer.vehicleResponsiblePerson?.value,
            vehicleTransferDto.vehicleResponsiblePerson?.get(),
        )
        assertEquals(vehicleTransfer.usingCostCenter?.value, vehicleTransferDto.usingCostCenter?.getOrNull())
        assertEquals(depreciationRelevantCostCenter, vehicleTransferDto.depreciationRelevantCostCenter?.getOrNull())
        assertEquals(vehicleTransfer.internalOrderNumber, vehicleTransferDto.internalOrderNumber?.getOrNull())
        assertEquals(vehicleTransfer.maintenanceOrderNumber, vehicleTransferDto.maintenanceOrderNumber?.getOrNull())
        assertEquals(vehicleTransfer.deliveryDate, vehicleTransferDto.deliveryDate?.getOrNull())
        assertEquals(vehicleTransfer.plannedDeliveryDate, vehicleTransferDto.plannedDeliveryDate?.getOrNull())
        assertEquals(vehicleTransfer.mileageAtDelivery, vehicleTransferDto.mileageAtDelivery?.getOrNull())
        assertEquals(vehicleTransfer.deliveryComment, vehicleTransferDto.deliveryComment?.getOrNull())
        assertEquals(vehicleTransfer.returnDate, vehicleTransferDto.returnDate?.getOrNull())
        assertEquals(vehicleTransfer.plannedReturnDate, vehicleTransferDto.plannedReturnDate?.getOrNull())
        assertEquals(vehicleTransfer.latestReturnDate, vehicleTransferDto.latestReturnDate?.getOrNull())
        assertEquals(vehicleTransfer.mileageAtReturn, vehicleTransferDto.mileageAtReturn?.getOrNull())
        assertEquals(vehicleTransfer.returnComment, vehicleTransferDto.returnComment?.getOrNull())
        assertEquals(vehicleTransfer.utilizationArea?.name, vehicleTransferDto.utilizationArea?.getOrNull()?.name)
    }

    @ParameterizedTest
    @MethodSource("vehicleTransferCreateAndDeliverDtoProvider")
    fun `should convert VehicleTransferCreateAndDeliverDto to CreatePlannedVehicleTransferDto`(
        vehicleTransferCreateAndDeliverDto: VehicleTransferCreateAndDeliverDto,
    ) {
        val vehicleId = UUID.randomUUID()
        val vehicleUsageId = UUID.randomUUID()

        val createPlannedVehicleTransferDto =
            vehicleTransferCreateAndDeliverDto.toCreatePlannedVehicleTransferDto(
                vehicleId = vehicleId,
                vehicleUsageId = vehicleUsageId,
            )
        assertEquals(vehicleId, createPlannedVehicleTransferDto.vehicleId)
        assertEquals(vehicleUsageId, createPlannedVehicleTransferDto.vehicleUsageId)
        assertEquals(
            createPlannedVehicleTransferDto.vehicleResponsiblePerson,
            createPlannedVehicleTransferDto.vehicleResponsiblePerson,
        )
        assertEquals(
            createPlannedVehicleTransferDto.internalContactPerson,
            createPlannedVehicleTransferDto.internalContactPerson,
        )
        assertEquals(createPlannedVehicleTransferDto.usingCostCenter, createPlannedVehicleTransferDto.usingCostCenter)
        assertEquals(
            createPlannedVehicleTransferDto.internalOrderNumber,
            createPlannedVehicleTransferDto.internalOrderNumber,
        )
    }

    @Test
    fun `should convert VehicleTransferStatus to VehicleTransferStatusDto`() {
        val status = VehicleTransferStatus.entries.random()
        val vehicleTransferStatusDto = status.toVehicleTransferStatusDto()

        assertEquals(status.name, vehicleTransferStatusDto.name)
    }

    @Test
    fun `should convert UtilizationArea to UtilizationAreaDto`() {
        val utilizationArea = UtilizationArea.entries.random()
        val utilizationAreaDto = utilizationArea.toUtilizationAreaDto()

        assertEquals(utilizationArea.name, utilizationAreaDto.name)
    }

    @Test
    fun `should convert NextProcessWithDefaultScrappingDto to NextProcess`() {
        val nextProcessWithDefaultScrappingDto = NextProcessWithDefaultScrappingDto.entries.random()
        val nextProcess = nextProcessWithDefaultScrappingDto.toNextProcess()

        assertEquals(nextProcessWithDefaultScrappingDto.name, nextProcess.name)
    }

    companion object {
        @JvmStatic
        fun vehicleTransferCreateAndDeliverDtoProvider(): Stream<Arguments> =
            VehicleTransferCreateAndDeliverDtoBuilder
                .buildMultiple(10)
                .map { Arguments.of(it) }
                .stream()

        @JvmStatic
        fun vehicleTransferProvider(): Stream<Arguments> = VehicleTransferBuilder.buildMultiple(10).map { Arguments.of(it) }.stream()
    }
}
