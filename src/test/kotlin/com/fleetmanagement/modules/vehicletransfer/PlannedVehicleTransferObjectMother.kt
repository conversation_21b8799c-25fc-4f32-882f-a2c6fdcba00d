/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
@file:Suppress("ktlint:standard:no-wildcard-imports")

package com.fleetmanagement.modules.vehicletransfer

import com.fleetmanagement.modules.consigneedatasheet.domain.CostCenterDescription
import com.fleetmanagement.modules.consigneedatasheet.domain.EmployeeNumber
import com.fleetmanagement.modules.vehicletransfer.domain.entities.*
import com.fleetmanagement.modules.vehicletransfer.domain.service.PlannedVehicleTransferInit
import java.time.OffsetDateTime
import java.util.*
import kotlin.random.Random

class PlannedVehicleTransferBuilder {
    private var vehicleId: UUID = UUID.randomUUID()
    private var consignee: String? = createRandomString(Random.nextInt(4, 23))
    private var vehicleUsageId: VehicleUsageId? = null
    private var vehicleResponsiblePerson: EmployeeNumber? = EmployeeNumber(Random.nextInt(1, 999999).toString())
    private var internalContactPerson: EmployeeNumber = EmployeeNumber(Random.nextInt(1, 999999).toString())
    private var depreciationRelevantCostCenterId: CostCenterId? = null
    private var usingCostCenter: CostCenterDescription? = CostCenterDescription("00H0015567")
    private var internalOrderNumber: String = createRandomString(Random.nextInt(4, 23))
    private var usageGroupId: UsageGroupId? = null
    private var businessPartnerId: String? = createRandomString(Random.nextInt(4, 23))
    private var vehicleTransferKey: VehicleTransferKey =
        VehicleTransferKey(Random.nextLong(1, 999999))
    private var initializePayload: PlannedVehicleTransferInit? = null
    private var predecessor: VehicleTransfer? = null
    private var predecessorLatestReturnDate: OffsetDateTime? = null
    private var licensePlate: String? = createRandomString(Random.nextInt(4, 23))
    private var leasingPrivilege: String? = createRandomString(Random.nextInt(4, 23))
    private var desiredDeliveryDate: OffsetDateTime? = OffsetDateTime.now()
    private var utilizationArea: UtilizationArea? = UtilizationArea.entries.random()
    private var deliveryLeipzig: Boolean? = null
    private var serviceCards: List<ServiceCard> = listOf(ServiceCard.entries.random())
    private var registrationNeeded: Boolean? = null
    private var provisionForDeliveryComment = createRandomString(Random.nextInt(4, 23))
    private var plannedDeliveryDate: OffsetDateTime? = null
    private var plannedReturnDate: OffsetDateTime? = null
    private var remark: String? = createRandomString(Random.nextInt(4, 23))
    private var desiredTireSet: TireSet? = null
    private var isCurrent: Boolean = false
    private var deleted: Boolean = false
    private var deliveryIndex: String? = createRandomString(Random.nextInt(4, 23))
    private var leasingPrivilegeValidationSuccessful: Boolean = true
    private var usageMhp = false
    private var usageVdw = false
    private var privateMonthlyKilometers: Int? = null

    fun vehicleId(vehicleId: UUID) = apply { this.vehicleId = vehicleId }

    fun vehicleTransferKey(vehicleTransferKey: Long) = apply { this.vehicleTransferKey = VehicleTransferKey(vehicleTransferKey) }

    fun consignee(consignee: String) = apply { this.consignee = consignee }

    fun vehicleUsageId(vehicleUsageId: VehicleUsageId?) = apply { this.vehicleUsageId = vehicleUsageId }

    fun leasingPrivilegeValidationSuccessful(leasingPrivilegeValidationSuccessful: Boolean) =
        apply {
            this.leasingPrivilegeValidationSuccessful =
                leasingPrivilegeValidationSuccessful
        }

    fun internalContactPerson(internalContactPerson: String) =
        apply {
            this.internalContactPerson = EmployeeNumber(internalContactPerson)
        }

    fun vehicleResponsiblePerson(employeeNumber: String?) =
        apply {
            this.vehicleResponsiblePerson =
                employeeNumber?.let { EmployeeNumber(employeeNumber) }
        }

    fun depreciationRelevantCostCenterId(costCenterId: CostCenterId) = apply { this.depreciationRelevantCostCenterId = costCenterId }

    fun usingCostCenter(usingCostCenter: CostCenterDescription?) = apply { this.usingCostCenter = usingCostCenter }

    fun usageGroupId(usageGroupId: UsageGroupId?) = apply { this.usageGroupId = usageGroupId }

    fun initialized(initializePayload: PlannedVehicleTransferInit = PlannedVehicleTransferInitBuilder.buildSingle()) =
        apply {
            this.initializePayload =
                initializePayload
        }

    fun predecessor(predecessor: VehicleTransfer?) = apply { this.predecessor = predecessor }

    fun leasingPrivilege(leasingPrivilege: String?) = apply { this.leasingPrivilege = leasingPrivilege }

    fun predecessorLatestReturnDate(date: OffsetDateTime?) = apply { this.predecessorLatestReturnDate = date }

    fun licensePlate(licensePlate: String?) = apply { this.licensePlate = licensePlate }

    fun desiredDeliveryDate(desiredDeliveryDate: OffsetDateTime?) = apply { this.desiredDeliveryDate = desiredDeliveryDate }

    fun plannedDeliveryDate(plannedDeliveryDate: OffsetDateTime?) = apply { this.plannedDeliveryDate = plannedDeliveryDate }

    fun utilizationArea(utilizationArea: UtilizationArea?) = apply { this.utilizationArea = utilizationArea }

    fun deliveryLeipzig(deliveryLeipzig: Boolean?) = apply { this.deliveryLeipzig = deliveryLeipzig }

    fun asCurrent() = apply { this.isCurrent = true }

    fun deleted() = apply { this.deleted = true }

    fun deliveryIndex(deliveryIndex: String?) = apply { this.deliveryIndex = deliveryIndex }

    fun build(): PlannedVehicleTransfer =
        PlannedVehicleTransfer(
            vehicleId = vehicleId,
            consignee = consignee,
            vehicleUsage = vehicleUsageId,
            vehicleResponsiblePerson = vehicleResponsiblePerson,
            internalContactPerson = internalContactPerson,
            usingCostCenter = usingCostCenter,
            internalOrderNumber = internalOrderNumber,
            maximumServiceLifeInMonths = Random.nextInt(1, 12),
            depreciationRelevantCostCenterId = depreciationRelevantCostCenterId,
            usageGroupId = usageGroupId,
            businessPartnerId = businessPartnerId,
            vehicleTransferKey = vehicleTransferKey,
            desiredDeliveryDate = desiredDeliveryDate,
            initialized = false,
            licensePlate = licensePlate,
            deliveryLeipzig = deliveryLeipzig,
            serviceCards = serviceCards,
            registrationNeeded = registrationNeeded,
            provisionForDeliveryComment = provisionForDeliveryComment,
            plannedDeliveryDate = plannedDeliveryDate,
            plannedReturnDate = plannedReturnDate,
            remark = remark,
            desiredTireSet = desiredTireSet,
            utilizationArea = utilizationArea,
            deliveryIndex = deliveryIndex,
            usageMhp = usageMhp,
            usageVdw = usageVdw,
            privateMonthlyKilometers = privateMonthlyKilometers,
        ).apply {
            initializePayload?.also {
                initialize(
                    maximumServiceLifeInMonths = it.maximumServiceLifeInMonths,
                    vehicleResponsiblePerson = it.vehicleResponsiblePerson,
                    vehicleUsage = it.vehicleUsageId?.let { VehicleUsageId(it) },
                    internalContactPerson = it.internalContactPerson,
                    usingCostCenter = it.usingCostCenter,
                    internalOrderNumber = it.internalOrderNumber,
                    depreciationRelevantCostCenterId = it.depreciationRelevantCostCenterId?.let { CostCenterId(it) },
                    businessPartnerId = it.businessPartnerId,
                    usageGroup = it.usageGroupId?.let { UsageGroupId(it) },
                    predecessor = it.predecessor,
                    predecessorLatestReturnDate = it.predecessor?.latestReturnDate,
                    utilizationArea = utilizationArea,
                    leasingPrivilege = leasingPrivilege,
                )
            }
            if (<EMAIL>) {
                this.setAsCurrent()
            }
            if (<EMAIL>) {
                this.delete()
            }
            this.updateLeasingPrivilegeResult(
                <EMAIL>,
                <EMAIL>,
            )
        }

    companion object {
        fun buildSingle() = PlannedVehicleTransferBuilder().build()

        fun buildMultiple(count: Int): Set<PlannedVehicleTransfer> = (1..count).map { PlannedVehicleTransferBuilder().build() }.toSet()
    }
}

class PlannedVehicleTransferInitBuilder {
    private var vehicleId: UUID = UUID.randomUUID()
    private var vehicleUsageId: UUID? = null
    private var vehicleResponsiblePerson: EmployeeNumber? = EmployeeNumber(Random.nextInt(1, 999999).toString())
    private var internalContactPerson: EmployeeNumber = EmployeeNumber(Random.nextInt(1, 999999).toString())
    private var depreciationRelevantCostCenterId: UUID? = null
    private var usingCostCenter: CostCenterDescription? = CostCenterDescription("00H0015567")
    private var internalOrderNumber: String = createRandomString(Random.nextInt(4, 23))
    private var usageGroupId: UUID? = null
    private var businessPartnerId: String? = createRandomString(Random.nextInt(4, 23))
    private var leasingPrivilege: String? = createRandomString(Random.nextInt(4, 23))
    private var vehicleTransferKey: VehicleTransferKey = VehicleTransferKey(Random.nextLong(1, 999999))
    private var predecessor: VehicleTransfer? = null

    fun vehicleId(vehicleId: UUID) = apply { this.vehicleId = vehicleId }

    fun vehicleTransferKey(vehicleTransferKey: Long) = apply { this.vehicleTransferKey = VehicleTransferKey(vehicleTransferKey) }

    fun vehicleUsageId(vehicleUsageId: UUID) = apply { this.vehicleUsageId = vehicleUsageId }

    fun vehicleResponsiblePerson(employeeNumber: String?) =
        apply {
            this.vehicleResponsiblePerson =
                employeeNumber?.let { EmployeeNumber(employeeNumber) }
        }

    fun depreciationRelevantCostCenterId(depreciationRelevantCostCenterId: UUID) =
        apply {
            this.depreciationRelevantCostCenterId =
                depreciationRelevantCostCenterId
        }

    fun usingCostCenter(usingCostCenter: CostCenterDescription) = apply { this.usingCostCenter = usingCostCenter }

    fun usageGroupId(usageGroupId: UUID?) = apply { this.usageGroupId = usageGroupId }

    fun predecessor(predecessor: VehicleTransfer?) = apply { this.predecessor = predecessor }

    fun build(): PlannedVehicleTransferInit =
        PlannedVehicleTransferInit(
            vehicleUsageId = vehicleUsageId,
            vehicleResponsiblePerson = vehicleResponsiblePerson,
            internalContactPerson = internalContactPerson,
            usingCostCenter = usingCostCenter,
            internalOrderNumber = internalOrderNumber,
            maximumServiceLifeInMonths = Random.nextInt(1, 12),
            depreciationRelevantCostCenterId = depreciationRelevantCostCenterId,
            usageGroupId = usageGroupId,
            businessPartnerId = businessPartnerId,
            predecessor = predecessor,
            leasingPrivilege = leasingPrivilege,
        )

    companion object {
        fun buildSingle() = PlannedVehicleTransferInitBuilder().build()

        fun buildMultiple(count: Int): Set<PlannedVehicleTransferInit> =
            (1..count).map { PlannedVehicleTransferInitBuilder().build() }.toSet()
    }
}

val charPool = ('a'..'z') + ('A'..'Z') + ('0'..'9')

fun createRandomString(length: Int = Random.nextInt(4, 23)): String =
    (1..length).map { Random.nextInt(0, charPool.size) }.map(charPool::get).joinToString("")
