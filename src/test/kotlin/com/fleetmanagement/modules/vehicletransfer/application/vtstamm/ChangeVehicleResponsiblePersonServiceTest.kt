/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehicletransfer.application.vtstamm

import ActiveVehicleTransferNotFoundException
import CurrentVehicleTransferNotFoundException
import com.fleetmanagement.TestcontainersConfiguration
import com.fleetmanagement.integrations.mailclient.adapter.EmailAdapter
import com.fleetmanagement.modules.consigneedatasheet.application.ConsigneeDataFinder
import com.fleetmanagement.modules.consigneedatasheet.application.UsageGroupFinder
import com.fleetmanagement.modules.consigneedatasheet.application.VehicleUsageFinder
import com.fleetmanagement.modules.consigneedatasheet.domain.EmployeeNumber
import com.fleetmanagement.modules.documentgeneration.features.generatepowerofattorney.GeneratePowerOfAttorneyService
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByVIN
import com.fleetmanagement.modules.vehicledata.api.domain.NextProcess
import com.fleetmanagement.modules.vehicledata.internal.repository.objectmothers.JPAVehicleEntityObjectMother.newVehicleEntityWithAllFieldsPopulated
import com.fleetmanagement.modules.vehicledata.repository.JPAVehicleRepository
import com.fleetmanagement.modules.vehicleperson.features.readvehicleperson.VehiclePersonDetailReadService
import com.fleetmanagement.modules.vehicleperson.objectmothers.VehiclePersonDetailsBuilder
import com.fleetmanagement.modules.vehicleregistration.api.ReadRegistrationOrder
import com.fleetmanagement.modules.vehicleregistration.api.dto.VehicleRegistrationAPIResponse
import com.fleetmanagement.modules.vehicleregistration.api.dto.VehicleRegistrationOrder
import com.fleetmanagement.modules.vehicletransfer.PlannedVehicleTransferBuilder
import com.fleetmanagement.modules.vehicletransfer.VehicleTransferBuilder
import com.fleetmanagement.modules.vehicletransfer.application.VehicleTransferFinder
import com.fleetmanagement.modules.vehicletransfer.domain.PlannedVehicleTransferRepository
import com.fleetmanagement.modules.vehicletransfer.domain.VehicleTransferRepository
import com.fleetmanagement.modules.vehicletransfer.domain.entities.UsageGroupId
import com.fleetmanagement.modules.vehicletransfer.domain.entities.VehicleTransferStatus
import com.fleetmanagement.modules.vehicletransfer.domain.entities.VehicleUsageId
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import jakarta.persistence.EntityManager
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertNotNull
import org.junit.jupiter.api.assertNull
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.annotation.Import
import org.springframework.transaction.annotation.Transactional
import java.util.*

@SpringBootTest
@Transactional
@Import(TestcontainersConfiguration::class)
class ChangeVehicleResponsiblePersonServiceTest {
    @Autowired
    private lateinit var changeVehicleResponsiblePersonService: ChangeVehicleResponsiblePersonService

    @Autowired
    private lateinit var vehicleTransferFinder: VehicleTransferFinder

    @Autowired
    private lateinit var readVehicleByVIN: ReadVehicleByVIN

    @Autowired
    private lateinit var entityManager: EntityManager

    @Autowired
    private lateinit var jpaVehicleRepository: JPAVehicleRepository

    @Autowired
    private lateinit var vehicleTransferRepository: VehicleTransferRepository

    @Autowired
    private lateinit var plannedVehicleTransferRepository: PlannedVehicleTransferRepository

    @Autowired
    private lateinit var vehicleUsageFinder: VehicleUsageFinder

    @Autowired
    private lateinit var usageGroupFinder: UsageGroupFinder

    @MockkBean
    private lateinit var vehiclePersonDetailReadService: VehiclePersonDetailReadService

    @MockkBean
    private lateinit var readRegistrationOrder: ReadRegistrationOrder

    @MockkBean(relaxed = true)
    private lateinit var consigneeDataFinder: ConsigneeDataFinder

    @MockkBean(relaxed = true)
    private lateinit var emailAdapter: EmailAdapter

    @MockkBean(relaxed = true)
    private lateinit var generatePowerOfAttorneyService: GeneratePowerOfAttorneyService

    @Test
    fun `should change vehicle responsible person`() {
        every { vehiclePersonDetailReadService.readVehiclePersonDetailByEmployeeNumber(any()) } returns
            VehiclePersonDetailsBuilder()
                .employeeNumber("00123666")
                .leasingPrivilegeLeasing("L2")
                .companyEmail("<EMAIL>")
                .build()
        val vehicleUsage = vehicleUsageFinder.getAllVehicleUsages().random()
        val usageGroup = usageGroupFinder.getAllUsageGroups().random()
        every { consigneeDataFinder.calculateUsageGroup(any()) } returns usageGroup.id
        every { consigneeDataFinder.readLeasingPrivilegeForVehicleUsage(any()) } returns listOf("L2")
        // only necessary for usageGroup PERSON
        every { readRegistrationOrder.getLatestOrderBy(any()) } returns
            VehicleRegistrationAPIResponse(
                data =
                    VehicleRegistrationOrder(
                        id = 1,
                        licencePlate = Optional.of("AB-CD 1234"),
                        registrationStatus = Optional.of("REGISTERED"),
                        version = 1,
                    ),
            )
        val vehicle =
            newVehicleEntityWithAllFieldsPopulated(vin = "vtstammVin1").also {
                jpaVehicleRepository.save(it)
            }
        val currentVehicleResponsiblePerson = "00123456"
        val currentVehicleTransfer =
            VehicleTransferBuilder()
                .vehicleId(vehicle.id!!)
                .vehicleResponsiblePerson(currentVehicleResponsiblePerson)
                .vehicleUsageId(VehicleUsageId(vehicleUsage.id.value))
                .usageGroupId(UsageGroupId(usageGroup.id.value))
                .build()
                .also {
                    vehicleTransferRepository.save(it)
                }
        entityManager.flush()

        val newVehicleTransferWithChangedResponsiblePerson =
            changeVehicleResponsiblePersonService.changeResponsiblePerson(
                vehicleId = vehicle.id!!,
                returnComment = "return me!",
                latestMileage = 23,
                vin = vehicle.vin!!,
                vehicleResponsiblePerson = EmployeeNumber("00123666"),
                nextProcess = NextProcess.SCRAPPED_CAR_RETURNED,
                deliveryComment = "deliver me!",
            )
        entityManager.flush()

        val newVehicleTransferWithChangedResponsiblePersonFromFinder =
            vehicleTransferFinder.getVehicleTransfer(newVehicleTransferWithChangedResponsiblePerson.key)
        val returnedVehicleTransferFromFinder = vehicleTransferFinder.getVehicleTransfer(currentVehicleTransfer.key)
        val updatedVehicle = readVehicleByVIN.readVehicleByVIN(vin = vehicle.vin!!)
        assertTrue(newVehicleTransferWithChangedResponsiblePerson.key != currentVehicleTransfer.key)
        assertEquals("return me!", returnedVehicleTransferFromFinder.returnComment)
        assertEquals(23, returnedVehicleTransferFromFinder.mileageAtReturn)
        assertEquals(VehicleTransferStatus.FINISHED, returnedVehicleTransferFromFinder.status)
        assertEquals(VehicleTransferStatus.ACTIVE, newVehicleTransferWithChangedResponsiblePersonFromFinder.status)
        assertEquals("deliver me!", newVehicleTransferWithChangedResponsiblePersonFromFinder.deliveryComment)
        assertEquals(23, newVehicleTransferWithChangedResponsiblePersonFromFinder.mileageAtDelivery)
        assertNotNull(newVehicleTransferWithChangedResponsiblePersonFromFinder.deliveryDate)
        assertEquals(
            "00123666",
            newVehicleTransferWithChangedResponsiblePersonFromFinder.vehicleResponsiblePerson?.value,
        )
        assertEquals(NextProcess.SCRAPPED_CAR_RETURNED, updatedVehicle.returnInfo?.nextProcess)
        assertEquals(
            returnedVehicleTransferFromFinder.vehicleId,
            newVehicleTransferWithChangedResponsiblePerson.vehicleId,
        )
        assertEquals(
            returnedVehicleTransferFromFinder.vehicleUsage,
            newVehicleTransferWithChangedResponsiblePerson.vehicleUsage,
        )
        assertEquals(
            returnedVehicleTransferFromFinder.internalContactPerson,
            newVehicleTransferWithChangedResponsiblePerson.internalContactPerson,
        )
        assertEquals(
            returnedVehicleTransferFromFinder.licensePlate,
            newVehicleTransferWithChangedResponsiblePerson.licensePlate,
        )
        assertEquals(
            returnedVehicleTransferFromFinder.deliveryLeipzig,
            newVehicleTransferWithChangedResponsiblePerson.deliveryLeipzig,
        )
        assertEquals(
            returnedVehicleTransferFromFinder.maximumServiceLifeInMonths,
            newVehicleTransferWithChangedResponsiblePerson.maximumServiceLifeInMonths,
        )
        assertEquals(
            returnedVehicleTransferFromFinder.usingCostCenter,
            newVehicleTransferWithChangedResponsiblePerson.usingCostCenter,
        )
        assertEquals(
            returnedVehicleTransferFromFinder.desiredTireSet,
            newVehicleTransferWithChangedResponsiblePerson.desiredTireSet,
        )
        assertEquals(
            returnedVehicleTransferFromFinder.serviceCards,
            newVehicleTransferWithChangedResponsiblePerson.serviceCards,
        )
        assertEquals(
            returnedVehicleTransferFromFinder.registrationNeeded,
            newVehicleTransferWithChangedResponsiblePerson.registrationNeeded,
        )
        assertEquals(returnedVehicleTransferFromFinder.remark, newVehicleTransferWithChangedResponsiblePerson.remark)
        assertEquals(
            returnedVehicleTransferFromFinder.usageMhp,
            newVehicleTransferWithChangedResponsiblePerson.usageMhp,
        )
        assertEquals(
            returnedVehicleTransferFromFinder.usageVdw,
            newVehicleTransferWithChangedResponsiblePerson.usageVdw,
        )
        assertEquals(
            returnedVehicleTransferFromFinder.privateMonthlyKilometers,
            newVehicleTransferWithChangedResponsiblePerson.privateMonthlyKilometers,
        )
        assertNull(newVehicleTransferWithChangedResponsiblePerson.provisionForDeliveryComment)
        assertNull(newVehicleTransferWithChangedResponsiblePerson.desiredDeliveryDate)
        assertNull(newVehicleTransferWithChangedResponsiblePerson.plannedReturnDate)
        assertNull(newVehicleTransferWithChangedResponsiblePerson.plannedDeliveryDate)
    }

    @Test
    fun `should perform delivery for given vehicle responsible person if current transfer is a planned one`() {
        every { vehiclePersonDetailReadService.readVehiclePersonDetailByEmployeeNumber(any()) } returns
            VehiclePersonDetailsBuilder()
                .employeeNumber("00123666")
                .leasingPrivilegeLeasing("L2")
                .companyEmail("<EMAIL>")
                .build()
        val vehicleUsage = vehicleUsageFinder.getAllVehicleUsages().random()
        val usageGroup = usageGroupFinder.getAllUsageGroups().random()
        every { consigneeDataFinder.calculateUsageGroup(any()) } returns usageGroup.id
        val vehicle =
            newVehicleEntityWithAllFieldsPopulated(vin = "vtstammVin1").also {
                jpaVehicleRepository.save(it)
            }
        // only necessary for usageGroup PERSON
        every { readRegistrationOrder.getLatestOrderBy(any()) } returns
            VehicleRegistrationAPIResponse(
                data =
                    VehicleRegistrationOrder(
                        id = 1,
                        licencePlate = Optional.of("AB-CD 1234"),
                        registrationStatus = Optional.of("REGISTERED"),
                        version = 1,
                    ),
            )
        val currentVehicleTransfer =
            PlannedVehicleTransferBuilder()
                .vehicleId(vehicle.id!!)
                .vehicleResponsiblePerson(null)
                .vehicleUsageId(VehicleUsageId(vehicleUsage.id.value))
                .usageGroupId(UsageGroupId(usageGroup.id.value))
                .build()
                .also {
                    plannedVehicleTransferRepository.save(it)
                }
        entityManager.flush()

        val newVehicleTransferWithChangedResponsiblePerson =
            changeVehicleResponsiblePersonService.changeResponsiblePerson(
                vehicleId = vehicle.id!!,
                returnComment = "return me!",
                latestMileage = 23,
                vin = vehicle.vin!!,
                vehicleResponsiblePerson = EmployeeNumber("00123666"),
                nextProcess = NextProcess.SCRAPPED_CAR_RETURNED,
                deliveryComment = "deliver me!",
            )
        entityManager.flush()

        val newVehicleTransferWithChangedResponsiblePersonFromFinder =
            vehicleTransferFinder.getVehicleTransfer(newVehicleTransferWithChangedResponsiblePerson.key)
        assertTrue(newVehicleTransferWithChangedResponsiblePerson.key == currentVehicleTransfer.key)
        assertEquals(VehicleTransferStatus.ACTIVE, newVehicleTransferWithChangedResponsiblePersonFromFinder.status)
        assertEquals("deliver me!", newVehicleTransferWithChangedResponsiblePersonFromFinder.deliveryComment)
        assertEquals(23, newVehicleTransferWithChangedResponsiblePersonFromFinder.mileageAtDelivery)
        assertNotNull(newVehicleTransferWithChangedResponsiblePersonFromFinder.deliveryDate)
        assertEquals(
            "00123666",
            newVehicleTransferWithChangedResponsiblePersonFromFinder.vehicleResponsiblePerson?.value,
        )
        assertEquals(
            currentVehicleTransfer.provisionForDeliveryComment,
            newVehicleTransferWithChangedResponsiblePerson.provisionForDeliveryComment,
        )
        assertEquals(
            currentVehicleTransfer.desiredDeliveryDate,
            newVehicleTransferWithChangedResponsiblePerson.desiredDeliveryDate,
        )
        assertEquals(
            currentVehicleTransfer.plannedReturnDate,
            newVehicleTransferWithChangedResponsiblePerson.plannedReturnDate,
        )
        assertEquals(
            currentVehicleTransfer.plannedDeliveryDate,
            newVehicleTransferWithChangedResponsiblePerson.plannedDeliveryDate,
        )
    }

    @Test
    fun `should throw ActiveVehicleTransferNotFoundException when no active vehicle transfer exists for current vehicle transfer key`() {
        val vehicleUsage = vehicleUsageFinder.getAllVehicleUsages().random()
        val usageGroup = usageGroupFinder.getAllUsageGroups().random()
        val vehicle =
            newVehicleEntityWithAllFieldsPopulated(vin = "vtstammVin3").also {
                jpaVehicleRepository.save(it)
            }
        VehicleTransferBuilder()
            .vehicleId(vehicle.id!!)
            .vehicleResponsiblePerson(null)
            .vehicleUsageId(VehicleUsageId(vehicleUsage.id.value))
            .usageGroupId(UsageGroupId(usageGroup.id.value))
            .returned()
            .build()
            .also {
                vehicleTransferRepository.save(it)
            }
        entityManager.flush()

        assertThrows<ActiveVehicleTransferNotFoundException> {
            changeVehicleResponsiblePersonService.changeResponsiblePerson(
                vehicleId = vehicle.id!!,
                returnComment = "return me!",
                latestMileage = 23,
                vin = vehicle.vin!!,
                vehicleResponsiblePerson = EmployeeNumber("00123666"),
                nextProcess = NextProcess.SCRAPPED_CAR_RETURNED,
                deliveryComment = "deliver me!",
            )
        }
    }

    @Test
    fun `should throw CurrentVehicleTransferNotFoundException if no vehicle transfer could be found`() {
        assertThrows<CurrentVehicleTransferNotFoundException> {
            changeVehicleResponsiblePersonService.changeResponsiblePerson(
                vehicleId = UUID.randomUUID(),
                returnComment = "return me!",
                latestMileage = 23,
                vin = "someVin",
                vehicleResponsiblePerson = EmployeeNumber("00123666"),
                nextProcess = NextProcess.SCRAPPED_CAR_RETURNED,
                deliveryComment = "deliver me!",
            )
        }
    }
}
