@file:Suppress("ktlint:standard:no-wildcard-imports")

package com.fleetmanagement.modules.vehicletransfer.application

import com.fleetmanagement.TestcontainersConfiguration
import com.fleetmanagement.modules.consigneedatasheet.domain.EmployeeNumber
import com.fleetmanagement.modules.vehicletransfer.VehicleTransferBuilder
import com.fleetmanagement.modules.vehicletransfer.domain.VehicleTransferRepository
import com.fleetmanagement.modules.vehicletransfer.domain.entities.UsageGroupId
import com.fleetmanagement.modules.vehicletransfer.domain.entities.UtilizationArea
import com.fleetmanagement.modules.vehicletransfer.domain.entities.VehicleTransfer
import com.fleetmanagement.modules.vehicletransfer.domain.entities.VehicleUsageId
import jakarta.persistence.EntityManager
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.containsInAnyOrder
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertNull
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.annotation.Import
import org.springframework.data.domain.Pageable
import org.springframework.transaction.annotation.Transactional
import java.time.OffsetDateTime
import java.util.Optional
import java.util.UUID

@SpringBootTest
@Transactional
@Import(TestcontainersConfiguration::class)
class VehicleTransferFinderTest {
    @Autowired
    private lateinit var vehicleTransferRepository: VehicleTransferRepository

    @Autowired
    private lateinit var vehicleTransferFinder: VehicleTransferFinder

    @Autowired
    private lateinit var entityManager: EntityManager

    @Test
    fun `should store Vehicle Transfer and find it`() {
        val vehicleTransfer = VehicleTransferBuilder.buildSingle()
        val stored = vehicleTransferRepository.save(vehicleTransfer)
        entityManager.flush()

        val vehicleTransfersFromFinder =
            vehicleTransferFinder.findVehicleTransfersByVehicleId(stored.vehicleId)
        val vehicleTransferFromFinder = vehicleTransfersFromFinder.single()
        assertEquals(1, vehicleTransfersFromFinder.size)
        assertEquals(vehicleTransfer.key, vehicleTransferFromFinder.key)
        assertEquals(vehicleTransfer.vehicleId, vehicleTransferFromFinder.vehicleId)
        assertEquals(vehicleTransfer.vehicleUsage, vehicleTransferFromFinder.vehicleUsage)
        assertEquals(vehicleTransfer.businessPartnerId, vehicleTransferFromFinder.businessPartnerId)
        assertEquals(vehicleTransfer.consignee, vehicleTransferFromFinder.consignee)
        assertEquals(
            vehicleTransfer.depreciationRelevantCostCenterId,
            vehicleTransferFromFinder.depreciationRelevantCostCenterId,
        )
        assertEquals(vehicleTransfer.createdBy, vehicleTransferFromFinder.createdBy)
        assertEquals(vehicleTransfer.id, vehicleTransferFromFinder.id)
        assertEquals(
            vehicleTransfer.internalContactPerson,
            vehicleTransferFromFinder.internalContactPerson,
        )
        assertEquals(
            vehicleTransfer.internalOrderNumber,
            vehicleTransferFromFinder.internalOrderNumber,
        )
        assertEquals(vehicleTransfer.lastModifiedBy, vehicleTransferFromFinder.lastModifiedBy)
        assertEquals(vehicleTransfer.licensePlate, vehicleTransferFromFinder.licensePlate)
        assertEquals(
            vehicleTransfer.licensePlateLastUpdate,
            vehicleTransferFromFinder.licensePlateLastUpdate,
        )
        assertEquals(
            vehicleTransfer.maximumServiceLifeInMonths,
            vehicleTransferFromFinder.maximumServiceLifeInMonths,
        )
        assertEquals(
            vehicleTransfer.plannedDeliveryDate,
            vehicleTransferFromFinder.plannedDeliveryDate,
        )
        assertEquals(vehicleTransfer.plannedReturnDate, vehicleTransferFromFinder.plannedReturnDate)
        assertEquals(vehicleTransfer.status, vehicleTransferFromFinder.status)
        assertEquals(
            vehicleTransfer.usingCostCenter,
            vehicleTransferFromFinder.usingCostCenter,
        )
        assertEquals(
            vehicleTransfer.vehicleResponsiblePerson,
            vehicleTransferFromFinder.vehicleResponsiblePerson,
        )
        assertEquals(vehicleTransfer.usageGroup, vehicleTransferFromFinder.usageGroup)
    }

    @Test
    fun `should return empty list as vehicle does not exist`() {
        val vehicleId = UUID.randomUUID()
        val vehicleTransferFromFinder =
            vehicleTransferFinder.findVehicleTransfersByVehicleId(vehicleId)
        assertTrue(vehicleTransferFromFinder.isEmpty())
    }

    @Test
    fun `should only find cars with null or before publish date`() {
        val licensePlate = "licensePlate"
        val publish = OffsetDateTime.now()
        val inTheFuture =
            vehicleTransferRepository.save(VehicleTransferBuilder().vehicleId(VEHICLE_ID).build())
        val inThePast =
            vehicleTransferRepository.save(VehicleTransferBuilder().vehicleId(VEHICLE_ID).build())
        val isNull =
            vehicleTransferRepository.save(VehicleTransferBuilder().licensePlate(null).vehicleId(VEHICLE_ID).build())

        inTheFuture.updateLicensePlate(
            licensePlate = licensePlate,
            publishAt = OffsetDateTime.now().plusDays(1),
        )
        inThePast.updateLicensePlate(
            licensePlate = licensePlate,
            publishAt = OffsetDateTime.now().minusYears(1),
        )

        entityManager.flush()
        val vehicleTransferFromFinder =
            vehicleTransferFinder.findVehicleTransfersByVehicleIdAndPublishDateBefore(
                VEHICLE_ID,
                publish,
            )

        val inThePastFinder = vehicleTransferFromFinder.single { inThePast.id == it.id }
        val inNullFinder = vehicleTransferFromFinder.single { isNull.id == it.id }

        assertEquals(2, vehicleTransferFromFinder.size)
        assertEquals(inThePastFinder.licensePlate, inThePast.licensePlate)
        assertEquals(inThePastFinder.licensePlateLastUpdate, inThePast.licensePlateLastUpdate)
        assertEquals(inNullFinder.licensePlate, isNull.licensePlate)
        assertEquals(inNullFinder.licensePlateLastUpdate, isNull.licensePlateLastUpdate)
    }

    @Test
    fun `should return active vehicle transfer matching vehicle responsible, vehicle usage and usage group`() {
        val employeeNumber = "00123456"
        val vehicleUsageId = VehicleUsageId(UUID.randomUUID())
        val usageGroupId = UsageGroupId(UUID.randomUUID())
        val vehicleTransfer =
            VehicleTransferBuilder()
                .vehicleResponsiblePerson(employeeNumber)
                .vehicleUsageId(vehicleUsageId)
                .usageGroupId(usageGroupId)
                .build()
        vehicleTransferRepository.save(vehicleTransfer)
        entityManager.flush()

        val vehicleTransfersFromFinder =
            vehicleTransferFinder
                .getActiveVehicleTransferByUsageGroupAndVehicleUsageAndVehicleResponsiblePerson(
                    vehicleUsageId = vehicleUsageId,
                    vehicleResponsiblePerson = EmployeeNumber(employeeNumber),
                    usageGroupId = usageGroupId,
                )
        assertEquals(1, vehicleTransfersFromFinder.size)
        val vehicleTransferFromFinder = vehicleTransfersFromFinder.single()
        assertEquals(vehicleTransfer.key, vehicleTransferFromFinder.key)
        assertEquals(vehicleTransfer.vehicleId, vehicleTransferFromFinder.vehicleId)
        assertEquals(vehicleTransfer.vehicleUsage, vehicleTransferFromFinder.vehicleUsage)
        assertEquals(vehicleTransfer.businessPartnerId, vehicleTransferFromFinder.businessPartnerId)
        assertEquals(vehicleTransfer.consignee, vehicleTransferFromFinder.consignee)
        assertEquals(
            vehicleTransfer.depreciationRelevantCostCenterId,
            vehicleTransferFromFinder.depreciationRelevantCostCenterId,
        )
        assertEquals(vehicleTransfer.createdBy, vehicleTransferFromFinder.createdBy)
        assertEquals(vehicleTransfer.id, vehicleTransferFromFinder.id)
        assertEquals(
            vehicleTransfer.internalContactPerson,
            vehicleTransferFromFinder.internalContactPerson,
        )
        assertEquals(
            vehicleTransfer.internalOrderNumber,
            vehicleTransferFromFinder.internalOrderNumber,
        )
        assertEquals(vehicleTransfer.lastModifiedBy, vehicleTransferFromFinder.lastModifiedBy)
        assertEquals(vehicleTransfer.licensePlate, vehicleTransferFromFinder.licensePlate)
        assertEquals(
            vehicleTransfer.licensePlateLastUpdate,
            vehicleTransferFromFinder.licensePlateLastUpdate,
        )
        assertEquals(
            vehicleTransfer.maximumServiceLifeInMonths,
            vehicleTransferFromFinder.maximumServiceLifeInMonths,
        )
        assertEquals(
            vehicleTransfer.plannedDeliveryDate,
            vehicleTransferFromFinder.plannedDeliveryDate,
        )
        assertEquals(vehicleTransfer.plannedReturnDate, vehicleTransferFromFinder.plannedReturnDate)
        assertEquals(vehicleTransfer.status, vehicleTransferFromFinder.status)
        assertEquals(
            vehicleTransfer.usingCostCenter,
            vehicleTransferFromFinder.usingCostCenter,
        )
        assertEquals(
            vehicleTransfer.vehicleResponsiblePerson,
            vehicleTransferFromFinder.vehicleResponsiblePerson,
        )
        assertEquals(vehicleTransfer.usageGroup, vehicleTransferFromFinder.usageGroup)
    }

    @Test
    fun `should not return vehicle transfer if not active`() {
        val employeeNumber = "00123456"
        val vehicleUsageId = VehicleUsageId(UUID.randomUUID())
        val usageGroupId = UsageGroupId(UUID.randomUUID())
        val vehicleTransfer =
            VehicleTransferBuilder()
                .vehicleResponsiblePerson(employeeNumber)
                .vehicleUsageId(vehicleUsageId)
                .usageGroupId(usageGroupId)
                .returned()
                .build()
        vehicleTransferRepository.save(vehicleTransfer)
        entityManager.flush()

        val vehicleTransferFromFinder =
            vehicleTransferFinder
                .getActiveVehicleTransferByUsageGroupAndVehicleUsageAndVehicleResponsiblePerson(
                    vehicleUsageId = vehicleUsageId,
                    vehicleResponsiblePerson = EmployeeNumber(employeeNumber),
                    usageGroupId = usageGroupId,
                )
        assertEquals(0, vehicleTransferFromFinder.size)
    }

    @Test
    fun `should not return vehicle transfer`() {
        val employeeNumber = "00123456"
        val vehicleUsageId = VehicleUsageId(UUID.randomUUID())
        val usageGroupId = UsageGroupId(UUID.randomUUID())
        val vehicleTransfer =
            VehicleTransferBuilder()
                .vehicleResponsiblePerson(employeeNumber)
                .vehicleUsageId(vehicleUsageId)
                .usageGroupId(usageGroupId)
                .returned()
                .build()
        vehicleTransferRepository.save(vehicleTransfer)
        entityManager.flush()

        val vehicleTransferFromFinder =
            vehicleTransferFinder
                .getActiveVehicleTransferByUsageGroupAndVehicleUsageAndVehicleResponsiblePerson(
                    vehicleUsageId = VehicleUsageId(UUID.randomUUID()),
                    vehicleResponsiblePerson = EmployeeNumber(employeeNumber),
                    usageGroupId = usageGroupId,
                )
        assertEquals(0, vehicleTransferFromFinder.size)
    }

    @Test
    fun `should return multiple vehicle transfer`() {
        val employeeNumber = "00123456"
        val vehicleUsageId = VehicleUsageId(UUID.randomUUID())
        val usageGroupId = UsageGroupId(UUID.randomUUID())
        val vehicleTransfer =
            VehicleTransferBuilder()
                .vehicleResponsiblePerson(employeeNumber)
                .vehicleUsageId(vehicleUsageId)
                .usageGroupId(usageGroupId)
                .build()

        val anotherVehicleTransfer =
            VehicleTransferBuilder()
                .vehicleResponsiblePerson(employeeNumber)
                .vehicleUsageId(vehicleUsageId)
                .usageGroupId(usageGroupId)
                .build()
        vehicleTransferRepository.save(vehicleTransfer)
        vehicleTransferRepository.save(anotherVehicleTransfer)
        entityManager.flush()

        val vehicleTransferFromFinder =
            vehicleTransferFinder
                .getActiveVehicleTransferByUsageGroupAndVehicleUsageAndVehicleResponsiblePerson(
                    vehicleUsageId = vehicleUsageId,
                    vehicleResponsiblePerson = EmployeeNumber(employeeNumber),
                    usageGroupId = usageGroupId,
                )
        assertEquals(2, vehicleTransferFromFinder.size)
    }

    @Test
    fun `should find  vehicle before given date`() {
        val vehicleTransfer = VehicleTransferBuilder.buildSingle()
        vehicleTransferRepository.saveAndFlush(vehicleTransfer)

        val tomorrow = OffsetDateTime.now().plusDays(1)
        val vehicleTransferFromFinder =
            vehicleTransferFinder.findByLastModifiedBefore(tomorrow).single()
        assertEquals(vehicleTransfer.key, vehicleTransferFromFinder.key)
    }

    @Test
    fun `should find vt by predecessor`() {
        val activeVehicleTransfer = VehicleTransferBuilder.buildSingle()
        val referencedVehicleTransfer = VehicleTransferBuilder.buildSingle()
        vehicleTransferRepository.saveAndFlush(referencedVehicleTransfer)
        activeVehicleTransfer.updatePredecessor(referencedVehicleTransfer)

        vehicleTransferRepository.saveAndFlush(activeVehicleTransfer)

        val storedVehicleTransfer =
            vehicleTransferFinder
                .findVehicleTransferByPredecessor(referencedVehicleTransfer)
                .single()

        assertEquals(activeVehicleTransfer.key, storedVehicleTransfer.key)
    }

    @Test
    fun `should find vt by key`() {
        val vehicleTransfer = VehicleTransferBuilder.buildSingle()
        vehicleTransferRepository.saveAndFlush(vehicleTransfer)

        val storedVehicleTransfer =
            vehicleTransferFinder.findVehicleTransferByKey(vehicleTransfer.key)

        assertEquals(vehicleTransfer.id, storedVehicleTransfer?.id)
    }

    @Test
    fun `should find active vehicle transfers by employee number`() {
        val vehicleResponsiblePerson = "00123456"
        val anotherVehicleResponsiblePerson = "00136566"
        val activeVehicleTransfer =
            (1..5)
                .map {
                    VehicleTransferBuilder()
                        .vehicleResponsiblePerson(vehicleResponsiblePerson)
                        .build()
                }.onEach { vehicleTransferRepository.save(it) }
        (1..2)
            .map {
                VehicleTransferBuilder()
                    .vehicleResponsiblePerson(vehicleResponsiblePerson)
                    .returned()
                    .build()
            }.onEach { vehicleTransferRepository.save(it) }
        (1..5)
            .map {
                VehicleTransferBuilder()
                    .vehicleResponsiblePerson(anotherVehicleResponsiblePerson)
                    .build()
            }.onEach { vehicleTransferRepository.save(it) }
        entityManager.flush()

        val activeVehicleTransfersFromFinder =
            vehicleTransferFinder.findActiveVehicleTransfersForEmployee(
                vehicleResponsiblePerson = EmployeeNumber(vehicleResponsiblePerson),
            )

        val expected = activeVehicleTransfer.map { it.key }
        val actual = activeVehicleTransfersFromFinder.map { it.key }
        assertThat(
            expected,
            containsInAnyOrder(*actual.toTypedArray()),
        )
    }

    @Test
    fun `should find planned vehicle transfer with return date between given times`() {
        val now = OffsetDateTime.now()
        val from = now.minusDays(1)
        val to = now.plusDays(1)
        val appointmentId = "1"

        val vehicleTransferKey = 123L
        val vehicleTransfer =
            VehicleTransferBuilder().vehicleTransferKey(vehicleTransferKey).build()
        vehicleTransfer.updatePlannedReturnDate(now)
        vehicleTransfer.updateMsbookingAppointmentId(appointmentId)

        vehicleTransferRepository.saveAndFlush(vehicleTransfer)

        val vehicleTransferFromFinder =
            vehicleTransferFinder
                .readAllAppointmentsIdAndKeyWherePlannedReturnDateBetween(from, to)

        val pair = vehicleTransferFromFinder.single()
        assertEquals(appointmentId, pair.first)
        assertEquals(vehicleTransferKey, pair.second.value)
    }

    @Test
    fun `should find ACTIVE vehicle transfers with usage area Zuffenhausen or Leipzig`() {
        val vt1 =
            VehicleTransferBuilder()
                .build()
                .also {
                    it.updateUtilizationArea(UtilizationArea.ZUFFENHAUSEN)
                }
        val vt2 =
            VehicleTransferBuilder()
                .build()
                .also {
                    it.updateUtilizationArea(UtilizationArea.WEISSACH)
                }

        val vt3 =
            VehicleTransferBuilder()
                .build()
                .also {
                    it.updateUtilizationArea(UtilizationArea.LEIPZIG)
                }

        vehicleTransferRepository.save(vt1)
        vehicleTransferRepository.save(vt2)
        vehicleTransferRepository.save(vt3)
        entityManager.flush()

        val actual =
            vehicleTransferFinder.findAllTransfersForUtilizationAreaZuffenhausenAndLeipzig(
                Pageable.unpaged(),
            )

        assertEquals(2, actual.size)
    }

    @Test
    fun `should find ACTIVE vehicle transfers without maintenance order number`() {
        (1..5)
            .map {
                VehicleTransferBuilder().build().also {
                    it.updateMaintenanceOrderNumber("maintenanceOrderNumber_$it")
                }
            }.map {
                vehicleTransferRepository.save(it)
            }
        val vehicleTransfersWithoutMaintenanceOrderNumber =
            VehicleTransferBuilder.buildMultiple(10).map {
                vehicleTransferRepository.save(it)
            }

        val vehicleTransfersFromFinder =
            vehicleTransferFinder.findActiveVehicleTransfersWithoutMaintenanceOrderNumber()

        val expected = vehicleTransfersWithoutMaintenanceOrderNumber.map { it.key }
        val actual = vehicleTransfersFromFinder.map { it.key }
        assertThat(
            expected,
            containsInAnyOrder(*actual.toTypedArray()),
        )
    }

    @Test
    fun `should find ACTIVE vehicle transfers with usage group person`() {
        val withCorrectUsageGroup = 5
        val usageGroupId = UUID.randomUUID()
        val vehicleTransfersToFind =
            (1..withCorrectUsageGroup)
                .map {
                    VehicleTransferBuilder().usageGroupId(UsageGroupId(usageGroupId)).build()
                }.map {
                    vehicleTransferRepository.save(it)
                }
        VehicleTransferBuilder.buildMultiple(10).map {
            vehicleTransferRepository.save(it)
        }

        val vehicleTransfersFromFinder =
            vehicleTransferFinder.findAllActiveTransfersForUsageGroup(usageGroupId)

        assertEquals(withCorrectUsageGroup, vehicleTransfersFromFinder.size)
        val expected = vehicleTransfersToFind.map { it.key }
        val actual = vehicleTransfersFromFinder.map { it.key }
        assertThat(
            expected,
            containsInAnyOrder(*actual.toTypedArray()),
        )
    }

    @Test
    fun `should find all ACTIVE vehicle transfers`() {
        val activeVehicleTransfers =
            (1..5)
                .map {
                    VehicleTransferBuilder()
                        .build()
                }.onEach { vehicleTransferRepository.save(it) }

        // finished transfers should not be included
        (1..3)
            .map {
                VehicleTransferBuilder()
                    .returned()
                    .build()
            }.onEach { vehicleTransferRepository.save(it) }
        (1..2)
            .map {
                VehicleTransferBuilder()
                    .returned()
                    .build()
            }.onEach { vehicleTransferRepository.save(it) }

        entityManager.flush()

        val vehicleTransfersFromFinder =
            vehicleTransferFinder.findAllActiveTransfers()

        assertEquals(5, vehicleTransfersFromFinder.size)
        val expected = activeVehicleTransfers.map { it.key }
        val actual = vehicleTransfersFromFinder.map { it.key }
        assertThat(
            expected,
            containsInAnyOrder(*actual.toTypedArray()),
        )
    }

    @Test
    fun `should find vehicle transfer by license plate`() {
        val firstActiveVT =
            VehicleTransferBuilder()
                .licensePlate("S-PL 1234")
                .build()
                .let { vehicleTransferRepository.save(it) }
        val secondActiveVT =
            VehicleTransferBuilder()
                .licensePlate("T-PL 1234e")
                .build()
                .let { vehicleTransferRepository.save(it) }
        val thirdActiveVT =
            VehicleTransferBuilder()
                .licensePlate("u-pl 1234H")
                .build()
                .let { vehicleTransferRepository.save(it) }
        val fourthActiveVT =
            VehicleTransferBuilder()
                .licensePlate("v-pl 1234")
                .build()
                .let { vehicleTransferRepository.save(it) }

        val firstResult = vehicleTransferFinder.findActiveVehicleTransferByLicensePlate("s-pl 1234H").singleOrNull()
        val secondResult = vehicleTransferFinder.findActiveVehicleTransferByLicensePlate("t-pl 1234e").singleOrNull()
        val thirdResult = vehicleTransferFinder.findActiveVehicleTransferByLicensePlate("u-pl 1234").singleOrNull()
        val fourthResult = vehicleTransferFinder.findActiveVehicleTransferByLicensePlate("v-pl 1234").singleOrNull()
        val fifthResult = vehicleTransferFinder.findActiveVehicleTransferByLicensePlate("v-pl 1234d").singleOrNull()
        assertEquals(firstActiveVT, firstResult)
        assertEquals(secondActiveVT, secondResult)
        assertEquals(thirdActiveVT, thirdResult)
        assertEquals(fourthActiveVT, fourthResult)
        assertNull(fifthResult)
    }

    companion object {
        private val VEHICLE_ID: UUID = UUID.randomUUID()

        private fun VehicleTransfer.updateUtilizationArea(area: UtilizationArea) {
            this.updatePrivilege(
                maximumServiceLifeInMonths = Optional.empty(),
                vehicleUsage = Optional.empty(),
                vehicleResponsiblePerson = Optional.empty(),
                internalContactPerson = Optional.empty(),
                depreciationRelevantCostCenterId = Optional.empty(),
                usingCostCenter = Optional.empty(),
                internalOrderNumber = Optional.empty(),
                usageGroupId = Optional.empty(),
                plannedDeliveryDate = Optional.empty(),
                desiredDeliveryDate = Optional.empty(),
                plannedReturnDate = Optional.empty(),
                deliveryDate = Optional.empty(),
                returnDate = Optional.empty(),
                mileageAtDelivery = Optional.empty(),
                mileageAtReturn = Optional.empty(),
                remark = Optional.empty(),
                provisionForDeliveryComment = Optional.empty(),
                deliveryComment = Optional.empty(),
                utilizationArea = Optional.of(area),
                tiresComment = Optional.empty(),
                deliveryLeipzig = Optional.empty(),
                leasingPrivilege = Optional.empty(),
                returnComment = Optional.empty(),
                serviceCards = Optional.empty(),
                registrationNeeded = Optional.empty(),
                usageMhp = Optional.empty(),
                usageVdw = Optional.empty(),
                privateMonthlyKilometers = Optional.empty(),
            )
        }
    }
}
