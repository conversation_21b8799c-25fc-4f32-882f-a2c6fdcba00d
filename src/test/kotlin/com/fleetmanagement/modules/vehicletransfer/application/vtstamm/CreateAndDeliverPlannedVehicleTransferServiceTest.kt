/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehicletransfer.application.vtstamm

import com.fleetmanagement.TestcontainersConfiguration
import com.fleetmanagement.emhshared.USAGE_GROUP_DESCRIPTION_PERSON
import com.fleetmanagement.integrations.mailclient.adapter.EmailAdapter
import com.fleetmanagement.modules.consigneedatasheet.application.ConsigneeDataFinder
import com.fleetmanagement.modules.consigneedatasheet.application.UsageGroupFinder
import com.fleetmanagement.modules.consigneedatasheet.application.VehicleUsageFinder
import com.fleetmanagement.modules.consigneedatasheet.domain.EmployeeNumber
import com.fleetmanagement.modules.consigneedatasheet.objectmothers.CostCenterBuilder
import com.fleetmanagement.modules.documentgeneration.features.generatepowerofattorney.GeneratePowerOfAttorneyService
import com.fleetmanagement.modules.vehicledata.internal.repository.objectmothers.JPAVehicleEntityObjectMother.newVehicleEntityWithAllFieldsPopulated
import com.fleetmanagement.modules.vehicledata.repository.JPAVehicleRepository
import com.fleetmanagement.modules.vehicleperson.features.readvehicleperson.VehiclePersonDetailReadService
import com.fleetmanagement.modules.vehicleperson.objectmothers.VehiclePersonDetailsBuilder
import com.fleetmanagement.modules.vehicleregistration.api.ReadRegistrationOrder
import com.fleetmanagement.modules.vehicleregistration.api.dto.VehicleRegistrationAPIResponse
import com.fleetmanagement.modules.vehicleregistration.api.dto.VehicleRegistrationOrder
import com.fleetmanagement.modules.vehicletransfer.application.VehicleTransferFinder
import com.fleetmanagement.modules.vehicletransfer.application.port.CreateAndDeliverPlannedVehicleTransferDto
import com.fleetmanagement.modules.vehicletransfer.application.port.CreateAndDeliverPlannedVehicleTransferUseCase
import com.fleetmanagement.modules.vehicletransfer.application.port.CreatePlannedVehicleTransferDto
import com.fleetmanagement.modules.vehicletransfer.application.port.DeliverPlannedVehicleTransferDto
import com.fleetmanagement.modules.vehicletransfer.domain.entities.VehicleTransferStatus
import com.fleetmanagement.modules.vehicletransfer.domain.service.DepreciationRelevantCostCenterIdCalculator
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import jakarta.persistence.EntityManager
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertNotNull
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.annotation.Import
import org.springframework.transaction.annotation.Transactional
import java.time.OffsetDateTime
import java.util.*

@SpringBootTest
@Transactional
@Import(TestcontainersConfiguration::class)
class CreateAndDeliverPlannedVehicleTransferServiceTest {
    @Autowired
    private lateinit var createAndDeliverPlannedVehicleTransferUseCase: CreateAndDeliverPlannedVehicleTransferUseCase

    @Autowired
    private lateinit var vehicleTransferFinder: VehicleTransferFinder

    @Autowired
    private lateinit var entityManager: EntityManager

    @Autowired
    private lateinit var jpaVehicleRepository: JPAVehicleRepository

    @Autowired
    private lateinit var vehicleUsageFinder: VehicleUsageFinder

    @Autowired
    private lateinit var usageGroupFinder: UsageGroupFinder

    @MockkBean
    private lateinit var vehiclePersonDetailReadService: VehiclePersonDetailReadService

    @MockkBean
    private lateinit var readRegistrationOrder: ReadRegistrationOrder

    @MockkBean(relaxed = true)
    private lateinit var generatePowerOfAttorneyService: GeneratePowerOfAttorneyService

    @MockkBean(relaxed = true)
    private lateinit var consigneeDataFinder: ConsigneeDataFinder

    @MockkBean(relaxed = true)
    private lateinit var depreciationRelevantCostCenterIdCalculator: DepreciationRelevantCostCenterIdCalculator

    @MockkBean(relaxed = true)
    private lateinit var emailAdapter: EmailAdapter

    @Test
    fun `should create and deliver new vehicle transfer`() {
        val vehicle =
            newVehicleEntityWithAllFieldsPopulated(vin = "vtstammVin1").also {
                jpaVehicleRepository.save(it)
            }
        every { vehiclePersonDetailReadService.readVehiclePersonDetailByEmployeeNumber(any()) } returns
            VehiclePersonDetailsBuilder()
                .employeeNumber("00123666")
                .leasingPrivilegeLeasing("L2")
                .companyEmail("<EMAIL>")
                .build()
        val vehicleUsage = vehicleUsageFinder.getAllVehicleUsages().single { "Dienstwagen" == it.usage }
        val usageGroup =
            usageGroupFinder.getAllUsageGroups().single { USAGE_GROUP_DESCRIPTION_PERSON == it.description }
        every { consigneeDataFinder.calculateUsageGroup(any()) } returns usageGroup.id
        every { consigneeDataFinder.readLeasingPrivilegeForVehicleUsage(any()) } returns listOf("L2")
        // only necessary for usageGroup PERSON
        every { readRegistrationOrder.getLatestOrderBy(any()) } returns
            VehicleRegistrationAPIResponse(
                data =
                    VehicleRegistrationOrder(
                        id = 1,
                        licencePlate = Optional.of("AB-CD 1234"),
                        registrationStatus = Optional.of("REGISTERED"),
                        version = 1,
                    ),
            )
        val depreciationRelevantCostCenter = CostCenterBuilder.buildSingle()
        every {
            depreciationRelevantCostCenterIdCalculator.getDepreciationRelevantCostCenterId(
                any(),
                any(),
            )
        } returns
            com.fleetmanagement.modules.vehicletransfer.domain.entities.CostCenterId(
                depreciationRelevantCostCenter.id.value,
            )
        newVehicleEntityWithAllFieldsPopulated(vin = "vtstammVin2").also {
            jpaVehicleRepository.save(it)
        }
        entityManager.flush()
        val createDto =
            CreatePlannedVehicleTransferDto(
                vehicleId = vehicle.id!!,
                vehicleUsageId = vehicleUsage.id.value,
                vehicleResponsiblePerson = "00123456",
                internalContactPerson = "00123777",
                usingCostCenter = "00H0012233",
                internalOrderNumber = "01243",
            )
        val deliveryDto =
            DeliverPlannedVehicleTransferDto(
                deliveryDate = OffsetDateTime.now(),
                mileageAtDelivery = 23,
                deliveryComment = "comment",
                vehicleResponsiblePerson = "00123456",
            )
        val newTransfer =
            createAndDeliverPlannedVehicleTransferUseCase.createAndDeliverPlannedVehicleTransfer(
                CreateAndDeliverPlannedVehicleTransferDto(
                    createPlannedVehicleTransferDto = createDto,
                    deliverPlannedVehicleTransferDto = deliveryDto,
                ),
            )
        entityManager.flush()

        val deliveredTransferFromFinder = vehicleTransferFinder.getVehicleTransfer(newTransfer.key)
        assertEquals(VehicleTransferStatus.ACTIVE, deliveredTransferFromFinder.status)
        assertEquals(23, deliveredTransferFromFinder.mileageAtDelivery)
        assertEquals("comment", deliveredTransferFromFinder.deliveryComment)
        assertEquals(EmployeeNumber("00123456"), deliveredTransferFromFinder.vehicleResponsiblePerson)
        assertEquals(EmployeeNumber("00123777"), deliveredTransferFromFinder.internalContactPerson)
        assertEquals(vehicleUsage.id.value, deliveredTransferFromFinder.vehicleUsage?.value)
        assertEquals(usageGroup.id.value, deliveredTransferFromFinder.usageGroup?.value)
        assertEquals("00H0012233", deliveredTransferFromFinder.usingCostCenter?.value)
        assertNotNull(deliveredTransferFromFinder.depreciationRelevantCostCenterId)
        assertNotNull(deliveredTransferFromFinder.utilizationArea)
    }
}
