/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehicletransfer

import com.fleetmanagement.modules.vtstamm.adapter.incoming.rest.model.*
import java.time.OffsetDateTime
import java.util.*
import kotlin.random.Random

class VehicleTransferCreateAndDeliverDtoBuilder {
    private var vin: String = createRandomString()
    private var vehicleUsage: VehicleUsageDto = VehicleUsageDto.entries.random()
    private var vehicleResponsiblePerson: String = "00123456"
    private var deliveryDate: OffsetDateTime = OffsetDateTime.now()
    private var mileageAtDelivery: Int = Random.nextInt(1, 99999999)

    fun vin(vin: String) = apply { this.vin = vin }

    fun vehicleUsage(vehicleUsage: VehicleUsageDto) = apply { this.vehicleUsage = vehicleUsage }

    fun vehicleResponsiblePerson(vehicleResponsiblePerson: String) = apply { this.vehicleResponsiblePerson = vehicleResponsiblePerson }

    fun deliveryDate(deliveryDate: OffsetDateTime) = apply { this.deliveryDate = deliveryDate }

    fun mileageAtDelivery(mileageAtDelivery: Int) = apply { this.mileageAtDelivery = mileageAtDelivery }

    fun build(): VehicleTransferCreateAndDeliverDto =
        VehicleTransferCreateAndDeliverDto(
            vin = vin,
            vehicleUsage = vehicleUsage,
            vehicleResponsiblePerson = vehicleResponsiblePerson,
            deliveryDate = deliveryDate,
            mileageAtDelivery = mileageAtDelivery,
        )

    companion object {
        fun buildSingle() = VehicleTransferCreateAndDeliverDtoBuilder().build()

        fun buildMultiple(count: Int): Set<VehicleTransferCreateAndDeliverDto> =
            (1..count)
                .map {
                    VehicleTransferCreateAndDeliverDtoBuilder().build()
                }.toSet()
    }
}

class VehicleTransferChangeVehicleResponsiblePersonDtoBuilder {
    private var nextProcess: NextProcessWithDefaultScrappingDto =
        NextProcessWithDefaultScrappingDto.SCRAPPED_CAR_RETURNED
    private var vehicleResponsiblePerson: String = "00123456"
    private var returnComment: String = createRandomString()
    private var deliveryComment: String = createRandomString()
    private var mileageAtReturn: Int? = Random.nextInt(1, 99999999)

    fun vehicleResponsiblePerson(vehicleResponsiblePerson: String) = apply { this.vehicleResponsiblePerson = vehicleResponsiblePerson }

    fun mileageAtReturn(mileageAtReturn: Int?) = apply { this.mileageAtReturn = mileageAtReturn }

    fun deliveryComment(deliveryComment: String) = apply { this.deliveryComment = deliveryComment }

    fun returnComment(returnComment: String) = apply { this.returnComment = returnComment }

    fun nextProcess(nextProcess: NextProcessWithDefaultScrappingDto) = apply { this.nextProcess = nextProcess }

    fun build(): VehicleTransferChangeVehicleResponsiblePersonDto =
        VehicleTransferChangeVehicleResponsiblePersonDto(
            nextProcess = nextProcess,
            vehicleResponsiblePerson = vehicleResponsiblePerson,
            mileageAtReturn = Optional.ofNullable(mileageAtReturn),
            returnComment = Optional.ofNullable(returnComment),
            deliveryComment = Optional.ofNullable(deliveryComment),
        )

    companion object {
        fun buildSingle() = VehicleTransferChangeVehicleResponsiblePersonDtoBuilder().build()

        fun buildMultiple(count: Int): Set<VehicleTransferChangeVehicleResponsiblePersonDto> =
            (1..count).map { VehicleTransferChangeVehicleResponsiblePersonDtoBuilder().build() }.toSet()
    }
}

class VehicleTransferReturnDtoBuilder {
    private var returnDate: OffsetDateTime = OffsetDateTime.now()
    private var mileageAtReturn: Int? = Random.nextInt(1, 99999999)
    private var returnComment: String = createRandomString()
    private var nextProcess: NextProcessWithDefaultScrappingDto =
        NextProcessWithDefaultScrappingDto.SCRAPPED_CAR_RETURNED

    fun returnDate(returnDate: OffsetDateTime) = apply { this.returnDate = returnDate }

    fun mileageAtReturn(mileageAtReturn: Int?) = apply { this.mileageAtReturn = mileageAtReturn }

    fun returnComment(returnComment: String) = apply { this.returnComment = returnComment }

    fun nextProcess(nextProcess: NextProcessWithDefaultScrappingDto) = apply { this.nextProcess = nextProcess }

    fun build(): VehicleTransferReturnDto =
        VehicleTransferReturnDto(
            returnDate = returnDate,
            mileageAtReturn = Optional.ofNullable(mileageAtReturn),
            returnComment = Optional.ofNullable(returnComment),
            nextProcess = nextProcess,
        )

    companion object {
        fun buildSingle() = VehicleTransferReturnDtoBuilder().build()

        fun buildMultiple(count: Int): Set<VehicleTransferReturnDto> = (1..count).map { VehicleTransferReturnDtoBuilder().build() }.toSet()
    }
}

class VehicleTransferDtoBuilder {
    private var key: String = createRandomString()
    private var vin: String = createRandomString(17)
    private var status: VehicleTransferStatusDto = VehicleTransferStatusDto.entries.random()
    private var vehicleUsage: VehicleUsageDto? = VehicleUsageDto.entries.random()
    private var internalContactPerson: String? = "00${Random.nextInt(100000, 999999)}"
    private var vehicleResponsiblePerson: String? = "00${Random.nextInt(100000, 999999)}"
    private var usingCostCenter: String? = createRandomString(10)
    private var depreciationRelevantCostCenter: String? = createRandomString(10)
    private var internalOrderNumber: String? = createRandomString(12)
    private var maintenanceOrderNumber: String? = createRandomString(12)
    private var deliveryDate: OffsetDateTime? = OffsetDateTime.now().minusDays(Random.nextLong(0, 30))
    private var plannedDeliveryDate: OffsetDateTime? = OffsetDateTime.now().plusDays(Random.nextLong(1, 30))
    private var mileageAtDelivery: Int? = Random.nextInt(1, 99999999)
    private var deliveryComment: String? = createRandomString()
    private var returnDate: OffsetDateTime? = null
    private var plannedReturnDate: OffsetDateTime? = OffsetDateTime.now().plusDays(Random.nextLong(30, 365))
    private var latestReturnDate: OffsetDateTime? = OffsetDateTime.now().plusDays(Random.nextLong(365, 730))
    private var mileageAtReturn: Int? = null
    private var returnComment: String? = null
    private var utilizationArea: UtilizationAreaDto? = UtilizationAreaDto.entries.random()

    fun key(key: String) = apply { this.key = key }

    fun vin(vin: String) = apply { this.vin = vin }

    fun status(status: VehicleTransferStatusDto) = apply { this.status = status }

    fun vehicleUsage(vehicleUsage: VehicleUsageDto?) = apply { this.vehicleUsage = vehicleUsage }

    fun internalContactPerson(internalContactPerson: String?) = apply { this.internalContactPerson = internalContactPerson }

    fun vehicleResponsiblePerson(vehicleResponsiblePerson: String?) = apply { this.vehicleResponsiblePerson = vehicleResponsiblePerson }

    fun usingCostCenter(usingCostCenter: String?) = apply { this.usingCostCenter = usingCostCenter }

    fun depreciationRelevantCostCenter(depreciationRelevantCostCenter: String?) =
        apply { this.depreciationRelevantCostCenter = depreciationRelevantCostCenter }

    fun internalOrderNumber(internalOrderNumber: String?) = apply { this.internalOrderNumber = internalOrderNumber }

    fun maintenanceOrderNumber(maintenanceOrderNumber: String?) = apply { this.maintenanceOrderNumber = maintenanceOrderNumber }

    fun deliveryDate(deliveryDate: OffsetDateTime?) = apply { this.deliveryDate = deliveryDate }

    fun plannedDeliveryDate(plannedDeliveryDate: OffsetDateTime?) = apply { this.plannedDeliveryDate = plannedDeliveryDate }

    fun mileageAtDelivery(mileageAtDelivery: Int?) = apply { this.mileageAtDelivery = mileageAtDelivery }

    fun deliveryComment(deliveryComment: String?) = apply { this.deliveryComment = deliveryComment }

    fun returnDate(returnDate: OffsetDateTime?) = apply { this.returnDate = returnDate }

    fun plannedReturnDate(plannedReturnDate: OffsetDateTime?) = apply { this.plannedReturnDate = plannedReturnDate }

    fun latestReturnDate(latestReturnDate: OffsetDateTime?) = apply { this.latestReturnDate = latestReturnDate }

    fun mileageAtReturn(mileageAtReturn: Int?) = apply { this.mileageAtReturn = mileageAtReturn }

    fun returnComment(returnComment: String?) = apply { this.returnComment = returnComment }

    fun utilizationArea(utilizationArea: UtilizationAreaDto?) = apply { this.utilizationArea = utilizationArea }

    fun build(): VehicleTransferDto =
        VehicleTransferDto(
            key = key,
            vin = vin,
            status = status,
            vehicleUsage = Optional.ofNullable(vehicleUsage),
            internalContactPerson = Optional.ofNullable(internalContactPerson),
            vehicleResponsiblePerson = Optional.ofNullable(vehicleResponsiblePerson),
            usingCostCenter = Optional.ofNullable(usingCostCenter),
            depreciationRelevantCostCenter = Optional.ofNullable(depreciationRelevantCostCenter),
            internalOrderNumber = Optional.ofNullable(internalOrderNumber),
            maintenanceOrderNumber = Optional.ofNullable(maintenanceOrderNumber),
            deliveryDate = Optional.ofNullable(deliveryDate),
            plannedDeliveryDate = Optional.ofNullable(plannedDeliveryDate),
            mileageAtDelivery = Optional.ofNullable(mileageAtDelivery),
            deliveryComment = Optional.ofNullable(deliveryComment),
            returnDate = Optional.ofNullable(returnDate),
            plannedReturnDate = Optional.ofNullable(plannedReturnDate),
            latestReturnDate = Optional.ofNullable(latestReturnDate),
            mileageAtReturn = Optional.ofNullable(mileageAtReturn),
            returnComment = Optional.ofNullable(returnComment),
            utilizationArea = Optional.ofNullable(utilizationArea),
        )

    companion object {
        fun buildSingle() = VehicleTransferDtoBuilder().build()

        fun buildMultiple(count: Int): Set<VehicleTransferDto> = (1..count).map { VehicleTransferDtoBuilder().build() }.toSet()
    }
}
