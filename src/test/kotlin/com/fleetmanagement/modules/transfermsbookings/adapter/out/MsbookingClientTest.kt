package com.fleetmanagement.modules.transfermsbookings.adapter.out

import com.fleetmanagement.modules.transfermsbookings.application.AppointmentType
import com.fleetmanagement.modules.transfermsbookings.application.AppointmentUpdateRequest
import com.fleetmanagement.modules.transfermsbookings.objectMother.MsBookingDTOBuilder
import io.mockk.*
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import org.springframework.http.ResponseEntity
import java.time.OffsetDateTime
import java.time.ZoneOffset

class MsbookingClientTest {
    private var msBookingWebClient: MsBookingWebClient = mockk()

    private val bookingId = "test-booking-id"
    private val serviceId = "test-service-id"
    val msBookingClient =
        MsbookingClient(
            bookingId = bookingId,
            msBookingWebClient = msBookingWebClient,
            serviceId = serviceId,
        )

    @Test
    fun `should return list of appointments when msBookingWebClient returns data`() {
        val msBookingDto =
            MsBookingDTOBuilder().serviceName("Auslieferung").build()

        val msBookingResponse = MsBookingResponse(value = listOf(msBookingDto))
        val readFrom = "2025-03-10T00:00:00"
        val readTo = "2025-03-10T23:59:59"

        val startTimeCaptor = slot<String>()
        val endTimeCaptor = slot<String>()

        every {
            msBookingWebClient.getCalendarView(bookingId, capture(startTimeCaptor), capture(endTimeCaptor))
        } returns msBookingResponse

        val result = msBookingClient.readCalendar(readFrom, readTo)

        assertEquals(1, result.size)
        assertEquals(msBookingDto.id, result[0].appointmentId)
        assertEquals(msBookingDto.customerEmailAddress, result[0].customerEmailAddress)
        assertEquals(msBookingDto.customerName, result[0].customerName)
        assertEquals(AppointmentType.DELIVERY, result[0].appointmentType)
        assertEquals(
            msBookingDto.serviceNotes,
            result[0]
                .keys.deliveryKey
                ?.value
                .toString(),
        )

        assertEquals(readFrom, startTimeCaptor.captured)
        assertEquals(readTo, endTimeCaptor.captured)

        verify(exactly = 1) { msBookingWebClient.getCalendarView(bookingId, any(), any()) }
    }

    @Test
    fun `should ignore invalid serviceNotes`() {
        val msBookingDtoInvalidNote =
            MsBookingDTOBuilder().serviceNotes("").build()
        val msBookingDto =
            MsBookingDTOBuilder().serviceName("Auslieferung").build()

        every {
            msBookingWebClient.getCalendarView(bookingId, any(), any())
        } returns MsBookingResponse(value = listOf(msBookingDto, msBookingDtoInvalidNote))

        val result = msBookingClient.readCalendar("2025-03-10T00:00:00", "2025-03-10T23:59:59")
        assertEquals(1, result.size)
        assertEquals(
            msBookingDto.serviceNotes,
            result
                .single()
                .keys.deliveryKey!!
                .value
                .toString(),
        )
    }

    @Test
    fun `should ignore invalid serviceName`() {
        val msBookingDtoInvalidNote =
            MsBookingDTOBuilder().serviceName("invalid").build()
        val msBookingDto =
            MsBookingDTOBuilder().serviceName("Auslieferung").build()

        every {
            msBookingWebClient.getCalendarView(bookingId, any(), any())
        } returns MsBookingResponse(value = listOf(msBookingDto, msBookingDtoInvalidNote))

        val result = msBookingClient.readCalendar("2025-03-10T00:00:00", "2025-03-10T23:59:59")

        assertEquals(1, result.size)
        assertEquals(
            msBookingDto.serviceNotes,
            result
                .single()
                .keys.deliveryKey!!
                .value
                .toString(),
        )
    }

    @Test
    fun `should call updateAppointment with correct parameters`() {
        val updateRequest = AppointmentUpdateRequest("", "name", "<EMAIL>", emptyList())
        val appointmentId = "appointmentId"
        every { msBookingWebClient.updateAppointment(any(), any(), any()) } returns ResponseEntity.noContent().build()

        msBookingClient.updateMsBookingId(appointmentId, updateRequest)

        // Assert
        verify(exactly = 1) {
            msBookingWebClient.updateAppointment(
                bookingsId = bookingId,
                appointmentId = appointmentId,
                updateRequest = updateRequest,
            )
        }
    }

    @Test
    fun `should create appointment on ms booking`() {
        val start = OffsetDateTime.of(2025, 5, 21, 9, 0, 0, 0, ZoneOffset.UTC)
        val end = OffsetDateTime.of(2025, 5, 21, 12, 0, 0, 0, ZoneOffset.ofHours(2))
        val serviceNotes = "54321"
        val customerEmail = "<EMAIL>"
        val appointmentType = AppointmentType.RETURN

        val expectedDateTime = "2025-05-21T09:00:00"
        val expectedEndDateTime = "2025-05-21T10:00:00" // 2 hours substracted due to timezone difference

        val mockResponse =
            MsBookingDTOBuilder()
                .customerEmailAddress(customerEmail)
                .serviceNotes(serviceNotes)
                .serviceName(appointmentType.description)
                .startDateTime(DateTimeDTO(expectedDateTime, "UTC"))
                .endDateTime(DateTimeDTO(expectedDateTime, "UTC"))
                .build()

        val requestSlot = slot<CreateAppointmentRequestDTO>()
        every { msBookingWebClient.createAppointment(any(), (capture(requestSlot))) } returns mockResponse

        val bookingId = msBookingClient.createAppointment(start, end, serviceNotes, customerEmail, appointmentType)

        // Assert
        assertEquals(mockResponse.id, bookingId)

        verify(exactly = 1) { msBookingWebClient.createAppointment(any(), any()) }

        val capturedRequest = requestSlot.captured
        assertEquals(customerEmail, capturedRequest.customerEmailAddress)
        assertEquals(serviceNotes, capturedRequest.serviceNotes)
        assertEquals(appointmentType.description, capturedRequest.serviceName)
        assertEquals(expectedDateTime, capturedRequest.startDateTime.dateTime)
        assertEquals(expectedEndDateTime, capturedRequest.endDateTime.dateTime)
        assertEquals("UTC", capturedRequest.startDateTime.timeZone)
        assertEquals("UTC", capturedRequest.endDateTime.timeZone)
    }
}
