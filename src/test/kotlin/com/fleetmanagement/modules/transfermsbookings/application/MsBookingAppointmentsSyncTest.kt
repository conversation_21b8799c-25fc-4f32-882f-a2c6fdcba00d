package com.fleetmanagement.modules.transfermsbookings.application

import com.fleetmanagement.modules.transfermsbookings.application.port.ReadAppointments
import com.fleetmanagement.modules.transfermsbookings.objectMother.AppointmentDTOBuilder
import io.mockk.*
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import java.time.OffsetDateTime

class MsBookingAppointmentsSyncTest {
    private val readAppointments: ReadAppointments = mockk()
    private val msbookingNewAppointment: MsbookingNewAppointment = mockk(relaxed = true)
    private val msbookingUpdateAppointment: MsbookingUpdateAppointment = mockk(relaxed = true)

    val msBookingAppointmentsSync =
        MsBookingAppointmentsSync(
            readAppointments = readAppointments,
            msbookingNewAppointment = msbookingNewAppointment,
            msbookingUpdateAppointment = msbookingUpdateAppointment,
        )

    @Test
    fun `syncAppointment calls handleNewAppointment when customer email is missing`() {
        val appointment = AppointmentDTOBuilder().customerEmailAddress("").build()

        every { readAppointments.readCalendar(any(), any()) } returns (listOf(appointment))

        val appointmentTypeSlot = slot<AppointmentType>()
        val keysSlot = slot<AppointmentKeys>()
        val idSlot = slot<String>()
        val startTime = slot<OffsetDateTime>()

        msBookingAppointmentsSync.syncAppointments()

        verify(exactly = 1) {
            msbookingNewAppointment.handleNewAppointment(
                capture(appointmentTypeSlot),
                capture(keysSlot),
                capture(idSlot),
                capture(startTime),
            )
        }

        assertEquals(appointment.appointmentType, appointmentTypeSlot.captured)
        assertEquals(appointment.keys.deliveryKey, keysSlot.captured.deliveryKey)
        assertEquals(appointment.appointmentId, idSlot.captured)
        assertEquals(appointment.startTime, startTime.captured)
    }

    @Test
    fun `syncAppointment calls handleUpdateAppointment when customer email is defined`() {
        val appointment = AppointmentDTOBuilder.buildSingle()

        every { readAppointments.readCalendar(any(), any()) } returns (listOf(appointment))
        val appointmentTypeSlot = slot<AppointmentType>()
        val keysSlot = slot<AppointmentKeys>()
        val time = slot<OffsetDateTime>()

        msBookingAppointmentsSync.syncAppointments()

        verify(exactly = 1) {
            msbookingUpdateAppointment.handleUpdateAppointment(
                capture(appointmentTypeSlot),
                capture(keysSlot),
                capture(time),
            )
        }

        assertEquals(appointment.appointmentType, appointmentTypeSlot.captured)
        assertEquals(appointment.startTime, time.captured)
    }
}
