package com.fleetmanagement.modules.transfermsbookings.application.job

import com.fleetmanagement.modules.transfermsbookings.application.MsBookingAppointmentsSync
import com.fleetmanagement.modules.transfermsbookings.application.MsbookingCancelAppointment
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Test

class MsBookingAppointmentsJobTest {
    private val msBookingAppointmentsSync = mockk<MsBookingAppointmentsSync>()
    private val msbookingCancelAppointment = mockk<MsbookingCancelAppointment>()

    private val msBookingAppointmentsJob: MsBookingAppointmentsJob =
        MsBookingAppointmentsJob(msBookingAppointmentsSync, msbookingCancelAppointment)

    @Test
    fun `should call initialize for all uninitialized PlannedVehicleTransfers when job is called`() {
        justRun { msBookingAppointmentsSync.syncAppointments() }
        justRun { msbookingCancelAppointment.handleCancelAppointment() }

        msBookingAppointmentsJob.execute(null)
        verify {
            msBookingAppointmentsSync.syncAppointments()
        }

        msBookingAppointmentsJob.execute(null)
        verify {
            msbookingCancelAppointment.handleCancelAppointment()
        }
    }
}
