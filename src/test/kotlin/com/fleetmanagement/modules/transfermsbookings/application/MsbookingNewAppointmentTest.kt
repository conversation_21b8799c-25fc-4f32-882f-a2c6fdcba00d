package com.fleetmanagement.modules.transfermsbookings.application

import com.fleetmanagement.modules.transfermsbookings.application.port.UpdateAppointment
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByVehicleId
import com.fleetmanagement.modules.vehicledata.objectmothers.VehicleDataDTOObjectMother
import com.fleetmanagement.modules.vehicleperson.api.dtos.VehiclePersonDetail
import com.fleetmanagement.modules.vehicleperson.api.readvehicleperson.ReadVehiclePersonDetailByEmployeeNumber
import com.fleetmanagement.modules.vehicletransfer.PlannedVehicleTransferBuilder
import com.fleetmanagement.modules.vehicletransfer.VehicleTransferBuilder
import com.fleetmanagement.modules.vehicletransfer.application.port.ReadPlannedVehicleTransferUseCase
import com.fleetmanagement.modules.vehicletransfer.application.port.UpdatePlannedVehicleTransferUseCase
import com.fleetmanagement.modules.vehicletransfer.application.port.UpdateVehicleTransferMsBookingUseCase
import com.fleetmanagement.modules.vehicletransfer.domain.entities.VehicleTransferKey
import io.mockk.*
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.time.OffsetDateTime
import java.util.UUID

class MsbookingNewAppointmentTest {
    private val updateAppointment: UpdateAppointment = mockk()
    private val updatePlannedVehicleTransferUseCase: UpdatePlannedVehicleTransferUseCase = mockk()
    private val updateVehicleTransferUseCase: UpdateVehicleTransferMsBookingUseCase = mockk()
    private val readVehiclePersonDetailByEmployeeNumber: ReadVehiclePersonDetailByEmployeeNumber = mockk()
    private val readVehicleByVehicleId: ReadVehicleByVehicleId = mockk()
    private val readPlannedVehicleTransferUseCase: ReadPlannedVehicleTransferUseCase = mockk()
    val msbookingQuestionsId: MsbookingQuestionsId = mockk()

    private val keys =
        AppointmentKeys(
            deliveryKey = VehicleTransferKey(123),
            returnKey = VehicleTransferKey(1234),
        )
    private val appointmentId = "appointmentId123"
    private val startDateTime = OffsetDateTime.now()

    private val vehicleId = UUID.randomUUID()

    val msbookingNewAppointment =
        MsbookingNewAppointment(
            updateAppointment,
            updatePlannedVehicleTransferUseCase,
            updateVehicleTransferUseCase,
            readVehiclePersonDetailByEmployeeNumber,
            readVehicleByVehicleId,
            msbookingQuestionsId,
            readPlannedVehicleTransferUseCase,
        )

    @BeforeEach
    fun setUp() {
        clearMocks(
            updatePlannedVehicleTransferUseCase,
            updateVehicleTransferUseCase,
            readVehiclePersonDetailByEmployeeNumber,
            readVehicleByVehicleId,
            msbookingQuestionsId,
        )

        every { msbookingQuestionsId.deliveryVin } returns "deliveryVinQuestionId"
        every { msbookingQuestionsId.deliveryLicensePlate } returns "deliveryLicensePlateQuestionId"
        every { msbookingQuestionsId.deliveryModel } returns "deliveryModelQuestionId"
        every { msbookingQuestionsId.returnVin } returns "returnVinQuestionId"
        every { msbookingQuestionsId.returnLicensePlate } returns "returnLicensePlateQuestionId"
        every { msbookingQuestionsId.returnModel } returns "returnModelQuestionId"
    }

    @Test
    fun `should handle DELIVERY appointment type correctly`() {
        val appointmentType = AppointmentType.DELIVERY
        val vehicleResponsiblePerson = "00508177"
        val vehiclePersonDetail =
            createVehiclePersonDetails(firstName = "John", lastName = "Doe", employeeNumber = vehicleResponsiblePerson)
        val vehicleData = VehicleDataDTOObjectMother.vehicleDTOWithAllFieldsPopulated()
        val planedVehicleTransfer =
            PlannedVehicleTransferBuilder()
                .vehicleResponsiblePerson(
                    vehicleResponsiblePerson,
                ).vehicleId(vehicleId)
                .build()

        val updateRequestCaptor = slot<AppointmentUpdateRequest>()

        every { updatePlannedVehicleTransferUseCase.updatePlannedDeliveryDate(startDateTime, keys.deliveryKey!!) } returns
            planedVehicleTransfer
        every { updatePlannedVehicleTransferUseCase.updateMsbookingAppointmentId(appointmentId, keys.deliveryKey!!) } returns
            planedVehicleTransfer
        every { updateAppointment.updateMsBookingId(appointmentId, capture(updateRequestCaptor)) } returns Unit
        every {
            readVehiclePersonDetailByEmployeeNumber.readVehiclePersonDetailByEmployeeNumber(
                planedVehicleTransfer.vehicleResponsiblePerson!!.value,
            )
        } returns
            vehiclePersonDetail
        every { readVehicleByVehicleId.readVehicleById(vehicleId) } returns vehicleData

        msbookingNewAppointment.handleNewAppointment(appointmentType, keys, appointmentId, startDateTime)

        val capturedRequest = updateRequestCaptor.captured
        assertNotNull(capturedRequest)
        verify(exactly = 1) {
            updatePlannedVehicleTransferUseCase.updatePlannedDeliveryDate(startDateTime, keys.deliveryKey!!)
        }
        verify(exactly = 1) {
            updateAppointment.updateMsBookingId(any(), any())
        }
        assertEquals("${vehiclePersonDetail.firstName} ${vehiclePersonDetail.lastName}", capturedRequest.customerName)
        assertEquals(vehiclePersonDetail.companyEmail, capturedRequest.customerEmailAddress)
    }

    @Test
    fun `should handle RETURN appointment type correctly`() {
        val appointmentType = AppointmentType.RETURN
        val vehicleResponsiblePerson = "00508177"
        val vehiclePersonDetail =
            createVehiclePersonDetails(firstName = "Jane", lastName = "Doe", employeeNumber = vehicleResponsiblePerson)
        val vehicleData = VehicleDataDTOObjectMother.vehicleDTOWithAllFieldsPopulated()
        val vehicleTransfer =
            VehicleTransferBuilder()
                .vehicleResponsiblePerson(
                    vehicleResponsiblePerson,
                ).vehicleId(vehicleId)
                .build()

        val updateRequestCaptor = slot<AppointmentUpdateRequest>()

        every { updateVehicleTransferUseCase.updatePlannedReturnDate(startDateTime, keys.returnKey!!) } returns vehicleTransfer
        every { updateVehicleTransferUseCase.updateMsbookingAppointmentId(appointmentId, keys.returnKey!!) } returns vehicleTransfer
        every { updateAppointment.updateMsBookingId(appointmentId, capture(updateRequestCaptor)) } returns Unit
        every {
            readVehiclePersonDetailByEmployeeNumber.readVehiclePersonDetailByEmployeeNumber(
                vehicleTransfer.vehicleResponsiblePerson!!.value,
            )
        } returns vehiclePersonDetail
        every { readVehicleByVehicleId.readVehicleById(vehicleId) } returns vehicleData

        msbookingNewAppointment.handleNewAppointment(appointmentType, keys, appointmentId, startDateTime)

        val capturedRequest = updateRequestCaptor.captured
        assertNotNull(capturedRequest)

        verify(exactly = 1) {
            updateVehicleTransferUseCase.updatePlannedReturnDate(startDateTime, keys.returnKey!!)
        }
        verify(exactly = 1) {
            updateAppointment.updateMsBookingId(any(), any())
        }
        assertEquals("${vehiclePersonDetail.firstName} ${vehiclePersonDetail.lastName}", capturedRequest.customerName)
        assertEquals(vehiclePersonDetail.companyEmail, capturedRequest.customerEmailAddress)
    }

    @Test
    fun `should handle EXCHANGE appointment type correctly`() {
        val appointmentType = AppointmentType.EXCHANGE
        val deliveryKey = VehicleTransferKey(1234)
        val returnKey = VehicleTransferKey(2345)
        val returnedVehicleId = UUID.randomUUID()
        val deliverVehicleId = UUID.randomUUID()

        val keys = AppointmentKeys(deliveryKey = deliveryKey)
        val vehicleResponsiblePerson = "00508177"
        val vehiclePersonDetail =
            createVehiclePersonDetails(firstName = "Jane", lastName = "Doe", employeeNumber = vehicleResponsiblePerson)
        val vehicleData = VehicleDataDTOObjectMother.vehicleDTOWithAllFieldsPopulated()

        val returnTransfer =
            VehicleTransferBuilder()
                .vehicleTransferKey(returnKey.value)
                .vehicleResponsiblePerson(vehicleResponsiblePerson)
                .vehicleId(returnedVehicleId)
                .build()

        val deliveryTransfer =
            PlannedVehicleTransferBuilder()
                .vehicleTransferKey(deliveryKey.value)
                .vehicleResponsiblePerson(vehicleResponsiblePerson)
                .vehicleId(deliverVehicleId)
                .build()

        deliveryTransfer.updatePredecessor(predecessor = returnTransfer)

        val updateRequestCaptor = mutableListOf<AppointmentUpdateRequest>()

        every { readPlannedVehicleTransferUseCase.findByKey(deliveryKey) } returns
            deliveryTransfer

        every { updatePlannedVehicleTransferUseCase.updatePlannedDeliveryDate(startDateTime, deliveryKey) } returns deliveryTransfer
        every { updatePlannedVehicleTransferUseCase.updateMsbookingAppointmentId(appointmentId, deliveryKey) } returns deliveryTransfer

        every { updateVehicleTransferUseCase.updatePlannedReturnDate(startDateTime, returnKey) } returns returnTransfer
        every { updateVehicleTransferUseCase.updateMsbookingAppointmentId(appointmentId, returnKey) } returns returnTransfer

        every {
            updateAppointment.updateMsBookingId(appointmentId, capture(updateRequestCaptor))
        } returns Unit

        every {
            readVehiclePersonDetailByEmployeeNumber.readVehiclePersonDetailByEmployeeNumber(vehicleResponsiblePerson)
        } returns vehiclePersonDetail

        every { readVehicleByVehicleId.readVehicleById(deliverVehicleId) } returns vehicleData
        every { readVehicleByVehicleId.readVehicleById(returnedVehicleId) } returns vehicleData

        msbookingNewAppointment.handleNewAppointment(appointmentType, keys, appointmentId, startDateTime)

        verify(exactly = 1) { updatePlannedVehicleTransferUseCase.updatePlannedDeliveryDate(startDateTime, deliveryKey) }
        verify(exactly = 1) { updateVehicleTransferUseCase.updatePlannedReturnDate(startDateTime, returnKey) }
        verify(exactly = 1) { updateAppointment.updateMsBookingId(any(), any()) }

        assertEquals(1, updateRequestCaptor.size)
    }

    @Test
    fun `should not handle EXCHANGE when predecessor (return key) is missing`() {
        val deliveryKey = VehicleTransferKey(1234)
        val keys = AppointmentKeys(deliveryKey = deliveryKey)

        every { readPlannedVehicleTransferUseCase.findByKey(deliveryKey) } returns null // no predecessor

        msbookingNewAppointment.handleNewAppointment(AppointmentType.EXCHANGE, keys, appointmentId, startDateTime)

        verify(exactly = 0) { updatePlannedVehicleTransferUseCase.updatePlannedDeliveryDate(any(), any()) }
        verify(exactly = 0) { updateVehicleTransferUseCase.updatePlannedReturnDate(any(), any()) }
        verify(exactly = 0) { updateAppointment.updateMsBookingId(any(), any()) }
    }

    @Test
    fun `should handle exception when server returns not 2xx the appointment update`() {
        val appointmentType = AppointmentType.DELIVERY
        val vehicleResponsiblePerson = "00508177"
        val vehiclePersonDetail =
            createVehiclePersonDetails(firstName = "John", lastName = "Doe", employeeNumber = vehicleResponsiblePerson)
        val vehicleData = VehicleDataDTOObjectMother.vehicleDTOWithAllFieldsPopulated()
        val planedVehicleTransfer =
            PlannedVehicleTransferBuilder()
                .vehicleResponsiblePerson(vehicleResponsiblePerson)
                .vehicleId(vehicleId)
                .build()

        val updateRequestCaptor = slot<AppointmentUpdateRequest>()

        every { updatePlannedVehicleTransferUseCase.updatePlannedDeliveryDate(startDateTime, keys.deliveryKey!!) } returns
            planedVehicleTransfer
        every { updatePlannedVehicleTransferUseCase.updateMsbookingAppointmentId(appointmentId, keys.deliveryKey!!) } returns
            planedVehicleTransfer
        every { updateAppointment.updateMsBookingId(appointmentId, capture(updateRequestCaptor)) } returns Unit

        every {
            readVehiclePersonDetailByEmployeeNumber.readVehiclePersonDetailByEmployeeNumber(
                planedVehicleTransfer.vehicleResponsiblePerson!!.value,
            )
        } returns vehiclePersonDetail
        every { readVehicleByVehicleId.readVehicleById(vehicleId) } returns vehicleData

        msbookingNewAppointment.handleNewAppointment(appointmentType, keys, appointmentId, startDateTime)

        // todo: how to verify exception was thrown ?
        assertTrue(true)
    }

    fun createVehiclePersonDetails(
        firstName: String,
        lastName: String,
        street: String? = null,
        postalCode: String? = null,
        city: String? = null,
        country: String? = null,
        employeeNumber: String,
    ) = VehiclePersonDetail(
        firstName = firstName,
        lastName = lastName,
        street = street,
        postalCode = postalCode,
        city = city,
        country = country,
        companyEmail = "someemail",
        accountingArea = "accounting-area",
        businessPartnerId = "12345",
        employeeNumber = employeeNumber,
    )
}
