package com.fleetmanagement.modules.transfermsbookings.application

import com.fleetmanagement.modules.transfermsbookings.application.port.ReadAppointments
import com.fleetmanagement.modules.transfermsbookings.objectMother.AppointmentDTOBuilder
import com.fleetmanagement.modules.vehicletransfer.application.port.ReadPlannedVehicleTransferUseCase
import com.fleetmanagement.modules.vehicletransfer.application.port.ReadVehicleTransferUseCase
import com.fleetmanagement.modules.vehicletransfer.application.port.UpdatePlannedVehicleTransferUseCase
import com.fleetmanagement.modules.vehicletransfer.application.port.UpdateVehicleTransferMsBookingUseCase
import com.fleetmanagement.modules.vehicletransfer.domain.entities.VehicleTransferKey
import io.mockk.*
import org.junit.jupiter.api.Test

class MsbookingCancelAppointmentTest {
    private val readAppointments: ReadAppointments = mockk()
    private val readPlannedVehicleTransferUseCase: ReadPlannedVehicleTransferUseCase = mockk()
    private val readVehicleTransferUseCase: ReadVehicleTransferUseCase = mockk()
    private val updatePlannedVehicleTransferUseCase: UpdatePlannedVehicleTransferUseCase = mockk(relaxed = true)
    private val updateVehicleTransferUseCase: UpdateVehicleTransferMsBookingUseCase = mockk(relaxed = true)

    val msbookingCancelAppointment =
        MsbookingCancelAppointment(
            cancelTimeframeInDays = 3,
            readPlannedVehicleTransferUseCase = readPlannedVehicleTransferUseCase,
            readVehicleTransferUseCase = readVehicleTransferUseCase,
            updatePlannedVehicleTransferUseCase = updatePlannedVehicleTransferUseCase,
            updateVehicleTransferUseCase = updateVehicleTransferUseCase,
            readAppointments = readAppointments,
        )

    @Test
    fun `handleCancelAppointment should cancel non-existent future appointments`() {
        val appointments = AppointmentDTOBuilder.buildMultiple(4).toList()
        val appointmentDoNotExist = "appointmentDoNotExist"
        val keyOfAppointmentDoNotExist = VehicleTransferKey(1)

        every { readAppointments.readCalendar(any(), any()) } returns appointments
        every { readPlannedVehicleTransferUseCase.readAllAppointmentsIdAndKeyWherePlannedDeliveryDateBetween(any(), any()) } returns
            listOf(
                Pair(appointments.first().appointmentId, VehicleTransferKey(2)),
                Pair(appointmentDoNotExist, keyOfAppointmentDoNotExist),
            )
        every { readVehicleTransferUseCase.readAllAppointmentsIdAndKeyWherePlannedReturnDateBetween(any(), any()) } returns
            listOf(
                Pair(appointments.first().appointmentId, VehicleTransferKey(2)),
                Pair(appointmentDoNotExist, keyOfAppointmentDoNotExist),
            )

        msbookingCancelAppointment.handleCancelAppointment()

        verify { updatePlannedVehicleTransferUseCase.updateMsbookingAppointmentId(null, keyOfAppointmentDoNotExist) }
        verify { updatePlannedVehicleTransferUseCase.updatePlannedDeliveryDate(null, keyOfAppointmentDoNotExist) }
        verify { updateVehicleTransferUseCase.updateMsbookingAppointmentId(null, keyOfAppointmentDoNotExist) }
        verify { updateVehicleTransferUseCase.updatePlannedReturnDate(null, keyOfAppointmentDoNotExist) }
    }

    @Test
    fun `handleCancelAppointment should do nothing when there are no matching appointments`() {
        val appId = "id"
        val appointments = AppointmentDTOBuilder().appointmentId(appId).build()

        every { readAppointments.readCalendar(any(), any()) } returns listOf(appointments)
        every { readPlannedVehicleTransferUseCase.readAllAppointmentsIdAndKeyWherePlannedDeliveryDateBetween(any(), any()) } returns
            listOf(Pair(appId, VehicleTransferKey(2)))
        every { readVehicleTransferUseCase.readAllAppointmentsIdAndKeyWherePlannedReturnDateBetween(any(), any()) } returns
            listOf(Pair(appId, VehicleTransferKey(2)))

        msbookingCancelAppointment.handleCancelAppointment()

        verify(exactly = 0) { updatePlannedVehicleTransferUseCase.updateMsbookingAppointmentId(any(), any()) }
        verify(exactly = 0) { updatePlannedVehicleTransferUseCase.updatePlannedDeliveryDate(any(), any()) }
        verify(exactly = 0) { updateVehicleTransferUseCase.updateMsbookingAppointmentId(any(), any()) }
        verify(exactly = 0) { updateVehicleTransferUseCase.updatePlannedReturnDate(any(), any()) }
    }
}
