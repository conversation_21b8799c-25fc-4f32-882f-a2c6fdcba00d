package com.fleetmanagement.modules.transfermsbookings.application

import com.fleetmanagement.modules.vehicletransfer.application.port.UpdatePlannedVehicleTransferUseCase
import com.fleetmanagement.modules.vehicletransfer.application.port.UpdateVehicleTransferMsBookingUseCase
import com.fleetmanagement.modules.vehicletransfer.domain.entities.VehicleTransferKey
import io.mockk.*
import org.junit.jupiter.api.Test
import java.time.OffsetDateTime

class MsbookingUpdateAppointmentTest {
    private val updatePlannedVehicleTransferUseCase: UpdatePlannedVehicleTransferUseCase = mockk(relaxed = true)
    private val updateVehicleTransferUseCase: UpdateVehicleTransferMsBookingUseCase = mockk(relaxed = true)

    val msbookingUpdateAppointment =
        MsbookingUpdateAppointment(
            updatePlannedVehicleTransferUseCase,
            updateVehicleTransferUseCase,
        )

    private val appointmentKeys =
        AppointmentKeys(
            deliveryKey = VehicleTransferKey(123),
            returnKey = VehicleTransferKey(1234),
        )
    private val startDateTime = OffsetDateTime.now()

    @Test
    fun `should update planned delivery date for DELIVERY appointment type`() {
        val appointmentType = AppointmentType.DELIVERY

        val msbookingUpdateAppointment =
            MsbookingUpdateAppointment(
                updatePlannedVehicleTransferUseCase,
                updateVehicleTransferUseCase,
            )

        msbookingUpdateAppointment.handleUpdateAppointment(appointmentType, appointmentKeys, startDateTime)

        verify(exactly = 1) {
            updatePlannedVehicleTransferUseCase.updatePlannedDeliveryDate(
                plannedDeliveryDate = startDateTime,
                key = appointmentKeys.deliveryKey!!,
            )
        }
        verify(exactly = 0) {
            updateVehicleTransferUseCase.updatePlannedReturnDate(any(), any())
        }
    }

    @Test
    fun `should update planned return date for RETURN appointment type`() {
        val appointmentType = AppointmentType.RETURN

        val msbookingUpdateAppointment =
            MsbookingUpdateAppointment(
                updatePlannedVehicleTransferUseCase,
                updateVehicleTransferUseCase,
            )

        msbookingUpdateAppointment.handleUpdateAppointment(appointmentType, appointmentKeys, startDateTime)

        verify(exactly = 0) {
            updatePlannedVehicleTransferUseCase.updatePlannedDeliveryDate(any(), any())
        }
        verify(exactly = 1) {
            updateVehicleTransferUseCase.updatePlannedReturnDate(startDateTime, appointmentKeys.returnKey!!)
        }
    }

    @Test
    fun `should update both planned delivery and return dates for EXCHANGE appointment type`() {
        val appointmentType = AppointmentType.EXCHANGE

        msbookingUpdateAppointment.handleUpdateAppointment(appointmentType, appointmentKeys, startDateTime)
        verify(exactly = 1) {
            updatePlannedVehicleTransferUseCase.updatePlannedDeliveryDate(startDateTime, appointmentKeys.deliveryKey!!)
        }
        verify(exactly = 1) {
            updateVehicleTransferUseCase.updatePlannedReturnDate(startDateTime, appointmentKeys.returnKey!!)
        }
    }
}
