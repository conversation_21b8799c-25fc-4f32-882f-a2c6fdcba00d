/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
@file:Suppress("PropertyName")

package com.fleetmanagement.modules.transfermsbookings.architecture

import com.tngtech.archunit.core.importer.ImportOption
import com.tngtech.archunit.junit.AnalyzeClasses

@AnalyzeClasses(packages = [MSBOOKING_DEFAULT_PACKAGE], importOptions = [ImportOption.DoNotIncludeTests::class])
class ArchitectureRulesTest {
//    @ArchTest
//    val `should respect onion architecture`: ArchRule =
//        onionArchitecture()
//            .domainModels(DOMAIN_PACKAGE)
//            .domainServices("..domain.service..")
//            .adapter("all", ADAPTER_PACKAGE)
//            .applicationServices(APPLICATION_PACKAGE)
//            .allowEmptyShould(true)
}

// Packages
const val MSBOOKING_DEFAULT_PACKAGE = "com.fleetmanagement.modules.transfermsbookings"
const val ADAPTER_PACKAGE = "..adapter.."
const val APPLICATION_PACKAGE = "..application.."
const val DOMAIN_PACKAGE = "..domain.."
