package com.fleetmanagement.modules.transfermsbookings.objectMother

import com.fleetmanagement.modules.transfermsbookings.application.AppointmentDto
import com.fleetmanagement.modules.transfermsbookings.application.AppointmentKeys
import com.fleetmanagement.modules.transfermsbookings.application.AppointmentType
import com.fleetmanagement.modules.vehicletransfer.domain.entities.VehicleTransferKey
import java.time.OffsetDateTime
import kotlin.random.Random

class AppointmentDTOBuilder {
    private var appointmentId: String = Random.nextInt(1000, 9999).toString()
    private var startTime: OffsetDateTime = OffsetDateTime.now()
    private var endTime: OffsetDateTime = OffsetDateTime.now().plusHours(1)
    private var customerEmailAddress: String? = "<EMAIL>"
    private var customerName: String? = "John Doe"
    private var keys: AppointmentKeys = AppointmentKeys(deliveryKey = VehicleTransferKey(1))
    private var appointmentType: AppointmentType = AppointmentType.DELIVERY
    private var lastUpdatedDateTime: OffsetDateTime = OffsetDateTime.now()

    fun appointmentId(appointmentId: String) = apply { this.appointmentId = appointmentId }

    fun startTime(startTime: OffsetDateTime) = apply { this.startTime = startTime }

    fun endTime(endTime: OffsetDateTime) = apply { this.endTime = endTime }

    fun customerEmailAddress(customerEmailAddress: String?) = apply { this.customerEmailAddress = customerEmailAddress }

    fun customerName(customerName: String?) = apply { this.customerName = customerName }

    fun keys(keys: AppointmentKeys) = apply { this.keys = keys }

    fun appointmentType(appointmentType: AppointmentType) = apply { this.appointmentType = appointmentType }

    fun lastUpdatedDateTime(lastUpdatedDateTime: OffsetDateTime) = apply { this.lastUpdatedDateTime = lastUpdatedDateTime }

    fun build(): AppointmentDto =
        AppointmentDto(
            appointmentId = appointmentId,
            startTime = startTime,
            endTime = endTime,
            customerEmailAddress = customerEmailAddress,
            customerName = customerName,
            keys = keys,
            appointmentType = appointmentType,
            lastUpdatedDateTime = lastUpdatedDateTime,
        )

    companion object {
        fun buildSingle() = AppointmentDTOBuilder().build()

        fun buildMultiple(count: Int): Set<AppointmentDto> = (1..count).map { AppointmentDTOBuilder().build() }.toSet()
    }
}
