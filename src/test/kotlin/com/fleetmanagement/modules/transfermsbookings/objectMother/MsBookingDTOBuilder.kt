package com.fleetmanagement.modules.transfermsbookings.objectMother

import com.fleetmanagement.modules.transfermsbookings.adapter.out.AddressDTO
import com.fleetmanagement.modules.transfermsbookings.adapter.out.CoordinatesDTO
import com.fleetmanagement.modules.transfermsbookings.adapter.out.DateTimeDTO
import com.fleetmanagement.modules.transfermsbookings.adapter.out.LocationDTO
import com.fleetmanagement.modules.transfermsbookings.adapter.out.MsBookingDTO
import java.time.OffsetDateTime
import java.time.format.DateTimeFormatter
import kotlin.random.Random

val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSSSSS'Z'")

class MsBookingDTOBuilder {
    private var id: String = Random.nextInt(1000, 9999).toString()
    private var selfServiceAppointmentId: String = Random.nextInt(1000, 9999).toString()
    private var customerName: String = "John Doe"
    private var customerEmailAddress: String = "<EMAIL>"
    private var serviceId: String = "service-" + Random.nextInt(1, 100)
    private var serviceName: String = "Car Maintenance"
    private var serviceNotes: String = Random.nextInt(1000, 9999).toString()
    private var duration: String = "60"
    private var startDateTime: DateTimeDTO = DateTimeDTO(OffsetDateTime.now().format(formatter), "UTC")
    private var endDateTime: DateTimeDTO = DateTimeDTO(OffsetDateTime.now().plusHours(1).format(formatter), "UTC")
    private var lastUpdatedDateTime: String = OffsetDateTime.now().format(formatter)

    fun id(id: String) = apply { this.id = id }

    fun serviceNotes(serviceNotes: String) = apply { this.serviceNotes = serviceNotes }

    fun selfServiceAppointmentId(selfServiceAppointmentId: String) = apply { this.selfServiceAppointmentId = selfServiceAppointmentId }

    fun customerName(customerName: String) = apply { this.customerName = customerName }

    fun customerEmailAddress(customerEmailAddress: String) = apply { this.customerEmailAddress = customerEmailAddress }

    fun serviceId(serviceId: String) = apply { this.serviceId = serviceId }

    fun serviceName(serviceName: String) = apply { this.serviceName = serviceName }

    fun duration(duration: String) = apply { this.duration = duration }

    fun startDateTime(startDateTime: DateTimeDTO) = apply { this.startDateTime = startDateTime }

    fun endDateTime(endDateTime: DateTimeDTO) = apply { this.endDateTime = endDateTime }

    fun build(): MsBookingDTO =
        MsBookingDTO(
            id = id,
            selfServiceAppointmentId = selfServiceAppointmentId,
            additionalInformation = null,
            isLocationOnline = false,
            joinWebUrl = null,
            customerName = customerName,
            customerEmailAddress = customerEmailAddress,
            customerPhone = "1234567890",
            customerTimeZone = "UTC",
            customerNotes = null,
            serviceId = serviceId,
            serviceName = serviceName,
            duration = duration,
            preBuffer = "10",
            postBuffer = "5",
            priceType = "Fixed",
            price = 100.0,
            serviceNotes = serviceNotes,
            optOutOfCustomerEmail = false,
            staffMemberIds = listOf("staff-1"),
            smsNotificationsEnabled = true,
            anonymousJoinWebUrl = null,
            maximumAttendeesCount = 1,
            filledAttendeesCount = 1,
            createdDateTime = lastUpdatedDateTime,
            lastUpdatedDateTime = lastUpdatedDateTime,
            isCustomerAllowedToManageBooking = true,
            appointmentLabel = null,
            startDateTime = DateTimeDTO(dateTime = startDateTime.dateTime, timeZone = "UTC"),
            endDateTime = DateTimeDTO(dateTime = endDateTime.dateTime, timeZone = "UTC"),
            serviceLocation =
                LocationDTO(
                    displayName = "Service Center",
                    locationEmailAddress = null,
                    locationUri = null,
                    locationType = "Physical",
                    uniqueId = "loc-1",
                    uniqueIdType = "String",
                    address =
                        AddressDTO(
                            street = "123 Main St",
                            city = "Metropolis",
                            state = "NY",
                            countryOrRegion = "USA",
                            postalCode = "10001",
                        ),
                    coordinates =
                        CoordinatesDTO(
                            altitude = 0.0,
                            latitude = 40.7128,
                            longitude = -74.0060,
                            accuracy = 1.0,
                            altitudeAccuracy = 1.0,
                        ),
                ),
            reminders = null,
            customers = null,
        )

    companion object {
        fun buildSingle() = MsBookingDTOBuilder().build()

        fun buildMultiple(count: Int): Set<MsBookingDTO> = (1..count).map { MsBookingDTOBuilder().build() }.toSet()
    }
}
