/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

@file:Suppress("ktlint:standard:no-wildcard-imports")

package com.fleetmanagement.modules.documentgeneration.features.generategreeninsurancecard

import com.fleetmanagement.modules.documentgeneration.api.GenerateGreenInsuranceCardException
import com.fleetmanagement.modules.documentgeneration.features.FillTemplateService
import com.fleetmanagement.modules.documentgeneration.features.FormFieldNotFoundException
import com.fleetmanagement.modules.documentgeneration.objectmothers.DocumentObjectMother
import com.fleetmanagement.modules.documentgeneration.objectmothers.RegistrationOrderObjectMother
import com.fleetmanagement.modules.vehicleregistration.api.ReadRegistrationOrder
import com.fleetmanagement.modules.vehicleregistration.api.dto.LatestRegistrationOrder
import com.fleetmanagement.modules.vehicleregistration.api.dto.VehicleRegistrationAPIResponse
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.containsInAnyOrder
import org.hamcrest.Matchers.equalTo
import org.hamcrest.Matchers.everyItem
import org.hamcrest.Matchers.hasProperty
import org.hamcrest.Matchers.instanceOf
import org.hamcrest.Matchers.`is`
import org.hamcrest.Matchers.notNullValue
import org.hamcrest.Matchers.nullValue
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.time.OffsetDateTime
import java.util.UUID

class GenerateGreenInsuranceCardServiceTest {
    private val readRegistrationOrder: ReadRegistrationOrder = mockk()

    private val fillTemplateService: FillTemplateService = mockk()

    @Test
    fun `should generate GreenInsurance card for provided vehicleIds`() {
        val vehicleIds = (1..5).map { UUID.randomUUID() }

        val registrationOrderResponse = RegistrationOrderObjectMother.latestRegistrationOrderSuccessResponse(vehicleIds)

        every { readRegistrationOrder.getLatestOrdersBy(any()) } returns registrationOrderResponse

        every { fillTemplateService.fillTemplate(any(), any()) } returns DocumentObjectMother.getDocument()

        val result =
            GenerateGreenInsuranceCardService(
                fillTemplateService,
                readRegistrationOrder,
            ).generateGreenInsuranceCard(vehicleIds)

        assertThat(result.data, notNullValue())
        assertThat(result.errors, `is`(emptyList()))

        verify(exactly = 1) { readRegistrationOrder.getLatestOrdersBy(vehicleIds) }
        verify(exactly = 5) { fillTemplateService.fillTemplate(TEMPLATE, any()) }
    }

    @Test
    fun `should return error details for vehicles which failed to generate green insurance card`() {
        val vehicleIds = (1..5).map { UUID.randomUUID() }

        every { readRegistrationOrder.getLatestOrdersBy(any()) } returns
            RegistrationOrderObjectMother.latestRegistrationOrderFailureResponse(vehicleIds)

        every { fillTemplateService.fillTemplate(any(), any()) } returns DocumentObjectMother.getDocument()

        val result =
            GenerateGreenInsuranceCardService(
                fillTemplateService,
                readRegistrationOrder,
            ).generateGreenInsuranceCard(vehicleIds)

        assertThat(result.data, nullValue())
        assertThat(result.errors.size, equalTo(5))
        assertThat(result.errors.map { it.vehicleId }, containsInAnyOrder(*vehicleIds.toTypedArray()))
        assertThat(
            result.errors,
            everyItem(hasProperty("message", equalTo("Error generating green insurance card. LastRegistrationDate is missing"))),
        )

        verify(exactly = 1) { readRegistrationOrder.getLatestOrdersBy(vehicleIds) }
        verify(exactly = 0) { fillTemplateService.fillTemplate(any(), any()) }
    }

    @Test
    fun `should return partial success response when green insurance card is generated for some vehicles`() {
        val successVehicleId = UUID.randomUUID()
        val failureVehicleId = UUID.randomUUID()

        val partialSuccessResponse =
            VehicleRegistrationAPIResponse(
                data =
                    listOf(
                        LatestRegistrationOrder(
                            vehicleId = successVehicleId,
                            vin = "WP0AA2A97MS123456",
                            lastRegistrationDate = OffsetDateTime.now(),
                            licencePlate = "SPO 1234",
                            sfme = true,
                            registrationType = 1,
                        ),
                        LatestRegistrationOrder(
                            vehicleId = failureVehicleId,
                            vin = "WP0AA2A97MS123456",
                            lastRegistrationDate = OffsetDateTime.now(),
                            licencePlate = null,
                            sfme = false,
                            registrationType = 2,
                        ),
                    ),
            )

        every { readRegistrationOrder.getLatestOrdersBy(any()) } returns
            partialSuccessResponse

        every { fillTemplateService.fillTemplate(any(), any()) } returns DocumentObjectMother.getDocument()

        val result =
            GenerateGreenInsuranceCardService(
                fillTemplateService,
                readRegistrationOrder,
            ).generateGreenInsuranceCard(listOf(successVehicleId, failureVehicleId))

        assertThat(result.data, notNullValue())
        assertThat(result.errors.size, equalTo(1))
        assertThat(result.errors.map { it.vehicleId }, containsInAnyOrder(failureVehicleId))
        assertThat(
            result.errors,
            everyItem(hasProperty("message", equalTo("Error generating green insurance card. LicensePlate is missing"))),
        )

        verify(exactly = 1) {
            readRegistrationOrder.getLatestOrdersBy(listOf(successVehicleId, failureVehicleId))
        }
        verify(exactly = 1) { fillTemplateService.fillTemplate(TEMPLATE, any()) }
    }

    @Test
    fun `should throw GenerateGreenInsuranceCardException if form field is not found in template`() {
        val vehicleIds = (1..5).map { UUID.randomUUID() }

        every { readRegistrationOrder.getLatestOrdersBy(any()) } returns
            RegistrationOrderObjectMother.latestRegistrationOrderSuccessResponse(vehicleIds)

        every { fillTemplateService.fillTemplate(any(), any()) } throws FormFieldNotFoundException("Form Field does not exists")

        val exception =
            assertThrows<GenerateGreenInsuranceCardException> {
                GenerateGreenInsuranceCardService(
                    fillTemplateService,
                    readRegistrationOrder,
                ).generateGreenInsuranceCard(vehicleIds)
            }

        assertThat(exception.message, equalTo("Error generating green insurance card. Form Field does not exists"))
        assertThat(exception.cause, instanceOf(FormFieldNotFoundException::class.java))

        verify(exactly = 1) { readRegistrationOrder.getLatestOrdersBy(vehicleIds) }
        verify(exactly = 1) { fillTemplateService.fillTemplate(TEMPLATE, any()) }
    }

    @Test
    fun `should return response with errors when registration order does not find any vehicles`() {
        val vehicleWithoutOrderId = UUID.randomUUID()

        val registrationOrderResponse =
            VehicleRegistrationAPIResponse(
                data = listOf<LatestRegistrationOrder>(),
            )

        every { readRegistrationOrder.getLatestOrdersBy(any()) } returns registrationOrderResponse
        every { fillTemplateService.fillTemplate(any(), any()) } returns DocumentObjectMother.getDocument()

        val result =
            GenerateGreenInsuranceCardService(
                fillTemplateService,
                readRegistrationOrder,
            ).generateGreenInsuranceCard(listOf(vehicleWithoutOrderId))

        assertThat(result.data, nullValue())
        assertThat(result.errors.size, equalTo(1))
        assertThat(result.errors[0].vehicleId, equalTo(vehicleWithoutOrderId))

        verify(exactly = 1) { readRegistrationOrder.getLatestOrdersBy(listOf(vehicleWithoutOrderId)) }
    }

    companion object {
        private const val TEMPLATE = "GreenInsuranceCard.docx"
    }
}
