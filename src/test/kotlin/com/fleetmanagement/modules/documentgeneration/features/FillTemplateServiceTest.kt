/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.documentgeneration.features

import com.aspose.barcode.barcoderecognition.BarCodeReader
import com.aspose.barcode.barcoderecognition.DecodeType
import com.aspose.words.FieldHyperlink
import com.aspose.words.FieldStart
import com.aspose.words.FieldType
import com.aspose.words.ImageType
import com.aspose.words.NodeType
import com.aspose.words.Paragraph
import com.aspose.words.Shape
import com.aspose.words.Table
import com.fleetmanagement.modules.documentgeneration.config.AsposeConfig
import com.fleetmanagement.modules.documentgeneration.features.generategreeninsurancecard.GICTemplateFields
import com.fleetmanagement.modules.documentgeneration.features.generatekeyflag.KeyFlagTemplateFields
import com.fleetmanagement.modules.documentgeneration.objectmothers.DocumentObjectMother
import org.hamcrest.CoreMatchers.notNullValue
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.boot.test.context.SpringBootTest
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.time.OffsetDateTime

@SpringBootTest(classes = [AsposeConfig::class])
class FillTemplateServiceTest {
    @Test
    fun `should return filled document`() {
        val validityStartDate = OffsetDateTime.now()
        val validityEndDate = validityStartDate.plusDays(30)
        val validityStartDay = validityStartDate.dayOfMonth.toString()
        val validityStartMonth = validityStartDate.month.value.toString()
        val validityStartYear = validityStartDate.year.toString()
        val validityEndDay = validityEndDate.dayOfMonth.toString()
        val validityEndMonth = validityEndDate.month.value.toString()
        val validityEndYear = validityEndDate.year.toString()
        val licencePlate = "BB-PO 150"
        val data =
            mapOf(
                GICTemplateFields.VALIDITY_START_DAY.fieldName to validityStartDay,
                GICTemplateFields.VALIDITY_START_MONTH.fieldName to validityStartMonth,
                GICTemplateFields.VALIDITY_START_YEAR.fieldName to validityStartYear,
                GICTemplateFields.VALIDITY_END_DAY.fieldName to validityEndDay,
                GICTemplateFields.VALIDITY_END_MONTH.fieldName to validityEndMonth,
                GICTemplateFields.VALIDITY_END_YEAR.fieldName to validityEndYear,
                GICTemplateFields.LICENSE_PLATE.fieldName to licencePlate,
            )
        val result = FillTemplateService().fillTemplate("GreenInsuranceCard.docx", data)

        assertThat(result, notNullValue())
        assertEquals(
            result.range.formFields
                .single { it.name == GICTemplateFields.VALIDITY_START_DAY.fieldName }
                .result,
            validityStartDay,
        )
        assertEquals(
            result.range.formFields
                .single { it.name == GICTemplateFields.VALIDITY_START_MONTH.fieldName }
                .result,
            validityStartMonth,
        )
        assertEquals(
            result.range.formFields
                .single { it.name == GICTemplateFields.VALIDITY_START_YEAR.fieldName }
                .result,
            validityStartYear,
        )
        assertEquals(
            result.range.formFields
                .single { it.name == GICTemplateFields.VALIDITY_END_DAY.fieldName }
                .result,
            validityEndDay,
        )
        assertEquals(
            result.range.formFields
                .single { it.name == GICTemplateFields.VALIDITY_END_MONTH.fieldName }
                .result,
            validityEndMonth,
        )
        assertEquals(
            result.range.formFields
                .single { it.name == GICTemplateFields.VALIDITY_END_YEAR.fieldName }
                .result,
            validityEndYear,
        )
        assertEquals(
            result.range.formFields
                .single { it.name == GICTemplateFields.LICENSE_PLATE.fieldName }
                .result,
            licencePlate,
        )
    }

    @Test
    fun `should return filled document with barcode image`() {
        val licencePlate = "BB-PO 150"
        val model = "Macan"
        val exteriorColor = "schwarz"
        val vin = "1N4AL2AP0AN438515"
        val data =
            mapOf(
                KeyFlagTemplateFields.LICENSE_PLATE.fieldName to licencePlate,
                KeyFlagTemplateFields.VEHICLE_MODEL.fieldName to model,
                KeyFlagTemplateFields.EXTERIOR_COLOR.fieldName to exteriorColor,
            )
        val result = FillTemplateService().fillTemplate("KeyFlag.docx", data, vin)

        assertThat(result, notNullValue())
        assertEquals(
            result.range.formFields
                .single { it.name == KeyFlagTemplateFields.LICENSE_PLATE.fieldName }
                .result,
            licencePlate,
        )
        assertEquals(
            result.range.formFields
                .single { it.name == KeyFlagTemplateFields.VEHICLE_MODEL.fieldName }
                .result,
            model,
        )
        assertEquals(
            result.range.formFields
                .single { it.name == KeyFlagTemplateFields.EXTERIOR_COLOR.fieldName }
                .result,
            exteriorColor,
        )

        val shapes = result.getChildNodes(NodeType.SHAPE, true).map { it as Shape }

        assertThat(shapes.size, Matchers.equalTo(1))
        val shape = shapes.single()
        assertThat(shape.hasImage(), Matchers.equalTo(true))
        assertThat(shape.imageData.imageType, Matchers.equalTo(ImageType.PNG))

        val outputStream = ByteArrayOutputStream()
        shape.imageData.save(outputStream)
        val reader = BarCodeReader(ByteArrayInputStream(outputStream.toByteArray()), DecodeType.ALL_SUPPORTED_TYPES)
        val barcode = reader.readBarCodes().single()

        assertThat(barcode.codeType.typeName, Matchers.equalTo(DecodeType.CODE_128.typeName))
    }

    @Test
    fun `should fill table in the document`() {
        val data =
            listOf(
                listOf(
                    "Row1Cell1",
                    "Row1Cell2",
                ),
                listOf(
                    "Row2Cell1",
                    "Row2Cell2",
                ),
            )
        val document = DocumentObjectMother.getDocumentWithTable(numberOfTables = 1, emptyTable = true)
        val tables = document.getChildNodes(NodeType.TABLE, true)
        FillTemplateService().fillTable(tables[0] as Table, data, document)
        val tableWithDataRows = document.getChildNodes(NodeType.TABLE, true)

        assertEquals(1, tableWithDataRows.count)

        val a = tableWithDataRows[0] as Table
        // skip header
        val dataRows = a.rows.drop(1)

        for ((index, pair) in data.zip(dataRows).withIndex()) {
            val (expected, actual) = pair
            assertEquals(
                expected[index],
                actual.cells[index]
                    .text
                    .replace("\u0007", "")
                    .trim(),
            )
        }
    }

    @Test
    fun `should append hyperlink`() {
        val document = DocumentObjectMother.getDocument()
        val paragraph =
            document
                .getChildNodes(NodeType.PARAGRAPH, true)
                .first() as Paragraph
        FillTemplateService().appendHyperlinkInline(paragraph, "Click here", "some-url", document)
        val fields = document.getChildNodes(NodeType.FIELD_START, true)

        assertEquals(1, fields.count)
        val hyperlink = fields[0] as FieldStart
        assertEquals(FieldType.FIELD_HYPERLINK, hyperlink.fieldType)
        val a = hyperlink.field as FieldHyperlink
        assertEquals("some-url", a.address)
    }

    @Test
    fun `should throw FormFieldNotFoundException when field is not present`() {
        val formField = "somefield"
        val template = "GreenInsuranceCard.docx"
        val data =
            mapOf(
                formField to "somevalue",
            )
        val exception = assertThrows<FormFieldNotFoundException> { FillTemplateService().fillTemplate(template, data) }

        assertEquals(exception.message, "Cannot find form field $formField in template $template")
    }
}
