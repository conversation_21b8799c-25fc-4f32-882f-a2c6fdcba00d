package com.fleetmanagement.modules.registrationmailexport.csv

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import java.time.LocalDateTime

class VehicleRegistrationExportCSVTest {
    @Test
    fun `should create empty CSV`() {
        // Given
        val csv = VehicleRegistrationExportCSV()

        // Then
        assertTrue(csv.isEmpty())
    }

    @Test
    fun `should add rows and not be empty`() {
        // Given
        val csv = VehicleRegistrationExportCSV()
        val row = createCSVRow()

        // When
        csv.addRow(row)

        // Then
        assertFalse(csv.isEmpty())
    }

    @Test
    fun `should generate CSV with correct header`() {
        // Given
        val csv = VehicleRegistrationExportCSV()
        val row = createCSVRow()
        csv.addRow(row)

        // When
        val inputStream = csv.inputStream()
        val csvContent = inputStream.bufferedReader().use { it.readText() }

        // Then
        val lines = csvContent.split("\n")
        val expectedHeader =
            "\"Datum/Uhrzeit der letzten Änderung\";\"FIN\";\"Aktuelles Kennzeichen\";\"HSN\";\"TSN\";\"Fahrzeugart\";\"Zulassungsart\";\"Zulassungs-/Abmeldedatum\""
        assertEquals(expectedHeader, lines[0])
    }

    @Test
    fun `should extract row by predicate`() {
        // Given
        val csv = VehicleRegistrationExportCSV()
        val row1 = createCSVRow(vin = "VIN123")
        val row2 = createCSVRow(vin = "VIN456")
        csv.addRow(row1)
        csv.addRow(row2)

        // When
        val extractedRow = csv.extract { it.vin == "VIN456" }

        // Then
        assertNotNull(extractedRow)
        assertEquals("VIN456", extractedRow!!.vin)
    }

    private fun createCSVRow(
        vin: String = "WP0ZZZ99ZTS123456",
        licensePlate: String = "S-AB 123",
        hsn: String = "1234",
        tsn: String = "567",
        vehicleType: String = "PKW",
        registrationType: String = "1 Erstzulassung",
        registrationDeregistrationDate: LocalDateTime? = LocalDateTime.now(),
        lastUpdateDate: LocalDateTime? = LocalDateTime.now(),
    ) = VehicleRegistrationExportCSVRow(
        vin = vin,
        licensePlate = licensePlate,
        hsn = hsn,
        tsn = tsn,
        vehicleType = vehicleType,
        registrationType = registrationType,
        registrationDeregistrationDate = registrationDeregistrationDate,
        lastUpdateDate = lastUpdateDate,
    )
}
