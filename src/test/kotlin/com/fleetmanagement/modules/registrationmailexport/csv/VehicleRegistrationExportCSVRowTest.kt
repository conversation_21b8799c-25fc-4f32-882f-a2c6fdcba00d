package com.fleetmanagement.modules.registrationmailexport.csv

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.time.LocalDateTime

class VehicleRegistrationExportCSVRowTest {
    @Test
    fun `should convert to typed array in correct order`() {
        // Given
        val lastUpdateDate = LocalDateTime.of(2023, 12, 1, 10, 30, 0)
        val registrationDate = LocalDateTime.of(2023, 11, 15, 14, 20, 0)

        val row =
            VehicleRegistrationExportCSVRow(
                vin = "WP0ZZZ99ZTS123456",
                licensePlate = "S-AB 123",
                hsn = "1234",
                tsn = "567",
                vehicleType = "PKW",
                registrationType = "1 Erstzulassung",
                registrationDeregistrationDate = registrationDate,
                lastUpdateDate = lastUpdateDate,
            )

        // When
        val array = row.toTypedArray()

        // Then - Order must match CSV header order
        assertEquals(8, array.size)
        assertEquals(lastUpdateDate.toString(), array[0]) // "Datum/Uhrzeit der letzten Änderung"
        assertEquals("WP0ZZZ99ZTS123456", array[1]) // "FIN"
        assertEquals("S-AB 123", array[2]) // "Aktuelles Kennzeichen"
        assertEquals("1234", array[3]) // "HSN"
        assertEquals("567", array[4]) // "TSN"
        assertEquals("PKW", array[5]) // "Fahrzeugart"
        assertEquals("1 Erstzulassung", array[6]) // "Zulassungsart"
        assertEquals(registrationDate.toString(), array[7]) // "Zulassungs-/Abmeldedatum"
    }

    @Test
    fun `should handle null dates correctly`() {
        // Given
        val row =
            VehicleRegistrationExportCSVRow(
                vin = "WP0ZZZ99ZTS123456",
                licensePlate = "S-AB 123",
                hsn = "1234",
                tsn = "567",
                vehicleType = "PKW",
                registrationType = "1 Erstzulassung",
                registrationDeregistrationDate = null,
                lastUpdateDate = null,
            )

        // When
        val array = row.toTypedArray()

        // Then
        assertEquals("null", array[0]) // lastUpdateDate
        assertEquals("null", array[7]) // registrationDeregistrationDate
    }
}
