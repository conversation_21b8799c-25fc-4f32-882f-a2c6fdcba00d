package com.fleetmanagement.modules.registrationmailexport

import com.fleetmanagement.modules.registrationmailexport.csv.VehicleRegistrationExportCSV
import com.fleetmanagement.modules.registrationmailexport.mailclient.VehicleRegistrationExportMailClient
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByVIN
import com.fleetmanagement.modules.vehicledata.api.VehicleNotFoundInFVM
import com.fleetmanagement.modules.vehicledata.api.dtos.VehicleDTO
import com.fleetmanagement.modules.vehicledata.objectmothers.VehicleDataDTOObjectMother
import com.fleetmanagement.modules.vehicleregistration.api.ReadRegistrationOrder
import com.fleetmanagement.modules.vehicleregistration.api.dto.VehicleRegistrationAPIResponse
import com.fleetmanagement.modules.vehicleregistration.api.dto.VehicleRegistrationOrder
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow
import java.time.ZonedDateTime
import java.util.Optional

class VehicleRegistrationExportServiceTest {
    private lateinit var readRegistrationOrder: ReadRegistrationOrder
    private lateinit var mailClient: VehicleRegistrationExportMailClient
    private lateinit var readVehicleByVIN: ReadVehicleByVIN
    private lateinit var service: VehicleRegistrationExportService

    @BeforeEach
    fun setUp() {
        readRegistrationOrder = mockk()
        mailClient = mockk()
        readVehicleByVIN = mockk()
        service =
            VehicleRegistrationExportService(
                readRegistrationOrder,
                mailClient,
                readVehicleByVIN,
            )
    }

    @Test
    fun `should create and send CSV when registrations updated in last 24 hours exist`() {
        // Given
        val now = ZonedDateTime.now()
        val registrations =
            listOf(
                createVehicleRegistrationOrder(
                    id = 1L,
                    vin = "WP0ZZZ99ZTS123456",
                    licencePlate = "S-AB 123",
                    updatedAt = now.minusHours(12),
                ),
                createVehicleRegistrationOrder(
                    id = 2L,
                    vin = "WP0ZZZ99ZTS234567",
                    licencePlate = "S-CD 456",
                    updatedAt = now.minusHours(6),
                ),
            )
        val apiResponse = VehicleRegistrationAPIResponse(data = registrations)

        every { readRegistrationOrder.getRegistrationsModifiedSince(any()) } returns apiResponse
        every { readVehicleByVIN.readVehicleByVIN("WP0ZZZ99ZTS123456") } returns
            createVehicleDTO(
                vin = "WP0ZZZ99ZTS123456",
            )
        every { readVehicleByVIN.readVehicleByVIN("WP0ZZZ99ZTS234567") } returns
            createVehicleDTO(
                vin = "WP0ZZZ99ZTS234567",
            )
        every { mailClient.sendEmail(any()) } returns Unit

        // When
        service.createAndSendRegistrationsData()

        // Then
        verify(exactly = 1) { readRegistrationOrder.getRegistrationsModifiedSince(any()) }
        verify(exactly = 1) { readVehicleByVIN.readVehicleByVIN("WP0ZZZ99ZTS123456") }
        verify(exactly = 1) { readVehicleByVIN.readVehicleByVIN("WP0ZZZ99ZTS234567") }
        verify(exactly = 1) { mailClient.sendEmail(any()) }
    }

    @Test
    fun `should verify the mail attachment content`() {
        // Given
        val now = ZonedDateTime.now()
        val registrations =
            listOf(
                createVehicleRegistrationOrder(
                    id = 1L,
                    vin = "WP0ZZZ99ZTS123456",
                    licencePlate = "S-AB 123",
                    hsn = "1234",
                    tsn = "567",
                    department = "Fleet Management",
                    registrationType = 1,
                    updatedAt = now.minusHours(12),
                ),
                createVehicleRegistrationOrder(
                    id = 2L,
                    vin = "WP0ZZZ99ZTS234567",
                    licencePlate = "S-CD 456",
                    hsn = "5678",
                    tsn = "901",
                    department = "Operations",
                    registrationType = 2,
                    updatedAt = now.minusHours(6),
                ),
            )
        val apiResponse = VehicleRegistrationAPIResponse(data = registrations)

        every { readRegistrationOrder.getRegistrationsModifiedSince(any()) } returns apiResponse
        every { readVehicleByVIN.readVehicleByVIN("WP0ZZZ99ZTS123456") } returns
            createVehicleDTO(
                vin = "WP0ZZZ99ZTS123456",
            )
        every { readVehicleByVIN.readVehicleByVIN("WP0ZZZ99ZTS234567") } returns
            createVehicleDTO(
                vin = "WP0ZZZ99ZTS234567",
            )

        // Capture the CSV content sent to mail client
        val capturedCSV = mutableListOf<VehicleRegistrationExportCSV>()
        every { mailClient.sendEmail(capture(capturedCSV)) } returns Unit

        // When
        service.createAndSendRegistrationsData()

        // Then
        verify(exactly = 1) { mailClient.sendEmail(any()) }

        // Verify CSV content
        val csv = capturedCSV.first()
        assert(!csv.isEmpty()) { "CSV should not be empty" }

        // Verify first registration data
        val firstRow = csv.extract { it.vin == "WP0ZZZ99ZTS123456" }
        assertNotNull(firstRow)
        assertEquals("WP0ZZZ99ZTS123456", firstRow!!.vin)
        assertEquals("S-AB 123", firstRow.licensePlate)
        assertEquals("1234", firstRow.hsn)
        assertEquals("567", firstRow.tsn)
        assertEquals("PKW", firstRow.vehicleType) // From mock vehicle DTO
        assertEquals("1 Erstzulassung", firstRow.registrationType)

        // Verify second registration data
        val secondRow = csv.extract { it.vin == "WP0ZZZ99ZTS234567" }
        assertNotNull(secondRow)
        assertEquals("WP0ZZZ99ZTS234567", secondRow!!.vin)
        assertEquals("S-CD 456", secondRow.licensePlate)
        assertEquals("5678", secondRow.hsn)
        assertEquals("901", secondRow.tsn)
        assertEquals("PKW", secondRow.vehicleType) // From mock vehicle DTO
        assertEquals("2 Wiederzulassung", secondRow.registrationType)

        // Verify CSV structure by checking input stream content
        val csvContent = csv.inputStream().bufferedReader().use { it.readText() }
        val lines = csvContent.split("\n")

        // Verify header with German labels
        val expectedHeader =
            "\"Datum/Uhrzeit der letzten Änderung\";\"FIN\";\"Aktuelles Kennzeichen\";\"HSN\";\"TSN\";\"Fahrzeugart\";\"Zulassungsart\";\"Zulassungs-/Abmeldedatum\""
        assertEquals(expectedHeader, lines[0])

        // Verify we have header + 2 data rows
        assertEquals(3, lines.size)

        // Verify first data row contains expected VIN
        assertTrue(lines[1].contains("WP0ZZZ99ZTS123456"))
        assertTrue(lines[1].contains("S-AB 123"))
        assertTrue(lines[1].contains("1 Erstzulassung"))

        // Verify second data row contains expected VIN
        assertTrue(lines[2].contains("WP0ZZZ99ZTS234567"))
        assertTrue(lines[2].contains("S-CD 456"))
        assertTrue(lines[2].contains("2 Wiederzulassung"))
    }

    @Test
    fun `should send an email without attachment when no registrations updated in last 24 hours`() {
        // Given
        val apiResponse = VehicleRegistrationAPIResponse(data = emptyList<VehicleRegistrationOrder>())

        every { readRegistrationOrder.getRegistrationsModifiedSince(any()) } returns apiResponse
        every { mailClient.sendEmailWithoutAttachment() } returns Unit

        // When
        service.createAndSendRegistrationsData()

        // Then
        verify(exactly = 1) { readRegistrationOrder.getRegistrationsModifiedSince(any()) }
        verify(exactly = 1) { mailClient.sendEmailWithoutAttachment() }
        verify(exactly = 0) { mailClient.sendEmail(any()) }
    }

    @Test
    fun `should handle API exception gracefully and not send email`() { // TODO: verify
        // Given
        every { readRegistrationOrder.getRegistrationsModifiedSince(any()) } throws RuntimeException("API Error")
        every { mailClient.sendEmailWithoutAttachment() } returns Unit

        // When
        assertDoesNotThrow<Unit> {
            service.createAndSendRegistrationsData()
        }

        // Then
        verify(exactly = 1) { readRegistrationOrder.getRegistrationsModifiedSince(any()) }
        verify(exactly = 1) { mailClient.sendEmailWithoutAttachment() }
        verify(exactly = 0) { mailClient.sendEmail(any()) }
    }

    @Test
    fun `should handle vehicle type fetch failure gracefully`() { // TODO: verify
        // Given
        val registrations =
            listOf(
                createVehicleRegistrationOrder(
                    id = 1L,
                    vin = "WP0ZZZ99ZTS123456",
                    updatedAt = ZonedDateTime.now().minusHours(12),
                ),
            )
        val apiResponse = VehicleRegistrationAPIResponse(data = registrations)

        every { readRegistrationOrder.getRegistrationsModifiedSince(any()) } returns apiResponse
        every { readVehicleByVIN.readVehicleByVIN("WP0ZZZ99ZTS123456") } throws
            VehicleNotFoundInFVM(
                "VIN",
                "WP0ZZZ99ZTS123456",
            )
        every { mailClient.sendEmail(any()) } returns Unit

        // When
        service.createAndSendRegistrationsData()

        // Then
        verify(exactly = 1) { readRegistrationOrder.getRegistrationsModifiedSince(any()) }
        verify(exactly = 1) { readVehicleByVIN.readVehicleByVIN("WP0ZZZ99ZTS123456") }
        verify(exactly = 1) { mailClient.sendEmail(any()) }
    }

    @Test
    fun `should map registration types correctly`() { // TODO: verify
        // Given
        val registrations =
            listOf(
                createVehicleRegistrationOrder(
                    id = 1L,
                    registrationType = 1,
                    updatedAt = ZonedDateTime.now().minusHours(12),
                ),
                createVehicleRegistrationOrder(
                    id = 2L,
                    registrationType = 2,
                    updatedAt = ZonedDateTime.now().minusHours(12),
                ),
                createVehicleRegistrationOrder(
                    id = 3L,
                    registrationType = 3,
                    updatedAt = ZonedDateTime.now().minusHours(12),
                ),
                createVehicleRegistrationOrder(
                    id = 4L,
                    registrationType = 999,
                    updatedAt = ZonedDateTime.now().minusHours(12),
                ),
            )
        val apiResponse = VehicleRegistrationAPIResponse(data = registrations)

        every { readRegistrationOrder.getRegistrationsModifiedSince(any()) } returns apiResponse
        every { readVehicleByVIN.readVehicleByVIN(any()) } returns createVehicleDTO("SUV")
        every { mailClient.sendEmail(any()) } returns Unit

        // When
        service.createAndSendRegistrationsData()

        // Then
        verify(exactly = 1) { readRegistrationOrder.getRegistrationsModifiedSince(any()) }
        verify(exactly = 1) { mailClient.sendEmail(any()) }
    }

    @Test
    fun `should handle null and empty optional values gracefully`() {
        // Given
        val registrations =
            listOf(
                createVehicleRegistrationOrderWithNulls(),
            )
        val apiResponse = VehicleRegistrationAPIResponse(data = registrations)

        every { readRegistrationOrder.getRegistrationsModifiedSince(any()) } returns apiResponse
        every { readVehicleByVIN.readVehicleByVIN("") } throws VehicleNotFoundInFVM("VIN", "")
        every { mailClient.sendEmail(any()) } returns Unit

        // When
        service.createAndSendRegistrationsData()

        // Then
        verify(exactly = 1) { readRegistrationOrder.getRegistrationsModifiedSince(any()) }
        verify(exactly = 1) { readVehicleByVIN.readVehicleByVIN("") }
        verify(exactly = 1) { mailClient.sendEmail(any()) }
    }

    @Test
    fun `should filter registrations by updatedAt correctly`() { // TODO: expected to be handled by provider
        // Given
        val now = ZonedDateTime.now()
        val registrations =
            listOf(
                createVehicleRegistrationOrder(id = 1L, updatedAt = now.minusHours(23)), // Should be included
                createVehicleRegistrationOrder(id = 2L, updatedAt = now.minusHours(25)), // Should be excluded
                createVehicleRegistrationOrder(
                    id = 3L,
                    updatedAt = null,
                    createdAt = now.minusHours(12),
                ), // Should be excluded (no updatedAt)
            )
        val apiResponse = VehicleRegistrationAPIResponse(data = registrations)

        every { readRegistrationOrder.getRegistrationsModifiedSince(any()) } returns apiResponse
        every { readVehicleByVIN.readVehicleByVIN(any()) } returns createVehicleDTO("SUV")
        every { mailClient.sendEmail(any()) } returns Unit

        // When
        service.createAndSendRegistrationsData()

        // Then
        verify(exactly = 1) { readRegistrationOrder.getRegistrationsModifiedSince(any()) }
        verify(exactly = 1) { readVehicleByVIN.readVehicleByVIN(any()) } // Only one should be processed
        verify(exactly = 1) { mailClient.sendEmail(any()) }
    }

    @Test
    fun `should cache vehicle types and avoid duplicate API calls for same VIN`() {
        // Given - Same VIN appears in multiple registrations
        val now = ZonedDateTime.now()
        val sameVin = "WP0ZZZ99ZTS123456"
        val registrations =
            listOf(
                createVehicleRegistrationOrder(
                    id = 1L,
                    vin = sameVin,
                    licencePlate = "S-AB 123",
                    registrationType = 1, // Registration
                    updatedAt = now.minusHours(12),
                ),
                createVehicleRegistrationOrder(
                    id = 2L,
                    vin = sameVin,
                    licencePlate = "S-AB 123",
                    registrationType = 2, // De-registration
                    updatedAt = now.minusHours(6),
                ),
            )
        val apiResponse = VehicleRegistrationAPIResponse(data = registrations)

        every { readRegistrationOrder.getRegistrationsModifiedSince(any()) } returns apiResponse

        // Mock vehicle service calls - should only be called once per unique VIN
        every { readVehicleByVIN.readVehicleByVIN(sameVin) } returns createVehicleDTO(vin = sameVin)

        // Capture the CSV content sent to mail client
        val capturedCSV = mutableListOf<VehicleRegistrationExportCSV>()
        every { mailClient.sendEmail(capture(capturedCSV)) } returns Unit

        // When
        service.createAndSendRegistrationsData()

        // Then
        verify(exactly = 1) { mailClient.sendEmail(any()) }

        // Verify that readVehicleByVIN was called only once per unique VIN
        verify(exactly = 1) { readVehicleByVIN.readVehicleByVIN(sameVin) }

        // Verify CSV contains all 2 registrations
        val csv = capturedCSV.first()
        assert(!csv.isEmpty()) { "CSV should not be empty" }

        // Verify all registrations for the same VIN have the same vehicle type
        val sameVinRows =
            listOf(
                csv.extract { it.vin == sameVin && it.registrationType == "1 Erstzulassung" },
                csv.extract { it.vin == sameVin && it.registrationType == "2 Wiederzulassung" },
            )

        assert(sameVinRows.size == 2) { "Should find 2 rows for the same VIN" }

        sameVinRows.forEach { row ->
            assertEquals(sameVin, row!!.vin)
            assertEquals("PKW", row.vehicleType) // All should have the same cached vehicle type
        }
    }

    private fun createVehicleRegistrationOrder(
        id: Long,
        vin: String = "WP0ZZZ99ZTS123456",
        licencePlate: String = "S-AB 123",
        hsn: String = "1234",
        tsn: String = "567",
        department: String = "Fleet Management",
        registrationType: Int = 1,
        registrationDate: ZonedDateTime? = ZonedDateTime.now().minusDays(30),
        lastDeRegistrationDate: ZonedDateTime? = null,
        createdAt: ZonedDateTime = ZonedDateTime.now().minusDays(30),
        updatedAt: ZonedDateTime? = ZonedDateTime.now().minusHours(12),
    ): VehicleRegistrationOrder =
        VehicleRegistrationOrder(
            id = id,
            vin = Optional.ofNullable(vin),
            licencePlate = Optional.ofNullable(licencePlate),
            hsn = Optional.ofNullable(hsn),
            tsn = Optional.ofNullable(tsn),
            department = Optional.ofNullable(department),
            registrationType = Optional.ofNullable(registrationType),
            registrationDate = Optional.ofNullable(registrationDate),
            lastDeRegistrationDate = Optional.ofNullable(lastDeRegistrationDate),
            createdAt = Optional.ofNullable(createdAt),
            updatedAt = Optional.ofNullable(updatedAt),
            version = 1L,
        )

    private fun createVehicleRegistrationOrderWithNulls(): VehicleRegistrationOrder =
        VehicleRegistrationOrder(
            id = 1L,
            vin = Optional.empty(),
            licencePlate = Optional.empty(),
            hsn = Optional.empty(),
            tsn = Optional.empty(),
            department = Optional.empty(),
            registrationType = Optional.empty(),
            registrationDate = Optional.empty(),
            lastDeRegistrationDate = Optional.empty(),
            createdAt = Optional.empty(),
            updatedAt = Optional.of(ZonedDateTime.now().minusHours(12)),
            version = 1L,
        )

    private fun createVehicleDTO(vin: String): VehicleDTO = VehicleDataDTOObjectMother.vehicleDTOWithAllFieldsPopulated(vin = vin)
}
