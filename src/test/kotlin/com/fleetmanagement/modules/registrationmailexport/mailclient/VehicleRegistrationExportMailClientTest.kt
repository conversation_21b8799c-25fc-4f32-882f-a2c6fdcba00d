package com.fleetmanagement.modules.registrationmailexport.mailclient

import com.fleetmanagement.integrations.mailclient.application.port.EmailDto
import com.fleetmanagement.integrations.mailclient.application.port.EmailOutPort
import com.fleetmanagement.modules.registrationmailexport.csv.VehicleRegistrationExportCSV
import com.fleetmanagement.modules.registrationmailexport.csv.VehicleRegistrationExportCSVRow
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.time.LocalDateTime

class VehicleRegistrationExportMailClientTest {
    private lateinit var emailOutPort: EmailOutPort
    private lateinit var mailClient: VehicleRegistrationExportMailClient

    private val emailSender = "<EMAIL>"
    private val emailRecipient = "<EMAIL>"

    @BeforeEach
    fun setUp() {
        emailOutPort = mockk(relaxed = true)
        mailClient = VehicleRegistrationExportMailClient(emailOutPort, emailSender, emailRecipient)
    }

    @Test
    fun `should send encrypted email with CSV attachment`() {
        // GIVEN
        val csvData = VehicleRegistrationExportCSV()
        val row =
            VehicleRegistrationExportCSVRow(
                vin = "WP0ZZZ99ZTS123456",
                licensePlate = "S-AB 123",
                hsn = "1234",
                tsn = "567",
                vehicleType = "PKW",
                registrationType = "1 Erstzulassung",
                registrationDeregistrationDate = LocalDateTime.now(),
                lastUpdateDate = LocalDateTime.now(),
            )
        csvData.addRow(row)

        // When
        mailClient.sendEmail(csvData)

        // Then
        val emailSlot = slot<EmailDto>()
        verify { emailOutPort.sendEmailEncrypted(capture(emailSlot)) }

        val capturedEmail = emailSlot.captured
        assertTrue(capturedEmail.subject.endsWith("_Vehicle_Registration_Data.csv"))
        assertTrue(capturedEmail.htmlBody.isNotEmpty())
        assertEquals(emailSender, capturedEmail.senderMailAddress.address)
        assertEquals(emailRecipient, capturedEmail.recipientsMailAddressInTo[0].address)
        assertEquals(1, capturedEmail.attachment.size)
        assertTrue(capturedEmail.attachment[0].name.matches(Regex("\\d{8}_\\d{6}_Vehicle_Registration_Data\\.csv")))
    }

    @Test
    fun `should send encrypted email without attachment`() {
        // Given
        val emailSlot = slot<EmailDto>()
        every { emailOutPort.sendEmailEncrypted(capture(emailSlot)) } returns Unit

        // When
        mailClient.sendEmailWithoutAttachment()

        // Then
        verify { emailOutPort.sendEmailEncrypted(any()) }

        val capturedEmail = emailSlot.captured
        assertEquals("Keine neuen Zulassungsdaten", capturedEmail.subject)
        assertTrue(capturedEmail.htmlBody.isNotEmpty())
        assertEquals(emailSender, capturedEmail.senderMailAddress.address)
        assertEquals(emailRecipient, capturedEmail.recipientsMailAddressInTo[0].address)
        assertEquals(0, capturedEmail.attachment.size)
    }
}
