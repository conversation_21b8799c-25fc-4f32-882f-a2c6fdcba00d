/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.migration.postprocessors

import com.fleetmanagement.modules.migration.FMSVehicleAggregateMother.sampleFMSVehicle
import com.fleetmanagement.modules.migration.appointments.MsBookingAppointmentsHandler
import com.fleetmanagement.modules.migration.job.FMSLatestTransferAggregate
import com.fleetmanagement.modules.migration.job.FMSLatestTransferAggregate.Companion.FMS_STATUSES_SIGNIFYING_PLANNED_TRANSFER
import com.fleetmanagement.modules.migration.job.FMSMaintenanceOrderNumber
import com.fleetmanagement.modules.migration.job.FMSRegistrationAggregate
import com.fleetmanagement.modules.migration.job.FMSTransferAggregate
import com.fleetmanagement.modules.migration.job.FMSVehicleAggregate
import com.fleetmanagement.modules.migration.job.PlannedAppointment
import com.fleetmanagement.modules.transfermsbookings.application.AppointmentType
import com.fleetmanagement.modules.vehicletransfer.application.port.MigrateVehicleTransferUseCase
import com.fleetmanagement.modules.vehicletransfer.application.port.VehicleTransferMigrationDto
import com.fleetmanagement.modules.vehicletransfer.domain.entities.VehicleTransferKey
import com.fleetmanagement.modules.vehicletransfer.domain.service.VehicleTransferMigrationResult
import io.mockk.Called
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertNull
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import java.time.OffsetDateTime
import java.time.OffsetDateTime.now

class VehicleTransferUpserterTest {
    private val mockMigrateVehicleTransferUseCase = mockk<MigrateVehicleTransferUseCase>()
    private val mockAppointmentScheduler = mockk<MsBookingAppointmentsHandler>(relaxed = true)
    private val serviceUnderTest =
        VehicleTransferUpserter(
            mockMigrateVehicleTransferUseCase,
            mockAppointmentScheduler,
        )
    val sampleResponsiblePerson = "12346789"
    val sampleRegistrations =
        listOf(
            FMSRegistrationAggregate(1, now(), "latestLicensePlate"),
            FMSRegistrationAggregate(2, now().minusDays(1), "oldOne"),
        )
    val capturedUpdates: MutableList<VehicleTransferMigrationDto> = mutableListOf()

    @BeforeEach
    fun setup() {
        every { mockMigrateVehicleTransferUseCase.migrateVehicleTransfer(capture(capturedUpdates)) } returns
            VehicleTransferMigrationResult(true, VehicleTransferKey(123), "", "")
    }

    @Test
    fun `create vehicle transfers with correct properties from specific vehicle transfer`() {
        val firstDeliveryDate = now().minusDays(413)
        val firstReturnDate = firstDeliveryDate.plusDays(99)
        val secondDeliveryDate = firstReturnDate.plusDays(12)
        val secondReturnDate = secondDeliveryDate.plusDays(90)
        val fmsVehicleAggregate =
            FMSVehicleAggregate(
                productGuid = "product-guid",
            ).apply {
                vin = "TEST-VIN"
                transfers.add(
                    FMSTransferAggregate(
                        fmsDeliveryIndex = "1",
                        deliveryDate = firstDeliveryDate,
                        mileageAtDelivery = 123,
                        returnDate = firstReturnDate,
                        mileageAtReturn = 567,
                        vehicleResponsiblePerson = "Sandy",
                    ),
                )
                transfers.add(
                    FMSTransferAggregate(
                        fmsDeliveryIndex = "2",
                        deliveryDate = secondDeliveryDate,
                        mileageAtDelivery = 590,
                        returnDate = secondReturnDate,
                        mileageAtReturn = 999,
                        vehicleResponsiblePerson = "Randy",
                    ),
                )
            }

        serviceUnderTest.upsert(fmsVehicleAggregate)

        assertEquals(2, capturedUpdates.size)
        // create latest vehicle transfer
        val expectedLatestTransfer =
            VehicleTransferMigrationDto(
                vin = "TEST-VIN",
                deliveryIndex = "1",
                deliveryDate = firstDeliveryDate,
                deliveryComment = null,
                mileageAtDelivery = 123,
                returnDate = firstReturnDate,
                mileageAtReturn = 567,
                returnComment = null,
                vehicleUsage = null,
                vehicleResponsiblePerson = "Sandy",
                internalContactPerson = null,
                internalOrderNumber = null,
                usingCostCenter = null,
                desiredDeliveryDate = null,
                plannedDeliveryDate = null,
                plannedReturnDate = null,
                latestReturnDate = null,
                deliveryLeipzig = null,
                maintenanceOrderNumber = null,
                licensePlate = null,
            )
        assertEquals(expectedLatestTransfer, capturedUpdates[0])
        // create historic vehicle transfer
        val expectedHistoricTransfer =
            VehicleTransferMigrationDto(
                vin = "TEST-VIN",
                deliveryIndex = "2",
                deliveryDate = secondDeliveryDate,
                deliveryComment = null,
                mileageAtDelivery = 590,
                returnDate = secondReturnDate,
                mileageAtReturn = 999,
                returnComment = null,
                vehicleUsage = null,
                vehicleResponsiblePerson = "Randy",
                internalContactPerson = null,
                internalOrderNumber = null,
                usingCostCenter = null,
                desiredDeliveryDate = null,
                plannedDeliveryDate = null,
                plannedReturnDate = null,
                latestReturnDate = null,
                deliveryLeipzig = null,
                maintenanceOrderNumber = null,
                licensePlate = null,
            )
        assertEquals(expectedHistoricTransfer, capturedUpdates[1])
    }

    @Test
    fun `the latest transfer should have additional properties`() {
        val firstDeliveryDate = now().minusDays(413)
        val firstReturnDate = firstDeliveryDate.plusDays(99)
        val secondDeliveryDate = firstReturnDate.plusDays(12)
        val secondReturnDate = secondDeliveryDate.plusDays(90)
        val latestReturnDate = secondDeliveryDate.plusDays(99)

        val plannedDeliveryDate = secondDeliveryDate.plusDays(1)
        val plannedReturnDate = latestReturnDate.plusDays(1)

        val fmsVehicleAggregate =
            FMSVehicleAggregate(
                productGuid = "product-guid",
            ).apply {
                vin = "TEST-VIN"
                latestTransferAdditionalData =
                    FMSLatestTransferAggregate(
                        internalOrderNumber = "sampleInternalOrderNumber",
                        internalContactPerson = "sampleInternalContactPerson",
                        usingCostCenter = "sampleUsingCostCenter",
                        fmsVehicleUsageId = 1,
                        deliveryLeipzig = true,
                        latestReturnDate = latestReturnDate,
                        maintenanceOrderNumber = "sampleMaintenanceOrderNumber",
                        plannedDeliveryAppointment =
                            PlannedAppointment(
                                from = plannedDeliveryDate,
                                to = null,
                                responsiblePerson = sampleResponsiblePerson,
                                appointmentKind = 1,
                            ),
                        plannedReturnAppointment =
                            PlannedAppointment(
                                from = plannedReturnDate,
                                to = null,
                                responsiblePerson = sampleResponsiblePerson,
                                appointmentKind = 1,
                            ),
                    )
                transfers.add(
                    FMSTransferAggregate(
                        fmsDeliveryIndex = "1",
                        deliveryDate = firstDeliveryDate,
                        mileageAtDelivery = 123,
                        returnDate = firstReturnDate,
                        mileageAtReturn = 567,
                        vehicleResponsiblePerson = sampleResponsiblePerson,
                        vehicleUsageGroupDescription = "Entwicklung",
                    ),
                )
                transfers.add(
                    FMSTransferAggregate(
                        fmsDeliveryIndex = "2",
                        deliveryDate = secondDeliveryDate,
                        mileageAtDelivery = 590,
                        returnDate = secondReturnDate,
                        mileageAtReturn = 999,
                        vehicleResponsiblePerson = sampleResponsiblePerson,
                        vehicleUsageGroupDescription = "Entwicklung",
                    ),
                )
            }

        serviceUnderTest.upsert(fmsVehicleAggregate)

        assertEquals(2, capturedUpdates.size)
        // create latest vehicle transfer with additional properties
        val expectedLatestTransfer =
            VehicleTransferMigrationDto(
                vin = "TEST-VIN",
                deliveryIndex = "2",
                deliveryDate = secondDeliveryDate,
                deliveryComment = null,
                mileageAtDelivery = 590,
                returnDate = secondReturnDate,
                mileageAtReturn = 999,
                returnComment = null,
                vehicleUsage = "Entwicklung", // since not a "planned-transfer"
                vehicleResponsiblePerson = sampleResponsiblePerson,
                internalContactPerson = "sampleInternalContactPerson",
                internalOrderNumber = "sampleInternalOrderNumber",
                usingCostCenter = "sampleUsingCostCenter", // since latest-transfer aggregate already has a using-cost-center
                desiredDeliveryDate = null,
                plannedDeliveryDate = plannedDeliveryDate,
                plannedReturnDate = plannedReturnDate,
                latestReturnDate = latestReturnDate,
                deliveryLeipzig = true,
                maintenanceOrderNumber = null,
                licensePlate = null, // not planned or active
            )
        assertEquals(expectedLatestTransfer, capturedUpdates[1])
    }

    @Test
    fun `handle missing using cost centers for historical vehicle transfers`() {
        val datetimeForTesting = now()
        val fmsVehicleAggregate =
            FMSVehicleAggregate(
                productGuid = "product-guid",
            ).apply {
                vin = "TEST-VIN"
                latestTransferAdditionalData =
                    FMSLatestTransferAggregate(
                        internalOrderNumber = "sampleInternalOrderNumber",
                        internalContactPerson = "sampleInternalContactPerson",
                        usingCostCenter = null, // this is not available for the purpose of this test
                        fmsVehicleUsageId = 1,
                        deliveryLeipzig = true,
                        maintenanceOrderNumber = "sampleMaintenanceOrderNumber",
                    )
                transfers.add(
                    FMSTransferAggregate(
                        fmsDeliveryIndex = "1",
                        deliveryDate = datetimeForTesting,
                        mileageAtDelivery = 123,
                        returnDate = datetimeForTesting.plusDays(365),
                        mileageAtReturn = 567,
                        vehicleResponsiblePerson = sampleResponsiblePerson,
                        vehicleUsageGroupDescription = "Entwicklung",
                        vehicleUsingCostCenter = "sampleUsingCostCenter",
                    ),
                )
                transfers.add(
                    FMSTransferAggregate(
                        fmsDeliveryIndex = "2",
                        deliveryDate = datetimeForTesting.plusDays(720),
                        mileageAtDelivery = 590,
                        returnDate = datetimeForTesting.plusDays(920),
                        mileageAtReturn = 999,
                        vehicleResponsiblePerson = sampleResponsiblePerson,
                        vehicleUsageGroupDescription = "Entwicklung",
                        vehicleUsingCostCenter = "sampleUsingCostCenter",
                    ),
                )
            }

        serviceUnderTest.upsert(fmsVehicleAggregate)

        assertEquals("sampleUsingCostCenter", capturedUpdates[1].usingCostCenter)
        assertEquals("sampleUsingCostCenter", capturedUpdates[1].usingCostCenter)
    }

    @Test
    fun `certain FMS primary statuses should signify a planned transfer`() {
        assertEquals(listOf("YF10", "YF11"), FMS_STATUSES_SIGNIFYING_PLANNED_TRANSFER)
    }

    @Test
    fun `if FMS status is signifying a planned transfer, an additional planned transfer is added as latest transfer`() {
        val olderDeliveryDate = now().minusDays(413)
        val plannedDeliveryDate = olderDeliveryDate.plusDays(100)
        val sampleActiveTransfer =
            FMSTransferAggregate(
                fmsDeliveryIndex = "1",
                deliveryDate = olderDeliveryDate,
                mileageAtDelivery = 123,
                vehicleResponsiblePerson = "another",
                vehicleUsageGroupDescription = "Entwicklung",
            )
        val fmsVehicleAggregate =
            FMSVehicleAggregate(
                productGuid = "product-guid",
            ).apply {
                vin = "TEST-VIN"
                fmsPrimaryStatus = FMS_STATUSES_SIGNIFYING_PLANNED_TRANSFER.random()
                registrations.addAll(sampleRegistrations)
                latestTransferAdditionalData =
                    FMSLatestTransferAggregate(
                        internalOrderNumber = "sampleInternalOrderNumber",
                        internalContactPerson = "sampleInternalContactPerson",
                        usingCostCenter = "sampleUsingCostCenter",
                        fmsVehicleUsageId = 1,
                        deliveryLeipzig = true,
                        maintenanceOrderNumber = "sampleMaintenanceOrderNumber",
                        vehicleResponsiblePerson = sampleResponsiblePerson,
                        plannedDeliveryAppointment =
                            PlannedAppointment(
                                from = plannedDeliveryDate,
                                to = null,
                                responsiblePerson = sampleResponsiblePerson,
                                appointmentKind = 1,
                            ),
                    )
                transfers.add(sampleActiveTransfer)
            }

        serviceUnderTest.upsert(fmsVehicleAggregate)

        assertEquals(2, capturedUpdates.size)
        // create latest vehicle transfer with additional properties
        val expectedLatestTransfer =
            VehicleTransferMigrationDto(
                vin = "TEST-VIN",
                deliveryIndex = null,
                deliveryDate = null,
                deliveryComment = null,
                mileageAtDelivery = null,
                returnDate = null,
                mileageAtReturn = null,
                returnComment = null,
                vehicleUsage = "Leasing Tarif", // since this is a "planned-transfer"
                vehicleResponsiblePerson = sampleResponsiblePerson,
                internalContactPerson = "sampleInternalContactPerson",
                internalOrderNumber = "sampleInternalOrderNumber",
                usingCostCenter = "sampleUsingCostCenter",
                desiredDeliveryDate = null,
                plannedDeliveryDate = plannedDeliveryDate,
                plannedReturnDate = null,
                latestReturnDate = null,
                deliveryLeipzig = true,
                maintenanceOrderNumber = null,
                licensePlate = "latestLicensePlate", // should attached latest license plate to planned/active transfer
            )

        // important: this one must be created last, because vehicleTransfer module will remove any planned transfer
        // when a transfer is migrated. This way it prevents stale and duplicate planned transfers when migration is executed multiple times.
        val lastTransferCreated = capturedUpdates.last()
        assertEquals(expectedLatestTransfer, lastTransferCreated)

        val activeTransfer = capturedUpdates.first()
        assertEquals(
            "latestLicensePlate",
            activeTransfer.licensePlate,
            "should additionally attach the latest license plate to the active transfer, so it will be on both active and planned",
        )
    }

    @Test
    fun `if FMS status is signifying a planned transfer, but no other transfer exist, should still create planned one`() {
        val fmsVehicleAggregate =
            FMSVehicleAggregate(
                productGuid = "product-guid",
            ).apply {
                vin = "TEST-VIN"
                fmsPrimaryStatus = FMS_STATUSES_SIGNIFYING_PLANNED_TRANSFER.random()
                latestTransferAdditionalData =
                    FMSLatestTransferAggregate(
                        internalOrderNumber = "sampleInternalOrderNumber",
                        internalContactPerson = "sampleInternalContactPerson",
                        usingCostCenter = "sampleUsingCostCenter",
                        fmsVehicleUsageId = 1,
                        deliveryLeipzig = true,
                        maintenanceOrderNumber = "sampleMaintenanceOrderNumber",
                        vehicleResponsiblePerson = sampleResponsiblePerson,
                    )
                transfers.clear()
            }

        serviceUnderTest.upsert(fmsVehicleAggregate)

        assertEquals(1, capturedUpdates.size)
        // create latest vehicle transfer with additional properties
        assertEquals(
            VehicleTransferMigrationDto(
                vin = "TEST-VIN",
                deliveryIndex = null,
                deliveryDate = null,
                deliveryComment = null,
                mileageAtDelivery = null,
                returnDate = null,
                mileageAtReturn = null,
                returnComment = null,
                vehicleUsage = "Leasing Tarif",
                vehicleResponsiblePerson = sampleResponsiblePerson,
                internalContactPerson = "sampleInternalContactPerson",
                internalOrderNumber = "sampleInternalOrderNumber",
                usingCostCenter = "sampleUsingCostCenter",
                desiredDeliveryDate = null,
                plannedDeliveryDate = null,
                plannedReturnDate = null,
                latestReturnDate = null,
                deliveryLeipzig = true,
                maintenanceOrderNumber = null,
                licensePlate = null,
            ),
            capturedUpdates[0],
        )
    }

    @Test
    fun `should attach all errors and warnings to the errors lists of the aggregate with the delivery index as prefix`() {
        val firstDeliveryDate = now().minusDays(413)
        val firstReturnDate = firstDeliveryDate.plusDays(99)
        val secondDeliveryDate = firstReturnDate.plusDays(12)
        val secondReturnDate = secondDeliveryDate.plusDays(90)
        val fmsVehicleAggregate =
            FMSVehicleAggregate(
                productGuid = "product-guid",
            ).apply {
                vin = "TEST-VIN"
                transfers.add(
                    FMSTransferAggregate(
                        fmsDeliveryIndex = "2",
                        deliveryDate = secondDeliveryDate,
                        mileageAtDelivery = 590,
                        returnDate = secondReturnDate,
                        mileageAtReturn = 999,
                        vehicleResponsiblePerson = "Randy",
                    ),
                )
            }
        every { mockMigrateVehicleTransferUseCase.migrateVehicleTransfer(any()) } returns
            VehicleTransferMigrationResult(
                false,
                VehicleTransferKey(123),
                "",
                "",
                errors = listOf("some error happened"),
                warnings = listOf("some warning was emitted"),
            )

        serviceUnderTest.upsert(fmsVehicleAggregate)

        assertTrue(fmsVehicleAggregate.errors.any { it.equals("Transfer[2]: some error happened") })
        assertTrue(fmsVehicleAggregate.errors.any { it.equals("Transfer[2]: some warning was emitted") })
    }

    @Nested
    inner class `Should handle planned appointments correctly` {
        val exactDeliveryTime: OffsetDateTime = now()
        val exactReturnTime: OffsetDateTime = now().plusDays(3)
        val sampleVehicleTransferKey = VehicleTransferKey(123)
        val samplePersonalUsageId = 1

        @ParameterizedTest(name = "for fms usage id {0}")
        // personal usages:
        @ValueSource(strings = ["1", "2", "3", "4", "5", "28"])
        fun `for personal vehicle, planned delivery + return date with time should be migrated including MS bookings`(
            fmsUsageId: Int,
        ) {
            val dateTimeSpecificAppointmentKind = 2
            val sampleAggregate =
                sampleFMSVehicle().apply {
                    this.transfers.add(
                        FMSTransferAggregate(
                            fmsDeliveryIndex = "1",
                            vehicleResponsiblePerson = sampleResponsiblePerson,
                            deliveryDate = now(),
                        ),
                    )
                    latestTransferAdditionalData =
                        FMSLatestTransferAggregate()
                            .apply {
                                this.plannedDeliveryAppointment =
                                    PlannedAppointment(
                                        from = exactDeliveryTime,
                                        to = exactDeliveryTime.plusMinutes(30),
                                        responsiblePerson = sampleResponsiblePerson,
                                        appointmentKind = dateTimeSpecificAppointmentKind,
                                    )
                                this.plannedReturnAppointment =
                                    PlannedAppointment(
                                        from = exactReturnTime,
                                        to = exactReturnTime.plusMinutes(30),
                                        responsiblePerson = sampleResponsiblePerson,
                                        appointmentKind = dateTimeSpecificAppointmentKind,
                                    )
                                this.fmsVehicleUsageId = fmsUsageId
                            }
                }

            serviceUnderTest.upsert(sampleAggregate)

            val actualUpdate = capturedUpdates[0]
            assertEquals(exactDeliveryTime, actualUpdate.plannedDeliveryDate)
            assertEquals(exactReturnTime, actualUpdate.plannedReturnDate)
            verify {
                mockAppointmentScheduler.upsertPendingMSBookingAppointment(
                    appointment = sampleAggregate.latestTransferAdditionalData.plannedDeliveryAppointment!!,
                    vin = sampleAggregate.vin!!,
                    vehicleTransferKey = sampleVehicleTransferKey,
                    appointmentType = AppointmentType.DELIVERY,
                )
            }
            verify {
                mockAppointmentScheduler.upsertPendingMSBookingAppointment(
                    appointment = sampleAggregate.latestTransferAdditionalData.plannedReturnAppointment!!,
                    vin = sampleAggregate.vin!!,
                    vehicleTransferKey = sampleVehicleTransferKey,
                    appointmentType = AppointmentType.RETURN,
                )
            }
        }

        @Test
        fun `for personal vehicle, planned delivery + return date with only day-specificity should NOT be migrated`() {
            val dateSpecificAppointmentKind = 6
            val sampleAggregate =
                sampleFMSVehicle().apply {
                    this.transfers.add(
                        FMSTransferAggregate(
                            fmsDeliveryIndex = "1",
                            vehicleResponsiblePerson = sampleResponsiblePerson,
                            deliveryDate = now(),
                        ),
                    )
                    latestTransferAdditionalData =
                        FMSLatestTransferAggregate()
                            .apply {
                                this.plannedDeliveryAppointment =
                                    PlannedAppointment(
                                        from = exactDeliveryTime,
                                        to = exactDeliveryTime.plusMinutes(30),
                                        responsiblePerson = "123",
                                        appointmentKind = dateSpecificAppointmentKind,
                                    )
                                this.plannedReturnAppointment =
                                    PlannedAppointment(
                                        from = exactReturnTime,
                                        to = exactReturnTime.plusMinutes(30),
                                        responsiblePerson = "1234",
                                        appointmentKind = dateSpecificAppointmentKind,
                                    )
                                this.fmsVehicleUsageId = samplePersonalUsageId
                            }
                }

            serviceUnderTest.upsert(sampleAggregate)

            val actualUpdate = capturedUpdates[0]
            assertNull(actualUpdate.plannedDeliveryDate)
            assertNull(actualUpdate.plannedReturnDate)
            verify {
                mockAppointmentScheduler wasNot Called
            }
        }

        @ParameterizedTest(name = "for fms usage id {0}")
        @ValueSource(
            // non personal usages:
            strings = [
                "6", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17",
                "18", "19", "20", "21", "22", "23", "24", "25", "26", "27",
            ],
        )
        fun `for non-personal vehicle, planned delivery + return date should be at midnight and MS bookings should NOT be called`(
            fmsUsageId: Int,
        ) {
            val sampleAggregate =
                sampleFMSVehicle().apply {
                    this.transfers.add(
                        FMSTransferAggregate(
                            fmsDeliveryIndex = "1",
                            vehicleResponsiblePerson = sampleResponsiblePerson,
                            deliveryDate = now(),
                        ),
                    )
                    latestTransferAdditionalData =
                        FMSLatestTransferAggregate()
                            .apply {
                                this.plannedDeliveryAppointment =
                                    PlannedAppointment(
                                        from = exactDeliveryTime,
                                        to = exactDeliveryTime.plusMinutes(30),
                                        responsiblePerson = sampleResponsiblePerson,
                                        appointmentKind = 2,
                                    )
                                this.plannedReturnAppointment =
                                    PlannedAppointment(
                                        from = exactReturnTime,
                                        to = exactReturnTime.plusMinutes(30),
                                        responsiblePerson = sampleResponsiblePerson,
                                        appointmentKind = 1,
                                    )
                                this.fmsVehicleUsageId = fmsUsageId
                            }
                }

            serviceUnderTest.upsert(sampleAggregate)

            val actualUpdate = capturedUpdates[0]
            val midnightDeliveryTime: OffsetDateTime =
                exactDeliveryTime.toLocalDate().atStartOfDay().atOffset(exactDeliveryTime.offset)
            val midnightReturnTime: OffsetDateTime =
                exactReturnTime.toLocalDate().atStartOfDay().atOffset(exactReturnTime.offset)
            assertEquals(midnightDeliveryTime, actualUpdate.plannedDeliveryDate)
            assertEquals(midnightReturnTime, actualUpdate.plannedReturnDate)
            verify {
                mockAppointmentScheduler wasNot Called
            }
        }

        @Test
        fun `should only call MSBookingsHandler for the latest vehicle transfer`() {
            val nonLatestTransfer =
                FMSTransferAggregate(
                    fmsDeliveryIndex = "1",
                    deliveryDate = now(),
                    returnDate = now(),
                )
            val nonLatestTransferKey = VehicleTransferKey(1L)
            val latestTransfer =
                FMSTransferAggregate(
                    fmsDeliveryIndex = "13",
                    deliveryDate = now(),
                    vehicleResponsiblePerson = sampleResponsiblePerson,
                )
            val latestTransferKey = VehicleTransferKey(99L)
            val fmsVehicleAggregate =
                sampleFMSVehicle().apply {
                    vin = "TEST-VIN"
                    val samplePersonalVehicleUsageId = 1
                    latestTransferAdditionalData.apply {
                        plannedDeliveryAppointment =
                            PlannedAppointment(
                                from = now(),
                                to = null,
                                responsiblePerson = sampleResponsiblePerson,
                                appointmentKind = 1,
                            )
                        fmsVehicleUsageId = samplePersonalVehicleUsageId
                    }
                    transfers.addAll(listOf(nonLatestTransfer, latestTransfer))
                }
            every { mockMigrateVehicleTransferUseCase.migrateVehicleTransfer(any()) } answers {
                val distinctTransferKey =
                    when (arg<VehicleTransferMigrationDto>(0).deliveryIndex) {
                        latestTransfer.fmsDeliveryIndex -> latestTransferKey
                        else -> nonLatestTransferKey
                    }
                VehicleTransferMigrationResult(true, distinctTransferKey, "", "")
            }

            serviceUnderTest.upsert(fmsVehicleAggregate)

            verify {
                mockAppointmentScheduler.upsertPendingMSBookingAppointment(any(), any(), latestTransferKey, any())
            }
            verify(exactly = 0) {
                mockAppointmentScheduler.upsertPendingMSBookingAppointment(any(), any(), nonLatestTransferKey, any())
            }
        }

        @Test
        fun `report error and not create MS Booking if appointment responsible person does not match vehicle responsible person`() {
            val dateTimeSpecificAppointmentKind = 2
            val sampleAggregate =
                sampleFMSVehicle().apply {
                    this.transfers.add(
                        FMSTransferAggregate(fmsDeliveryIndex = "1", vehicleResponsiblePerson = "RESPONSIBLE PERSON", deliveryDate = now()),
                    )
                    latestTransferAdditionalData =
                        FMSLatestTransferAggregate()
                            .apply {
                                this.plannedDeliveryAppointment =
                                    PlannedAppointment(
                                        from = exactDeliveryTime,
                                        to = exactDeliveryTime.plusMinutes(30),
                                        responsiblePerson = "NOT THE SAME",
                                        appointmentKind = dateTimeSpecificAppointmentKind,
                                    )
                                this.plannedReturnAppointment =
                                    PlannedAppointment(
                                        from = exactReturnTime,
                                        to = exactReturnTime.plusMinutes(30),
                                        responsiblePerson = "EVEN DIFFERENTER",
                                        appointmentKind = dateTimeSpecificAppointmentKind,
                                    )
                                this.fmsVehicleUsageId = 1
                            }
                }

            serviceUnderTest.upsert(sampleAggregate)

            assertTrue(
                sampleAggregate.errors.any {
                    it == "Planned appointment's responsible person did not match the latest vehicle transfer's responsible person!"
                },
            )
            verify {
                mockAppointmentScheduler wasNot Called
            }
        }
    }

    @Test
    fun `given vehicle with transfers and maintenanceOrderNumbers, should pass mapped order numbers`() {
        val sampleFMSVehicleAggregate = sampleFMSVehicle()
        val transferDates = now().minusDays(15) to now().minusDays(5)
        sampleFMSVehicleAggregate.apply {
            // Create order numbers with creation dates that fall within the transfer periods
            this.maintenanceOrderNumbers.add(
                FMSMaintenanceOrderNumber(
                    orderNumber = "000073011111",
                    creationDate = transferDates.first.plusDays(1),
                    responsibleUser = "user1",
                ),
            )
            this.transfers.add(
                FMSTransferAggregate(
                    fmsDeliveryIndex = "1",
                    deliveryDate = transferDates.first,
                    returnDate = transferDates.second,
                    vehicleResponsiblePerson = "user1",
                ),
            )
        }

        serviceUnderTest.upsert(sampleFMSVehicleAggregate)

        assertEquals("000073011111", capturedUpdates.find { it.deliveryIndex == "1" }!!.maintenanceOrderNumber)
    }
}
