package com.fleetmanagement.modules.migration.postprocessors.appointments

import com.fleetmanagement.TestcontainersConfiguration
import com.fleetmanagement.modules.consigneedatasheet.domain.EmployeeNumber
import com.fleetmanagement.modules.migration.FMSMigrationDBConfiguration
import com.fleetmanagement.modules.migration.appointments.FMSMigrationAppointmentStatus
import com.fleetmanagement.modules.migration.appointments.JPAFMSAppointment
import com.fleetmanagement.modules.migration.appointments.JPAFMSAppointmentRepository
import com.fleetmanagement.modules.migration.appointments.MsBookingAppointmentsHandler
import com.fleetmanagement.modules.migration.job.PlannedAppointment
import com.fleetmanagement.modules.transfermsbookings.application.AppointmentType
import com.fleetmanagement.modules.transfermsbookings.application.port.CreateAppointment
import com.fleetmanagement.modules.vehicleperson.api.dtos.People
import com.fleetmanagement.modules.vehicleperson.api.peoplesearch.PeopleSearch
import com.fleetmanagement.modules.vehicletransfer.domain.entities.VehicleTransferKey
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertAll
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest
import org.springframework.context.annotation.Import
import java.time.OffsetDateTime

@DataJpaTest
@Import(
    TestcontainersConfiguration::class,
    FMSMigrationDBConfiguration::class,
)
class MsBookingAppointmentsHandlerTest {
    @Autowired
    private lateinit var jpaFMSAppointmentsRepository: JPAFMSAppointmentRepository
    private lateinit var createAppointmentMock: CreateAppointment
    private lateinit var peopleSearchService: PeopleSearch
    private lateinit var msBookingAppointmentsHandler: MsBookingAppointmentsHandler

    @BeforeEach
    fun setup() {
        jpaFMSAppointmentsRepository.deleteAll()
        createAppointmentMock = mockk(relaxed = true)
        peopleSearchService = mockk(relaxed = true)
        msBookingAppointmentsHandler =
            MsBookingAppointmentsHandler(
                jpaFMSAppointmentsRepository = jpaFMSAppointmentsRepository,
                createAppointmentService = createAppointmentMock,
                peopleSearchService = peopleSearchService,
            )
        every {
            peopleSearchService.peopleSearch(
                employeeNumber = any(),
                firstName = null,
                lastName = null,
                companyEmail = null,
            )
        } returns
            listOf(
                People(
                    employeeNumber = "00129123",
                    firstName = "John",
                    lastName = "Doe",
                    companyEmail = "<EMAIL>",
                ),
            )
    }

    @Nested
    inner class `Test appointment creation` {
        @Test
        fun `should create a delivery appointment entry with a planned delivery date`() {
            val samplePlannedDeliveryDate = OffsetDateTime.now().plusDays(10)
            val sampleFVMVehicleTransferKey = VehicleTransferKey(124123)
            val sampleAppointment =
                PlannedAppointment(
                    from = samplePlannedDeliveryDate,
                    to = samplePlannedDeliveryDate.plusMinutes(30),
                    responsiblePerson = "129123",
                    appointmentKind = 2,
                )

            msBookingAppointmentsHandler.upsertPendingMSBookingAppointment(
                appointment = sampleAppointment,
                vin = "sample_vin",
                vehicleTransferKey = sampleFVMVehicleTransferKey,
                appointmentType = AppointmentType.DELIVERY,
            )

            val actual = jpaFMSAppointmentsRepository.findAll().first()
            assertAll(
                { assertEquals("sample_vin", actual.vehicleVin) },
                { assertEquals(samplePlannedDeliveryDate, actual.appointmentFrom) },
                { assertEquals(samplePlannedDeliveryDate.plusMinutes(30), actual.appointmentTo) },
                { assertEquals(EmployeeNumber("129123").value, actual.personNumber.value) },
                { assertEquals(sampleFVMVehicleTransferKey, actual.vehicleTransferKey) },
                { assertEquals(AppointmentType.DELIVERY, actual.appointmentType) },
            )
        }

        @Test
        fun `should update existing appointment if it is not sent to MS bookings yet`() {
            val expectedVIN = "WP0ZZZ99ZLS123456"
            val matchingType = AppointmentType.DELIVERY
            val originalTransferKey = VehicleTransferKey(124123)
            val originalDate = OffsetDateTime.now().plusDays(2)
            val originalEntry =
                jpaFMSAppointmentsRepository.save(
                    JPAFMSAppointment(
                        vehicleVin = expectedVIN,
                        appointmentType = matchingType,
                        appointmentFrom = originalDate,
                        appointmentTo = originalDate.plusMinutes(30),
                        personNumber = EmployeeNumber("12345"),
                        vehicleTransferKey = originalTransferKey,
                    ),
                )
            val updatedAppointmentDate = OffsetDateTime.now().plusDays(10)
            val updatedVehicleResponsiblePerson = "988765"
            val updatedTransferKey = VehicleTransferKey(99900023)
            val updatedAppointment =
                PlannedAppointment(
                    from = updatedAppointmentDate,
                    to = updatedAppointmentDate.plusMinutes(30),
                    responsiblePerson = updatedVehicleResponsiblePerson,
                    appointmentKind = 2,
                )
            msBookingAppointmentsHandler.upsertPendingMSBookingAppointment(
                appointment = updatedAppointment,
                vin = expectedVIN,
                vehicleTransferKey = updatedTransferKey,
                appointmentType = AppointmentType.DELIVERY,
            )

            val actual = jpaFMSAppointmentsRepository.findAll().first()
            assertAll(
                { assertEquals(originalEntry.id, actual.id) },
                { assertEquals(expectedVIN, actual.vehicleVin) },
                { assertEquals(updatedAppointmentDate, actual.appointmentFrom) },
                { assertEquals(updatedAppointmentDate.plusMinutes(30), actual.appointmentTo) },
                { assertEquals(EmployeeNumber(updatedVehicleResponsiblePerson).value, actual.personNumber.value) },
                { assertEquals(updatedTransferKey, actual.vehicleTransferKey) },
                { assertEquals(AppointmentType.DELIVERY, actual.appointmentType) },
            )
        }

        @Test
        fun `should NOT update existing appointment if already sent to MS Booking`() {
            val expectedVIN = "WP0ZZZ99ZLS123456"
            val matchingType = AppointmentType.DELIVERY
            val originalTransferKey = VehicleTransferKey(124123)
            val originalDate = OffsetDateTime.now().plusDays(2)
            val originalPerson = "12345"
            val originalEntry =
                jpaFMSAppointmentsRepository.save(
                    JPAFMSAppointment(
                        vehicleVin = expectedVIN,
                        appointmentType = matchingType,
                        appointmentFrom = originalDate,
                        appointmentTo = originalDate.plusMinutes(30),
                        personNumber = EmployeeNumber(originalPerson),
                        vehicleTransferKey = originalTransferKey,
                        status = FMSMigrationAppointmentStatus.SENT_TO_MS_BOOKINGS,
                    ),
                )
            val updatedAppointmentDate = OffsetDateTime.now().plusDays(10)
            val updatedAppointment =
                PlannedAppointment(
                    from = updatedAppointmentDate,
                    to = updatedAppointmentDate.plusMinutes(30),
                    responsiblePerson = "129123",
                    appointmentKind = 2,
                )
            val updatedTransferKey = VehicleTransferKey(99900023)
            msBookingAppointmentsHandler.upsertPendingMSBookingAppointment(
                appointment = updatedAppointment,
                vin = expectedVIN,
                vehicleTransferKey = updatedTransferKey,
                appointmentType = AppointmentType.DELIVERY,
            )

            msBookingAppointmentsHandler.upsertPendingMSBookingAppointment(
                appointment = updatedAppointment,
                vin = expectedVIN,
                vehicleTransferKey = updatedTransferKey,
                appointmentType = AppointmentType.DELIVERY,
            )

            val actual = jpaFMSAppointmentsRepository.findAll().first()
            assertAll(
                { assertEquals(originalEntry.id, actual.id) },
                { assertEquals(expectedVIN, actual.vehicleVin) },
                { assertEquals(originalDate, actual.appointmentFrom) },
                { assertEquals(originalDate.plusMinutes(30), actual.appointmentTo) },
                { assertEquals(EmployeeNumber(originalPerson).value, actual.personNumber.value) },
                { assertEquals(originalTransferKey, actual.vehicleTransferKey) },
                { assertEquals(AppointmentType.DELIVERY, actual.appointmentType) },
            )
        }
    }

    @Nested
    inner class `Test scheduling of pending appointments in MS Booking` {
        @Test
        fun `should schedule MS Booking appointment that are Pending`() {
            val samplePlannedDeliveryDate = OffsetDateTime.now().plusDays(10)
            val sampleFVMVehicleTransferKey = VehicleTransferKey(124123)
            jpaFMSAppointmentsRepository.save(
                JPAFMSAppointment(
                    vehicleVin = "WP0ZZZ99ZLS123456",
                    appointmentType = AppointmentType.DELIVERY,
                    appointmentFrom = samplePlannedDeliveryDate,
                    appointmentTo = samplePlannedDeliveryDate.plusMinutes(30),
                    personNumber = EmployeeNumber("129123"),
                    vehicleTransferKey = sampleFVMVehicleTransferKey,
                ),
            )
            msBookingAppointmentsHandler.scheduleMSBookingAppointments(batchSize = 1)

            val actual = jpaFMSAppointmentsRepository.findAll()
            assertEquals(
                FMSMigrationAppointmentStatus.SENT_TO_MS_BOOKINGS,
                actual.first().status,
            )
        }

        @Test
        fun `should schedule MS Booking appointments`() {
            val samplePlannedDeliveryDate = OffsetDateTime.now().plusDays(10)
            val sampleFVMVehicleTransferKey = VehicleTransferKey(124123)
            jpaFMSAppointmentsRepository.save(
                JPAFMSAppointment(
                    vehicleVin = "WP0ZZZ99ZLS123456",
                    appointmentType = AppointmentType.DELIVERY,
                    appointmentFrom = samplePlannedDeliveryDate,
                    appointmentTo = samplePlannedDeliveryDate.plusMinutes(30),
                    personNumber = EmployeeNumber("129123"),
                    vehicleTransferKey = sampleFVMVehicleTransferKey,
                ),
            )
            every {
                peopleSearchService.peopleSearch(
                    employeeNumber = "00129123",
                    firstName = null,
                    lastName = null,
                    companyEmail = null,
                )
            } returns
                listOf(
                    People(
                        employeeNumber = "00129123",
                        firstName = "John",
                        lastName = "Doe",
                        companyEmail = "<EMAIL>",
                    ),
                )

            msBookingAppointmentsHandler.scheduleMSBookingAppointments(batchSize = 1)

            verify(exactly = 1) {
                peopleSearchService.peopleSearch(
                    employeeNumber = "00129123",
                    firstName = null,
                    lastName = null,
                    companyEmail = null,
                )
                createAppointmentMock.createAppointment(
                    startDateTime = samplePlannedDeliveryDate,
                    endDateTime = samplePlannedDeliveryDate.plusMinutes(30),
                    serviceNotes = sampleFVMVehicleTransferKey.value.toString(),
                    customerEmail = "<EMAIL>",
                    serviceName = AppointmentType.DELIVERY,
                )
            }
        }

        @Test
        fun `should continue appointment scheduling MS Booking appointments even on failure`() {
            val samplePlannedDeliveryDate = OffsetDateTime.now().plusDays(10)
            val sampleFVMVehicleTransferKey = VehicleTransferKey(124123)
            jpaFMSAppointmentsRepository.saveAll(
                listOf(
                    JPAFMSAppointment(
                        vehicleVin = "WP0ZZZ99ZLS123456",
                        appointmentType = AppointmentType.DELIVERY,
                        appointmentFrom = samplePlannedDeliveryDate,
                        appointmentTo = samplePlannedDeliveryDate.plusMinutes(30),
                        personNumber = EmployeeNumber("129123"),
                        vehicleTransferKey = sampleFVMVehicleTransferKey,
                    ),
                    JPAFMSAppointment(
                        vehicleVin = "1FMHK8D82BGA40646",
                        appointmentType = AppointmentType.RETURN,
                        appointmentFrom = samplePlannedDeliveryDate,
                        appointmentTo = samplePlannedDeliveryDate.plusMinutes(30),
                        personNumber = EmployeeNumber("126123"),
                        vehicleTransferKey = sampleFVMVehicleTransferKey,
                    ),
                ),
            )
            every {
                peopleSearchService.peopleSearch(
                    employeeNumber = any(),
                    firstName = null,
                    lastName = null,
                    companyEmail = null,
                )
            } returnsMany
                listOf(
                    emptyList(),
                    listOf(
                        People(
                            employeeNumber = "00129123",
                            firstName = "John",
                            lastName = "Doe",
                            companyEmail = "<EMAIL>",
                        ),
                    ),
                )

            msBookingAppointmentsHandler.scheduleMSBookingAppointments(batchSize = 2)

            val sentAppointments =
                jpaFMSAppointmentsRepository.findByStatus(
                    status = FMSMigrationAppointmentStatus.SENT_TO_MS_BOOKINGS,
                )
            assertEquals(
                FMSMigrationAppointmentStatus.SENT_TO_MS_BOOKINGS,
                sentAppointments.first().status,
            )

            val failedAppointments =
                jpaFMSAppointmentsRepository.findByStatus(
                    status = FMSMigrationAppointmentStatus.FAILED_TO_SEND,
                )
            assertEquals(
                FMSMigrationAppointmentStatus.FAILED_TO_SEND,
                failedAppointments.first().status,
            )
        }

        @Test
        fun `should schedule appointments only for the current and future dates`() {
            val appointmentWithFutureDate = OffsetDateTime.now().plusDays(10)
            val appointmentInPast = OffsetDateTime.now().minusDays(1)
            val sampleFVMVehicleTransferKey = VehicleTransferKey(124123)
            jpaFMSAppointmentsRepository.saveAll(
                listOf(
                    JPAFMSAppointment(
                        vehicleVin = "WP0ZZZ99ZLS123456",
                        appointmentType = AppointmentType.DELIVERY,
                        appointmentFrom = appointmentWithFutureDate,
                        appointmentTo = appointmentWithFutureDate.plusMinutes(30),
                        personNumber = EmployeeNumber("129123"),
                        vehicleTransferKey = sampleFVMVehicleTransferKey,
                    ),
                    JPAFMSAppointment(
                        vehicleVin = "1FMHK8D82BGA40646",
                        appointmentType = AppointmentType.RETURN,
                        appointmentFrom = appointmentInPast,
                        appointmentTo = appointmentInPast.plusMinutes(30),
                        personNumber = EmployeeNumber("126123"),
                        vehicleTransferKey = sampleFVMVehicleTransferKey,
                    ),
                ),
            )

            msBookingAppointmentsHandler.scheduleMSBookingAppointments(batchSize = 2)

            verify(exactly = 1) {
                createAppointmentMock.createAppointment(
                    startDateTime = appointmentWithFutureDate,
                    endDateTime = appointmentWithFutureDate.plusMinutes(30),
                    serviceNotes = sampleFVMVehicleTransferKey.value.toString(),
                    customerEmail = "<EMAIL>",
                    serviceName = AppointmentType.DELIVERY,
                )
            }
            verify(exactly = 0) {
                createAppointmentMock.createAppointment(
                    startDateTime = appointmentInPast,
                    endDateTime = appointmentInPast.plusMinutes(30),
                    serviceNotes = sampleFVMVehicleTransferKey.value.toString(),
                    customerEmail = "<EMAIL>",
                    serviceName = AppointmentType.RETURN,
                )
            }
        }
    }
}
