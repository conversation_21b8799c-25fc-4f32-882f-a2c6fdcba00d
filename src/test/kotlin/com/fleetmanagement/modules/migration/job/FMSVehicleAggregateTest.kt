package com.fleetmanagement.modules.migration.job

import com.fleetmanagement.modules.migration.FMSVehicleAggregateMother.sampleFMSVehicle
import com.fleetmanagement.modules.vehicledata.api.domain.NextProcess
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import java.time.OffsetDateTime
import java.time.OffsetDateTime.now

class FMSVehicleAggregateTest {
    @ParameterizedTest(name = "{3} when: FMS primary status {2}; scrapped: {0}; blocked for sales: {1}")
    @CsvSource(
        value = [
            "false,false,YF35,SALES",
            "false,false,YF40,SALES",
            "false,false,YQ98,SALES",
            "false,false,YF30,SALES",

            "true,false,YF69,SCRAPPED_CAR_RETURNED",
            "true,false,YF70,SCRAPPED_CAR_RETURNED",
            "true,false,YXXX,SCRAPPED_CAR_RETURNED",

            "false,true,YF30,PROFITABILITY_AUDIT_IN_PREPARATION",

            // allow for a return to happen:
            "false,false,YF90,CHECK_IF_REUSAGE_IS_POSSIBLE",
        ],
    )
    fun `should correctly calculate Next Process`(
        isScrapVehicle: Boolean,
        isBlockedForSale: Boolean,
        fmsStatus: String,
        expectedNextProcess: NextProcess,
    ) {
        val sampleFMSVehicleAggregate = sampleFMSVehicle()
        sampleFMSVehicleAggregate.apply {
            this.fmsPrimaryStatus = fmsStatus
            this.blockedForSale = isBlockedForSale
            this.scrapVehicle = isScrapVehicle
        }

        assertEquals(expectedNextProcess, sampleFMSVehicleAggregate.nextProcess)
    }

    @Test
    fun `should map order numbers to transfers based on delivery and creation dates`() {
        val deliveryDate1 = now().minusDays(10)
        val returnDate1 = now().minusDays(5)
        val deliveryDate2 = now().minusDays(3)

        val sampleFMSVehicleAggregate = sampleFMSVehicle()
        sampleFMSVehicleAggregate.apply {
            // Order number created during first transfer
            this.maintenanceOrderNumbers.add(
                FMSMaintenanceOrderNumber(
                    orderNumber = "000073015555",
                    creationDate = now().minusDays(7), // between delivery1 and return1
                    responsibleUser = "user1",
                ),
            )
            // Order number created during second transfer
            this.maintenanceOrderNumbers.add(
                FMSMaintenanceOrderNumber(
                    orderNumber = "000073019999",
                    creationDate = now().minusDays(2), // after delivery2
                    responsibleUser = "user2",
                ),
            )

            this.transfers.add(
                FMSTransferAggregate(
                    fmsDeliveryIndex = "1",
                    deliveryDate = deliveryDate1,
                    returnDate = returnDate1,
                    vehicleResponsiblePerson = "user1",
                ),
            )
            this.transfers.add(
                FMSTransferAggregate(
                    fmsDeliveryIndex = "2",
                    deliveryDate = deliveryDate2,
                    vehicleResponsiblePerson = "user2",
                ),
            )
        }

        val transfers = sampleFMSVehicleAggregate.calculateTransfersWithOrderNumbers()

        assertEquals("000073015555", transfers.find { it.fmsDeliveryIndex == "1" }!!.maintenanceOrderNumber)
        assertEquals("000073019999", transfers.find { it.fmsDeliveryIndex == "2" }!!.maintenanceOrderNumber)
    }

    @Test
    fun `should prioritize order number where responsible user matches vehicle responsible person`() {
        val deliveryDate = now().minusDays(10)
        val returnDate = now().minusDays(1)

        val sampleFMSVehicleAggregate = sampleFMSVehicle()
        sampleFMSVehicleAggregate.apply {
            // Two order numbers created during the same transfer period
            this.maintenanceOrderNumbers.add(
                FMSMaintenanceOrderNumber(
                    orderNumber = "000073011111",
                    creationDate = now().minusDays(7),
                    responsibleUser = "different_user", // doesn't match
                ),
            )
            this.maintenanceOrderNumbers.add(
                FMSMaintenanceOrderNumber(
                    orderNumber = "000073022222",
                    creationDate = now().minusDays(6), // newer but matches user
                    responsibleUser = "correct_user", // matches vehicle responsible person
                ),
            )

            this.transfers.add(
                FMSTransferAggregate(
                    fmsDeliveryIndex = "1",
                    deliveryDate = deliveryDate,
                    returnDate = returnDate,
                    vehicleResponsiblePerson = "correct_user",
                ),
            )
        }

        val transfers = sampleFMSVehicleAggregate.calculateTransfersWithOrderNumbers()

        assertEquals("000073022222", transfers.find { it.fmsDeliveryIndex == "1" }!!.maintenanceOrderNumber)
    }

    private fun Pair<Pair<OffsetDateTime, OffsetDateTime?>, String>.deliveryDate() = first.first

    private fun Pair<Pair<OffsetDateTime, OffsetDateTime?>, String>.returnDate() = first.second

    private fun Pair<Pair<OffsetDateTime, OffsetDateTime?>, String>.responsiblePerson() = second

    @Test
    fun `given vehicle with transfers and maintenanceOrderNumbers, should apply order numbers too`() {
        val sampleFMSVehicleAggregate = sampleFMSVehicle()
        val transfer1 = now().minusDays(20) to now().minusDays(15) to "user1"
        val transfer2 = now().minusDays(14) to now().minusDays(10) to "user2"
        val transfer3 = now().minusDays(9) to null to "user3"
        sampleFMSVehicleAggregate.apply {
            // Create order numbers with creation dates that fall within the transfer periods
            this.maintenanceOrderNumbers.add(
                FMSMaintenanceOrderNumber(
                    orderNumber = "000073011111",
                    creationDate = transfer1.deliveryDate().plusDays(1),
                    responsibleUser = transfer1.responsiblePerson(),
                ),
            )
            this.maintenanceOrderNumbers.add(
                FMSMaintenanceOrderNumber(
                    orderNumber = "000073022222",
                    creationDate = transfer2.deliveryDate().plusDays(1),
                    responsibleUser = transfer2.responsiblePerson(),
                ),
            )
            this.maintenanceOrderNumbers.add(
                FMSMaintenanceOrderNumber(
                    orderNumber = "000073033333",
                    creationDate = transfer3.deliveryDate().plusDays(1),
                    responsibleUser = "non_matching_user", // This one should not match any transfer
                ),
            )
            this.maintenanceOrderNumbers.shuffle()
            this.transfers.add(
                FMSTransferAggregate(
                    fmsDeliveryIndex = "1",
                    deliveryDate = transfer1.deliveryDate(),
                    returnDate = transfer1.returnDate(),
                    vehicleResponsiblePerson = transfer1.responsiblePerson(),
                ),
            )
            this.transfers.add(
                FMSTransferAggregate(
                    fmsDeliveryIndex = "2",
                    deliveryDate = transfer2.deliveryDate(),
                    returnDate = transfer2.returnDate(),
                    vehicleResponsiblePerson = transfer2.responsiblePerson(),
                ),
            )
            this.transfers.add(
                FMSTransferAggregate(
                    fmsDeliveryIndex = "3",
                    deliveryDate = transfer3.deliveryDate(),
                    vehicleResponsiblePerson = transfer3.responsiblePerson(),
                ),
            )
            this.transfers.shuffle()
        }

        val transfers = sampleFMSVehicleAggregate.calculateTransfersWithOrderNumbers()

        // Order numbers should be mapped based on creation date within transfer period
        assertEquals("000073011111", transfers.find { it.fmsDeliveryIndex == "1" }!!.maintenanceOrderNumber)
        assertEquals("000073022222", transfers.find { it.fmsDeliveryIndex == "2" }!!.maintenanceOrderNumber)
        assertEquals(null, transfers.find { it.fmsDeliveryIndex == "3" }!!.maintenanceOrderNumber)
    }

    @Test
    fun `multiple maintenanceOrderNumbers matching by time but not by responsible person should not apply`() {
        val deliveryDate = now().minusDays(10)
        val returnDate = now().minusDays(5)

        val sampleFMSVehicleAggregate = sampleFMSVehicle()
        sampleFMSVehicleAggregate.apply {
            // Two order numbers created during the same transfer period, but neither matches the responsible person
            this.maintenanceOrderNumbers.add(
                FMSMaintenanceOrderNumber(
                    orderNumber = "000073011111",
                    creationDate = now().minusDays(8), // within transfer period
                    responsibleUser = "wrong_user_1", // doesn't match
                ),
            )
            this.maintenanceOrderNumbers.add(
                FMSMaintenanceOrderNumber(
                    orderNumber = "000073022222",
                    creationDate = now().minusDays(7), // within transfer period
                    responsibleUser = "wrong_user_2", // doesn't match
                ),
            )

            this.transfers.add(
                FMSTransferAggregate(
                    fmsDeliveryIndex = "1",
                    deliveryDate = deliveryDate,
                    returnDate = returnDate,
                    vehicleResponsiblePerson = "correct_user", // doesn't match any order numbers
                ),
            )
        }

        val transfers = sampleFMSVehicleAggregate.calculateTransfersWithOrderNumbers()

        // No order number should be assigned because none match the responsible person
        assertEquals(null, transfers.find { it.fmsDeliveryIndex == "1" }!!.maintenanceOrderNumber)

        // An error should be reported for the multiple non-matching order numbers
        assertEquals(1, sampleFMSVehicleAggregate.errors.size)
        assertEquals(
            "Maintenance order numbers '[000073011111, 000073022222]' do not match responsible person for fmsDeliveryIndex 1.",
            sampleFMSVehicleAggregate.errors.first(),
        )
    }
}
