package com.fleetmanagement.modules.migration.job

import com.fleetmanagement.TestcontainersConfiguration
import com.fleetmanagement.modules.migration.MigrationModuleConfiguration
import com.fleetmanagement.modules.migration.job.ports.Preprocessor
import com.fleetmanagement.modules.migration.preprocessors.excelparsing.S3BucketFileProvider
import com.fleetmanagement.modules.migration.report.S3CSVMigrationReportFactory
import com.fleetmanagement.modules.transfermsbookings.application.port.CreateAppointment
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByVIN
import com.fleetmanagement.modules.vehicledata.api.domain.FinancialAssetType
import com.fleetmanagement.modules.vehicledata.api.exceptions.PVHVehicleNotFoundException
import com.fleetmanagement.modules.vehicledata.integrations.pvh.api.PVHClient
import com.fleetmanagement.modules.vehicleperson.integration.userservice.rest.client.EmployeeClient
import com.fleetmanagement.modules.vehicleperson.integration.userservice.rest.model.EmployeeDto
import com.fleetmanagement.modules.vehicleperson.integration.userservice.rest.model.EmployeeTypeDto
import com.fleetmanagement.modules.vehicleregistration.api.dto.VehicleRegistrationAPIResponse
import com.fleetmanagement.modules.vehicleregistration.client.VehicleRegistrationClient
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.annotation.Import
import org.springframework.core.io.ClassPathResource
import org.springframework.test.context.TestPropertySource
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional
import kotlin.io.path.Path

@SpringBootTest
@Import(
    TestcontainersConfiguration::class,
    MigrationModuleConfiguration::class,
)
@TestPropertySource(
    properties = [
        "fms-migration-job.enabled=true",
    ],
)
class FMSMigrationVehicleDataUpdateIntegrationTest {
    @Autowired
    private lateinit var preprocessors: List<Preprocessor>

    @Autowired
    private lateinit var service: FMSMigrationJobService

    @Autowired
    private lateinit var readVehicleByVIN: ReadVehicleByVIN

    @MockkBean
    private lateinit var pvhClient: PVHClient

    @MockkBean
    private lateinit var employeeClient: EmployeeClient

    @MockkBean(relaxed = true)
    private lateinit var createAppointment: CreateAppointment

    @MockkBean(S3BucketFileProvider::class)
    private lateinit var fileProviderMock: S3BucketFileProvider

    @MockkBean(relaxed = true)
    private lateinit var s3CSVMigrationReportFactory: S3CSVMigrationReportFactory

    @MockkBean(relaxed = true)
    private lateinit var vehicleRegistrationClient: VehicleRegistrationClient

    @BeforeEach
    fun setupLocalFileProvider() {
        every { fileProviderMock.provideFile(any()) } answers {
            val requestedFilename = Path(this.arg<String>(0)).fileName.toString()
            ClassPathResource("migration/testdata/$requestedFilename")
        }
    }

    @BeforeEach
    fun setup() {
        every { s3CSVMigrationReportFactory.startReport(any()) } returns mockk(relaxed = true)
        every { employeeClient.filterEmployees(employeeNumber = any()) } returns
            listOf(
                EmployeeDto(
                    key = "sampleEmployeeKey",
                    employeeNumber = "********",
                    firstName = "Hans",
                    lastName = "TestName",
                    companyEmail = "<EMAIL>",
                    accountingArea = "accountingarea",
                    type = EmployeeTypeDto.PAG_EMPLOYEE,
                ),
            )
    }

    @Test
    fun import() {
        assertEquals(12, preprocessors.size)
    }

    @Test
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    fun importsVehicleDataForAVehicleNotAvailableInPVH() {
        every {
            pvhClient.vehicleDataByVin(any())
        } throws PVHVehicleNotFoundException("")

        every {
            vehicleRegistrationClient.getCompletedOrdersBy(any())
        } returns VehicleRegistrationAPIResponse(emptyList())

        val cache = FMSVehicleAggregateCache()
        service.executeMigration(cache)

        // Financial Asset Type
        assertEquals(
            FinancialAssetType.FE,
            readVehicleByVIN.readVehicleByVIN("WP0ZZZWA5TS100007").financialAssetType,
        )

        // Blocked for sales
        assertEquals(
            true,
            readVehicleByVIN.readVehicleByVIN("WP0ZZZWA4SS060050").order?.blockedForSale,
        )
        assertEquals(
            false,
            readVehicleByVIN.readVehicleByVIN("WP0ZZZY16RSA77118").order?.blockedForSale,
        )
        // Scrap Vehicle
        assertEquals(
            true,
            readVehicleByVIN.readVehicleByVIN("WP0ZZZWA4SS060050").fleet?.scrapVehicle,
        )
        assertEquals(
            false,
            readVehicleByVIN.readVehicleByVIN("WP0ZZZY16RSA77247").fleet?.scrapVehicle,
        )

        // Sold Date
        assertNotNull(
            readVehicleByVIN.readVehicleByVIN("WP0ZZZY16RSA77247").fleet?.soldDate,
        )

        // Scrap Date
        assertNotNull(
            readVehicleByVIN.readVehicleByVIN("WP0ZZZWA8SS060018").fleet?.scrappedDate,
        )

        // Approved for scrapping date
        assertNotNull(
            readVehicleByVIN.readVehicleByVIN("WP0ZZZWA4SS060050").fleet?.approvedForScrappingDate,
        )

        // Vehicle Sent to sales date
        assertNotNull(
            readVehicleByVIN.readVehicleByVIN("WP0ZZZY16RSA77118").fleet?.vehicleSentToSalesDate,
        )

        // Preparation done date
        assertNotNull(
            readVehicleByVIN.readVehicleByVIN("WP0ZZZY16RSA56611").delivery?.preparationDoneDate,
        )

        // Residual Market Value
        assertTrue(
            checkNotNull(
                readVehicleByVIN.readVehicleByVIN("WP0ZZZY16RSA77118").fleet?.isResidualValueMarket,
            ),
        )

        assertTrue(
            checkNotNull(
                readVehicleByVIN.readVehicleByVIN("WP0ZZZWA7SS060060").fleet?.isClassic,
            ),
        )

        assertFalse(
            checkNotNull(
                readVehicleByVIN.readVehicleByVIN("WP0ZZZY16RSA77118").fleet?.isClassic,
            ),
        )

        assertEquals(
            "AP 125 C SES301",
            readVehicleByVIN.readVehicleByVIN("WP0ZZZWA5TS100007").equiId,
        )
    }
}
