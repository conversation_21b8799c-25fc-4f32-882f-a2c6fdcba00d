package com.fleetmanagement.modules.migration.preprocessors

import com.fleetmanagement.modules.migration.LocalFileProvider
import com.fleetmanagement.modules.migration.job.FMSVehicleAggregateCache
import com.fleetmanagement.modules.migration.preprocessors.excelparsing.ExcelPreprocessor
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.time.LocalDate

class PreProcessorMaintenanceOrderNumbersTest {
    private lateinit var cache: FMSVehicleAggregateCache
    private lateinit var serviceUnderTest: PreProcessorMaintenanceOrderNumbers

    @BeforeEach
    fun setup() {
        cache = FMSVehicleAggregateCache()
        // must preprocess DLZM to have all vehicle cached with VIN
        PreProcessorDLZM(
            ExcelPreprocessor(
                LocalFileProvider("/migration/testdata/DLZM.XLSX"),
            ),
        ).process(cache)
        serviceUnderTest =
            PreProcessorMaintenanceOrderNumbers(
                ExcelPreprocessor(
                    LocalFileProvider("/migration/testdata/ORDER_NUMBERS.XLSX"),
                ),
            )
    }

    @Test
    fun `should validate the file`() {
        val result = serviceUnderTest.validate()
        assertTrue(result, "Expected validation to succeed when all headers are present")
        val resultOfInvalidFile =
            PreProcessorEQUI(ExcelPreprocessor(LocalFileProvider("/migration/testdata/empty.XLSX"))).validate()
        assertFalse(resultOfInvalidFile)
    }

    @Test
    fun `should correctly map maintenance order numbers`() {
        serviceUnderTest.process(cache)

        val vehicleWithMultiple = cache.getAggregateByVin("WP0ZZZY16RSA77118")!!
        assertEquals(5, vehicleWithMultiple.maintenanceOrderNumbers.size)
        assertTrue(vehicleWithMultiple.maintenanceOrderNumbers.any { it.orderNumber == "000073010125" })
        assertTrue(vehicleWithMultiple.maintenanceOrderNumbers.any { it.orderNumber == "000073010129" })

        // Check that the order numbers have all required fields
        val firstOrderNumber = vehicleWithMultiple.maintenanceOrderNumbers.first()
        assertEquals(LocalDate.of(2025, 7, 25), firstOrderNumber.creationDate.toLocalDate())
        assertEquals("076307", firstOrderNumber.responsibleUser)

        val oneWithMissingOrderNumbers = cache.getAggregateByVin("WP0ZZZWA7SS060060")!!
        assertEquals(0, oneWithMissingOrderNumbers.maintenanceOrderNumbers.size)
    }

    @Test
    fun `should not map order numbers that are NOT of type maintenance order numbers`() {
        serviceUnderTest.process(cache)

        val oneWithMissingOrderNumbers = cache.getAggregateByVin("WP0ZZZWA6SS000514")!!
        assertEquals(0, oneWithMissingOrderNumbers.maintenanceOrderNumbers.size)
    }
}
