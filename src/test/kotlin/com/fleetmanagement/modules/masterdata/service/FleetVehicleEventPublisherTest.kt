package com.fleetmanagement.modules.masterdata.service

import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fleetmanagement.TestcontainersConfiguration
import com.fleetmanagement.emhshared.LeasingArt
import com.fleetmanagement.modules.consigneedatasheet.application.UsageGroupDto
import com.fleetmanagement.modules.consigneedatasheet.application.VehicleUsageDto
import com.fleetmanagement.modules.consigneedatasheet.domain.CostCenterDescription
import com.fleetmanagement.modules.consigneedatasheet.domain.EmployeeNumber
import com.fleetmanagement.modules.masterdata.FleetVehicleMasterDataDBConfiguration
import com.fleetmanagement.modules.masterdata.configuration.FleetVehicleMasterDataConfigurationProperties
import com.fleetmanagement.modules.masterdata.dto.ExternalConsumptionInfo
import com.fleetmanagement.modules.masterdata.dto.ExternalFleetInfo
import com.fleetmanagement.modules.masterdata.dto.ExternalFleetMasterVehicle
import com.fleetmanagement.modules.masterdata.dto.ExternalOrderInfo
import com.fleetmanagement.modules.masterdata.dto.ExternalVehicle
import com.fleetmanagement.modules.masterdata.dto.ExternalVehicleColorData
import com.fleetmanagement.modules.masterdata.dto.ExternalVehicleEvaluationData
import com.fleetmanagement.modules.masterdata.dto.ExternalVehicleModel
import com.fleetmanagement.modules.masterdata.dto.ExternalVehiclePrice
import com.fleetmanagement.modules.masterdata.dto.ExternalVehicleProduction
import com.fleetmanagement.modules.masterdata.dto.ExternalVehicleRegistration
import com.fleetmanagement.modules.masterdata.dto.ExternalVehicleSalesData
import com.fleetmanagement.modules.masterdata.dto.ExternalVehicleTechnicalData
import com.fleetmanagement.modules.masterdata.dto.ExternalVehicleWltpInfo
import com.fleetmanagement.modules.masterdata.entities.FleetVehicleEventHash
import com.fleetmanagement.modules.masterdata.entities.FleetVehicleEventHashRepository
import com.fleetmanagement.modules.masterdata.entities.FleetVehicleMasterData
import com.fleetmanagement.modules.masterdata.entities.FleetVehicleMasterDataRepository
import com.fleetmanagement.modules.masterdata.entities.domain.VehicleEventType
import com.fleetmanagement.modules.vehicledata.api.ReadMileageReadings
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByVehicleId
import com.fleetmanagement.modules.vehicledata.api.domain.FuelType
import com.fleetmanagement.modules.vehicledata.api.dtos.MileageReadingDTO
import com.fleetmanagement.modules.vehicledata.api.dtos.MileageReadingSource
import com.fleetmanagement.modules.vehicledata.objectmothers.VehicleDataDTOObjectMother.vehicleDTOWithAllFieldsPopulated
import com.fleetmanagement.modules.vehicleperson.api.readvehicleperson.ReadVehiclePersonDetailByEmployeeNumber
import com.fleetmanagement.modules.vehicleperson.features.readvehicleperson.toVehiclePersonDetail
import com.fleetmanagement.modules.vehicleperson.integration.userservice.rest.objectmothers.EmployeeDtoObjectMother
import com.fleetmanagement.modules.vehicleregistration.api.ReadRegistrationOrder
import com.fleetmanagement.modules.vehicleregistration.api.dto.RegistrationPeriod
import com.fleetmanagement.modules.vehicleregistration.api.dto.VehicleRegistrationAPIResponse
import com.fleetmanagement.modules.vehicleregistration.api.dto.VehicleRegistrationOrder
import com.fleetmanagement.modules.vehiclesales.api.ReadVehicleSaleByVehicleId
import com.fleetmanagement.modules.vehiclesales.api.dto.VehicleSalesDto
import com.fleetmanagement.modules.vehicletransfer.application.VehicleTransferAbstractionDto
import com.fleetmanagement.modules.vehicletransfer.application.port.VehicleTransferAbstractionUsecase
import com.fleetmanagement.modules.vehicletransfer.domain.entities.CostCenterId
import com.fleetmanagement.modules.vehicletransfer.domain.entities.UtilizationArea
import com.fleetmanagement.modules.vehicletransfer.domain.entities.VehicleTransferKey
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.verify
import jakarta.transaction.Transactional
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertNotEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertNotNull
import org.junit.jupiter.api.assertNull
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase
import org.springframework.boot.test.autoconfigure.json.AutoConfigureJson
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest
import org.springframework.boot.test.autoconfigure.orm.jpa.TestEntityManager
import org.springframework.context.annotation.Import
import java.math.BigDecimal
import java.text.SimpleDateFormat
import java.time.OffsetDateTime
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.util.Date
import java.util.Optional
import java.util.UUID

@DataJpaTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@AutoConfigureJson
@Import(
    TestcontainersConfiguration::class,
    FleetVehicleMasterDataDBConfiguration::class,
)
@Transactional
class FleetVehicleEventPublisherTest {
    @Autowired
    private lateinit var entityManager: TestEntityManager

    @Autowired
    private lateinit var hashRepository: FleetVehicleEventHashRepository

    private val producer: StreamzillaKafkaProducer<ExternalFleetMasterVehicle> = mockk(relaxed = true)
    private val repository: FleetVehicleMasterDataRepository = mockk()
    private val config: FleetVehicleMasterDataConfigurationProperties = mockk()
    private val readVehicle: ReadVehicleByVehicleId = mockk()
    private val readVehicleTransfer: VehicleTransferAbstractionUsecase = mockk()
    private val readRegistration: ReadRegistrationOrder = mockk()
    private val readMileageReadings: ReadMileageReadings = mockk()
    private val readVehicleSaleByVehicleId: ReadVehicleSaleByVehicleId = mockk()
    private val readVehiclePersonDetail: ReadVehiclePersonDetailByEmployeeNumber = mockk()

    private lateinit var publisher: FleetVehicleEventPublisher
    private lateinit var deduplicator: FleetVehicleEventDeduplicator

    @BeforeEach
    fun setup() {
        every { config.topic } returns "test-topic"
        every { producer.send(any(), any(), any(), any()) } just Runs

        deduplicator = FleetVehicleEventDeduplicator(hashRepository)
        publisher =
            FleetVehicleEventPublisher(
                producer = producer,
                config = config,
                readVehicle = readVehicle,
                readVehicleTransfer = readVehicleTransfer,
                readRegistration = readRegistration,
                readMileageReadings = readMileageReadings,
                readVehicleSaleByVehicleId = readVehicleSaleByVehicleId,
                readVehiclePersonDetail = readVehiclePersonDetail,
                deduplicator = deduplicator,
                objectMapper = jacksonObjectMapper(),
            )
    }

    @AfterEach
    fun cleanup() {
        entityManager.entityManager.createQuery("DELETE FROM FleetVehicleMasterData").executeUpdate()
        entityManager.entityManager.createQuery("DELETE FROM FleetVehicleEventHash ").executeUpdate()
    }

    @Test
    fun `should produce Kafka event for each vehicle`() {
        // GIVEN
        val vehicleId = UUID.randomUUID()
        val event1 = FleetVehicleMasterData(vehicleId = vehicleId, eventType = VehicleEventType.VEHICLE_CREATED)
        val event2 =
            FleetVehicleMasterData(vehicleId = vehicleId, eventType = VehicleEventType.VEHICLE_TRANSFER_CREATED)

        val vehicle = vehicleDTOWithAllFieldsPopulated(id = vehicleId)
        val vehicleRegistration = vehicleRegistrationOrder(vehicleId)
        val vehicleTransfers =
            listOf(
                emptyList(),
                listOf(
                    vehicleTransferDto(vehicleId = vehicleId, status = "ACTIVE"),
                    vehicleTransferDto(vehicleId = vehicleId, status = "PLANNED"),
                    vehicleTransferDto(vehicleId = vehicleId, status = "FINISHED").copy(
                        returnDate = OffsetDateTime.parse("2024-02-16T10:00:00Z"),
                    ),
                ),
            )

        val vehicleSales = vehicleSalesDto(vehicleId)
        val mileageList =
            listOf(
                mileageReadingDTO(mileage = 65200, readDate = OffsetDateTime.now().minusDays(5)),
                mileageReadingDTO(mileage = 68200, readDate = OffsetDateTime.now()), // current mileage
                mileageReadingDTO(mileage = 66200, readDate = OffsetDateTime.now().minusDays(3)),
            )

        val personDetails = EmployeeDtoObjectMother.createEmployeeDto("P123908").toVehiclePersonDetail()

        val capturedPayloads = mutableListOf<ExternalFleetMasterVehicle>()
        every { readVehicle.readVehicleById(any()) } returns vehicle
        every { repository.findAllByProcessedFalse() } returns listOf(event1, event2)
        every { readVehicleTransfer.findVehicleTransfersBy(any()) } returnsMany vehicleTransfers
        every { readRegistration.getLatestOrderBy(any()) } returns VehicleRegistrationAPIResponse(data = vehicleRegistration)
        every {
            readRegistration.getRegistrationPeriodByVehicleId(
                vehicleId,
                any(),
                any(),
            )
        } returns VehicleRegistrationAPIResponse(data = registrationPeriods(vehicleId))
        every { readVehicleSaleByVehicleId.readVehicleSalesBy(vehicleId) } returns vehicleSales
        every { readMileageReadings.readMileageReadings(vehicleId) } returns mileageList
        every { readVehiclePersonDetail.readVehiclePersonDetailByEmployeeNumber(any()) } returns personDetails

        every { producer.send(any(), any(), capture(capturedPayloads), any()) } just Runs

        // WHEN
        listOf(event1, event2).forEach { publisher.publishKafkaEvent(it) }

        // THEN
        verify(exactly = 1) {
            producer.send(
                eq("test-topic"),
                any(),
                match { it.vehicle.transfers.isEmpty() },
                match { it["eventType"] == VehicleEventType.VEHICLE_CREATED.name },
            )

            producer.send(
                eq("test-topic"),
                any(),
                match { it.vehicle.transfers.size == 3 },
                match { it["eventType"] == VehicleEventType.VEHICLE_TRANSFER_CREATED.name },
            )

            readRegistration.getRegistrationPeriodByVehicleId(vehicleId, OffsetDateTime.parse("2024-02-15T10:00:00Z"))

            readRegistration.getRegistrationPeriodByVehicleId(
                vehicleId,
                OffsetDateTime.parse("2024-02-15T10:00:00Z"),
                OffsetDateTime.parse("2024-02-16T10:00:00Z"),
            )
        }

        verify(exactly = 2) {
            readVehicleTransfer.findVehicleTransfersBy(vehicleId)
            readRegistration.getLatestOrderBy(vehicleId)
        }

        // verify first event
        val firstEventVehicle = capturedPayloads.first().vehicle
        assertEquals(0, firstEventVehicle.transfers.size)
        with(firstEventVehicle) {
            assertEquals("13e4a669-2ba8-4a7e-be98-27173133b6f0", this.vguid)
            assertEquals("WP0CB2A92FS143285", this.vin)
            assertEquals("EQUI-ID-$vehicleId-WP0CB2A92FS143285", this.equiId)
            assertEquals(123456789, this.equipmentNumber)
        }
        with(firstEventVehicle.production) {
            assertEquals("5027003", this?.number)
            assertEquals("1100", this?.factory)
            assertEquals(toOffsetDateTime(SimpleDateFormat("yyyy-MM-dd").parse("2021-10-06")), this?.zp8Date)
            assertEquals(toOffsetDateTime(SimpleDateFormat("yyyy-MM-dd").parse("2021-10-06")), this?.plannedEndDate)
            assertEquals(2022, this?.technicalModelYear)
            assertEquals('T', this?.gearBoxClass)
        }
        with(firstEventVehicle.model) {
            assertEquals("95BAU1", this?.orderType)
            assertEquals("Taycan", this?.modelRange)
            assertEquals("Porsche", this?.manufacturer)
            assertEquals("Taycan GTS", this?.description)
            assertEquals("PKW", this?.vehicleType)
        }
        with(firstEventVehicle.price) {
            assertEquals(BigDecimal("142000.00"), this?.vehicleFactoryGrossPriceEUR)
            assertEquals(BigDecimal("120000.00"), this?.vehicleFactoryNetPriceEUR)
            assertEquals(BigDecimal("11000.00"), this?.grossPriceWithExtras)
        }
        with(firstEventVehicle.consumption) {
            assertEquals("PETROL", this?.driveType)
            assertEquals("WLTP_EU", this?.typification)
            assertEquals(FuelType.SUPER, this?.primaryFuelType)
            assertEquals(FuelType.DIESEL, this?.secondaryFuelType)
        }
        with(firstEventVehicle.wltp) {
            assertEquals(102, this?.co2Combined)
            assertEquals(29, this?.electricRangeCity)
            assertEquals(30, this?.electricRange)
        }
        with(firstEventVehicle.order) {
            assertEquals(true, this?.blockedForSale)
            assertEquals("FD95", this?.primaryStatus)
            assertEquals("1003000", this?.tradingPartnerNumber)
        }
        with(firstEventVehicle.color) {
            assertEquals("D71V", this?.exterior)
            assertEquals(
                "sharkblue. Hinweis: aktuell eingeschränkte Verfügbarkeit. Bitte informieren Sie sich bei Ihrem Porsche Zentrum./Verdeck, schwarz",
                this?.exteriorDescription,
            )
            assertEquals("AU", this?.interior)
            assertEquals("Lederausstattung in Schwarz", this?.interiorDescription)
        }
        with(firstEventVehicle.fleet) {
            assertEquals(false, this?.scrapVehicle)
            assertEquals(false, this?.residualValueMarket)
            assertEquals(true, this?.raceCar)
            assertEquals(true, this?.classic)
        }
        with(firstEventVehicle.evaluation) {
            assertEquals(BigDecimal("1000"), this?.appraisalNetPrice)
        }
        with(firstEventVehicle.currentMileage) {
            assertEquals(68200, this?.mileage)
        }
        with(firstEventVehicle.sales) {
            assertEquals(vehicleSales.reservedForB2C, this?.reservedForB2C)
            assertEquals(vehicleSales.contractSigned, this?.contractSigned)
        }
        with(firstEventVehicle.registration) {
            assertEquals(
                vehicleRegistration.firstRegistrationDate?.get()?.toOffsetDateTime(),
                this?.firstRegistrationDate,
            )
            assertEquals(vehicleRegistration.licencePlate?.get(), this?.currentLicensePlate)
            assertEquals(vehicleRegistration.testNumber?.get(), this?.testNumber)
            assertEquals(vehicleRegistration.hsn?.get(), this?.hsn)
            assertEquals(vehicleRegistration.tsn?.get(), this?.tsn)
            assertEquals(vehicleRegistration.sfme?.get(), this?.sfme)
            assertNull(this?.currentLicensePlateSuffix)
        }
        with(firstEventVehicle.technical) {
            assertEquals(5, this?.amountSeats)
            assertEquals(23.0f, this?.acceleration0100KmhLaunchControl)
            assertEquals(22.0f, this?.acceleration0100Kmh)
            assertEquals(28.0f, this?.acceleration80120Kmh)
            assertEquals(1, this?.cargoVolume)
            assertEquals(18.0f, this?.chargingTimeAc11Kw0100)
            assertEquals(17.0f, this?.chargingTimeAc22Kw)
            assertEquals(19.0f, this?.chargingTimeAc96Kw0100)
            assertEquals(30.0f, this?.chargingTimeDcMaxPower580)
            assertEquals(2.0f, this?.engineCapacity)
            assertEquals(15, this?.curbWeightDin)
            assertEquals(13, this?.curbWeightEu)
            assertEquals(21.0f, this?.grossBatteryCapacity)
            assertEquals(12, this?.grossVehicleWeight)
            assertEquals(25, this?.height)
            assertEquals(27, this?.length)
            assertEquals(10, this?.maximumChargingPowerDc)
            assertEquals(16, this?.maximumPayload)
            assertEquals(29, this?.maxRoofLoadWithPorscheRoofTransportSystem)
            assertEquals(20.0f, this?.netBatteryCapacity)
            assertEquals(32, this?.powerKw)
            assertEquals(24, this?.topSpeed)
            assertEquals(14, this?.totalPowerKw)
            assertEquals(12, this?.grossVehicleWeight)
            assertEquals(20, this?.vehicleWidthMirrorsExtended)
            assertEquals(26, this?.widthMirrorsFolded)
        }

        // verify second event
        val secondEvent = capturedPayloads.last().vehicle
        with(secondEvent.registration) {
            assertEquals(
                vehicleRegistration.firstRegistrationDate?.get()?.toOffsetDateTime(),
                this?.firstRegistrationDate,
            )
            assertEquals(vehicleRegistration.licencePlate?.get(), this?.currentLicensePlate)
            assertEquals(vehicleRegistration.testNumber?.get(), this?.testNumber)
            assertEquals(vehicleRegistration.hsn?.get(), this?.hsn)
            assertEquals(vehicleRegistration.tsn?.get(), this?.tsn)
        }

        val transferWithStatus = secondEvent.transfers
        assertEquals(3, transferWithStatus.size) // ["ACTIVE", "PLANNED"]
        with(transferWithStatus) {
            val activeTransfer = this[0]
            assertEquals("ACTIVE", activeTransfer.status)
            assertEquals(1, activeTransfer.transfers.size)
            assertEquals(
                2,
                activeTransfer.transfers
                    .first()
                    .licensePlates.size,
            )

            assertEquals("00H0015553", activeTransfer.transfers.first().usingCostCenter)
            assertEquals(
                UUID.fromString("79118325-68d5-4261-aa8a-306a89eb3fe6"),
                activeTransfer.transfers.first().depreciationRelevantCostCenterId,
            )
            assertEquals("ordernr123", activeTransfer.transfers.first().internalOrderNumber)
            assertEquals(
                OffsetDateTime.parse("2024-02-14T10:00:00Z"),
                activeTransfer.transfers.first().plannedDeliveryDate,
            )

            // assert leasing art is set correctly
            var leasingArt = activeTransfer.transfers.first().leasingArt
            assertEquals(LeasingArt.L9_2.id, leasingArt?.id)
            assertEquals(LeasingArt.L9_2.description, leasingArt?.description)
            assertEquals(LeasingArt.L9_2.isPool, leasingArt?.pool)

            val plannedTransfer = this[1]
            assertEquals("PLANNED", plannedTransfer.status)
            assertEquals(1, plannedTransfer.transfers.size)
            // Verify license plates are shipped with only Active transfers
            assertEquals(
                0,
                plannedTransfer.transfers
                    .first()
                    .licensePlates.size,
            )
            assertNotNull(
                plannedTransfer.transfers
                    .first()
                    .vehicleResponsiblePerson
                    ?.department,
            )

            // assert leasing art is set correctly
            leasingArt =
                plannedTransfer.transfers
                    .first()
                    .leasingArt
            assertEquals(LeasingArt.L9_2.id, leasingArt?.id)
            assertEquals(LeasingArt.L9_2.description, leasingArt?.description)
            assertEquals(LeasingArt.L9_2.isPool, leasingArt?.pool)

            val finishedTransfer = this[2]
            assertEquals("FINISHED", finishedTransfer.status)
            assertEquals(1, finishedTransfer.transfers.size)
            // Verify license plates are shipped for finished transfers
            assertEquals(
                2,
                finishedTransfer.transfers
                    .first()
                    .licensePlates.size,
            )
        }

        listOf(event1, event2).forEach {
            assertTrue(it.processed)
            assertFalse(it.failed)
        }
    }

    @Test
    fun `should split license plate suffix while sending Kafka event for a vehicle`() {
        // GIVEN
        val vehicleId = UUID.randomUUID()
        val event =
            FleetVehicleMasterData(vehicleId = vehicleId, eventType = VehicleEventType.VEHICLE_TRANSFER_CREATED)

        val vehicle = vehicleDTOWithAllFieldsPopulated(id = vehicleId)
        val vehicleRegistration =
            vehicleRegistrationOrder(vehicleId).copy(
                licencePlate = Optional.of("PZ-AB 123H"),
            )
        val vehicleTransfers = listOf(vehicleTransferDto(vehicleId = vehicleId, status = "ACTIVE"))

        val registrationPeriods =
            registrationPeriods(vehicleId).map {
                it.copy(licencePlate = "PZ-AB 123E")
            }

        val vehicleSales = vehicleSalesDto(vehicleId)

        val personDetails = EmployeeDtoObjectMother.createEmployeeDto("P123908").toVehiclePersonDetail()

        val capturedPayloads = mutableListOf<ExternalFleetMasterVehicle>()
        every { readVehicle.readVehicleById(any()) } returns vehicle
        every { repository.findAllByProcessedFalse() } returns listOf(event)
        every { readVehicleTransfer.findVehicleTransfersBy(any()) } returns vehicleTransfers
        every { readRegistration.getLatestOrderBy(any()) } returns VehicleRegistrationAPIResponse(data = vehicleRegistration)
        every {
            readRegistration.getRegistrationPeriodByVehicleId(
                vehicleId,
                any(),
                any(),
            )
        } returns VehicleRegistrationAPIResponse(data = registrationPeriods)
        every { readVehicleSaleByVehicleId.readVehicleSalesBy(vehicleId) } returns vehicleSales
        every { readMileageReadings.readMileageReadings(vehicleId) } returns emptyList()
        every { readVehiclePersonDetail.readVehiclePersonDetailByEmployeeNumber(any()) } returns personDetails

        every { producer.send(any(), any(), capture(capturedPayloads), any()) } just Runs

        // WHEN
        publisher.publishKafkaEvent(event)

        // THEN
        verify(exactly = 1) {
            producer.send(
                eq("test-topic"),
                any(),
                match { it.vehicle.transfers.size == 1 },
                match { it["eventType"] == VehicleEventType.VEHICLE_TRANSFER_CREATED.name },
            )
        }

        // verify first event
        val vehicleData = capturedPayloads.first().vehicle
        with(vehicleData.registration) {
            assertEquals("PZ-AB 123", this?.currentLicensePlate)
            assertEquals("H", this?.currentLicensePlateSuffix)
        }

        val transferWithStatus = vehicleData.transfers
        assertEquals(1, transferWithStatus.size)
        with(transferWithStatus) {
            val activeTransfer = this[0]
            assertEquals("ACTIVE", activeTransfer.status)
            assertTrue(
                activeTransfer.transfers
                    .first()
                    .licensePlates
                    .all { it.licensePlateSuffix == "E" },
            )
            assertTrue(
                activeTransfer.transfers
                    .first()
                    .licensePlates
                    .all { it.licensePlate == "PZ-AB 123" },
            )
        }
    }

    @Test
    fun `should mark as failed when vehicle is not found in database`() {
        // GIVEN
        val vehicleId = UUID.randomUUID()
        val vehicle = FleetVehicleMasterData(vehicleId = vehicleId, eventType = VehicleEventType.VEHICLE_CREATED)
        every { repository.findAllByProcessedFalse() } returns listOf(vehicle)
        every { readVehicle.readVehicleById(vehicleId) } returns null

        // WHEN
        publisher.publishKafkaEvent(vehicle)

        // THEN
        assertTrue(vehicle.failed)
        assertTrue(vehicle.processed)
        assertNull(hashRepository.findByVehicleIdAndEventType(vehicleId, VehicleEventType.VEHICLE_CREATED))

        verify(exactly = 0) {
            producer.send(any(), any(), any(), any())
        }
    }

    @Test
    fun `should mark vehicle as failed when Kafka producer fails`() {
        // GIVEN
        val vehicleId = UUID.randomUUID()
        val vehicle = FleetVehicleMasterData(vehicleId = vehicleId, eventType = VehicleEventType.VEHICLE_CREATED)
        every { repository.findAllByProcessedFalse() } returns listOf(vehicle)
        every { readVehicle.readVehicleById(vehicleId) } returns vehicleDTOWithAllFieldsPopulated(id = vehicleId)
        every { readVehicleTransfer.findVehicleTransfersBy(any()) } returns emptyList()
        every { readRegistration.getLatestOrderBy(any()) } returns VehicleRegistrationAPIResponse(null)
        every { readVehicleSaleByVehicleId.readVehicleSalesBy(vehicleId) } returns vehicleSalesDto(vehicleId)
        every { readMileageReadings.readMileageReadings(vehicleId) } returns emptyList()
        every { producer.send(any(), any(), any(), any()) } throws RuntimeException("Kafka error")

        // WHEN
        publisher.publishKafkaEvent(vehicle)

        // THEN
        assertTrue(vehicle.failed)
        assertTrue(vehicle.processed)
        assertNull(hashRepository.findByVehicleIdAndEventType(vehicleId, VehicleEventType.VEHICLE_CREATED))

        verify(exactly = 1) {
            producer.send(any(), any(), any(), any())
        }
    }

    @Test
    fun `should not produce Kafka message when there is no change in the payload`() {
        // GIVEN
        val vehicleId = UUID.randomUUID()
        val duplicateEvent = FleetVehicleMasterData(vehicleId = vehicleId, eventType = VehicleEventType.VEHICLE_CREATED)
        val vehicleData = vehicleDTOWithAllFieldsPopulated(id = vehicleId)
        val salesData = vehicleSalesDto(vehicleId)
        val expectedPayload =
            ExternalFleetMasterVehicle(
                vehicle =
                    ExternalVehicle(
                        vguid = vehicleData.vguid,
                        vin = vehicleData.vin,
                        equiId = vehicleData.equiId,
                        equipmentNumber = vehicleData.equipmentNumber,
                        status = vehicleData.status,
                        production = ExternalVehicleProduction.from(vehicleData.production),
                        model = ExternalVehicleModel.from(vehicleData.model),
                        price = ExternalVehiclePrice.from(vehicleData.price),
                        registration = ExternalVehicleRegistration.from(null),
                        transfers = emptyList(),
                        wltp = ExternalVehicleWltpInfo.from(vehicleData.wltpInfo),
                        consumption = ExternalConsumptionInfo.from(vehicleData.consumption),
                        currentMileage = null,
                        order = ExternalOrderInfo.from(vehicleData.order),
                        fleet = ExternalFleetInfo.from(vehicleData.fleet),
                        evaluation = ExternalVehicleEvaluationData.from(vehicleData.evaluation),
                        sales = ExternalVehicleSalesData.from(salesData),
                        technical = ExternalVehicleTechnicalData.from(vehicleData.technical),
                        color = ExternalVehicleColorData.from(vehicleData.color),
                        options =
                            jacksonObjectMapper().convertValue(
                                vehicleData.options,
                                object : TypeReference<Map<String, Any>>() {},
                            ),
                    ),
            )

        val existingHash =
            hashRepository.save(
                FleetVehicleEventHash(
                    vehicleId = vehicleId,
                    eventType = VehicleEventType.VEHICLE_CREATED,
                    hash = deduplicator.computeHash(expectedPayload),
                ),
            )

        every { readVehicle.readVehicleById(any()) } returns vehicleData
        every { readVehicleTransfer.findVehicleTransfersBy(any()) } returns emptyList()
        every { readRegistration.getLatestOrderBy(any()) } returns VehicleRegistrationAPIResponse(data = null)
        every {
            readRegistration.getRegistrationPeriodByVehicleId(
                vehicleId,
                any(),
                any(),
            )
        } returns VehicleRegistrationAPIResponse(data = emptyList())
        every { readVehicleSaleByVehicleId.readVehicleSalesBy(vehicleId) } returns salesData
        every { readMileageReadings.readMileageReadings(vehicleId) } returns emptyList()
        every { readVehiclePersonDetail.readVehiclePersonDetailByEmployeeNumber(any()) } returns null

        // WHEN
        publisher.publishKafkaEvent(duplicateEvent)

        // THEN
        val latestHash = hashRepository.findByVehicleIdAndEventType(vehicleId, VehicleEventType.VEHICLE_CREATED)
        assertEquals(existingHash.id, latestHash?.id)

        verify(exactly = 0) {
            producer.send(
                eq("test-topic"),
                any(),
                match { it.vehicle.transfers.isEmpty() },
                match { it["eventType"] == VehicleEventType.VEHICLE_CREATED.name },
            )
        }
    }

    @Test
    fun `should produce Kafka message and replace the existing hash when payload is different`() {
        // GIVEN
        val vehicleId = UUID.randomUUID()
        val updatedEvent = FleetVehicleMasterData(vehicleId = vehicleId, eventType = VehicleEventType.VEHICLE_UPDATED)
        val vehicleData = vehicleDTOWithAllFieldsPopulated(id = vehicleId)
        val salesData = vehicleSalesDto(vehicleId)

        val existingHash =
            hashRepository
                .save(
                    FleetVehicleEventHash(
                        vehicleId = vehicleId,
                        eventType = VehicleEventType.VEHICLE_UPDATED,
                        hash = deduplicator.computeHash("dummy-hash-to-be-replaced"),
                    ),
                ).hash

        every { readVehicle.readVehicleById(any()) } returns vehicleData
        every { readVehicleTransfer.findVehicleTransfersBy(any()) } returns emptyList()
        every { readRegistration.getLatestOrderBy(any()) } returns VehicleRegistrationAPIResponse(data = null)
        every {
            readRegistration.getRegistrationPeriodByVehicleId(
                vehicleId,
                any(),
                any(),
            )
        } returns VehicleRegistrationAPIResponse(data = emptyList())
        every { readVehicleSaleByVehicleId.readVehicleSalesBy(vehicleId) } returns salesData
        every { readMileageReadings.readMileageReadings(vehicleId) } returns emptyList()
        every { readVehiclePersonDetail.readVehiclePersonDetailByEmployeeNumber(any()) } returns null

        // WHEN
        publisher.publishKafkaEvent(updatedEvent)

        // THEN
        val latestHash = hashRepository.findByVehicleIdAndEventType(vehicleId, VehicleEventType.VEHICLE_UPDATED)?.hash
        assertNotNull(latestHash)
        assertNotEquals(existingHash, latestHash)

        verify(exactly = 1) {
            producer.send(
                eq("test-topic"),
                any(),
                match { it.vehicle.transfers.isEmpty() },
                match { it["eventType"] == VehicleEventType.VEHICLE_UPDATED.name },
            )
        }
    }

    private fun vehicleTransferDto(
        vehicleId: UUID = UUID.randomUUID(),
        status: String = "ACTIVE",
    ): VehicleTransferAbstractionDto =
        VehicleTransferAbstractionDto(
            vehicleResponsiblePerson = EmployeeNumber("P123908"),
            deliveryIndex = 1,
            vehicleTransferKey = VehicleTransferKey(1L),
            deliveryDate = OffsetDateTime.parse("2024-02-15T10:00:00Z"),
            returnDate = null,
            leasingPrivilege = "Full",
            vehicleUsage = VehicleUsageDto(id = UUID.randomUUID(), usage = "Company Car", version = 1, usageId = 1L),
            usageGroup =
                UsageGroupDto(
                    id = UUID.randomUUID(),
                    description = "Corporate Fleet",
                    version = 1,
                    usageGroupId = 1L,
                ),
            mileageAtDelivery = 1000,
            mileageAtReturn = 5000,
            utilizationArea = UtilizationArea.ZUFFENHAUSEN,
            status = status,
            vehicleId = vehicleId,
            maintenanceOrderNumber = "00123456",
            usingCostCenter = CostCenterDescription("00H0015553"),
            depreciationRelevantCostCenterId = CostCenterId(UUID.fromString("79118325-68d5-4261-aa8a-306a89eb3fe6")),
            internalOrderNumber = "ordernr123",
            plannedDeliveryDate = OffsetDateTime.parse("2024-02-14T10:00:00Z"),
        )

    private fun registrationPeriods(vehicleId: UUID = UUID.randomUUID()): List<RegistrationPeriod> =
        listOf(
            RegistrationPeriod(
                licencePlate = "S KN 1234",
                fromDate = ZonedDateTime.parse("2024-01-01T00:00:00Z"),
                toDate = ZonedDateTime.parse("2025-04-01T00:00:00Z"),
                vehicleId = vehicleId,
                vin = "", // can be ignored
            ),
            RegistrationPeriod(
                licencePlate = "S KN 1234",
                fromDate = ZonedDateTime.parse("2025-05-01T00:00:00Z"),
                toDate = null,
                vehicleId = vehicleId,
                vin = "", // can be ignored
            ),
        )

    private fun vehicleRegistrationOrder(vehicleId: UUID?) =
        VehicleRegistrationOrder(
            id = 12345L,
            vin = Optional.of("1HGCM82633A123456"),
            vehicleId = Optional.of("$vehicleId"),
            registrationOffice = Optional.of("NY DMV"),
            plannedLicencePlate = Optional.of("ABC-1234"),
            licencePlate = Optional.of("S KN 5678"),
            firstRegistrationDate = Optional.of(ZonedDateTime.parse("2023-01-01T00:00:00Z")),
            testNumber = Optional.of(1L),
            hsn = Optional.of("583"),
            tsn = Optional.of("ANU00171"),
            orderStatus = Optional.of("CREATED"),
            registrationDate = Optional.of(ZonedDateTime.now()),
            sfme = Optional.of(true),
            version = 1L,
        )

    private fun mileageReadingDTO(
        mileage: Int,
        readDate: OffsetDateTime,
    ) = MileageReadingDTO(
        mileage = mileage,
        readDate = readDate,
        source = MileageReadingSource.CAR_SYNC,
        createdBy = "test user",
    )

    private fun vehicleSalesDto(vehicleId: UUID) =
        VehicleSalesDto(
            vehicleId = vehicleId,
            reservedForB2C = true,
            contractSigned = true,
        )

    private fun toOffsetDateTime(date: Date?): OffsetDateTime? =
        date
            ?.toInstant()
            ?.atOffset(ZoneOffset.UTC)
}
