package com.fleetmanagement.modules.masterdata

import app.getxray.xray.junit.customjunitxml.annotations.XrayTest
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fleetmanagement.TestcontainersConfiguration
import com.fleetmanagement.modules.consigneedatasheet.application.ConsigneeDataFinder
import com.fleetmanagement.modules.consigneedatasheet.application.UsageGroupFinder
import com.fleetmanagement.modules.consigneedatasheet.application.toUsageGroupDto
import com.fleetmanagement.modules.consigneedatasheet.domain.UsageGroupId
import com.fleetmanagement.modules.consigneedatasheet.objectmothers.UsageGroupBuilder
import com.fleetmanagement.modules.dmsvehiclemigration.adapter.out.kafka.DMSVehicleMigrationKafkaPublishAdapter
import com.fleetmanagement.modules.documentgeneration.api.GeneratePowerOfAttorney
import com.fleetmanagement.modules.masterdata.configuration.FleetVehicleMasterDataKafkaProducerConfig
import com.fleetmanagement.modules.masterdata.dto.ExternalFleetMasterVehicle
import com.fleetmanagement.modules.masterdata.dto.ExternalUsageGroup
import com.fleetmanagement.modules.masterdata.dto.ExternalVehicleRegistration
import com.fleetmanagement.modules.masterdata.dto.ExternalVehicleResponsiblePerson
import com.fleetmanagement.modules.masterdata.dto.ExternalVehicleTransfer
import com.fleetmanagement.modules.masterdata.dto.ExternalVehicleTransferWithStatus
import com.fleetmanagement.modules.masterdata.dto.ExternalVehicleUsage
import com.fleetmanagement.modules.masterdata.dto.LicensePlatePeriod
import com.fleetmanagement.modules.masterdata.entities.FleetVehicleMasterDataRepository
import com.fleetmanagement.modules.masterdata.objectmothers.ExternalFleetMasterVehicleObjectMother
import com.fleetmanagement.modules.masterdata.service.FleetVehicleOutboxProcessor
import com.fleetmanagement.modules.masterdata.service.StreamzillaKafkaProducer
import com.fleetmanagement.modules.vehicledata.api.AddCurrentMileageReading
import com.fleetmanagement.modules.vehicledata.api.domain.FinancialAssetType
import com.fleetmanagement.modules.vehicledata.api.domain.FuelType
import com.fleetmanagement.modules.vehicledata.api.domain.TireSet
import com.fleetmanagement.modules.vehicledata.api.dtos.CreateUIVehicleDTO
import com.fleetmanagement.modules.vehicledata.api.dtos.MileageReadingSource
import com.fleetmanagement.modules.vehicledata.api.dtos.VehicleDTO
import com.fleetmanagement.modules.vehicledata.features.createvehicle.VehicleCreationService
import com.fleetmanagement.modules.vehicledata.repository.JPAVehicleRepository
import com.fleetmanagement.modules.vehiclehistory.service.VehicleChangesService
import com.fleetmanagement.modules.vehicleperson.api.dtos.VehiclePersonDetail
import com.fleetmanagement.modules.vehicleperson.api.readvehicleperson.ReadVehiclePersonDetailByEmployeeNumber
import com.fleetmanagement.modules.vehicleperson.features.readvehicleperson.toVehiclePersonDetail
import com.fleetmanagement.modules.vehicleperson.integration.userservice.rest.objectmothers.EmployeeDtoObjectMother
import com.fleetmanagement.modules.vehicleregistration.api.dto.RegistrationPeriod
import com.fleetmanagement.modules.vehicleregistration.api.dto.VehicleRegistrationAPIResponse
import com.fleetmanagement.modules.vehicleregistration.api.dto.VehicleRegistrationOrder
import com.fleetmanagement.modules.vehicleregistration.client.VehicleRegistrationClient
import com.fleetmanagement.modules.vehiclesales.api.UpsertVehicleSale
import com.fleetmanagement.modules.vehiclesales.api.dto.VehicleSaleUpdateDto
import com.fleetmanagement.modules.vehicletransfer.PlannedVehicleTransferBuilder
import com.fleetmanagement.modules.vehicletransfer.VehicleTransferUpdateDtoBuilder
import com.fleetmanagement.modules.vehicletransfer.application.PlannedVehicleTransferLeasingPrivilegeResolverService
import com.fleetmanagement.modules.vehicletransfer.application.port.CreatePlannedVehicleTransferDto
import com.fleetmanagement.modules.vehicletransfer.application.port.CreatePlannedVehicleTransferUseCase
import com.fleetmanagement.modules.vehicletransfer.application.port.UpdateVehicleTransferUseCase
import com.fleetmanagement.modules.vehicletransfer.domain.PlannedVehicleTransferRepository
import com.fleetmanagement.modules.vehicletransfer.domain.VehicleTransferRepository
import com.fleetmanagement.modules.vehicletransfer.domain.entities.UtilizationArea
import com.fleetmanagement.modules.vehicletransfer.domain.entities.VehicleTransferKey
import com.fleetmanagement.modules.vehicletransfer.domain.entities.VehicleUsageId
import com.fleetmanagement.modules.vehicletransfer.domain.service.CurrentVehicleTransferUpdateService
import com.fleetmanagement.security.features.tokenvalidation.alb.entraid.FixedGroupIds
import com.fleetmanagement.security.utils.WithMockALBUser
import com.ninjasquad.springmockk.MockkBean
import com.ninjasquad.springmockk.SpykBean
import io.confluent.kafka.schemaregistry.testutil.MockSchemaRegistry
import io.mockk.every
import io.mockk.just
import io.mockk.runs
import io.mockk.slot
import org.apache.kafka.clients.consumer.ConsumerConfig
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.apache.kafka.clients.consumer.KafkaConsumer
import org.apache.kafka.clients.producer.ProducerRecord
import org.apache.kafka.common.header.Headers
import org.apache.kafka.common.serialization.StringDeserializer
import org.awaitility.Awaitility.await
import org.awaitility.Durations
import org.awaitility.kotlin.await
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.api.assertNull
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Import
import org.springframework.core.io.ClassPathResource
import org.springframework.kafka.annotation.EnableKafka
import org.springframework.kafka.core.KafkaTemplate
import org.springframework.kafka.test.EmbeddedKafkaBroker
import org.springframework.kafka.test.context.EmbeddedKafka
import org.springframework.kafka.test.utils.KafkaTestUtils
import org.springframework.test.context.TestPropertySource
import java.io.File
import java.lang.Thread.sleep
import java.math.BigDecimal
import java.nio.charset.StandardCharsets.UTF_8
import java.security.MessageDigest
import java.time.Duration
import java.time.OffsetDateTime
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.time.temporal.ChronoUnit
import java.util.Optional
import java.util.UUID
import java.util.concurrent.Callable
import java.util.concurrent.TimeUnit
import java.util.function.Predicate

const val TEST_TOPIC = "test-topic"

@SpringBootTest
@EmbeddedKafka(partitions = 1, topics = [TEST_TOPIC])
@Import(
    TestcontainersConfiguration::class,
    ProducerTestEmbeddedKafkaConfig::class,
    FleetVehicleMasterDataKafkaProducerConfig::class,
)
@TestPropertySource(
    properties = [
        "fleet-master-data-publisher.enabled=true",
        "fleet-master-data-publisher.topic=$TEST_TOPIC",
        "spring.kafka.bootstrap-servers=\${spring.embedded.kafka.brokers}",
        "spring.kafka.properties.security.protocol=PLAINTEXT",
        "fleet-master-data-publisher.producer.schema-registry-url=mock://schema-registry",
        "fleet-master-data-publisher.producer.schema-registry-user=mock-registry-user",
        "fleet-master-data-publisher.producer.schema-registry-password=mock-registry-password",
    ],
)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class FleetVehicleMasterDataProducerIntegrationTest {
    private val schemaRegistryClient = MockSchemaRegistry.getClientForScope("schema-registry")

    @Autowired
    private lateinit var createVehicleService: VehicleCreationService

    @Autowired
    private lateinit var vehicleTransferRepo: VehicleTransferRepository

    @Autowired
    private lateinit var createPlannedVehicleTransferUseCase: CreatePlannedVehicleTransferUseCase

    @Autowired
    private lateinit var plannedVehicleTransferRepository: PlannedVehicleTransferRepository

    @Autowired
    private lateinit var updateVehicleTransferUseCase: UpdateVehicleTransferUseCase

    @Autowired
    private lateinit var jpaVehicleRepository: JPAVehicleRepository

    @Autowired
    private lateinit var fleetVehicleMasterDataRepository: FleetVehicleMasterDataRepository

    @Autowired
    private lateinit var objectMapper: ObjectMapper

    @Autowired
    private lateinit var consumer: KafkaConsumer<String, String>

    @MockkBean(relaxed = true)
    private lateinit var vehicleRegistrationClient: VehicleRegistrationClient

    @MockkBean
    private lateinit var usageGroupFinder: UsageGroupFinder

    @MockkBean
    private lateinit var calculateUsageGroupUseCase: ConsigneeDataFinder

    @MockkBean
    private lateinit var readVehiclePersonDetailByEmployeeNumber: ReadVehiclePersonDetailByEmployeeNumber

    @MockkBean(relaxed = true)
    private lateinit var currentVehicleTransferUpdateService: CurrentVehicleTransferUpdateService

    // disables history for tests here, as changeservice keeps crashing with concurrent modification
    @MockkBean(relaxed = true)
    private lateinit var vehicleChangesService: VehicleChangesService

    @MockkBean(relaxed = true)
    private lateinit var dmsVehicleMigrationKafkaPublishAdapter: DMSVehicleMigrationKafkaPublishAdapter

    @MockkBean(relaxed = true)
    private lateinit var generatePowerOfAttorney: GeneratePowerOfAttorney

    @Autowired
    private lateinit var upsertVehicleSale: UpsertVehicleSale

    @Autowired
    private lateinit var addCurrentMileageReading: AddCurrentMileageReading

    @MockkBean(relaxed = true)
    private lateinit var readVehiclePersonDetail: ReadVehiclePersonDetailByEmployeeNumber

    @Autowired
    private lateinit var fleetVehicleOutboxProcessor: FleetVehicleOutboxProcessor

    @SpykBean
    @Qualifier("fleetMasterDataKafkaTemplate")
    private lateinit var kafkaTemplate: KafkaTemplate<String, ExternalFleetMasterVehicle>

    @MockkBean(relaxed = true)
    private lateinit var plannedVehicleTransferLeasingPrivilegeResolverService: PlannedVehicleTransferLeasingPrivilegeResolverService

    val usageGroup = UsageGroupBuilder().description("PERSON").build()

    @BeforeEach
    fun setup() {
        consumer.subscribe(listOf(TEST_TOPIC))
        jpaVehicleRepository.findAll().forEach {
            vehicleTransferRepo.findByVehicleId(it.id!!)
        }
        jpaVehicleRepository.deleteAll()
        consumer.poll(Duration.ofMillis(100))

        every { usageGroupFinder.findUsageGroupById(any()) } returns usageGroup
        every { usageGroupFinder.readAllUsageGroups() } returns listOf(usageGroup.toUsageGroupDto())
        every {
            calculateUsageGroupUseCase.calculateUsageGroup(any())
        } returns UsageGroupId(usageGroup.id.value)
        every { calculateUsageGroupUseCase.readLeasingPrivilegeForVehicleUsage(any()) }
        every { readVehiclePersonDetailByEmployeeNumber.readVehiclePersonDetailByEmployeeNumber(any()) } returns
            VehiclePersonDetail(
                employeeNumber = "********",
                firstName = "",
                lastName = "",
                companyEmail = "<EMAIL>",
                accountingArea = "",
                businessPartnerId = null,
            )
        every {
            plannedVehicleTransferLeasingPrivilegeResolverService.handlePlannedVehicleTransferLeasingPrivilegeUpdateEvent(
                any(),
            )
        } just
            runs
    }

    @Test
    @WithMockALBUser(groups = [FixedGroupIds.reader])
    fun `should publish and consume with schema registry`() {
        // GIVEN
        val createdVehicle = createVehicle()
        createRegistration(vehicleId = createdVehicle.id)
        every { vehicleRegistrationClient.getRegistrationPeriodByVehicleId(any(), any()) } returns
            VehicleRegistrationAPIResponse(data = registrationPeriods())
        createPlannedVehicleTransfer(createdVehicle.id)
        addMileageToVehicle(createdVehicle.id)
        createVehicleSales(createdVehicle.id)
        val slot = slot<ProducerRecord<String, ExternalFleetMasterVehicle>>()
        every { kafkaTemplate.send(capture(slot)) } answers {
            callOriginal() // calls the real send method
        }
        await().atMost(5, TimeUnit.SECONDS).until {
            fleetVehicleMasterDataRepository.findAllByProcessedFalse().isNotEmpty()
        }

        // WHEN
        fleetVehicleOutboxProcessor.process()
        kafkaTemplate.flush()

        val latestSchemaMetadata = schemaRegistryClient.getSchemaMetadata("$TEST_TOPIC-value", 1)
        val actualSchemaJson = jacksonObjectMapper().readTree(latestSchemaMetadata.schema)
        val consumedRecord = consumeEventsFromKafkaRaw().first()
        val message = removeMagicByte(consumedRecord)

        // payload should match what was sent. The individual fields are asserted in unit tests:
        val actualPayload = objectMapper.readValue(message, ExternalFleetMasterVehicle::class.java)
        assertEquals(objectMapper.writeValueAsString(slot.captured.value()), message)
        // correct headers should be set:
        val headers = consumedRecord.headers()
        assertTrue(mandatoryHeadersExists(headers))
        assertEquals("vin", getHeaderValue(headers, "vin"))
        assertEquals("VEHICLE_CREATED", getHeaderValue(headers, "eventType"))
        val messageKey = checkNotNull(createdVehicle.vguid ?: createdVehicle.vin)
        assertEquals(getExpectedKey(messageKey), consumedRecord.key())
        // correct schema should be published:
        assertEquals(expectedSchemaJson(), actualSchemaJson, "Schema mismatched!")
    }

    @Nested
    inner class VehicleTransferTests {
        private val expectedEmployeeInformation =
            ExternalVehicleResponsiblePerson(
                employeeNumber = "********",
                department = "department",
            )

        @Test
        @XrayTest(key = "FP20-2856")
        fun `should publish and consume vehicle with PLANNED transfers and NO registrations`() {
            // GIVEN
            every { vehicleRegistrationClient.latestOrderBy(any()) } returns VehicleRegistrationAPIResponse(null)
            every {
                plannedVehicleTransferLeasingPrivilegeResolverService.handlePlannedVehicleTransferManualCreatedEvent(
                    any(),
                )
            } just runs
            val vehicle = createVehicle()
            val vehicleTransferToCreate =
                CreatePlannedVehicleTransferDto(
                    vehicleId = vehicle.id,
                    vehicleUsageId = UUID.randomUUID(),
                    vehicleResponsiblePerson = "********",
                    internalContactPerson = "********",
                    internalOrderNumber = null,
                    licensePlate = null,
                    desiredDeliveryDate = OffsetDateTime.parse("2024-10-01T00:00:00Z"),
                    plannedDeliveryDate = OffsetDateTime.parse("2024-10-01T00:00:00Z"),
                    deliveryLeipzig = true,
                )

            val key = createPlannedVehicleTransferUseCase.manualCreatePlannedVehicleTransfer(vehicleTransferToCreate)
            await().atMost(20, TimeUnit.SECONDS).until {
                fleetVehicleMasterDataRepository.findAllByProcessedFalse().size == 2
            }

            // WHEN
            fleetVehicleOutboxProcessor.process()
            kafkaTemplate.flush()

            // THEN
            val records = consumeEventsFromKafka()
            val actual = records[0].vehicle
            val expectedPlannedTransfer =
                ExternalVehicleTransfer(
                    vehicleResponsiblePerson = expectedEmployeeInformation.copy(department = null),
                    deliveryIndex = null,
                    vehicleTransferKey = key.value,
                    deliveryDate = OffsetDateTime.parse("2024-10-01T00:00:00Z"),
                    returnDate = null,
                    leasingPrivilege = null,
                    vehicleUsage = ExternalVehicleUsage(usage = null),
                    usageGroup = ExternalUsageGroup(description = "PERSON"),
                    licensePlates = emptyList(),
                    mileageAtDelivery = null,
                    mileageAtReturn = null,
                    utilizationArea = UtilizationArea.LEIPZIG.name,
                    maintenanceOrderNumber = null,
                    usingCostCenter = null,
                    depreciationRelevantCostCenterId = null,
                    internalOrderNumber = null,
                    plannedDeliveryDate = OffsetDateTime.parse("2024-10-01T00:00:00Z"),
                    leasingArt = null,
                )
            assertNull(actual.registration)
            assertEquals(1, actual.transfers.size)
            assertEquals("PLANNED", actual.transfers[0].status)
            assertEquals(1, actual.transfers[0].transfers.size)
            assertEquals(expectedPlannedTransfer, actual.transfers[0].transfers[0])
        }

        @Test
        @XrayTest(key = "FP20-2858")
        @WithMockALBUser(groups = [FixedGroupIds.reader])
        fun `should publish and consume vehicle with ACTIVE transfers and has de-registration on it`() {
            // GIVEN
            val createdVehicle = createVehicle()
            createRegistration(vehicleId = createdVehicle.id, registrationStatus = "DEREGISTERED")

            val createdPlannedVehicleTransfer = createPlannedVehicleTransfer(createdVehicle.id)
            sleep(Duration.ofMillis(100)) // give it a second to get the previous change properly flushed
            convertPlannedToActiveTransfer(createdPlannedVehicleTransfer)

            createVehicleSales(createdVehicle.id)
            addMileageToVehicle(createdVehicle.id)
            createPersonDetails("********")

            every { vehicleRegistrationClient.getRegistrationPeriodByVehicleId(any(), any()) } returns
                VehicleRegistrationAPIResponse(data = registrationPeriods())

            await().atMost(5, TimeUnit.SECONDS).until {
                fleetVehicleMasterDataRepository.findAllByProcessedFalse().isNotEmpty()
            }

            // WHEN
            fleetVehicleOutboxProcessor.process()
            kafkaTemplate.flush()

            // THEN
            val transfer = vehicleTransferRepo.findByKey(createdPlannedVehicleTransfer)
            val actual = consumeEventsFromKafka().first().vehicle
            val expectedTransfer =
                ExternalVehicleTransfer(
                    vehicleResponsiblePerson = expectedEmployeeInformation,
                    deliveryIndex = null,
                    vehicleTransferKey = createdPlannedVehicleTransfer.value,
                    deliveryDate = OffsetDateTime.now(ZoneOffset.UTC).truncatedTo(ChronoUnit.DAYS),
                    returnDate = null,
                    leasingPrivilege = "Full",
                    vehicleUsage = ExternalVehicleUsage(usage = null),
                    usageGroup = ExternalUsageGroup(description = "PERSON"),
                    licensePlates =
                        listOf(
                            LicensePlatePeriod(
                                licensePlate = "S KN 1234",
                                fromDate = OffsetDateTime.parse("2024-01-01T00:00:00Z"),
                                toDate = OffsetDateTime.parse("2025-04-01T00:00:00Z"),
                            ),
                            LicensePlatePeriod(
                                licensePlate = "S KN 5678",
                                fromDate = OffsetDateTime.parse("2025-05-01T00:00:00Z"),
                                toDate = null,
                            ),
                        ),
                    mileageAtDelivery = 1000,
                    mileageAtReturn = null,
                    utilizationArea = UtilizationArea.LEIPZIG.name,
                    maintenanceOrderNumber = null,
                    usingCostCenter = transfer?.usingCostCenter?.value,
                    depreciationRelevantCostCenterId = null,
                    internalOrderNumber = "123456",
                    plannedDeliveryDate = transfer?.plannedDeliveryDate,
                    leasingArt = null,
                )
            assertEquals(1, actual.transfers.size)
            assertEquals("ACTIVE", actual.transfers[0].status)
            assertEquals(1, actual.transfers[0].transfers.size)
            assertEquals(expectedTransfer, actual.transfers[0].transfers[0])
        }

        @Test
        @XrayTest(key = "FP20-2857")
        fun `should publish and consume vehicle with registration and NO transfers`() {
            // GIVEN
            val createdVehicle = createVehicle()
            createRegistration(vehicleId = createdVehicle.id)
            await().atMost(5, TimeUnit.SECONDS).until {
                fleetVehicleMasterDataRepository.findAllByProcessedFalse().isNotEmpty()
            }

            // WHEN
            fleetVehicleOutboxProcessor.process()
            kafkaTemplate.flush()

            // THEN
            val records = consumeEventsFromKafka()[0].vehicle
            assertEquals(0, records.transfers.size)
            assertEquals(expectedRegistration, records.registration)
        }

        @Test
        @XrayTest(key = "FP20-3224")
        fun `should publish and consume NULL in currentLicensePlate when the vehicle is deregistered`() {
            // GIVEN
            val createdVehicle = createVehicle()
            createRegistration(vehicleId = createdVehicle.id, registrationStatus = "DEREGISTERED")
            await().atMost(5, TimeUnit.SECONDS).until {
                fleetVehicleMasterDataRepository.findAllByProcessedFalse().isNotEmpty()
            }

            // WHEN
            fleetVehicleOutboxProcessor.process()
            kafkaTemplate.flush()

            // THEN
            val expectedRegistrationWithoutLicensePlate = expectedRegistration.copy(currentLicensePlate = null)
            val actualRegistration = consumeEventsFromKafka()[0].vehicle.registration
            assertEquals(expectedRegistrationWithoutLicensePlate, actualRegistration)
        }

        @Test
        @XrayTest(key = "FP20-3225")
        @WithMockALBUser(groups = [FixedGroupIds.reader]) // FIXME: security is leaked into vehicle transfer module
        fun `should publish and consume vehicle with multiple transfers and registration`() {
            // GIVEN
            val createdVehicle = createVehicle()
            createRegistration(vehicleId = createdVehicle.id)
            every { vehicleRegistrationClient.getRegistrationPeriodByVehicleId(any(), any()) } returns
                VehicleRegistrationAPIResponse(data = registrationPeriods())

            val plannedVehicleTransferKey = createPlannedVehicleTransfer(createdVehicle.id)
            val activeVehicleTransferKey = createPlannedVehicleTransfer(createdVehicle.id)
            convertPlannedToActiveTransfer(activeVehicleTransferKey)

            await().atMost(5, TimeUnit.SECONDS).until {
                fleetVehicleMasterDataRepository.findAllByProcessedFalse().isNotEmpty()
            }

            addMileageToVehicle(createdVehicle.id)
            createVehicleSales(createdVehicle.id)
            createPersonDetails("********")

            // WHEN
            fleetVehicleOutboxProcessor.process()
            kafkaTemplate.flush()
            // THEN
            val activeTransfer = vehicleTransferRepo.findByKey(activeVehicleTransferKey)
            val plannedTransfer = plannedVehicleTransferRepository.findByKey(plannedVehicleTransferKey)

            val expectedTransfers =
                listOf(
                    ExternalVehicleTransferWithStatus(
                        status = "ACTIVE",
                        transfers =
                            listOf(
                                ExternalVehicleTransfer(
                                    vehicleResponsiblePerson = expectedEmployeeInformation,
                                    deliveryIndex = null,
                                    vehicleTransferKey = activeVehicleTransferKey.value,
                                    deliveryDate = OffsetDateTime.now(ZoneOffset.UTC).truncatedTo(ChronoUnit.DAYS),
                                    returnDate = null,
                                    leasingPrivilege = "Full",
                                    vehicleUsage = ExternalVehicleUsage(usage = null),
                                    usageGroup = ExternalUsageGroup(description = "PERSON"),
                                    licensePlates =
                                        listOf(
                                            LicensePlatePeriod(
                                                licensePlate = "S KN 1234",
                                                fromDate = OffsetDateTime.parse("2024-01-01T00:00:00Z"),
                                                toDate = OffsetDateTime.parse("2025-04-01T00:00:00Z"),
                                            ),
                                            LicensePlatePeriod(
                                                licensePlate = "S KN 5678",
                                                fromDate = OffsetDateTime.parse("2025-05-01T00:00:00Z"),
                                                toDate = null,
                                            ),
                                        ),
                                    mileageAtDelivery = 1000,
                                    mileageAtReturn = null,
                                    utilizationArea = UtilizationArea.LEIPZIG.name,
                                    maintenanceOrderNumber = null,
                                    usingCostCenter = activeTransfer?.usingCostCenter?.value,
                                    depreciationRelevantCostCenterId = null,
                                    internalOrderNumber = activeTransfer?.internalOrderNumber,
                                    plannedDeliveryDate = activeTransfer?.plannedDeliveryDate,
                                    leasingArt = null,
                                ),
                            ),
                    ),
                    ExternalVehicleTransferWithStatus(
                        status = "PLANNED",
                        transfers =
                            listOf(
                                ExternalVehicleTransfer(
                                    vehicleResponsiblePerson = expectedEmployeeInformation,
                                    deliveryIndex = null,
                                    vehicleTransferKey = plannedVehicleTransferKey.value,
                                    deliveryDate = OffsetDateTime.parse("2024-10-01T00:00:00Z"),
                                    returnDate = null,
                                    leasingPrivilege = plannedTransfer?.leasingPrivilege,
                                    vehicleUsage = ExternalVehicleUsage(usage = null),
                                    usageGroup = ExternalUsageGroup(description = null),
                                    licensePlates = emptyList(),
                                    mileageAtDelivery = null,
                                    mileageAtReturn = null,
                                    utilizationArea = plannedTransfer?.utilizationArea?.name,
                                    maintenanceOrderNumber = null,
                                    usingCostCenter = plannedTransfer?.usingCostCenter?.value,
                                    depreciationRelevantCostCenterId = null,
                                    internalOrderNumber = plannedTransfer?.internalOrderNumber,
                                    plannedDeliveryDate = OffsetDateTime.parse("2024-10-01T00:00:00Z"),
                                    leasingArt = null,
                                ),
                            ),
                    ),
                )
            val actualVehicle = consumeEventsFromKafka()[0].vehicle
            assertEquals(expectedRegistration, actualVehicle.registration)
            assertEquals(expectedTransfers, actualVehicle.transfers)
        }
    }

    private fun consumeEventsFromKafka(): List<ExternalFleetMasterVehicle> =
        consumeEventsFromKafkaRaw().map {
            val message = removeMagicByte(it)
            objectMapper.readValue(message, ExternalFleetMasterVehicle::class.java)
        }

    private fun consumeEventsFromKafkaRaw(): List<ConsumerRecord<String, String>> {
        val supplier: Callable<List<ConsumerRecord<String, String>>> =
            Callable {
                consumer.poll(Duration.ofSeconds(1)).map { it }
            }
        val predicate: Predicate<List<ConsumerRecord<String, String>>> =
            Predicate { records -> records.isNotEmpty() }

        return await
            .atMost(Durations.TEN_SECONDS)
            .pollDelay(Durations.TWO_HUNDRED_MILLISECONDS)
            .until(supplier, predicate)
    }

    private fun getHeaderValue(
        headers: Headers,
        headerName: String,
    ): String? {
        val header = headers.lastHeader(headerName)
        return header?.value()?.let { String(it, UTF_8) }
    }

    private fun mandatoryHeadersExists(headers: Headers) =
        listOf("vguid", "vin", "equipmentNumber", "eventType").all { headerName ->
            headers.lastHeader(headerName) != null
        }

    private val expectedRegistration =
        ExternalVehicleRegistration(
            firstRegistrationDate = OffsetDateTime.parse("2023-06-01T00:00:00Z"),
            currentLicensePlate = "S KN 5678",
            testNumber = 1L,
            hsn = "583",
            tsn = "ANU00171",
            sfme = false,
        )

    private fun createRegistration(
        vehicleId: UUID,
        vin: String = "vin",
        registrationStatus: String = "REGISTERED",
        licencePlate: String = "S KN 5678",
    ) {
        every { vehicleRegistrationClient.latestOrderBy(any()) } returns
            VehicleRegistrationAPIResponse(
                data =
                    VehicleRegistrationOrder(
                        id = 12345L,
                        vin = Optional.of(vin),
                        vehicleId = Optional.of("$vehicleId"),
                        registrationOffice = Optional.of("NY DMV"),
                        plannedLicencePlate = Optional.of("ABC-1234"),
                        licencePlate = if (registrationStatus == "REGISTERED") Optional.of(licencePlate) else null,
                        firstRegistrationDate = Optional.of(ZonedDateTime.parse("2023-06-01T00:00:00Z")),
                        testNumber = Optional.of(1L),
                        hsn = Optional.of("583"),
                        tsn = Optional.of("ANU00171"),
                        orderStatus = Optional.of("CREATED"),
                        registrationDate = Optional.of(ZonedDateTime.now()),
                        version = 1L,
                        registrationStatus = Optional.of(registrationStatus),
                        sfme = Optional.of(false),
                    ),
            )
    }

    private fun removeMagicByte(consumedRecord: ConsumerRecord<String, String>): String {
        val valueBytes = consumedRecord.value().toByteArray()
        return String(valueBytes.copyOfRange(5, valueBytes.size))
    }

    private fun expectedSchemaJson(): JsonNode {
        val resource = ClassPathResource("masterdata/expected_schema_definition.json")
        val expectedJsonString = resource.inputStream.bufferedReader(UTF_8).use { it.readText() }
        return jacksonObjectMapper().readTree(expectedJsonString)
    }

    private fun createVehicle(vin: String = "vin"): VehicleDTO {
        val vehicleToCreate =
            CreateUIVehicleDTO(
                vin = vin,
                manufacturer = "Porsche",
                modelDescription = "Macan",
                amountSeats = 5,
                engineCapacity = 2.0f,
                licencePlate = "SW-1234",
                technicalModelYear = 2022,
                primaryFuelType = "${FuelType.DIESEL}",
                currentTires = "${TireSet.WR}",
                orderType = "95BAU1",
                secondaryFuelType = "${FuelType.ELECTRIC}",
                netPriceWithExtras = BigDecimal("8800"),
                vehicleType = "PKW",
                pmpDataOdometer = 123,
                enginePower = 150,
                vehicleResponsiblePerson = "0012345",
                internalContactPerson = "0012345",
                externalLeaseStart = ZonedDateTime.of(2024, 10, 1, 0, 0, 0, 0, ZoneOffset.UTC),
                externalLeaseEnd = ZonedDateTime.of(2024, 10, 1, 0, 0, 0, 0, ZoneOffset.UTC),
                externalLeaseLessee = "John Doe",
                externalLeaseRate = 300.0f,
                vehicleUsageId = UUID.fromString("a2de1023-3b08-4e38-b287-594904adf9f1"),
                internalOrderNumber = "ts4WaENv64dazuC9i9Te",
                financialAssetType = "${FinancialAssetType.AV}",
                registrationDate = null,
                usingCostCenter = null,
            )
        return createVehicleService.createOrUpdateVehicle(vehicleToCreate, "testUser")
    }

    private fun createPlannedVehicleTransfer(vehicleId: UUID): VehicleTransferKey {
        val vehicleUsageId = UUID.randomUUID()
        val pvt =
            PlannedVehicleTransferBuilder()
                .vehicleId(vehicleId)
                .vehicleUsageId(VehicleUsageId(vehicleUsageId))
                .vehicleResponsiblePerson("********")
                .internalContactPerson("********")
                .licensePlate(null)
                .desiredDeliveryDate(OffsetDateTime.parse("2024-10-01T00:00:00Z"))
                .plannedDeliveryDate(OffsetDateTime.parse("2024-10-01T00:00:00Z"))
                .build()

        plannedVehicleTransferRepository.saveAndFlush(pvt)
//        val key = createPlannedVehicleTransferUseCase.manualCreatePlannedVehicleTransfer(vehicleTransferToCreate)
        return pvt.key
    }

    private fun convertPlannedToActiveTransfer(plannedVehicleTransferKey: VehicleTransferKey) {
        val plannedVehicleTransfer =
            plannedVehicleTransferRepository.findByKey(
                VehicleTransferKey(plannedVehicleTransferKey.value),
            )
        val deliveryDate = OffsetDateTime.now(ZoneOffset.UTC).truncatedTo(ChronoUnit.DAYS)
        val vehicleResponsible = "********"
        val deliveryPayload =
            VehicleTransferUpdateDtoBuilder()
                .internalOrderNumber(Optional.of("123456"))
                .deliveryDate(Optional.of(deliveryDate))
                .mileageAtDelivery(Optional.of(1000))
                .vehicleResponsiblePerson(Optional.of(vehicleResponsible))
                .leasingPrivilege(Optional.of("Full"))
                .deliveryLeipzig(Optional.of(true))
                .build()

        updateVehicleTransferUseCase.updateVehicleTransfer(
            key =
                com.fleetmanagement.modules.vehicletransfer.application.port.VehicleTransferKey(
                    plannedVehicleTransferKey.value,
                ),
            version = plannedVehicleTransfer!!.version,
            vehicleTransferUpdateDto = deliveryPayload,
        )
    }

    private fun registrationPeriods(vehicleId: UUID = UUID.randomUUID()) =
        listOf(
            RegistrationPeriod(
                vehicleId = vehicleId,
                vin = "1HGCM82633A123456",
                licencePlate = "S KN 1234",
                fromDate = ZonedDateTime.parse("2024-01-01T00:00:00Z"),
                toDate = ZonedDateTime.parse("2025-04-01T00:00:00Z"),
            ),
            RegistrationPeriod(
                vehicleId = vehicleId,
                vin = "1HGCM82633A123456",
                licencePlate = "S KN 5678",
                fromDate = ZonedDateTime.parse("2025-05-01T00:00:00Z"),
                toDate = null,
            ),
        )

    private fun getExpectedKey(input: String): String {
        val bytes = MessageDigest.getInstance("SHA-256").digest(input.toByteArray())
        return bytes.joinToString("") { "%02x".format(it) }
    }

    private fun createVehicleSales(vehicleId: UUID) {
        val vehicleSales =
            VehicleSaleUpdateDto(
                vehicleId = vehicleId,
                reservedForB2C = Optional.of(true),
                comment = Optional.of("reserved for test user"),
                contractSigned = Optional.of(true),
                plannedDeliveryDate = Optional.of(OffsetDateTime.parse("2024-10-01T00:00:00Z")),
            )
        upsertVehicleSale.upsert(vehicleSales, "test-user")
    }

    private fun addMileageToVehicle(vehicleId: UUID) {
        addCurrentMileageReading.addCurrentMileageReading(
            vehicleId = vehicleId,
            mileage = 65000,
            readDate = ExternalFleetMasterVehicleObjectMother.MileageUtils.readDate,
            source = MileageReadingSource.MANUAL,
        )
    }

    private fun createPersonDetails(employeeNumber: String) {
        val personDetails = EmployeeDtoObjectMother.createEmployeeDto(employeeNumber).toVehiclePersonDetail()
        every { readVehiclePersonDetail.readVehiclePersonDetailByEmployeeNumber(any()) } returns personDetails
    }

    @Nested
    inner class SampleKafkaMessageGeneratorTest {
        /**
         * This test generates a sample Kafka message for the README file, that has to be copied manually in case of changes
         * to src/test/resources/masterdata/sample_kafka_message.json
         */
        @Test
        fun validateSampleKafkaMessageForReadme() {
            val sampleExternalFleetMasterVehicle = ExternalFleetMasterVehicleObjectMother.withAllFieldsPopulated()

            val readmeFilePath = "src/test/resources/masterdata/sample_kafka_message.json"
            val producer = StreamzillaKafkaProducer(kafkaTemplate)

            // produce and consume a sample message
            producer.send(TEST_TOPIC, "sample-key", sampleExternalFleetMasterVehicle)
            val consumedRecord = consumeEventsFromKafkaRaw().first()
            val message = removeMagicByte(consumedRecord)
            val generatedJson = jacksonObjectMapper().readTree(message).toPrettyString()

            val checkedInFile = File(readmeFilePath)
            val existingJson = checkedInFile.readText()

            assertEquals(
                existingJson.trimIndent(),
                generatedJson.trimIndent(),
                "The Kafka message format has changed! " +
                    "Please review the changes and update the sample file manually: $readmeFilePath " +
                    "You can copy the following content to update the file:" +
                    "$generatedJson",
            )
        }
    }
}

@TestConfiguration
@EnableKafka
class ProducerTestEmbeddedKafkaConfig {
    @Bean
    fun consumer(embeddedKafkaBroker: EmbeddedKafkaBroker): KafkaConsumer<String, String> {
        val props =
            KafkaTestUtils.consumerProps("test-group", "true", embeddedKafkaBroker).apply {
                put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "earliest")
                put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer::class.java)
                put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer::class.java)
            }
        return KafkaConsumer(props)
    }
}
