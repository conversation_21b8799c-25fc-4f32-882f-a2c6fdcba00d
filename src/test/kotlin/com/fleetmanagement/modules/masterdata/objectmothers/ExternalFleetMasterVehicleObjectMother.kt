/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.masterdata.objectmothers

import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fleetmanagement.emhshared.LeasingArt
import com.fleetmanagement.modules.masterdata.dto.ExternalConsumptionInfo
import com.fleetmanagement.modules.masterdata.dto.ExternalCurrentMileage
import com.fleetmanagement.modules.masterdata.dto.ExternalFleetInfo
import com.fleetmanagement.modules.masterdata.dto.ExternalFleetMasterVehicle
import com.fleetmanagement.modules.masterdata.dto.ExternalLeasingArt
import com.fleetmanagement.modules.masterdata.dto.ExternalOrderInfo
import com.fleetmanagement.modules.masterdata.dto.ExternalUsageGroup
import com.fleetmanagement.modules.masterdata.dto.ExternalVehicle
import com.fleetmanagement.modules.masterdata.dto.ExternalVehicleColorData
import com.fleetmanagement.modules.masterdata.dto.ExternalVehicleEvaluationData
import com.fleetmanagement.modules.masterdata.dto.ExternalVehicleModel
import com.fleetmanagement.modules.masterdata.dto.ExternalVehiclePrice
import com.fleetmanagement.modules.masterdata.dto.ExternalVehicleProduction
import com.fleetmanagement.modules.masterdata.dto.ExternalVehicleRegistration
import com.fleetmanagement.modules.masterdata.dto.ExternalVehicleResponsiblePerson
import com.fleetmanagement.modules.masterdata.dto.ExternalVehicleSalesData
import com.fleetmanagement.modules.masterdata.dto.ExternalVehicleTechnicalData
import com.fleetmanagement.modules.masterdata.dto.ExternalVehicleTransfer
import com.fleetmanagement.modules.masterdata.dto.ExternalVehicleTransferWithStatus
import com.fleetmanagement.modules.masterdata.dto.ExternalVehicleUsage
import com.fleetmanagement.modules.masterdata.dto.ExternalVehicleWltpInfo
import com.fleetmanagement.modules.masterdata.dto.LicensePlatePeriod
import com.fleetmanagement.modules.vehicledata.api.domain.FuelType
import com.fleetmanagement.modules.vehicledata.api.dtos.MileageReadingDTO
import com.fleetmanagement.modules.vehicledata.api.dtos.MileageReadingSource
import com.fleetmanagement.modules.vehicledata.builders.vehicledto.builders.VehicleOptionBuilder
import com.fleetmanagement.modules.vehicledata.repository.entities.VehicleStatus
import com.fleetmanagement.modules.vehicletransfer.domain.entities.UtilizationArea
import java.math.BigDecimal
import java.time.LocalDate
import java.time.OffsetDateTime
import java.time.OffsetTime
import java.time.ZoneOffset
import java.util.UUID

object ExternalFleetMasterVehicleObjectMother {
    object MileageUtils {
        // to avoid mileageAtDelivery to be used as current mileage this has to be future date so that will be used as a current mileage
        val readDate: OffsetDateTime =
            OffsetDateTime.of(
                LocalDate.now().plusDays(1),
                OffsetTime.of(0, 0, 0, 0, ZoneOffset.UTC).toLocalTime(),
                ZoneOffset.UTC,
            )
    }

    fun withAllFieldsPopulated() =
        ExternalFleetMasterVehicle(
            vehicle =
                ExternalVehicle(
                    vguid = "1}H22976432018H",
                    vin = "1HGCM82633A123456",
                    equiId = "EQ1",
                    equipmentNumber = 1001,
                    status = VehicleStatus.SX97.name,
                    production =
                        ExternalVehicleProduction(
                            number = "2245051",
                            factory = "0010",
                            zp8Date = OffsetDateTime.parse("2023-10-06T00:00:00Z"),
                            plannedEndDate = OffsetDateTime.parse("2024-01-01T00:00:00Z"),
                            technicalModelYear = 2024,
                            gearBoxClass = 'P',
                        ),
                    model =
                        ExternalVehicleModel(
                            orderType = "95BAU1",
                            modelRange = "Porsche 911",
                            manufacturer = "Porsche",
                            vehicleType = "Car",
                            description = "911 GT3",
                        ),
                    price =
                        ExternalVehiclePrice(
                            vehicleFactoryGrossPriceEUR = BigDecimal(142000.00),
                            vehicleFactoryNetPriceEUR = BigDecimal(120000.00),
                            grossPriceWithExtras = BigDecimal(11000.00),
                        ),
                    registration =
                        ExternalVehicleRegistration(
                            firstRegistrationDate = OffsetDateTime.parse("2024-01-01T00:00:00Z"),
                            currentLicensePlate = "S KN 567",
                            currentLicensePlateSuffix = "E",
                            testNumber = 123L,
                            hsn = "583",
                            tsn = "ANU00171",
                            sfme = true,
                        ),
                    consumption =
                        ExternalConsumptionInfo(
                            driveType = "OVC_HEV",
                            typification = "ELECTRIC",
                            primaryFuelType = FuelType.SUPER,
                            secondaryFuelType = FuelType.REGULAR,
                        ),
                    transfers =
                        listOf(
                            ExternalVehicleTransferWithStatus(
                                status = "FINISHED",
                                transfers =
                                    listOf(
                                        ExternalVehicleTransfer(
                                            vehicleResponsiblePerson =
                                                ExternalVehicleResponsiblePerson(
                                                    employeeNumber = "00123456",
                                                    department = "Sales",
                                                ),
                                            deliveryIndex = 1,
                                            vehicleTransferKey = 1L,
                                            deliveryDate = OffsetDateTime.parse("2025-05-01T00:00:00Z"),
                                            returnDate = OffsetDateTime.parse("2025-06-01T00:00:00Z"),
                                            leasingPrivilege = "Full",
                                            vehicleUsage = ExternalVehicleUsage(usage = "Leasing Tarif"),
                                            usageGroup = ExternalUsageGroup(description = "PERSON"),
                                            licensePlates =
                                                listOf(
                                                    LicensePlatePeriod(
                                                        licensePlate = "S KN 1234",
                                                        licensePlateSuffix = null,
                                                        fromDate = OffsetDateTime.parse("2024-01-01T00:00:00Z"),
                                                        toDate = OffsetDateTime.parse("2025-04-01T00:00:00Z"),
                                                    ),
                                                    LicensePlatePeriod(
                                                        licensePlate = "S KN 567",
                                                        licensePlateSuffix = "E",
                                                        fromDate = OffsetDateTime.parse("2025-05-01T00:00:00Z"),
                                                        toDate = null,
                                                    ),
                                                ),
                                            mileageAtDelivery = 1000,
                                            mileageAtReturn = 1200,
                                            utilizationArea = UtilizationArea.LEIPZIG.name,
                                            maintenanceOrderNumber = "someMaintenanceOrderNumber",
                                            usingCostCenter = "RCC001",
                                            depreciationRelevantCostCenterId = UUID.fromString("3b0f1077-8644-4571-8f79-1ec3b13206ec"),
                                            internalOrderNumber = "978-1-9743-1028-9",
                                            plannedDeliveryDate = OffsetDateTime.parse("2025-05-01T00:00:00Z"),
                                            leasingArt =
                                                ExternalLeasingArt(
                                                    id = LeasingArt.L9_6.id,
                                                    description = LeasingArt.L9_6.description,
                                                    pool = LeasingArt.L9_6.isPool,
                                                ),
                                        ),
                                    ),
                            ),
                        ),
                    wltp =
                        ExternalVehicleWltpInfo(
                            co2Combined = 102,
                            electricRangeCity = 29,
                            electricRange = 30,
                        ),
                    currentMileage =
                        ExternalCurrentMileage.from(
                            MileageReadingDTO(
                                mileage = 65200,
                                readDate = OffsetDateTime.of(2025, 2, 18, 14, 40, 0, 0, ZoneOffset.UTC),
                                source = MileageReadingSource.CAR_SYNC,
                                createdBy = "test user",
                            ),
                        ),
                    order =
                        ExternalOrderInfo(
                            blockedForSale = false,
                            primaryStatus = "FD95",
                            tradingPartnerNumber = "1003000",
                        ),
                    fleet =
                        ExternalFleetInfo(
                            scrapVehicle = false,
                            residualValueMarket = false,
                            raceCar = true,
                            classic = true,
                        ),
                    evaluation =
                        ExternalVehicleEvaluationData(
                            appraisalNetPrice = BigDecimal(120000.00),
                        ),
                    sales =
                        ExternalVehicleSalesData(
                            reservedForB2C = true,
                            contractSigned = true,
                        ),
                    technical =
                        ExternalVehicleTechnicalData(
                            amountSeats = 5,
                            acceleration0100KmhLaunchControl = 100.0f,
                            acceleration0100Kmh = 100.0f,
                            acceleration80120Kmh = 100.0f,
                            cargoVolume = 5,
                            chargingTimeAc11Kw0100 = 100.0f,
                            chargingTimeAc22Kw = 100.0f,
                            chargingTimeAc96Kw0100 = 100.0f,
                            chargingTimeDcMaxPower580 = 100.0f,
                            engineCapacity = 100.0f,
                            curbWeightDin = 5,
                            curbWeightEu = 5,
                            grossBatteryCapacity = 100.0f,
                            grossVehicleWeight = 5,
                            height = 5,
                            length = 5,
                            maximumChargingPowerDc = 5,
                            maximumPayload = 5,
                            maxRoofLoadWithPorscheRoofTransportSystem = 5,
                            netBatteryCapacity = 100.0f,
                            powerKw = 5,
                            topSpeed = 5,
                            totalPowerKw = 5,
                            vehicleWidthMirrorsExtended = 5,
                            widthMirrorsFolded = 5,
                        ),
                    color =
                        ExternalVehicleColorData(
                            exterior = "A1B2C3",
                            exteriorDescription = "GT Silver Metallic",
                            interior = "X1Y2Z3",
                            interiorDescription = "Black Leather",
                        ),
                    options =
                        VehicleOptionBuilder().build().let {
                            jacksonObjectMapper().convertValue(it, object : TypeReference<Map<String, Any>>() {})
                        },
                ),
        )

    fun withIdFieldsPopulated() =
        ExternalFleetMasterVehicle(
            vehicle =
                ExternalVehicle(
                    vguid = "1}H22976432018H",
                    vin = "1HGCM82633A123456",
                    equiId = "EQ1",
                    equipmentNumber = 1001,
                    status = null,
                    production = null,
                    model = null,
                    price = null,
                    registration = null,
                    consumption = null,
                    transfers = emptyList(),
                    wltp = null,
                    currentMileage = null,
                    order = null,
                    fleet = null,
                    evaluation = null,
                    sales = null,
                    technical = null,
                    options = null,
                    color = null,
                ),
        )
}
