package com.fleetmanagement.modules.rela.objectMother

import com.fleetmanagement.modules.rela.application.port.RelaAppointmentRequest
import java.time.OffsetDateTime

object RelaAppointmentRequestMother {
    fun relaAppointmentRequest(): RelaAppointmentRequest =
        RelaAppointmentRequest(
            appointment = OffsetDateTime.now().plusDays(7),
            vehicleLicensePlate = "S-PL 1234",
            vehicleVin = "WP0ZZZ999SS123456",
            customerLastName = "Mayer",
            customerFirstName = "Hans",
            serviceBayNumber = 1,
            serviceTypeId = 2,
            vehicleTypeCode = "992642",
            vehicleTypeDescription = "911 Carrera 4 GTS Cabriolet",
            pccbCode = "1LQ",
            wheelCode = "C2Q",
            cwlCode = "1PJ",
            rasCode = "ON5",
            pccbDescription = "Porsche Ceramic Composite Brake (PCCB)",
            orderedByEmail = "<EMAIL>",
            orderDate = OffsetDateTime.now(),
        )
}
