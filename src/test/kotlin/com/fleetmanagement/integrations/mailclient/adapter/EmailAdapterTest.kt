/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.integrations.mailclient.adapter

import com.aspose.email.Attachment
import com.aspose.email.MailAddress
import com.aspose.email.system.exceptions.Exception
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fleetmanagement.TestcontainersConfiguration
import com.fleetmanagement.integrations.mailclient.application.port.EmailDto
import com.fleetmanagement.integrations.mailclient.application.port.EmailException
import okhttp3.OkHttpClient
import okhttp3.Request
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertInstanceOf
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Import
import org.springframework.context.annotation.Primary
import org.springframework.test.context.DynamicPropertyRegistry
import org.springframework.test.context.DynamicPropertySource
import org.testcontainers.containers.GenericContainer
import org.testcontainers.containers.wait.strategy.Wait
import org.testcontainers.junit.jupiter.Container
import org.testcontainers.junit.jupiter.Testcontainers
import org.testcontainers.utility.DockerImageName

@SpringBootTest
@Import(TestcontainersConfiguration::class, EmailAdapterTest.ExtendsTestConfiguration::class)
@Testcontainers
class EmailAdapterTest(
    @Value("\${mailclient.smtp.host}") val smtpHost: String,
) {
    @Autowired
    private lateinit var emailAdapter: EmailAdapter

    private var emailResolver: EmailRecipientResolver = DefaultEmailRecipientResolver()

    companion object {
        @Container
        @JvmStatic
        val mailPitContainer: GenericContainer<*> =
            GenericContainer(DockerImageName.parse("axllent/mailpit:v1.21.0"))
                .withExposedPorts(1025, 8025)
                // https://mailpit.axllent.org/docs/configuration/smtp/
                .withEnv("MP_SMTP_AUTH_ALLOW_INSECURE", "true")
                .withEnv("MP_SMTP_AUTH_ACCEPT_ANY", "true")
                .waitingFor(Wait.forLogMessage(".*accessible via.*", 1))
                .withReuse(true)

        @DynamicPropertySource
        @JvmStatic
        fun configureMail(registry: DynamicPropertyRegistry) {
            registry.add("mailclient.smtp.host") { mailPitContainer.host }
            registry.add("mailclient.smtp.port") { mailPitContainer.getMappedPort(1025) }
        }
    }

    @Test
    fun `should send email to smtp server with correct data`() {
        val subject = "subject"
        val body = "This is a text email"
        val html = "<html><body>$body</body></html>"
        val recipientsMailAddressInTo = listOf(MailAddress("<EMAIL>"))
        val recipientsMailAddressInCC = listOf(MailAddress("<EMAIL>"))
        val senderMailAddress = MailAddress("<EMAIL>")
        val attachment = Attachment.createAttachmentFromString("this is a text file", "file.txt")
        val emailDTO =
            EmailDto(
                subject = subject,
                htmlBody = html,
                recipientsMailAddressInTo = recipientsMailAddressInTo,
                recipientsMailAddressInCC = recipientsMailAddressInCC,
                senderMailAddress = senderMailAddress,
                attachment = listOf(attachment),
            )

        emailAdapter.sendEmail(emailDTO)

        val apiPort: Int = mailPitContainer.getMappedPort(8025)
        val client = OkHttpClient()
        val request = Request.Builder().url("http://$smtpHost:$apiPort/api/v1/message/latest").build()

        client.newCall(request).execute().use { response ->
            val responseBody = response.body?.string()
            val message = jacksonObjectMapper().readValue(responseBody!!, EmailMessage::class.java)
            assertEquals(subject, message.subject)
            assertEquals(senderMailAddress.address, message.from.address)
            assertEquals(recipientsMailAddressInTo.map { it.address }, message.to.map { it.address })
            assertEquals(recipientsMailAddressInCC.map { it.address }, message.cc.map { it.address })
            assertEquals(html, message.html)
            assertEquals(body, message.text)
            assertEquals(1, message.attachments.size)
            val attachment = message.attachments[0]
            assertEquals("file.txt", attachment.fileName)
        }
    }

    @Test
    fun `should handle aspose email exceptions when sending email`() {
        val subject = "subject"
        val body = "This is a text email"
        val html = "<html><body>$body</body></html>"
        val recipientsMailAddressInTo = listOf(MailAddress("<EMAIL>"))
        val recipientsMailAddressInCC = listOf(MailAddress("<EMAIL>"))
        val senderMailAddress = MailAddress("<EMAIL>")
        val attachment = Attachment.createAttachmentFromString("this is a text file", "file.txt")
        val emailDTOs =
            EmailDto(
                subject = subject,
                htmlBody = html,
                recipientsMailAddressInTo = recipientsMailAddressInTo,
                recipientsMailAddressInCC = recipientsMailAddressInCC,
                senderMailAddress = senderMailAddress,
                attachment = listOf(attachment),
            )

        val exception =
            assertThrows<EmailException> {
                EmailAdapter(
                    "invalidhost",
                    465,
                    "",
                    "",
                    emailResolver,
                ).sendEmail(emailDTOs)
            }
        assertInstanceOf<Exception>(exception.cause)
    }

    @Test
    fun `should send encrypted email with crypt prefix in subject line`() {
        val subject = "Test Subject"
        val body = "This is a text email"
        val html = "<html><body>$body</body></html>"
        val recipientsMailAddressInTo = listOf(MailAddress("<EMAIL>"))
        val recipientsMailAddressInCC = listOf(MailAddress("<EMAIL>"))
        val senderMailAddress = MailAddress("<EMAIL>")
        val attachment = Attachment.createAttachmentFromString("this is a text file", "file.txt")
        val emailDTO =
            EmailDto(
                subject = subject,
                htmlBody = html,
                recipientsMailAddressInTo = recipientsMailAddressInTo,
                recipientsMailAddressInCC = recipientsMailAddressInCC,
                senderMailAddress = senderMailAddress,
                attachment = listOf(attachment),
            )

        emailAdapter.sendEmailEncrypted(emailDTO)

        val apiPort: Int = mailPitContainer.getMappedPort(8025)
        val client = OkHttpClient()
        val request = Request.Builder().url("http://$smtpHost:$apiPort/api/v1/message/latest").build()

        client.newCall(request).execute().use { response ->
            val responseBody = response.body?.string()
            val message = jacksonObjectMapper().readValue(responseBody!!, EmailMessage::class.java)
            assertEquals("#crypt_*$subject", message.subject)
            assertEquals(senderMailAddress.address, message.from.address)
            assertEquals(recipientsMailAddressInTo.map { it.address }, message.to.map { it.address })
            assertEquals(recipientsMailAddressInCC.map { it.address }, message.cc.map { it.address })
            assertEquals(html, message.html)
            assertEquals(body, message.text)
            assertEquals(1, message.attachments.size)
            val attachment = message.attachments[0]
            assertEquals("file.txt", attachment.fileName)
        }
    }

    @Test
    fun `should handle aspose email exceptions when sending encrypted email`() {
        val subject = "subject"
        val body = "This is a text email"
        val html = "<html><body>$body</body></html>"
        val recipientsMailAddressInTo = listOf(MailAddress("<EMAIL>"))
        val recipientsMailAddressInCC = listOf(MailAddress("<EMAIL>"))
        val senderMailAddress = MailAddress("<EMAIL>")
        val attachment = Attachment.createAttachmentFromString("this is a text file", "file.txt")
        val emailDTOs =
            EmailDto(
                subject = subject,
                htmlBody = html,
                recipientsMailAddressInTo = recipientsMailAddressInTo,
                recipientsMailAddressInCC = recipientsMailAddressInCC,
                senderMailAddress = senderMailAddress,
                attachment = listOf(attachment),
            )

        val exception =
            assertThrows<EmailException> {
                EmailAdapter(
                    "invalidhost",
                    465,
                    "",
                    "",
                    emailResolver,
                ).sendEmailEncrypted(emailDTOs)
            }
        assertInstanceOf<Exception>(exception.cause)
    }

    internal class ExtendsTestConfiguration {
        @Bean
        @Primary
        fun emailRecipientResolver(): EmailRecipientResolver = DefaultEmailRecipientResolver()
    }
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class EmailMessage(
    @JsonProperty("Subject")
    val subject: String,
    @JsonProperty("From")
    val from: EmailContact,
    @JsonProperty("To")
    val to: List<EmailContact>,
    @JsonProperty("Cc")
    val cc: List<EmailContact>,
    @JsonProperty("HTML")
    val html: String?,
    @JsonProperty("Text")
    val text: String?,
    @JsonProperty("Attachments")
    val attachments: List<EmailAttachment>,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class EmailContact(
    @JsonProperty("Address")
    val address: String,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class EmailAttachment(
    @JsonProperty("FileName")
    val fileName: String,
)
