package com.fleetmanagement.integrations.mailclient.adapter

import com.aspose.email.MailAddress
import com.fleetmanagement.integrations.mailclient.application.port.EmailDto
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test

class EmailRecipientResolverTest {
    private val recipieints = "<EMAIL>, <EMAIL>"
    private val emailDto =
        EmailDto(
            subject = "Test Subject",
            htmlBody = "<p>Test Body</p>",
            senderMailAddress = MailAddress("<EMAIL>"),
            recipientsMailAddressInTo = listOf(MailAddress("<EMAIL>")),
            recipientsMailAddressInCC = listOf(MailAddress("<EMAIL>")),
            attachment = emptyList(),
        )
    private val testEmailRecipientResolver: EmailRecipientResolver = TestEmailRecipientResolver(recipieints)
    private val defaultEmailRecipientResolver: EmailRecipientResolver = DefaultEmailRecipientResolver()

    @Test
    fun `should change recipients to emails`() {
        val toAddresses = testEmailRecipientResolver.resolveToRecipients(emailDto)
        val ccAddresses = testEmailRecipientResolver.resolveCcRecipients(emailDto)
        assertTrue(toAddresses.contains(MailAddress("<EMAIL>")))
        assertTrue(toAddresses.contains(MailAddress("<EMAIL>")))
        assertTrue(ccAddresses.isEmpty())
    }

    @Test
    fun `should change recipients to emails even if recipient is empty`() {
        val emailDto =
            EmailDto(
                subject = "Test Subject",
                htmlBody = "<p>Test Body</p>",
                senderMailAddress = MailAddress("<EMAIL>"),
                recipientsMailAddressInTo = emptyList(),
                recipientsMailAddressInCC = emptyList(),
                attachment = emptyList(),
            )
        val toAddresses = testEmailRecipientResolver.resolveToRecipients(emailDto)
        val ccAddresses = testEmailRecipientResolver.resolveCcRecipients(emailDto)
        assertTrue(toAddresses.contains(MailAddress("<EMAIL>")))
        assertTrue(toAddresses.contains(MailAddress("<EMAIL>")))
        assertTrue(ccAddresses.isEmpty())
    }

    @Test
    fun `should not change recipients`() {
        val toAddresses = defaultEmailRecipientResolver.resolveToRecipients(emailDto)
        val ccAddresses = defaultEmailRecipientResolver.resolveCcRecipients(emailDto)
        assertTrue(toAddresses.contains(MailAddress("<EMAIL>")))
        assertTrue(ccAddresses.contains(MailAddress("<EMAIL>")))
    }
}
