/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

create schema if not exists vehicle;
create schema if not exists fvm;
create schema if not exists security;
create schema if not exists vehicleregistration;
create schema if not exists vehiclelocation;
create schema if not exists vehiclehistory;
create schema if not exists jobs;
create schema if not exists emh_user;
create schema if not exists vehicletransfer;
create schema if not exists predelivery;
create schema if not exists consigneedatasheet;
create schema if not exists vehiclecampaigns;
create schema if not exists emh_fleet_data;
create schema if not exists noncustomeradequate;
create schema if not exists vehiclesales;
create schema if not exists balancesheet;
create schema if not exists fms_migration;
create schema if not exists tirechangeappointment;

CREATE OR REPLACE VIEW vehicleregistration.vehicle_manager_latest_registration AS
SELECT
        NULL::UUID AS vehicle_id,
        NULL::VARCHAR(255) AS order_status,
        NULL::VARCHAR(15) AS planned_licence_plate,
        NULL::TIMESTAMP(6) AS planned_registration_date,
        NULL::VARCHAR(5) AS registration_office,
        NULL::VARCHAR(255) AS commenter,
        NULL::TIMESTAMP(6) AS first_registration_date,
        NULL::TIMESTAMP(6) AS last_registration_date,
        NULL::TIMESTAMP(6) AS last_deregistration_date,
        NULL::VARCHAR(4) AS hsn,
        NULL::VARCHAR(32) AS tsn,
        NULL::VARCHAR(15) AS licence_plate,
        NULL::TIMESTAMP(6) AS registration_date,
        NULL::VARCHAR(35) AS registration_status,
        NULL::INTEGER AS registration_type,
        NULL::BOOLEAN AS sfme,
        NULL::VARCHAR(255) AS remark,
        NULL::VARCHAR(15) AS storage_location,
        NULL::BOOLEAN AS test_vehicle,
        NULL::INTEGER AS test_number
    WHERE 1 = 0;
