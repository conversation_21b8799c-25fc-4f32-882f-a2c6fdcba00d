spring:
  cloud:
    aws:
      s3:
        region: eu-west-1
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.quartz.QuartzAutoConfiguration # no quartz integration in tests
  datasource:
    hikari:
      jdbc-url: ${SPRING_DATASOURCE_URL:***********************************************}
      username: ${SPRING_DATASOURCE_USERNAME:application_user}
      password: ${SPRING_DATASOURCE_PASSWORD:Password1}
      maximum-pool-size: 3
      minimum-idle: 1
      idle-timeout: 30000 # 30 seconds
      connection-timeout: 5000 # 5 seconds
      max-lifetime: 300000 # 5 minutes
      pool-name: TestcontainersHikariPool
      driver-class-name: org.postgresql.Driver
  quartz:
    auto-startup: false
  kafka:
    bootstrap-servers: localhost:9092
    consumer:
      auto-offset-reset: earliest
      group-id: test-consumer-group-id
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
    properties:
      sasl:
        mechanism: PLAIN
        jaas:
          config: org.apache.kafka.common.security.plain.PlainLoginModule required username="user" password="password";
      security:
        protocol: PLAINTEXT
      session:
        timeout:
          ms: 45000
  jackson:
    mapper:
      default-view-inclusion: true
  security:
    oauth2:
      client:
        registration:
          cap:
            authorization-grant-type: client_credentials
            client-id: ${CAP_OAUTH2_CLIENT_ID:test-cap-oauth2-client-id}
            client-secret: ${CAP_OAUTH2_CLIENT_SECRET:test-cap-oauth2-client-secret}
          entra-id:
            authorization-grant-type: client_credentials
            client-id: ${ENTRA_ID_OAUTH2_CLIENT_ID:test-entra-id-client-id}
            client-secret: ${ENTRA_ID_OAUTH2_CLIENT_SECRET:test-entra-id-client-secret}
            scope: ${ENTRA_ID_API_SCOPE:test-entra-id-client-id}
          vehicle-location:
            authorization-grant-type: client_credentials
            client-id: ${VEHICLE_LOCATION_API_CLIENT_ID}
            client-secret: ${VEHICLE_LOCATION_API_CLIENT_SECRET}
            scope: ${VEHICLE_LOCATION_API_SCOPE}
          dmsi:
            client-id: "dmsi-client-id"
            client-secret: "dmsi-client-secret"
            authorization-grant-type: client_credentials
          pace:
            authorization-grant-type: client_credentials
            client-id: TEST_CLIENT_ID
            client-secret: TEST_CLIENT_SECRET
            client-authentication-method: client_secret_post
          pace-balance-sheet:
            authorization-grant-type: client_credentials
            client-id: TEST_CLIENT_ID
            client-secret: TEST_CLIENT_SECRET
            client-authentication-method: client_secret_post
        provider:
          vehicle-location:
            token-uri: https://login.microsoftonline.com/${VEHICLE_LOCATION_AZURE_TENANT_ID}/oauth2/v2.0/token
          cap:
            token-uri: https://${PPN_HOST:test-ppn-host}/as/token.oauth2
          dmsi:
            token-uri: http://replaced-in-tests.com
          entra-id:
            token-uri: https://login.microsoftonline.com/${ENTRA_ID_AZURE_TENANT_ID:test-entra-id-tenant}/oauth2/v2.0/token
          pace:
            token-uri: http://localhost:8092/oauth/token
          pace-balance-sheet:
            token-uri: http://localhost:8092/oauth/token

logging:
  level:
    org.apache.kafka: ERROR
    org.springframework.kafka: ERROR
    org.quartz.core.QuartzScheduler: ERROR
    org.springframework.scheduling.quartz.SchedulerFactoryBean: ERROR

fleet-master-data-publisher:
  enabled: true
  topic: "test-topic"
  producer:
    key-serializer: org.apache.kafka.common.serialization.StringSerializer
    value-serializer: io.confluent.kafka.serializers.json.KafkaJsonSchemaSerializer
    schema-registry-url: "mock://schema-registry"
    schema-registry-user: "mock-registry-user"
    schema-registry-password: "mock-registry-password"
    auto-register-schemas: true
  scheduler:
    cron: ${FLEET_MASTER_DATA_CRON_SCHEDULE:0 * * * * ?} # Publish master data events every one minute

security:
  enabled: true
  cors:
    enabled: false
  api-gateway-token-verification:
    issuer-uri: ${OAUTH2_ISSUER_URI:https://login.microsoftonline.com/07f8138c-e3e4-4b45-842b-9ecc3ba58cf4/v2.0}
    jwks-uri: ${OAUTH2_JWKS_URI:https://login.microsoftonline.com/07f8138c-e3e4-4b45-842b-9ecc3ba58cf4/discovery/v2.0/keys}
    audience: ${OAUTH2_AUDIENCE:1d612e2b-317e-4d59-80f5-f80b3e3a33c0}
  alb-token-verification:
    expected-alb-arn: ${EXPECTED_ALB_ARN:arn:aws:elasticloadbalancing:eu-west-1:938255451121:loadbalancer/app/fleet-management-ui-alb/ec94b0fb4c906a17}
    public-key-host: https://public-keys.auth.elb.${AWS_REGION}.amazonaws.com
    issuer-uri: https://sts.windows.net/07f8138c-e3e4-4b45-842b-9ecc3ba58cf4
    app-id: cfb99f74-19b0-4747-b96f-81989c59bb4a
    app-domain: mobilityservicesdev.porsche.services
    logout-uri: ${OAUTH2_LOGOUT_URI:https://login.microsoftonline.com/07f8138c-e3e4-4b45-842b-9ecc3ba58cf4/oauth2/v2.0/logout}
    logout-redirect-uri: ${LOGOUT_LANDING_PAGE:https://mobilityservicesdev.porsche.services/login}
    toggles:
      allow-overwrite-of-groups-from-request-header: false
  entra-id:
    enabled: ${ENABLE_ENTRA_ID_SCHEDULER:false}
    scheduler:
      # SECOND MINUTE HOUR DAY_OF_MONTH MONTH DAY_OF_WEEK
      # 0 0 0 1 * ?
      cron: ${VEHICLE_ARCHIVE_CRON_SCHEDULE:0 0 0 * * ?}
    base-url: "https://graph.microsoft.com/v1.0"
    administrative-unit-id: ${ENTRA_ID_ADMINISTRATIVE_UNIT_ID:6bbcd412-527e-4e4d-89c1-5862b4e5d239}
    app-role-assignment-id: ${ENTRA_ID_APP_ROLE_ASSIGNMENT_ID:6f9c0024-186a-43d9-aa3a-cbcb8c9067d0}

vehicle-data:
  vw-oidc-token:
    uri: "https://some.test.idp.url/token"
    refresh-interval-in-sec: 3600
  pvh:
    base-uri: "https://some.local.pvh.url/token"
    client-id: test-client-id
    client-secret: test-client-secret
    streamzilla:
      enabled: false
      group-id: FRA_emhs_pvh_Vehicle_Embargo_01_2024_test
      stop-consuming-on-error: ${KAFKA_PVH_STOP_ON_ERROR:false}
      auto-offset-reset: latest
      timestamp-based-filtering:
        disable-after: 2024-12-31T08:00:00Z
  dms:
    number-of-damages:
      kafka:
        consumer:
          topic: ${NUMBER_OF_DAMAGES_TOPIC:FRA_emhs_dms_numer_of_damages_topic_dev}
          group-id: ${NUMBER_OF_DAMAGES_ID:FRA_emhs_dms_numer_of_damages_topic_dev.consumer}
        enabled: ${NUMBER_OF_DAMAGES_TOPIC_KAFKA_CONSUMERS:false}

  status:
    recalculation-cron: 0 0 0 1 1 ? 2099

  factory-car-preparation-order-number:
    factory-car-preparation-order-number-update-cron: ${FACTORY_CAR_PREPARATION_ORDER_NUMBER_UPDATE_SCHEDULE:0 0 0 1 1 ? 2099}

  pia:
    kafka:
      enabled: ${ENABLE_KAFKA_PIA_INTEGRATION:false}
      consumer:
        topic: ${KAFKA_PIA_TOPIC:FRA_one_vms_piaom_importer_invoice}
        group-id: ${KAFKA_PIA_CONSUMER_GROUP_ID:FRA_emhs_one_vms_piaom_importer_invoice_dev}
        auto-offset-reset: ${KAFKA_PIA_AUTO_OFFSET_RESET:latest}
  p40:
    kafka:
      enabled: ${ENABLE_KAFKA_P40_INTEGRATION:false}
      consumer:
        topic: ${KAFKA_FINANCIAL_ASSET_TYPE_TOPIC:FRA_account_2_report_Fleet_Vehicle_Manager_Asset_Class}
        group-id: ${KAFKA_FINANCIAL_ASSET_TYPE_CONSUMER_GROUP_ID:FRA_emhs_p40_account_2_report_fleet_vehicle_manager_asset_class_local}
        auto-offset-reset: ${KAFKA_FINANCIAL_ASSET_TYPE_AUTO_OFFSET_RESET:earliest}

  financial-asset-type-update:
    financial-asset-type-update-sync-cron: ${FINANCIAL_ASSET_TYPE_UPDATE_SYNC_CRON:0 0 0 1 1 ? 2099}

fvm:
  vehicle-registration-base-uri: https://localhost:8082
  access-control:
    history-deletion:
      scheduler:
        cron: "0 0 3 * * ?"
      retention-period: 30 # Days

oidc:
  azure:
    token-url: ${OAUTH2_TOKEN_URI:https://login.microsoftonline.com/07f8138c-e3e4-4b45-842b-9ecc3ba58cf4/oauth2/v2.0}
    refresh-interval-in-sec: 3600

fms-migration-job:
  enabled: false
  fms-source:
    storage:
      sse: aws:kms
      sse-kms-key: ${FMS_MIGRATION_BUCKET_KMS_KEY_ID:test-key}
      bucket: ${FMS_MIGRATION_BUCKET:test-fms-bucket}

vehicle-archive:
  enabled: false
  vehicle-history:
    storage:
      sse: aws:kms
      sse-kms-key: ${STORAGE_ARCHIVE_BUCKET_KMS_KEY_ID:test-key}
      bucket: ${STORAGE_ARCHIVE_BUCKET_VEHICLE_HISTORY:test-vehicle-history-bucket}
  vehicle-data:
    storage:
      sse: aws:kms
      sse-kms-key: ${STORAGE_ARCHIVE_BUCKET_KMS_KEY_ID:test-key}
      bucket: ${STORAGE_ARCHIVE_BUCKET:test-vehicle-bucket}
  vehicle-transfer:
    storage:
      sse: aws:kms
      sse-kms-key: ${STORAGE_ARCHIVE_BUCKET_KMS_KEY_ID:test-key}
      bucket: ${STORAGE_ARCHIVE_BUCKET_VEHICLE_TRANSFER:test-vehicle-transfer-bucket}
  clients:
    vehicle-registration:
      base-url: ${VR_BASE_URL:http://localhost:8081/api/vr}
      client-id: ${VEHICLE_REGISTRATION_ARCHIVE_API_CLIENT_ID:test-client-id}
      client-secret: ${VEHICLE_REGISTRATION_ARCHIVE_API_CLIENT_SECRET:test-client-secret}
      scope: ${VEHICLE_REGISTRATION_ARCHIVE_API_SCOPE:test-scope/.default}

vehicle-legalhold:
  storage:
    sse: aws:kms
    sse-kms-key: ${VEHICLE_LEGALHOLD_STORAGE_BUCKET_KMS_KEY_ID:test-key}
    vehicle-data:
      bucket: ${VEHICLE_LEGALHOLD_STORAGE_BUCKET:test-bucket}
    vehicle-sales:
      bucket: ${VEHICLE_LEGALHOLD_STORAGE_BUCKET_VEHICLE_SALES:test-sales-bucket}
  clients:
    vehicle-registration:
      base-url: ${VR_BASE_URL:http://localhost:8081/api/vr}
      client-id: ${VEHICLE_REGISTRATION_LEGALHOLD_API_CLIENT_ID:test-client-id}
      client-secret: ${VEHICLE_REGISTRATION_LEGALHOLD_API_CLIENT_SECRET:test-client-secret}
      scope: ${VEHICLE_REGISTRATION_LEGALHOLD_API_SCOPE:test-scope/.default}

registration-mail-export:
    # SECOND MINUTE HOUR DAY_OF_MONTH MONTH DAY_OF_WEEK
    cron: '0 */5 * ? * *'
    enabled: false
    email-sender: 'do-not-use-me'
    email-recipient: 'do-not-use-me'

vehicle-location:
  base-url: ${VEHICLE_LOCATION_BASE_URL:http://localhost:8081/}
  sync-strategy: ${VEHICLE_LOCATION_SYNC_STRATEGY:disabled}
  object-location-event:
    updated:
      kafka:
        consumer:
          topic: ${OBJECT_LOCATION_UPDATED_TOPIC:FRA_emhs_vls_object_location_event}
          group-id: ${OBJECT_LOCATION_UPDATED_TOPIC_GROUP_ID:FRA_emhs_vls_object_location_event.consumer}
  location-event:
    updated:
      kafka:
        consumer:
          topic: ${LOCATION_UPDATED_TOPIC:FRA_emhs_vls_location_updated_event}
          group-id: ${LOCATION_UPDATED_TOPIC_GROUP_ID:FRA_emhs_vls_location_updated_event.consumer}
    deleted:
      kafka:
        consumer:
          topic: ${LOCATION_DELETED_TOPIC:FRA_emhs_vls_location_deleted_event}
          group-id: ${LOCATION_DELETED_TOPIC_GROUP_ID:FRA_emhs_vls_location_deleted_event.consumer}

rela:
  enabled: true
  base-url: http://localhost:8092
  api-key: test-api-key

tire-change:
  scheduler:
    rela-sync-cron: ${TIRE_CHANGE_RELA_SYNC_JOB_SCHEDULE:0 0/15 * * * ?}

vehicle-person:
  feature-flags:
    read-vehicle-person-enabled: true
  employee:
    base-url: http://localhost:8091/api
    client-id: TEST_CLIENT_ID
    client-secret: TEST_CLIENT_SECRET
    scope: TEST_SCOPE
    token-uri: http://localhost:8091/oauth/token
  cap:
    base-uri: ${CAP_BASE_URL}
    region: ${CAP_REGION:test-region}
    X-Porsche-Client-Id: ${CAP_IBM_API_GATEWAY_CLIENT_ID:test-client-id}
    X-Porsche-Client-Secret: ${CAP_IBM_API_GATEWAY_CLIENT_SECRET:test-client-secret}

vehicle-transfer:
  allow-incoming-traffic: ${VEHICLE_TRANSFER_ALLOW_INCOMING_TRAFFIC:true}
  license-plate-event:
    updated:
      kafka:
        consumer:
          topic: ${LICENSE_PLATE_TOPIC:FRA_emh_vr_license_plate_dev}
          group-id: ${LICENSE_PLATE_GROUP_ID:FRA_emh_vr_license_plate_dev.dev.consumer123}
        enabled: ${LICENSE_PLATE_KAFKA_CONSUMERS:false}
  employee-updated-event:
      kafka:
        consumer:
          topic: ${EMPLOYEE_UPDATED_EVENT_TOPIC:FRA_emhs_us_employee_update_topic_dev}
          group-id: ${EMPLOYEE_UPDATED_EVENT_GROUP_ID:FRA_emhs_us_employee_update_topic_dev.consumer}
        enabled: ${EMPLOYEE_UPDATED_EVENT_KAFKA_CONSUMERS:false}
  scheduler:
    # SECOND MINUTE HOUR DAY_OF_MONTH MONTH DAY_OF_WEEK
    # 0 0 0 1 * ?
    planned-vehicle-transfer-initialization-cron: ${PLANNED_VEHICLE_TRANSFER_INITIALIZATION_SCHEDULE:0 0 0 1 1 ? 2099}
    maintenance-order-number-update-cron: ${MAINTENANCE_ORDER_NUMBER_UPDATE_SCHEDULE:0 0 0 1 1 ? 2099}
  delivery-lead-time: 2
  delivery-email:
    sender-email-address: <EMAIL>

pre-delivery-inspection:
  # pdi lead time in working days
  pdi-lead-time: 2
  pdi-ordering:
    scheduler:
      pdi-ordering-email-cron: ${PDI_ORDERING_EMAIL_SCHEDULE:0 0 0 1 1 ? 2099}
    email:
      recipient-to-email-address: <EMAIL>,<EMAIL>
      recipient-cc-email-address: <EMAIL>,<EMAIL>
      sender-email-address: <EMAIL>

mailclient:
  smtp:
    host: localhost
    port: 1025
    username: smtp-username
    password: smtp-password
  override-email-recipients: false
  override-recipients-list: ""

msbooking:
  base-url: https://graph.microsoft.com/v1.0
  booking-id: ${BOOKING_ID:<EMAIL>}
  service-id: ${SERVICE_ID:870dd2d1-5c83-4ad3-b7e8-48a98942fe2f}
  enable: ${MS_BOOKING_ENABLE:false} # true will cause authentication happening during spring boot tests
  cancel-timeframe: 30
  security:
    username: ${MS_BOOKING_USERNAME:<EMAIL>}
    password: ${MS_BOOKING_PASSWORD}
    client-id: ${MS_BOOKING_CLIENT_CLIENT_ID}
    client-secret: ${MS_BOOKING_CLIENT_CLIENT_SECRET}
    authorization-grant-type: password
    scope: ${MS_BOOKING_CLIENT_SCOPE:https://graph.microsoft.com/.default}
    token-uri: https://login.microsoftonline.com/07f8138c-e3e4-4b45-842b-9ecc3ba58cf4/oauth2/v2.0/token
  questions-id:
    delivery_vin: ${DELIVERY_VIN:4281d90c-84bf-4124-8e2d-adba84806d27}
    delivery_license-plate: ${DELIVERY_LICENSE_PLATE:e062aba4-ab0e-46cc-898e-1ba309e07406}
    delivery_model: ${DELIVERY_MODEL:5710b5b7-e0ee-4f64-97c0-cca28cddfcfb}
    return_vin: ${RETURN_VIN:86df2ffa-49e5-461f-8194-90a4f00263f2}
    return_license-plate: ${RETURN_LICENSE_PLATE:e062aba4-ab0e-46cc-898e-1ba309e07406}
    return_model: ${RETURN_MODEL:5a97db48-f4e1-45cb-94bf-f6b0d188b4c4}
  scheduler:
    msbooking-appointments-cron: ${MS_BOOKING_APPOINTMENTS_JOB_SCHEDULE:0 0 0 1 1 ? 2099}

vehicle-campaigns:
  enabled: ${VEHICLE_CAMPAIGNS_ENABLED:false}
  cleanup-vehicles:
    cron: '0 0 0 * * ?'
  filter-vehicles:
    enabled: true
    vehicle-transfer-batch-size: 1000
    max-vehicles-to-schedule-per-run: 3500
    cron: '30 * * ? * *'
  dmsi:
    # SECOND MINUTE HOUR DAY_OF_MONTH MONTH DAY_OF_WEEK
    cron: '30 * * ? * *'
    enabled: false
    base-uri: 'defined-in-tests'
    x-personal-id: 'defined-in-tests'

pace:
  base-url: http://localhost:8092

non-customer-adequate-vehicles:
  scheduler:
    evaluation-cron: '* * * ? * *'

tire-management:
  enabled: false
  data-export:
    # SECOND MINUTE HOUR DAY_OF_MONTH MONTH DAY_OF_WEEK
    cron: '0 */5 * ? * *'
  email:
    sender: 'do-not-use-me'
    recipient: 'do-not-use-me'

repair-fix:
  base-url: http://localhost:8092

vehicle-evaluation:
  tuv-commission:
    tuv-email:
      recipient-to-email-address: <EMAIL>,<EMAIL>
      sender-email-address: <EMAIL>
    logistics-provider-email:
      recipient-to-email-address: <EMAIL>,<EMAIL>
      sender-email-address: <EMAIL>

balance-sheet:
  scheduler:
    synchronize-scrapping-status-update-cron: ${SYNCHRONIZE_SCRAPPING_STATUS_UPDATE_CRON:0 0 0 1 1 ? 2099}
    cost-center-update-cron: ${COST_CENTER_UPDATE_CRON:0 0 0 1 1 ? 2099}
    synchronize-blocked-for-sale-update-cron: ${SYNCHRONIZE_BLOCKED_FOR_SALE_UPDATE_CRON:0 0 0 1 1 ? 2099}
    cost-center-update-preparation-cron: ${COST_CENTER_UPDATE_PREPARATION_CRON:0 0 0 1 1 ? 2099}
    prepare-blocked-for-sale-update-cron: ${PREPARE_BLOCKED_FOR_SALE_UPDATE_CRON:0 0 0 1 1 ? 2099}
  pace:
    base-url: http://localhost:8092

ecc:
  allow-incoming-traffic: ${ECC_ALLOW_INCOMING_TRAFFIC:true}

dms-vehicle-migration:
  kafka-producer:
    bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:localhost:29092}
    key-serializer: org.apache.kafka.common.serialization.StringSerializer
    value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
    sasl-mechanism: PLAIN
    sasl-jaas-config: org.apache.kafka.common.security.plain.PlainLoginModule required username="${KAFKA_USER}" password="${KAFKA_PASSWORD}";
    security-protocol: ${KAFKA_SECURITY_PROTOCOL:PLAINTEXT}
    session-timeout-ms: 45000
  topic: ${DMS_VEHICLE_MIGRATION_TOPIC:FRA_emhs_dms_vehicle_migration_dev}
  enabled: ${DMS_VEHICLE_MIGRATION_ENABLED:true}

carsync:
  enabled: ${ENABLE_CARSYNC:false}
  password: ${CARSYNC_USER_PASSWORD:password}
  username: ${CARSYNC_USER_USERNAME:user}
  base-url: ${CARSYNC_BASE_URL:http://localhost:8093}
  certificate:
    keystore: ${CARSYNC_TRUSTSTORE:LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURhekNDQWxPZ0F3SUJBZ0lVUXlCZUNnd1VMVzhlaDloL1l2dGZ2aWE1TzFvd0RRWUpLb1pJaHZjTkFRRUwKQlFBd1JURUxNQWtHQTFVRUJoTUNRVlV4RXpBUkJnTlZCQWdNQ2xOdmJXVXRVM1JoZEdVeElUQWZCZ05WQkFvTQpHRWx1ZEdWeWJtVjBJRmRwWkdkcGRITWdVSFI1SUV4MFpEQWVGdzB5TlRBMk1UTXdOalUwTlRSYUZ3MHpNekE0Ck16QXdOalUwTlRSYU1FVXhDekFKQmdOVkJBWVRBa0ZWTVJNd0VRWURWUVFJREFwVGIyMWxMVk4wWVhSbE1TRXcKSHdZRFZRUUtEQmhKYm5SbGNtNWxkQ0JYYVdSbmFYUnpJRkIwZVNCTWRHUXdnZ0VpTUEwR0NTcUdTSWIzRFFFQgpBUVVBQTRJQkR3QXdnZ0VLQW9JQkFRRFV4MjNUWjBvUXk2WDM3ZXUxaHZqRzZBQzkrLzVRTkNxeHBPcWJKQTNHCnI2U240TE1vMllzTkRwWEV5dkc5YVJrS284aThlOWlwYWthbjhYQ3grcDg4K3JpL3RCMWtNSSs0U1o2ZUxGMUYKNTVMbWpTNldwR3BDQi9XTllmbzZpNi8yVzltMjdSclVvQUhHWVVpZWNEUkZRU2NmOE5zbGtLd3dnMHdBT3UxSwp4QmZ4dXFCYjg4eitEUzNScURPYTFhb21NNjdTNWZLM2FxaitVTkhVTTFkM0o1VHY0TDMzMWQ2QnF1RFEzRGFNCkZHZnc5ckxuc2daOS85R1hMMkYwYlI5WkVCejhhVXNIUG1GVzVjZTVNNzFSVkU0ZzhuRGlrd3FUeWlMMmxuci8KNXNUTmh5aDV5RHhlL2NiZjFZbFJOV1lNemorUW1WRFJsdzUrN0puWUl2eDVBZ01CQUFHalV6QlJNQjBHQTFVZApEZ1FXQkJTT29xd0VYUTNkYVl1czlKU1ZXbVA2RmkybWJqQWZCZ05WSFNNRUdEQVdnQlNPb3F3RVhRM2RhWXVzCjlKU1ZXbVA2RmkybWJqQVBCZ05WSFJNQkFmOEVCVEFEQVFIL01BMEdDU3FHU0liM0RRRUJDd1VBQTRJQkFRQnEKWmNZZlpJdmdBUnlHMHVHamMrcHVXNG53VDZ5NDRoN3B1MWJWNkNhNXhnVkd2S1BCZkxUOHE4R2tlNmpqVExJQQp6bmRETUo0M1JvYzNVTlJBQnM3ZVMwRFRLMUI2bU1ubHJBUkpVVlQzY2ZSMlY4dlZHWjBzWHhyNnlXa2ljTEc5Cm1iSkh5K1c1bytvNE15bStUSXZMVUNGNFpqelduVmlKOFFFVzMzUmlaU05haVJoZTk4VW9ROVpveWtlMnEybzgKSFZlY2VSR1YralY4UHRucFMwaWs2bFlVNVFTMzRxSnV1MUpQUTdhNmNWWmpOU3ErSlI5WHVTRGhUWEJlcVJ5SgpQTEtVNWtTUHBBQy9FQm5NWVZQZ0psWUdSbXpFWW5jL2twcjZCbVdabHpCQmZTdmpwbFVhYkRDWUh6Y0NDbTNHCldmcW92UEpjbDU2NnM4d1Q4SDFJCi0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K}
    truststore: ${CARSYNC_KEYSTORE:****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************}
  scheduler:
    sync-mileage-job-cron: ${SYNC_MILEAGE_CRON:0 0 0 1 1 ? 2099}

vtstamm:
  allow-incoming-traffic: ${VTSTAMM_ALLOW_INCOMING_TRAFFIC:true}

vehicle-sales:
  b2b-integration:
    enabled: true
    consumer:
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: io.confluent.kafka.serializers.KafkaAvroDeserializer
      schema-registry-url: mock://schema-registry
      schema-registry-user: mock-registry-user
      schema-registry-password: mock-registry-password
      group-id: FRA_emhs_honeypotters_completed_auctions_dev

reporting:
  enabled: false
  producer:
    key-serializer: org.apache.kafka.common.serialization.StringSerializer
    value-serializer: io.confluent.kafka.serializers.json.KafkaJsonSchemaSerializer
    schema-registry-url: "mock://schema-registry"
    schema-registry-user: "mock-registry-user"
    schema-registry-password: "mock-registry-password"
    auto-register-schemas: true

tire-change-appointment:
  ms-bookings:
    base-url: http://localhost:8092
    bookings-id: bookings-id
    cancel-timeframe: 30
    service-id: service-id
    enable: false # true will cause authentication happening during spring boot tests
    security:
      username: ms-bookings-username
      password: ms-bookings-password
      client-id: ms-bookings-client-d
      client-secret: ms-bookings-client-secret
      authorization-grant-type: password
      scope: http://localhost:8092/.default}
      token-uri: http://localhost:8092/oauth/token
