load_test:
  stage: load-test
  image: python:3.11  # Use a Python Docker image
  variables:
    HOST: "https://mobilityservicesstaging.porsche.services"
    USERS: "10"
    SPAWN_RATE: "1"
    RUN_TIME: "5m"
    RESPONSE_TIME_THRESHOLD_IN_MS: 400
  before_script:
    - python -m venv venv
    - source venv/bin/activate
    - pip install --upgrade pip
    - pip install -r loadtesting/requirements.txt
    - python -m playwright install-deps
    - python -m playwright install
    - mkdir -p reports/loadtest
  script:
    - locust -f loadtesting/fvm/load_test.py --host=${HOST} --users=${USERS} --spawn-rate=${SPAWN_RATE} --run-time=${RUN_TIME} --headless --html reports/loadtest/report.html
  cache:
    paths:
      - venv/
  artifacts:
    when: always
    paths:
      - reports/loadtest/report.html  # Save the HTML report for review
  when: on_success
  allow_failure: true
  rules:
    - if: $CI_PIPELINE_SOURCE == "schedule" && $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
