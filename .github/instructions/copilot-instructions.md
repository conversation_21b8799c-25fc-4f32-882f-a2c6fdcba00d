---
applyTo: '**'
---
# Notes to developers
This file is experimental. Please feel free to extend, improve or modify.

# Copilot Instructions

## General
- Keep this file readable and visible for humans.
- Use a friendly, informal tone. If you don't know something, just say so.

## Architecture & Structure
- The project is a modular monolith, with domain-focused modules in `src/main/kotlin/com/fleetmanagement/modules`.
- Modules expose their API via an `api` or `ports` package. Other modules must use these APIs for cross-module access.
- The FVM module (`modules/fvm`) acts as a backend-for-frontend, aggregating data for the UI.
- Data is aggregated via Postgres database views called DLZ (one for Vehicle, one for Vehicle Transfer). Modules contribute their own public views, which are joined in the DLZ.
- Module boundaries are enforced and checked with ArchUnit tests (see `src/test/kotlin/com/fleetmanagement/architecture`).

## Developer Workflow
- For integration tests, Testcontainers and EmbeddedKafka are used. See module-level README files for details.

# Testing
- Prefer classicist TDD (write tests first, avoid excessive mocking except for API clients).
- use <PERSON>ck<PERSON>, never use Mockito
- Use Junit5 for tests and assertions
- Do not test built-in functions!
- Prefer unit tests, use `@SpringBootTest` for integration tests and `@WebMvcTest` for controller tests.
- Use "ObjectMother" classes to generate test data (reference: `src/test/kotlin/com/fleetmanagement/vehicledata/ObjectMother.kt`).

## Conventions & Patterns
- Avoid unnecessary code, use the simplest solution that works.
- Follow DDD: model core domains, use ubiquitous language, and keep bounded contexts clear.
- Always read the README in a module before making changes. If missing, just continue.
- For database changes, update the liquibase migration scripts and module-level views.

## Integration & External Dependencies
- Use Quartz jobs for scheduling Jobs (reference [src/main/kotlin/com/fleetmanagement/modules/archival/job/configurations/VehicleArchiveJobConfig.kt](../../src/main/kotlin/com/fleetmanagement/modules/archival/job/configurations/VehicleArchiveJobConfig.kt)).
- To consume async messages from Kafka (reference: [src/main/kotlin/com/fleetmanagement/modules/vehicledata/integrations/pvh/streamzilla/PVHStreamzillaConsumer.kt](../../src/main/kotlin/com/fleetmanagement/modules/vehicledata/integrations/pvh/streamzilla/PVHStreamzillaConsumer.kt).
- To provide async messages via Kafka (reference: [src/main/kotlin/com/fleetmanagement/modules/masterdata/service/StreamzillaKafkaProducer.kt](../../src/main/kotlin/com/fleetmanagement/modules/masterdata/service/StreamzillaKafkaProducer.kt)
- Outbox pattern is used for async message delivery (see `docs/OUTBOX_PATTERN.md`).
- CI/CD pipeline is managed via GitLab and Terraform (see `terraform/README.md`).

## Quick Reference
- Main architecture: `README.md`, diagrams in `docs/images/`
- Local dev: `docs/LOCAL_DEVELOPMENT.md`
- Testing: `docs/TESTING.md`, BDD: `src/test/kotlin/com/fleetmanagement/bdd/README.md`
- API guidelines: `docs/API_GUIDELINES.md`
- Module patterns: module-level README files
- For a new module, see `docs/NEW_APPLICATION_MODULE.md`.
- For exposing APIs, see `docs/EXPOSE_MODULE_API.md`.
- For database write patterns, see `docs/DATABASE_WRITE.md`.
- For consuming Kafka, see `docs/STREAMZILLA_INTEGRATION.md`.
