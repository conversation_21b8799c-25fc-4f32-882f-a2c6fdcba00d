HELP.md
.gradle
build/
!gradle/wrapper/gradle-wrapper.jar
!**/src/main/**/build/
!**/src/test/**/build/

**/__pycache__/
**/venv/

### STS ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache
bin/
!**/src/main/**/bin/
!**/src/test/**/bin/

### IntelliJ IDEA ###
.idea
*.iws
*.iml
*.ipr
out/
!**/src/main/**/out/
!**/src/test/**/out/

### NetBeans ###
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/

### VS Code ###
.vscode/

.DS_Store
/.envrc
.env
/http-requests/http-client.private.env.json
**/*.private.env.json
/http-requests/outputs/
/http-requests/**/outputs/
.terraform/
.terraform.*

/libs/**
**/.DS_Store
./kotlin/**

### generated code ###
/**/generated/
/**/*generated*/
/api/

### test-results
/reports/**
/.kotlin/sessions/*
/.kotlin/errors/*

*.pfx

.github/plans/*
