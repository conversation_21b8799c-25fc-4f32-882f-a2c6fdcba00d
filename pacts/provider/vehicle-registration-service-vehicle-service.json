{"consumer": {"name": "vehicle-registration-service"}, "interactions": [{"description": "a request to get the vehicle object", "providerStates": [{"name": "a vehicle with VIN test-vin is already loaded"}], "request": {"headers": {"Authorization": "Bearer b25lLnR3by50aHJlZQ=="}, "matchingRules": {"header": {"Authorization": {"combine": "AND", "matchers": [{"match": "regex", "regex": "Bearer .*"}]}}}, "method": "GET", "path": "/vehicles/vin/test-vin"}, "response": {"body": {"consumption": {"driveType": "test-drive-type"}, "equiId": "TEST EQUI ID 111 222", "equipmentNumber": **********, "id": "6af8cd32-8e7f-4a7e-8651-dde5045316a7", "model": {"description": "test-model-description"}, "order": {"department": "test-department", "leasingType": "test-leasing-type"}, "vguid": "test-vguid", "vin": "test-vin"}, "headers": {"Content-Type": "application/json; charset=UTF-8"}, "matchingRules": {"body": {"$.consumption.driveType": {"combine": "AND", "matchers": [{"match": "regex", "regex": ".*"}]}, "$.equiId": {"combine": "AND", "matchers": [{"match": "regex", "regex": ".*"}]}, "$.equipmentNumber": {"combine": "AND", "matchers": [{"match": "number"}, {"match": "regex", "regex": ".*"}]}, "$.id": {"combine": "AND", "matchers": [{"match": "regex", "regex": ".*"}]}, "$.model.description": {"combine": "AND", "matchers": [{"match": "regex", "regex": ".*"}]}, "$.order.department": {"combine": "AND", "matchers": [{"match": "regex", "regex": ".*"}]}, "$.order.leasingType": {"combine": "AND", "matchers": [{"match": "regex", "regex": ".*"}]}, "$.vguid": {"combine": "AND", "matchers": [{"match": "regex", "regex": ".*"}]}, "$.vin": {"combine": "AND", "matchers": [{"match": "regex", "regex": ".*"}]}}, "header": {"Content-Type": {"combine": "AND", "matchers": [{"match": "regex", "regex": "application/json(;\\s?charset=[\\w\\-]+)?"}]}}}, "status": 200}}, {"description": "a request to get the vehicle object using equiId", "providerStates": [{"name": "a vehicle with equiId EQUI ID 111 222 is already loaded"}], "request": {"headers": {"Authorization": "Bearer b25lLnR3by50aHJlZQ=="}, "matchingRules": {"header": {"Authorization": {"combine": "AND", "matchers": [{"match": "regex", "regex": "Bearer .*"}]}}}, "method": "GET", "path": "/vehicles/find", "query": {"equiId": ["EQUI ID 111 222"]}}, "response": {"body": {"consumption": {"driveType": "test-drive-type"}, "equiId": "EQUI ID 111 222", "equipmentNumber": **********, "id": "6af8cd32-8e7f-4a7e-8651-dde5045316a7", "model": {"description": "test-model-description"}, "order": {"department": "test-department", "leasingType": "test-leasing-type"}, "vguid": "test-vguid", "vin": "test-vin"}, "headers": {"Content-Type": "application/json; charset=UTF-8"}, "matchingRules": {"body": {"$.consumption.driveType": {"combine": "AND", "matchers": [{"match": "regex", "regex": ".*"}]}, "$.equiId": {"combine": "AND", "matchers": [{"match": "regex", "regex": ".*"}]}, "$.equipmentNumber": {"combine": "AND", "matchers": [{"match": "number"}, {"match": "regex", "regex": ".*"}]}, "$.id": {"combine": "AND", "matchers": [{"match": "regex", "regex": ".*"}]}, "$.model.description": {"combine": "AND", "matchers": [{"match": "regex", "regex": ".*"}]}, "$.order.department": {"combine": "AND", "matchers": [{"match": "regex", "regex": ".*"}]}, "$.order.leasingType": {"combine": "AND", "matchers": [{"match": "regex", "regex": ".*"}]}, "$.vguid": {"combine": "AND", "matchers": [{"match": "regex", "regex": ".*"}]}, "$.vin": {"combine": "AND", "matchers": [{"match": "regex", "regex": ".*"}]}}, "header": {"Content-Type": {"combine": "AND", "matchers": [{"match": "regex", "regex": "application/json(;\\s?charset=[\\w\\-]+)?"}]}}}, "status": 200}}], "metadata": {"pact-jvm": {"version": "4.6.14"}, "pactSpecification": {"version": "3.0.0"}}, "provider": {"name": "vehicle-service"}}