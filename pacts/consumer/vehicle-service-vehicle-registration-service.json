{"consumer": {"name": "vehicle-service"}, "interactions": [{"description": "Get Registrations that are modified since specified date", "providerStates": [{"name": "Registration exists that has been modified since specified date"}], "request": {"headers": {"Authorization": "Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.***************************.dw1oz77jAV2AUdIv7MAiarGl1EmVM8HGJUPxCaC5GUyD6VLp3c8K58fbgOPnslpgDf8wmgiwr2KzgPPNXCX5ebxz3b_q-09fHirXn_8fhUV2GAbcvgW9aCL8LxmUH-zLbyBYWcdc-GGFucOVNCB7uP-nWHgjim7BLiyUn1XwRuJhZTaZtMGnAgZ8oTw83yznLFdjpZBD9NUGE_m_FlGT_7559ixUk1jQVPkDjRZldQWwjSSzHVwLpQXHgCoHhWhRykmTgyg8KERtwywvBJikQABOEYw592uP2cWl023g3reZu8xl-17ojSFUplz2J4zqMhqZptP3z7kpe0C_SQQlNw"}, "matchingRules": {"header": {"Authorization": {"combine": "AND", "matchers": [{"match": "regex", "regex": "Bearer eyJ[a-zA-Z0-9-_]+\\.[a-zA-Z0-9-_]+\\.[a-zA-Z0-9-_]+"}]}}, "path": {"combine": "AND", "matchers": [{"match": "regex", "regex": ".*/orders/registrations"}]}, "query": {"modifiedSince": {"combine": "AND", "matchers": [{"match": "regex", "regex": "\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}(:\\d{2})?Z"}]}}}, "method": "GET", "path": "/orders/registrations", "query": {"modifiedSince": ["2024-01-01T00:00:00Z"]}}, "response": {"body": {"data": [{"firstRegistrationDate": "2024-01-01T00:00:00Z", "hsn": "583", "licencePlate": "BB-PS 111", "registrationDate": "2024-01-01T00:00:00Z", "registrationStatus": "REGISTERED", "registrationType": 1, "sfme": true, "testNumber": 1, "tsn": "ANU00171", "vehicleId": "ba7dab53-f31e-4ef8-b9d7-db131840a336", "vin": "WP0AA2A97MS123456"}, {"firstRegistrationDate": "2024-01-02T00:00:00Z", "hsn": "583", "licencePlate": null, "registrationDate": "2024-01-01T00:00:00Z", "registrationStatus": "DE_REGISTERED", "registrationType": 4, "sfme": true, "testNumber": 2, "tsn": "ANU00171", "vehicleId": "ba7dab53-f31e-4ef8-b9d7-db131840a336", "vin": "WP0AA2A97MS123456"}]}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$.data[0].firstRegistrationDate": {"combine": "AND", "matchers": [{"match": "regex", "regex": ".*"}]}, "$.data[0].hsn": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].licencePlate": {"combine": "AND", "matchers": [{"match": "regex", "regex": ".*"}]}, "$.data[0].registrationDate": {"combine": "AND", "matchers": [{"match": "regex", "regex": ".*"}]}, "$.data[0].registrationStatus": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].registrationType": {"combine": "AND", "matchers": [{"match": "integer"}]}, "$.data[0].sfme": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].testNumber": {"combine": "AND", "matchers": [{"match": "integer"}]}, "$.data[0].tsn": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleId": {"combine": "AND", "matchers": [{"match": "regex", "regex": "^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}"}]}, "$.data[0].vin": {"combine": "AND", "matchers": [{"match": "regex", "regex": "^[A-HJ-NPR-Z0-9]{17}$"}]}, "$.data[1].firstRegistrationDate": {"combine": "AND", "matchers": [{"match": "regex", "regex": ".*"}]}, "$.data[1].hsn": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[1].registrationDate": {"combine": "AND", "matchers": [{"match": "regex", "regex": ".*"}]}, "$.data[1].registrationStatus": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[1].registrationType": {"combine": "AND", "matchers": [{"match": "integer"}]}, "$.data[1].sfme": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[1].testNumber": {"combine": "AND", "matchers": [{"match": "integer"}]}, "$.data[1].tsn": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[1].vehicleId": {"combine": "AND", "matchers": [{"match": "regex", "regex": "^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}"}]}, "$.data[1].vin": {"combine": "AND", "matchers": [{"match": "regex", "regex": "^[A-HJ-NPR-Z0-9]{17}$"}]}}}, "status": 200}}, {"description": "Get Completed Registration Orders for VehicleId", "providerStates": [{"name": "Vehicle has completed registration orders"}], "request": {"headers": {"Authorization": "Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.***************************.dw1oz77jAV2AUdIv7MAiarGl1EmVM8HGJUPxCaC5GUyD6VLp3c8K58fbgOPnslpgDf8wmgiwr2KzgPPNXCX5ebxz3b_q-09fHirXn_8fhUV2GAbcvgW9aCL8LxmUH-zLbyBYWcdc-GGFucOVNCB7uP-nWHgjim7BLiyUn1XwRuJhZTaZtMGnAgZ8oTw83yznLFdjpZBD9NUGE_m_FlGT_7559ixUk1jQVPkDjRZldQWwjSSzHVwLpQXHgCoHhWhRykmTgyg8KERtwywvBJikQABOEYw592uP2cWl023g3reZu8xl-17ojSFUplz2J4zqMhqZptP3z7kpe0C_SQQlNw"}, "matchingRules": {"header": {"Authorization": {"combine": "AND", "matchers": [{"match": "regex", "regex": "Bearer eyJ[a-zA-Z0-9-_]+\\.[a-zA-Z0-9-_]+\\.[a-zA-Z0-9-_]+"}]}}, "path": {"combine": "AND", "matchers": [{"match": "regex", "regex": ".*/orders/completed/ba7dab53-f31e-4ef8-b9d7-db131840a336$"}]}}, "method": "GET", "path": "/orders/completed/ba7dab53-f31e-4ef8-b9d7-db131840a336"}, "response": {"body": {"data": [{"firstRegistrationDate": "2024-01-01T00:00:00Z", "hsn": "583", "licencePlate": "BB-PS 111", "registrationDate": "2024-01-01T00:00:00Z", "registrationStatus": "REGISTERED", "registrationType": 1, "sfme": true, "testNumber": 1, "tsn": "ANU00171", "vehicleId": "ba7dab53-f31e-4ef8-b9d7-db131840a336", "vin": "WP0AA2A97MS123456"}, {"firstRegistrationDate": "2024-01-02T00:00:00Z", "hsn": "583", "licencePlate": null, "registrationDate": "2024-01-01T00:00:00Z", "registrationStatus": "DE_REGISTERED", "registrationType": 4, "sfme": true, "testNumber": 2, "tsn": "ANU00171", "vehicleId": "ba7dab53-f31e-4ef8-b9d7-db131840a336", "vin": "WP0AA2A97MS123456"}]}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$.data[0].firstRegistrationDate": {"combine": "AND", "matchers": [{"match": "regex", "regex": ".*"}]}, "$.data[0].hsn": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].licencePlate": {"combine": "AND", "matchers": [{"match": "regex", "regex": ".*"}]}, "$.data[0].registrationDate": {"combine": "AND", "matchers": [{"match": "regex", "regex": ".*"}]}, "$.data[0].registrationStatus": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].registrationType": {"combine": "AND", "matchers": [{"match": "integer"}]}, "$.data[0].sfme": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].testNumber": {"combine": "AND", "matchers": [{"match": "integer"}]}, "$.data[0].tsn": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleId": {"combine": "AND", "matchers": [{"match": "regex", "regex": "^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}"}]}, "$.data[0].vin": {"combine": "AND", "matchers": [{"match": "regex", "regex": "^[A-HJ-NPR-Z0-9]{17}$"}]}, "$.data[1].firstRegistrationDate": {"combine": "AND", "matchers": [{"match": "regex", "regex": ".*"}]}, "$.data[1].hsn": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[1].registrationDate": {"combine": "AND", "matchers": [{"match": "regex", "regex": ".*"}]}, "$.data[1].registrationStatus": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[1].registrationType": {"combine": "AND", "matchers": [{"match": "integer"}]}, "$.data[1].sfme": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[1].testNumber": {"combine": "AND", "matchers": [{"match": "integer"}]}, "$.data[1].tsn": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[1].vehicleId": {"combine": "AND", "matchers": [{"match": "regex", "regex": "^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}"}]}, "$.data[1].vin": {"combine": "AND", "matchers": [{"match": "regex", "regex": "^[A-HJ-NPR-Z0-9]{17}$"}]}}}, "status": 200}}, {"description": "Get Latest Registration Orders for VehicleIds", "providerStates": [{"name": "Vehicles with vehicle ids [12345678-1234-1234-1234-000000000111, 12345678-1234-1234-1234-000000000222] has registration date,licence plate,sfme and registration type information"}], "request": {"body": ["12345678-1234-1234-1234-000000000111", "12345678-1234-1234-1234-000000000222"], "headers": {"Authorization": "Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.***************************.dw1oz77jAV2AUdIv7MAiarGl1EmVM8HGJUPxCaC5GUyD6VLp3c8K58fbgOPnslpgDf8wmgiwr2KzgPPNXCX5ebxz3b_q-09fHirXn_8fhUV2GAbcvgW9aCL8LxmUH-zLbyBYWcdc-GGFucOVNCB7uP-nWHgjim7BLiyUn1XwRuJhZTaZtMGnAgZ8oTw83yznLFdjpZBD9NUGE_m_FlGT_7559ixUk1jQVPkDjRZldQWwjSSzHVwLpQXHgCoHhWhRykmTgyg8KERtwywvBJikQABOEYw592uP2cWl023g3reZu8xl-17ojSFUplz2J4zqMhqZptP3z7kpe0C_SQQlNw", "Content-Type": "application/json"}, "matchingRules": {"body": {"$[0]": {"combine": "AND", "matchers": [{"match": "regex", "regex": "^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}"}]}, "$[1]": {"combine": "AND", "matchers": [{"match": "regex", "regex": "^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}"}]}}, "header": {"Authorization": {"combine": "AND", "matchers": [{"match": "regex", "regex": "Bearer eyJ[a-zA-Z0-9-_]+\\.[a-zA-Z0-9-_]+\\.[a-zA-Z0-9-_]+"}]}}, "path": {"combine": "AND", "matchers": [{"match": "regex", "regex": ".*/orders/latest$"}]}}, "method": "POST", "path": "/orders/latest"}, "response": {"body": {"data": [{"lastRegistrationDate": "2024-01-01T00:00:00Z", "licencePlate": "BB-PS 111", "registrationType": 1, "sfme": true, "vehicleId": "12345678-1234-1234-1234-000000000111", "vin": "WP0AA2A97MS123456"}, {"lastRegistrationDate": "2024-01-02T00:00:00Z", "licencePlate": "BB-PS 222", "registrationType": 1, "sfme": true, "vehicleId": "12345678-1234-1234-1234-000000000222", "vin": "WP0AA2A97MS123456"}]}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$.data[0].lastRegistrationDate": {"combine": "AND", "matchers": [{"match": "regex", "regex": ".*"}]}, "$.data[0].licencePlate": {"combine": "AND", "matchers": [{"match": "regex", "regex": ".*"}]}, "$.data[0].registrationType": {"combine": "AND", "matchers": [{"match": "integer"}]}, "$.data[0].sfme": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleId": {"combine": "AND", "matchers": [{"match": "regex", "regex": "^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}"}]}, "$.data[0].vin": {"combine": "AND", "matchers": [{"match": "regex", "regex": "^[A-HJ-NPR-Z0-9]{17}$"}]}, "$.data[1].lastRegistrationDate": {"combine": "AND", "matchers": [{"match": "regex", "regex": ".*"}]}, "$.data[1].licencePlate": {"combine": "AND", "matchers": [{"match": "regex", "regex": ".*"}]}, "$.data[1].registrationType": {"combine": "AND", "matchers": [{"match": "integer"}]}, "$.data[1].sfme": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[1].vehicleId": {"combine": "AND", "matchers": [{"match": "regex", "regex": "^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}"}]}, "$.data[1].vin": {"combine": "AND", "matchers": [{"match": "regex", "regex": "^[A-HJ-NPR-Z0-9]{17}$"}]}}}, "status": 200}}, {"description": "a request to get registration periods", "providerStates": [{"name": "vehicle has registration periods"}], "request": {"headers": {"Authorization": "Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.***************************.dw1oz77jAV2AUdIv7MAiarGl1EmVM8HGJUPxCaC5GUyD6VLp3c8K58fbgOPnslpgDf8wmgiwr2KzgPPNXCX5ebxz3b_q-09fHirXn_8fhUV2GAbcvgW9aCL8LxmUH-zLbyBYWcdc-GGFucOVNCB7uP-nWHgjim7BLiyUn1XwRuJhZTaZtMGnAgZ8oTw83yznLFdjpZBD9NUGE_m_FlGT_7559ixUk1jQVPkDjRZldQWwjSSzHVwLpQXHgCoHhWhRykmTgyg8KERtwywvBJikQABOEYw592uP2cWl023g3reZu8xl-17ojSFUplz2J4zqMhqZptP3z7kpe0C_SQQlNw"}, "matchingRules": {"header": {"Authorization": {"combine": "AND", "matchers": [{"match": "regex", "regex": "Bearer eyJ[a-zA-Z0-9-_]+\\.[a-zA-Z0-9-_]+\\.[a-zA-Z0-9-_]+"}]}}, "path": {"combine": "AND", "matchers": [{"match": "regex", "regex": ".*/orders/registration-period/vehicle/8447bad4-2791-41b9-8a7b-33ae827b933c$"}]}, "query": {"activeAfter": {"combine": "AND", "matchers": [{"match": "regex", "regex": "\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}(:\\d{2})?Z"}]}}}, "method": "GET", "path": "/orders/registration-period/vehicle/8447bad4-2791-41b9-8a7b-33ae827b933c", "query": {"activeAfter": ["2024-01-01T00:00:00Z"]}}, "response": {"body": {"data": [{"fromDate": "2024-01-01T00:00:00Z", "licencePlate": "BB-PS 111", "toDate": "2024-01-01T00:00:00Z", "vehicleId": "8447bad4-2791-41b9-8a7b-33ae827b933c", "vin": "WP0AA2A97MS123456"}, {"fromDate": "2024-01-01T00:00:00Z", "licencePlate": "BB-PS 111", "toDate": null, "vehicleId": "8447bad4-2791-41b9-8a7b-33ae827b933c", "vin": "WP0AA2A97MS123456"}]}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$.data[0].fromDate": {"combine": "AND", "matchers": [{"format": "yyyy-MM-dd'T'HH:mm:ssXXX", "match": "date"}]}, "$.data[0].licencePlate": {"combine": "AND", "matchers": [{"match": "regex", "regex": ".*"}]}, "$.data[0].toDate": {"combine": "AND", "matchers": [{"format": "yyyy-MM-dd'T'HH:mm:ssXXX", "match": "date"}]}, "$.data[0].vehicleId": {"combine": "AND", "matchers": [{"match": "regex", "regex": "^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}"}]}, "$.data[0].vin": {"combine": "AND", "matchers": [{"match": "regex", "regex": "^[A-HJ-NPR-Z0-9]{17}$"}]}, "$.data[1].fromDate": {"combine": "AND", "matchers": [{"format": "yyyy-MM-dd'T'HH:mm:ssXXX", "match": "date"}]}, "$.data[1].licencePlate": {"combine": "AND", "matchers": [{"match": "regex", "regex": ".*"}]}, "$.data[1].vehicleId": {"combine": "AND", "matchers": [{"match": "regex", "regex": "^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}"}]}, "$.data[1].vin": {"combine": "AND", "matchers": [{"match": "regex", "regex": "^[A-HJ-NPR-Z0-9]{17}$"}]}}}, "status": 200}}, {"description": "a request to remove registration restriction", "providerStates": [{"name": "vehicle with vehicle-id 06e46b6b-a898-449e-82ca-6a8a5bbb9dfe has a previous restriction"}], "request": {"headers": {"Authorization": "Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.***************************.dw1oz77jAV2AUdIv7MAiarGl1EmVM8HGJUPxCaC5GUyD6VLp3c8K58fbgOPnslpgDf8wmgiwr2KzgPPNXCX5ebxz3b_q-09fHirXn_8fhUV2GAbcvgW9aCL8LxmUH-zLbyBYWcdc-GGFucOVNCB7uP-nWHgjim7BLiyUn1XwRuJhZTaZtMGnAgZ8oTw83yznLFdjpZBD9NUGE_m_FlGT_7559ixUk1jQVPkDjRZldQWwjSSzHVwLpQXHgCoHhWhRykmTgyg8KERtwywvBJikQABOEYw592uP2cWl023g3reZu8xl-17ojSFUplz2J4zqMhqZptP3z7kpe0C_SQQlNw"}, "matchingRules": {"header": {"Authorization": {"combine": "AND", "matchers": [{"match": "regex", "regex": "Bearer eyJ[a-zA-Z0-9-_]+\\.[a-zA-Z0-9-_]+\\.[a-zA-Z0-9-_]+"}]}}, "path": {"combine": "AND", "matchers": [{"match": "regex", "regex": ".*/vehicle-restrictions/06e46b6b-a898-449e-82ca-6a8a5bbb9dfe$"}]}}, "method": "DELETE", "path": "/vehicle-restrictions/06e46b6b-a898-449e-82ca-6a8a5bbb9dfe"}, "response": {"status": 204}}, {"description": "a request to restrict registrations", "providerStates": [{"name": "vehicle with vehicle-id 06e46b6b-a898-449e-82ca-6a8a5bbb9dfe has no previous restrictions"}], "request": {"headers": {"Authorization": "Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.***************************.dw1oz77jAV2AUdIv7MAiarGl1EmVM8HGJUPxCaC5GUyD6VLp3c8K58fbgOPnslpgDf8wmgiwr2KzgPPNXCX5ebxz3b_q-09fHirXn_8fhUV2GAbcvgW9aCL8LxmUH-zLbyBYWcdc-GGFucOVNCB7uP-nWHgjim7BLiyUn1XwRuJhZTaZtMGnAgZ8oTw83yznLFdjpZBD9NUGE_m_FlGT_7559ixUk1jQVPkDjRZldQWwjSSzHVwLpQXHgCoHhWhRykmTgyg8KERtwywvBJikQABOEYw592uP2cWl023g3reZu8xl-17ojSFUplz2J4zqMhqZptP3z7kpe0C_SQQlNw"}, "matchingRules": {"header": {"Authorization": {"combine": "AND", "matchers": [{"match": "regex", "regex": "Bearer eyJ[a-zA-Z0-9-_]+\\.[a-zA-Z0-9-_]+\\.[a-zA-Z0-9-_]+"}]}}, "path": {"combine": "AND", "matchers": [{"match": "regex", "regex": ".*/vehicle-restrictions/06e46b6b-a898-449e-82ca-6a8a5bbb9dfe$"}]}, "query": {"reasonCode": {"combine": "AND", "matchers": [{"match": "regex", "regex": ".*"}]}}}, "method": "POST", "path": "/vehicle-restrictions/06e46b6b-a898-449e-82ca-6a8a5bbb9dfe", "query": {"reasonCode": ["ACTIVE_VEHICLE_TRANSFER_FOR_PERSON"]}}, "response": {"status": 200}}], "metadata": {"pact-jvm": {"version": "4.6.17"}, "pactSpecification": {"version": "3.0.0"}}, "provider": {"name": "vehicle-registration-service"}}