#  This code is protected by intellectual property rights.
#  Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
#  © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.

from locust import task
from utils import check_response, handle_exception, BaseTestUser, VEHICLE_SEARCH_ENDPOINT

from .search_payloads import (
    monitoring_schrottfahrzeuge,
    identifizierung_schrottfahrzeuge,
    angebotene_schrottfahrzeuge,
    zur_verschrottung_freigegeben
)


class Scrapping(BaseTestUser):

    # @task
    # Disabled for now, as this there is no proper configuration from business
    def read_monitoring_schrottfahrzeuge_view(self):
        response = None
        try:
            with self.client.post(
                    VEHICLE_SEARCH_ENDPOINT,
                    name="Scrapping: Monitoring Schrottfahrzeuge (Default)",
                    cookies=self.cookies,
                    headers={"x-trace-id": 'monitoring-schrottfahrzeuge'},
                    json=monitoring_schrottfahrzeuge(),
                    catch_response=True
            ) as response:
                check_response(response)
        except Exception as e:
            handle_exception(e, "Failed to fetch data", response)

    @task
    def read_identifizierung_schrottfahrzeuge_view(self):
        response = None
        try:
            with self.client.post(
                    VEHICLE_SEARCH_ENDPOINT,
                    name="Scrapping: Identifizierung Schrottfahrzeuge (Default)",
                    cookies=self.cookies,
                    headers={"x-trace-id": 'identifizierung-schrottfahrzeuge'},
                    json=identifizierung_schrottfahrzeuge(),
                    catch_response=True
            ) as response:
                check_response(response)
        except Exception as e:
            handle_exception(e, "Failed to fetch data", response)

    @task
    def read_angebotene_schrottfahrzeuge_view(self):
        response = None
        try:
            with self.client.post(
                    VEHICLE_SEARCH_ENDPOINT,
                    name="Scrapping: Angebotene Schrottfahrzeuge (Default)",
                    cookies=self.cookies,
                    headers={"x-trace-id": 'angebotene-schrottfahrzeuge'},
                    json=angebotene_schrottfahrzeuge(),
                    catch_response=True
            ) as response:
                check_response(response)
        except Exception as e:
            handle_exception(e, "Failed to fetch data", response)

    @task
    def read_identifizierung_schrottfahrzeuge_view(self):
        response = None
        try:
            with self.client.post(
                    VEHICLE_SEARCH_ENDPOINT,
                    name="Scrapping: Zur Verschrottung freigegeben (Default)",
                    cookies=self.cookies,
                    headers={"x-trace-id": 'zur_verschrottung_freigegeben'},
                    json=zur_verschrottung_freigegeben(),
                    catch_response=True
            ) as response:
                check_response(response)
        except Exception as e:
            handle_exception(e, "Failed to fetch data", response)
