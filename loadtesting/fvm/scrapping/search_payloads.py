#  This code is protected by intellectual property rights.
#  Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
#  © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.

def monitoring_schrottfahrzeuge():
    return {
        "startRow": 0,
        "endRow": 150,
        "rowGroupCols": [],
        "valueCols": [],
        "pivotCols": [],
        "pivotMode": False,
        "groupKeys": [],
        "filterModel": {},
        "sortModel": []
    }


def identifizierung_schrottfahrzeuge():
    return {
        "startRow": 0,
        "endRow": 150,
        "rowGroupCols": [],
        "valueCols": [],
        "pivotCols": [],
        "pivotMode": False,
        "groupKeys": [],
        "filterModel": {},
        "sortModel": [
            {
                "sort": "desc",
                "colId": "vehicle.production.endDate"
            }
        ]
    }


def angebotene_schrottfahrzeuge():
    return {
        "startRow": 0,
        "endRow": 150,
        "rowGroupCols": [],
        "valueCols": [],
        "pivotCols": [],
        "pivotMode": False,
        "groupKeys": [],
        "filterModel": {},
        "sortModel": [
            {
                "sort": "asc",
                "colId": "vehicle.fleet.scrappedVehicleOfferedDate"
            }
        ]
    }


def zur_verschrottung_freigegeben():
    return {
        "startRow": 0,
        "endRow": 150,
        "rowGroupCols": [],
        "valueCols": [],
        "pivotCols": [],
        "pivotMode": False,
        "groupKeys": [],
        "filterModel": {},
        "sortModel": [
            {
                "sort": "asc",
                "colId": "vehicle.fleet.approvedForScrappingDate"
            }
        ]
    }
