#  This code is protected by intellectual property rights.
#  Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
#  © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.

from locust import task

from .search_payloads import (
    monitoring_nachlauf_nkfs
)
from utils import check_response, handle_exception, BaseTestUser, VEHICLE_SEARCH_ENDPOINT


class NonCustomerAdequate(BaseTestUser):

    @task
    def read_monitoring_nachlauf_nkfs_view(self):
        response = None
        try:
            with self.client.post(
                    VEHICLE_SEARCH_ENDPOINT,
                    name="Non-customer Adequate Vehicles: Monitoring Nachlauf NKFs (Default)",
                    cookies=self.cookies,
                    headers={"x-trace-id": 'monitoring-nachlauf-nkfs'},
                    json=monitoring_nachlauf_nkfs(),
                    catch_response=True
            ) as response:
                check_response(response)
        except Exception as e:
            handle_exception(e, "Failed to fetch data", response)
