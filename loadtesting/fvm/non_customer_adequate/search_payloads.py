#  This code is protected by intellectual property rights.
#  Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
#  © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.

def monitoring_nachlauf_nkfs():
    return {
        "startRow": 0,
        "endRow": 150,
        "rowGroupCols": [],
        "valueCols": [],
        "pivotCols": [],
        "pivotMode": False,
        "groupKeys": [],
        "filterModel": {
            "nonCustomerAdequate.ncaStatus": {
                "values": [
                    "NCA"
                ],
                "filterType": "set"
            }
        },
        "sortModel": [
            {
                "sort": "asc",
                "colId": "nonCustomerAdequate.profitabilityAuditDone"
            },
            {
                "sort": "asc",
                "colId": "vehicleTransfer.returnDate"
            }
        ]
    }
