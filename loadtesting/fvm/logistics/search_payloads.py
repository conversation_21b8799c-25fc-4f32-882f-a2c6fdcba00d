#  This code is protected by intellectual property rights.
#  Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
#  © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.

def default_tires():
    return {
        "startRow": 0,
        "endRow": 150,
        "rowGroupCols": [],
        "valueCols": [],
        "pivotCols": [],
        "pivotMode": False,
        "groupKeys": [],
        "filterModel": {
            "vehicleTransfer.status": {
                "values": [
                    "PLANNED"
                ],
                "filterType": "set"
            }
        },
        "sortModel": [
            {
                "sort": "asc",
                "colId": "vehicleTransfer.plannedDeliveryDate"
            }
        ]
    }


def logistik_default():
    return {
        "startRow": 0,
        "endRow": 150,
        "rowGroupCols": [],
        "valueCols": [],
        "pivotCols": [],
        "pivotMode": False,
        "groupKeys": [],
        "filterModel": {
            "preDeliveryInspection.plannedDate": {
                "dateFrom": None,
                "dateTo": None,
                "filterType": "date",
                "type": "notBlank"
            }
        },
        "sortModel": []
    }

def bereitstellung_default():
    return {
        "startRow": 0,
        "endRow": 150,
        "rowGroupCols": [],
        "valueCols": [],
        "pivotCols": [],
        "pivotMode": False,
        "groupKeys": [],
        "filterModel": {
            "vehicleTransfer.status": {
                "values": [
                    "PLANNED"
                ],
                "filterType": "set"
            },
            "vehicleTransfer.usageGroupId": {
                "values": [
                    "c9c5be95-9586-4188-a6f2-f0b579b41a3c"
                ],
                "filterType": "set"
            }
        },
        "sortModel": [
            {
                "sort": "asc",
                "colId": "vehicleTransfer.plannedDeliveryDate"
            }
        ]
    }
