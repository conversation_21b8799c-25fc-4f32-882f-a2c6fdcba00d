#  This code is protected by intellectual property rights.
#  Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
#  © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.

from locust import task
from utils import check_response, handle_exception, BaseTestUser, VEHICLE_TRANSFER_SEARCH_ENDPOINT

from .search_payloads import (
    default_tires,
    logistik_default,
    bereitstellung_default
)


class Logistics(BaseTestUser):

    @task
    def read_tires_default_view(self):
        response = None
        try:
            with self.client.post(
                    VEHICLE_TRANSFER_SEARCH_ENDPOINT,
                    name="Logistics: Standard-Ansicht (Default)",
                    cookies=self.cookies,
                    headers={"x-trace-id": 'tires-default-view'},
                    json=default_tires(),
                    catch_response=True
            ) as response:
                check_response(response)
        except Exception as e:
            handle_exception(e, "Failed to fetch data", response)

    @task
    def read_logistik_default_view(self):
        response = None
        try:
            with self.client.post(
                    VEHICLE_TRANSFER_SEARCH_ENDPOINT,
                    name="Logistics: Logistik (Default)",
                    cookies=self.cookies,
                    headers={"x-trace-id": 'logistik-default'},
                    json=logistik_default(),
                    catch_response=True
            ) as response:
                check_response(response)
        except Exception as e:
            handle_exception(e, "Failed to fetch data", response)

    @task
    def read_bereitstellung_view(self):
        response = None
        try:
            with self.client.post(
                    VEHICLE_TRANSFER_SEARCH_ENDPOINT,
                    name="Logistics: Bereitstellung (Default)",
                    cookies=self.cookies,
                    headers={"x-trace-id": 'bereitstellung-default'},
                    json=bereitstellung_default(),
                    catch_response=True
            ) as response:
                check_response(response)
        except Exception as e:
            handle_exception(e, "Failed to fetch data", response)
