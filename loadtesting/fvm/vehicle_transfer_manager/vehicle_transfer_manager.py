#  This code is protected by intellectual property rights.
#  Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
#  © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.

from locust import task

from .search_payloads import (
    default_vehicle_transfer_manager
)
from utils import check_response, handle_exception, BaseTestUser, VEHICLE_SEARCH_ENDPOINT


class VehicleTransferManager(BaseTestUser):

    @task
    def read_fahrzeugüberlassungen_view(self):
        response = None
        try:
            with self.client.post(
                    VEHICLE_SEARCH_ENDPOINT,
                    name="Vehicle Transfer: Fahrzeugüberlassungen (Default)",
                    cookies=self.cookies,
                    headers={"x-trace-id": 'vehicle-transfer-manager-default'},
                    json=default_vehicle_transfer_manager(),
                    catch_response=True
            ) as response:
                check_response(response)
        except Exception as e:
            handle_exception(e, "Failed to fetch data with four filters", response)
