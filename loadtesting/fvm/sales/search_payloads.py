#  This code is protected by intellectual property rights.
#  Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
#  © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.

def fahrzeuge_suchen_verkauf():
    return {
        "startRow": 0,
        "endRow": 150,
        "rowGroupCols": [],
        "valueCols": [],
        "pivotCols": [],
        "pivotMode": False,
        "groupKeys": [],
        "filterModel": {
            "vehicle.status": {
                "values": [
                    "S200",
                    "S300",
                    "S350",
                    "S400",
                    "S410",
                    "S420"
                ],
                "filterType": "set"
            }
        },
        "sortModel": [
            {
                "sort": "desc",
                "colId": "vehicleTransfer.returnDate"
            }
        ]
    }


def fahrzeuge_verkauft():
    return {
        "startRow": 0,
        "endRow": 150,
        "rowGroupCols": [],
        "valueCols": [],
        "pivotCols": [],
        "pivotMode": False,
        "groupKeys": [],
        "filterModel": {
            "vehicleSales.contractSigned": {
                "values": [
                    "true"
                ],
                "filterType": "set"
            },
            "vehicleSales.reservedForB2C": {
                "values": [
                    "true"
                ],
                "filterType": "set"
            },
            "vehicle.status": {
                "values": [
                    "S400",
                    "S410",
                    "S420"
                ],
                "filterType": "set"
            }
        },
        "sortModel": [
            {
                "sort": "asc",
                "colId": "vehicleTransfer.plannedReturnDate"
            },
            {
                "sort": "asc",
                "colId": "vehicleTransfer.returnDate"
            }
        ]
    }


def fahrzeuge_verkauf_faktura_vorbereiten():
    return {
        "startRow": 0,
        "endRow": 150,
        "rowGroupCols": [],
        "valueCols": [],
        "pivotCols": [],
        "pivotMode": False,
        "groupKeys": [],
        "filterModel": {
            "vehicleSales.contractSigned": {
                "values": [
                    "true"
                ],
                "filterType": "set"
            },
            "vehicleSales.reservedForB2C": {
                "values": [
                    "true"
                ],
                "filterType": "set"
            }
        },
        "sortModel": [
            {
                "sort": "desc",
                "colId": "vehicleSales.contractSigned"
            }
        ]
    }

def an_kunden_ausgelieferte_fahrzeuge():
    return {
        "startRow": 0,
        "endRow": 150,
        "rowGroupCols": [],
        "valueCols": [],
        "pivotCols": [],
        "pivotMode": False,
        "groupKeys": [],
        "filterModel": {},
        "sortModel": [
            {
                "sort": "desc",
                "colId": "vehicle.status"
            }
        ]
    }
