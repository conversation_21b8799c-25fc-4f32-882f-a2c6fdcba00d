#  This code is protected by intellectual property rights.
#  Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
#  © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.

from locust import task

from .search_payloads import (
    fahrzeuge_suchen_verkauf,
    fahrzeuge_verkauft,
    fahrzeuge_verkauf_faktura_vorbereiten,
    an_kunden_ausgelieferte_fahrzeuge
)
from utils import check_response, handle_exception, BaseTestUser, VEHICLE_SEARCH_ENDPOINT


class Sales(BaseTestUser):

    @task
    def read_fahrzeuge_suchen_verkauf_view(self):
        response = None
        try:
            with self.client.post(
                    VEHICLE_SEARCH_ENDPOINT,
                    name="Sales: Fahrzeuge suchen (Verkauf) (Default)",
                    cookies=self.cookies,
                    headers={"x-trace-id": 'fahrzeuge-suchen-verkauf'},
                    json=fahrzeuge_suchen_verkauf(),
                    catch_response=True
            ) as response:
                check_response(response)
        except Exception as e:
            handle_exception(e, "Failed to fetch data", response)

    @task
    def read_fahrzeuge_verkauft_view(self):
        response = None
        try:
            with self.client.post(
                    VEHICLE_SEARCH_ENDPOINT,
                    name="Sales: Fahrzeuge Verkauft",
                    cookies=self.cookies,
                    headers={"x-trace-id": 'fahrzeuge-verkauft'},
                    json=fahrzeuge_verkauft(),
                    catch_response=True
            ) as response:
                check_response(response)
        except Exception as e:
            handle_exception(e, "Failed to fetch data", response)

    @task
    def read_fahrzeuge_verkauf_faktura_vorbereiten_view(self):
        response = None
        try:
            with self.client.post(
                    VEHICLE_SEARCH_ENDPOINT,
                    name="Sales: Fahrzeuge für Verkauf/Faktura Vorbereiten (Default)",
                    cookies=self.cookies,
                    headers={"x-trace-id": 'fahrzeuge-verkauf-faktura-vorbereiten'},
                    json=fahrzeuge_verkauf_faktura_vorbereiten(),
                    catch_response=True
            ) as response:
                check_response(response)
        except Exception as e:
            handle_exception(e, "Failed to fetch data", response)

    @task
    def read_an_kunden_ausgelieferte_fahrzeuge_view(self):
        response = None
        try:
            with self.client.post(
                    VEHICLE_SEARCH_ENDPOINT,
                    name="Sales: An Kunden ausgelieferte Fahrzeuge (Default)",
                    cookies=self.cookies,
                    headers={"x-trace-id": 'an-kunden-ausgelieferte-fahrzeuge'},
                    json=an_kunden_ausgelieferte_fahrzeuge(),
                    catch_response=True
            ) as response:
                check_response(response)
        except Exception as e:
            handle_exception(e, "Failed to fetch data", response)
