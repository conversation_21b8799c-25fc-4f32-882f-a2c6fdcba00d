#  This code is protected by intellectual property rights.
#  Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
#  © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.

from locust import task

from .search_payloads import (
    pdi_dienst_leasing,
    auslieferung_dienst_leasing,
    pdi_abteilung_fahrzeuge,
    rücknahme_dienst_leasing,
    auslieferung_abteilung,
    auslieferung_entwicklung
)
from utils import BaseTestUser, check_response, handle_exception, VEHICLE_TRANSFER_SEARCH_ENDPOINT


class ClientManagement(BaseTestUser):
    """
    This class defines various load testing scenarios for the preparation of delivery,
    testing different filter combinations and performance characteristics.
    """

    @task
    def read_pdi_dienst_leasing_view(self):
        response = None
        try:
            with self.client.post(
                    VEHICLE_TRANSFER_SEARCH_ENDPOINT,
                    name="ClientManagement: PDI Dienst/Leasing",
                    cookies=self.cookies,
                    headers={"x-trace-id": "pdi-dienst-leasing"},
                    json=pdi_dienst_leasing(),
                    catch_response=True
            ) as response:
                check_response(response)
        except Exception as e:
            handle_exception(e, "Failed to fetch data", response)

    @task
    def read_auslieferung_dienst_leasing_view(self):
        response = None
        try:
            with self.client.post(
                    VEHICLE_TRANSFER_SEARCH_ENDPOINT,
                    name="ClientManagement: Auslieferung Dienst/Leasing",
                    cookies=self.cookies,
                    headers={"x-trace-id": "auslieferung-dienst-leasing"},
                    json=auslieferung_dienst_leasing(),
                    catch_response=True
            ) as response:
                check_response(response)
        except Exception as e:
            handle_exception(e, "Failed to fetch data", response)

    @task
    def read_pdi_abteilung_fahrzeuge_view(self):
        response = None
        try:
            with self.client.post(
                    VEHICLE_TRANSFER_SEARCH_ENDPOINT,
                    name="ClientManagement: PDI Abteilungfahrzeuge",
                    cookies=self.cookies,
                    headers={"x-trace-id": "pdi-abteilung-fahrzeuge"},
                    json=pdi_abteilung_fahrzeuge(),
                    catch_response=True
            ) as response:
                check_response(response)
        except Exception as e:
            handle_exception(e, "Failed to fetch data", response)

    @task
    def read_rücknahme_dienst_leasing_view(self):
        response = None
        try:
            with self.client.post(
                    VEHICLE_TRANSFER_SEARCH_ENDPOINT,
                    name="ClientManagement: Rücknahme Dienst/Leasing",
                    cookies=self.cookies,
                    headers={"x-trace-id": "rücknahme-dienst-leasing"},
                    json=rücknahme_dienst_leasing(),
                    catch_response=True
            ) as response:
                check_response(response)
        except Exception as e:
            handle_exception(e, "Failed to fetch data", response)

    @task
    def read_auslieferung_abteilung_view(self):
        response = None
        try:
            with self.client.post(
                    VEHICLE_TRANSFER_SEARCH_ENDPOINT,
                    name="ClientManagement: Auslieferung Abteilung",
                    cookies=self.cookies,
                    headers={"x-trace-id": "auslieferung-abteilung"},
                    json=auslieferung_abteilung(),
                    catch_response=True
            ) as response:
                check_response(response)
        except Exception as e:
            handle_exception(e, "Failed to fetch data", response)

    @task
    def read_auslieferung_entwicklung_view(self):
        response = None
        try:
            with self.client.post(
                    VEHICLE_TRANSFER_SEARCH_ENDPOINT,
                    name="ClientManagement: Auslieferung Entwicklung",
                    cookies=self.cookies,
                    headers={"x-trace-id": "auslieferung-entwicklung"},
                    json=auslieferung_entwicklung(),
                    catch_response=True
            ) as response:
                check_response(response)
        except Exception as e:
            handle_exception(e, "Failed to fetch data", response)
