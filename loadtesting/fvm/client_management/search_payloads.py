#  This code is protected by intellectual property rights.
#  Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
#  © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.

from typing import Dict, Any


def pdi_dienst_leasing() -> Dict[str, Any]:
    """
    Test filter performance in tables that the vehicle-master-data
    has a JOIN clause with.

    Returns:
        dict: Search payload with filters on 4 domain fields and 1 sort
    """
    return {
        "startRow": 0,
        "endRow": 150,
        "rowGroupCols": [],
        "valueCols": [],
        "pivotCols": [],
        "pivotMode": False,
        "groupKeys": [],
        "filterModel": {
            "vehicleTransfer.status": {
                "values": [
                    "PLANNED"
                ],
                "filterType": "set"
            },
            "vehicleTransfer.usageGroupId": {
                "values": [
                    "c9c5be95-9586-4188-a6f2-f0b579b41a3c"
                ],
                "filterType": "set"
            },
            "preDeliveryInspection.isRelevant": {
                "values": [
                    "true"
                ],
                "filterType": "set"
            },
            "preDeliveryInspection.orderedDate": {
                "dateFrom": None,
                "dateTo": None,
                "filterType": "date",
                "type": "blank"
            },
            "preDeliveryInspection.plannedDate": {
                "dateFrom": None,
                "dateTo": None,
                "filterType": "date",
                "type": "blank"
            },
            "preDeliveryInspection.completedDate": {
                "dateFrom": None,
                "dateTo": None,
                "filterType": "date",
                "type": "blank"
            }
        },
        "sortModel": [
            {
                "sort": "asc",
                "colId": "vehicle.production.endDate"
            }
        ]
    }


def auslieferung_dienst_leasing() -> Dict[str, Any]:
    return {
        "startRow": 0,
        "endRow": 150,
        "rowGroupCols": [],
        "valueCols": [],
        "pivotCols": [],
        "pivotMode": False,
        "groupKeys": [],
        "filterModel": {
            "vehicleTransfer.status": {
                "values": [
                    "PLANNED"
                ],
                "filterType": "set"
            },
            "vehicleTransfer.usageGroupId": {
                "values": [
                    "c9c5be95-9586-4188-a6f2-f0b579b41a3c"
                ],
                "filterType": "set"
            },
            "preDeliveryInspection.orderedDate": {
                "dateFrom": None,
                "dateTo": None,
                "filterType": "date",
                "type": "notBlank"
            }
        },
        "sortModel": [
            {
                "sort": "asc",
                "colId": "preDeliveryInspection.orderedDate"
            }
        ]
    }


def pdi_abteilung_fahrzeuge() -> Dict[str, Any]:
    return {
        "startRow": 0,
        "endRow": 150,
        "rowGroupCols": [],
        "valueCols": [],
        "pivotCols": [],
        "pivotMode": False,
        "groupKeys": [],
        "filterModel": {
            "vehicleTransfer.status": {
                "values": [
                    "PLANNED"
                ],
                "filterType": "set"
            },
            "vehicleTransfer.usageGroupId": {
                "values": [
                    "e18b81fc-57a5-4b31-a95d-0ee70bdb7646"
                ],
                "filterType": "set"
            },
            "preDeliveryInspection.isRelevant": {
                "values": [
                    "true"
                ],
                "filterType": "set"
            },
            "preDeliveryInspection.orderedDate": {
                "dateFrom": None,
                "dateTo": None,
                "filterType": "date",
                "type": "blank"
            },
            "preDeliveryInspection.plannedDate": {
                "dateFrom": None,
                "dateTo": None,
                "filterType": "date",
                "type": "blank"
            },
            "preDeliveryInspection.completedDate": {
                "dateFrom": None,
                "dateTo": None,
                "filterType": "date",
                "type": "blank"
            }
        },
        "sortModel": [
            {
                "sort": "asc",
                "colId": "vehicle.production.endDate"
            }
        ]
    }


def rücknahme_dienst_leasing() -> Dict[str, Any]:
    return {
        "startRow": 0,
        "endRow": 150,
        "rowGroupCols": [],
        "valueCols": [],
        "pivotCols": [],
        "pivotMode": False,
        "groupKeys": [],
        "filterModel": {
            "vehicleTransfer.status": {
                "values": [
                    "ACTIVE"
                ],
                "filterType": "set"
            },
            "vehicleTransfer.usageGroupId": {
                "values": [
                    "c9c5be95-9586-4188-a6f2-f0b579b41a3c"
                ],
                "filterType": "set"
            },
            "vehicleTransfer.plannedReturnDate": {
                "dateFrom": None,
                "dateTo": None,
                "filterType": "date",
                "type": "blank"
            },
            "vehicleTransfer.successorOrderDate": {
                "dateFrom": None,
                "dateTo": None,
                "filterType": "date",
                "type": "blank"
            }
        },
        "sortModel": [
            {
                "sort": "asc",
                "colId": "vehicleTransfer.latestReturnDate"
            }
        ]
    }


def auslieferung_abteilung() -> Dict[str, Any]:
    return {
        "startRow": 0,
        "endRow": 150,
        "rowGroupCols": [],
        "valueCols": [],
        "pivotCols": [],
        "pivotMode": False,
        "groupKeys": [],
        "filterModel": {
            "vehicleTransfer.status": {
                "values": [
                    "PLANNED"
                ],
                "filterType": "set"
            },
            "vehicleTransfer.usageGroupId": {
                "values": [
                    "c9c5be95-9586-4188-a6f2-f0b579b41a3c"
                ],
                "filterType": "set"
            },
            "preDeliveryInspection.orderedDate": {
                "dateFrom": None,
                "dateTo": None,
                "filterType": "date",
                "type": "notBlank"
            },
            "vehicleTransfer.vehicleUsageId": {
                "values": [
                    "0ac32a95-97fa-4cd5-95c9-8ebce51ef49e",
                    "1d4ad375-4ae1-499e-8a7d-406a5dbe7caa",
                    "2ef69405-7e5d-4279-968c-9fac4f907e1f",
                    "4813a87b-4f70-4f8d-8d70-305b986838cb",
                    "8bb3c735-1d91-45b8-a4fd-3bbba5258981",
                    "94b91a4d-fd16-47a0-a8d1-a09514d97ff3",
                    "a8106fbd-ac03-4bcd-b9a7-320b1a111f66",
                    "b6464bf9-caf5-4d4c-be39-98afe910c162",
                    "dfc8978c-7eb7-460c-bf3c-672d1fb3df00",
                    "e6fc7ca2-57ba-4931-8c1a-ca24b8fbebd1",
                    "f35dd48c-a866-4b83-a3a1-28271e48da29",
                    "fb798ca4-1c91-43e6-8a0f-28e2a171281e"
                ],
                "filterType": "set"
            }
        },
        "sortModel": [
            {
                "sort": "asc",
                "colId": "preDeliveryInspection.orderedDate"
            }
        ]
    }


def auslieferung_entwicklung() -> Dict[str, Any]:
    return {
        "startRow": 0,
        "endRow": 150,
        "rowGroupCols": [],
        "valueCols": [],
        "pivotCols": [],
        "pivotMode": False,
        "groupKeys": [],
        "filterModel": {
            "vehicleTransfer.vehicleUsageId": {
                "values": [
                    "621ce78a-bed0-47d7-8a04-76ff778a8fb0"
                ],
                "filterType": "set"
            }
        },
        "sortModel": [
            {
                "sort": "asc",
                "colId": "preDeliveryInspection.orderedDate"
            }
        ]
    }
