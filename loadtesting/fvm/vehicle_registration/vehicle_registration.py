#  This code is protected by intellectual property rights.
#  Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
#  © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.

from locust import task
from utils import BaseTestUser
from utils import check_response, handle_exception, VEHICLE_SEARCH_ENDPOINT, VEHICLE_REGISTRATION_SEARCH_ENDPOINT, BaseTestUser

from .search_payloads import (
    default_vehicle_registration,
    versichertenkarte_vollmacht,
    drucken_default,
    erprobungsnummer_erneuern)


class VehicleRegistration(BaseTestUser):

    @task
    def read_zulassung_importieren_view(self):
        response = None
        try:
            with self.client.post(
                    VEHICLE_REGISTRATION_SEARCH_ENDPOINT,
                    name="VehicleRegistration: Zulassung Importieren (Default)",
                    cookies=self.cookies,
                    headers={"x-trace-id": 'zulassung-importieren-default'},
                    json=default_vehicle_registration(),
                    catch_response=True
            ) as response:
                check_response(response)
        except Exception as e:
            handle_exception(e, "Failed to fetch data with four filters", response)

    @task
    def read_drucken_view(self):
        response = None
        try:
            with self.client.post(
                    VEHICLE_SEARCH_ENDPOINT,
                    name="VehicleRegistration: Drucken (Default)",
                    cookies=self.cookies,
                    headers={"x-trace-id": 'drucken-default'},
                    json=drucken_default(),
                    catch_response=True
            ) as response:
                check_response(response)
        except Exception as e:
            handle_exception(e, "Failed to fetch data with four filters", response)

    @task
    def read_versichertenkarte_vollmacht_view(self):
        response = None
        try:
            with self.client.post(
                    VEHICLE_SEARCH_ENDPOINT,
                    name="VehicleRegistration: Versichertenkarte und Vollmacht",
                    cookies=self.cookies,
                    headers={"x-trace-id": 'versichertenkarte-vollmacht'},
                    json=versichertenkarte_vollmacht(),
                    catch_response=True
            ) as response:
                check_response(response)
        except Exception as e:
            handle_exception(e, "Failed to fetch data with four filters", response)

    @task
    def read_erprobungsnummer_erneuern_view(self):
        response = None
        try:
            with self.client.post(
                    VEHICLE_SEARCH_ENDPOINT,
                    name="VehicleRegistration: Erprobungsnummer erneuern (Default)",
                    cookies=self.cookies,
                    headers={"x-trace-id": 'erprobungsnummer-erneuern'},
                    json=erprobungsnummer_erneuern(),
                    catch_response=True
            ) as response:
                check_response(response)
        except Exception as e:
            handle_exception(e, "Failed to fetch data with four filters", response)
