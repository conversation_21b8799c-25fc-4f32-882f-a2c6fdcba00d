#  This code is protected by intellectual property rights.
#  Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
#  © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.

def default_vehicle_registration():
    return {
        "startRow": 0,
        "endRow": 150,
        "rowGroupCols": [],
        "valueCols": [],
        "pivotCols": [],
        "pivotMode": False,
        "groupKeys": [],
        "filterModel": {
            "orderStatus": {
                "filterType": "text",
                "type": "equals",
                "filter": "COMPLETED"
            }
        },
        "sortModel": []
    }

def versichertenkarte_vollmacht():
    return {
        "startRow": 0,
        "endRow": 150,
        "rowGroupCols": [],
        "valueCols": [],
        "pivotCols": [],
        "pivotMode": False,
        "groupKeys": [],
        "filterModel": {
            "vehicleRegistration.firstRegistrationDate": {
                "dateFrom": None,
                "dateTo": None,
                "filterType": "date",
                "type": "notBlank"
            }
        },
        "sortModel": [
            {
                "sort": "desc",
                "colId": "vehicleRegistration.lastRegistrationDate"
            }
        ]
    }

def drucken_default():
    return {
        "startRow": 0,
        "endRow": 150,
        "rowGroupCols": [],
        "valueCols": [],
        "pivotCols": [],
        "pivotMode": False,
        "groupKeys": [],
        "filterModel": {
            "vehicleRegistration.firstRegistrationDate": {
                "dateFrom": None,
                "dateTo": None,
                "filterType": "date",
                "type": "notBlank"
            }
        },
        "sortModel": [
            {
                "sort": "desc",
                "colId": "vehicleRegistration.lastRegistrationDate"
            }
        ]
    }

def erprobungsnummer_erneuern():
    return {
        "startRow": 0,
        "endRow": 150,
        "rowGroupCols": [],
        "valueCols": [],
        "pivotCols": [],
        "pivotMode": False,
        "groupKeys": [],
        "filterModel": {
            "vehicleRegistration.registrationStatus": {
                "values": [
                    "REGISTERED"
                ],
                "filterType": "set"
            }
        },
        "sortModel": []
    }
