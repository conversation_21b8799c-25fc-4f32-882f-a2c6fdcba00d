#  This code is protected by intellectual property rights.
#  Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
#  © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.

from locust import task

from utils import BaseTestUser, check_response, handle_exception, PEOPLE_SEARCH_ENDPOINT


def search_people_without_sort():
    """Search people data without sorting."""
    return {
        "startRow": 0,
        "endRow": 150,
        "rowGroupCols": [],
        "valueCols": [],
        "pivotCols": [],
        "pivotMode": False,
        "groupKeys": [],
        "filterModel": {},
        "sortModel": []
    }


def search_people_with_filter_and_sort():
    """Search people data with filter and sorting."""
    return {
        "startRow": 0,
        "endRow": 50,
        "rowGroupCols": [],
        "valueCols": [],
        "pivotCols": [],
        "pivotMode": False,
        "groupKeys": [],
        "filterModel": {
            "peopleData.city": {
                "filterType": "text",
                "type": "contains",
                "filter": "Winterbach"
            }
        },
        "sortModel": [{"sort": "asc", "colId": "peopleData.city"}]
    }


class PeopleManager(BaseTestUser):
    """Load testing scenarios for people manager functionality."""
    # Required as all tests are disabled
    abstract = True

    # @task
    # Disabled for now, as this there is no proper configuration from business
    def search_people_without_sort(self):
        """Test people search without sorting."""
        response = None
        try:
            with self.client.post(
                    PEOPLE_SEARCH_ENDPOINT,
                    name="People Manager: Search without sort",
                    cookies=self.cookies,
                    headers={"x-trace-id": "people-manager-without-sort"},
                    json=search_people_without_sort(),
                    catch_response=True
            ) as response:
                check_response(response)
        except Exception as e:
            handle_exception(e, "Failed to search people without sort", response)

    # @task
    # Disabled for now, as this there is no proper configuration from business
    def search_people_with_filter_and_sort(self):
        """Test people search with filter and sorting."""
        response = None
        try:
            with self.client.post(
                    PEOPLE_SEARCH_ENDPOINT,
                    name="People Manager: Search with filter and sort",
                    cookies=self.cookies,
                    headers={"x-trace-id": "people-manager-with-sort"},
                    json=search_people_with_filter_and_sort(),
                    catch_response=True
            ) as response:
                check_response(response)
        except Exception as e:
            handle_exception(e, "Failed to search people with filter and sort", response)
