#  This code is protected by intellectual property rights.
#  Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
#  © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.

import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S"
)

# Enable performance validation for all test classes
from utils import performance_validator

# Ensure performance validator is loaded (prevents unused import warning)
assert performance_validator is not None

# Import test classes from different modules
from client_management import ClientManagement
from vehicle_manager import VehicleManager
from vehicle_transfer_manager import VehicleTransferManager
from vehicle_registration import VehicleRegistration
from people_manager import PeopleManager
from logistics import Logistics
from vehicle_evaluation import VehicleEvaluation
from scrapping import Scrapping
from sales import Sales
from non_customer_adequate import NonCustomerAdequate

__all__ = [
    "ClientManagement",
    "VehicleManager",
    "PeopleManager",
    "VehicleRegistration",
    "VehicleTransferManager",
    "Logistics",
    "VehicleEvaluation",
    "Scrapping",
    "Sales",
    "NonCustomerAdequate"
]
