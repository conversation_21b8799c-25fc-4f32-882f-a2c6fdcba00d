#  This code is protected by intellectual property rights.
#  Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
#  © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.

import logging
import threading

from locust import HttpUser
from playwright.sync_api import sync_playwright

from .config import AZURE_AD_TECHNICAL_USER_NAME, AZURE_AD_TECHNICAL_USER_SECRET
from .config import HOST

# Global variables for shared authentication
_shared_cookies = None
_auth_lock = threading.Lock()
_auth_initialized = False


def get_shared_cookies() -> dict:
    """
    Get shared authentication cookies. Only authenticate once per test run.

    Returns:
        dict: Dictionary containing authentication cookies
    """
    global _shared_cookies, _auth_initialized

    with _auth_lock:
        if not _auth_initialized:
            logging.info("Performing one-time authentication for all users...")
            _shared_cookies = automate_login_and_get_cookies(
                AZURE_AD_TECHNICAL_USER_NAME,
                AZURE_AD_TECHNICAL_USER_SECRET
            )
            _auth_initialized = True
            logging.info("Authentication completed and cached for all users")

        return _shared_cookies.copy() if _shared_cookies else {}


def automate_login_and_get_cookies(email: str, password: str) -> dict:
    """
    Authenticate using Playwright and return cookies.
    This should only be called once per test run.
    """
    with sync_playwright() as p:
        # Launch browser in headless mode (background mode)
        browser = p.chromium.launch(headless=True)  # Set to False for debugging
        context = browser.new_context()

        # Open a new page
        page = context.new_page()

        try:
            # Step 1: Navigate to EMH login page
            page.goto(HOST)
            page.click("p-button#login-button")

            # Step 2: Enter email/username
            page.fill("input[type='email']", email)
            page.click("input[type='submit']")

            # Step 3: Wait for password field to load and enter password
            page.wait_for_selector("input[type='password']")
            page.fill("input[type='password']", password)
            page.click("input[type='submit']")

            # Step 4: Wait for redirection to the target application
            with page.expect_navigation():
                page.locator('input[type=submit]').click()

            # Step 5: Retrieve AWS ALB cookies
            cookies = context.cookies()
            alb_cookie_dict = {}
            for cookie in cookies:
                if "AWSELBAuthSessionCookie" in cookie['name']:
                    alb_cookie_dict[cookie['name']] = cookie['value']

            logging.info(f"Successfully retrieved {len(alb_cookie_dict)} authentication cookies")
            return alb_cookie_dict

        except Exception as e:
            logging.error(f"Authentication failed: {e}")
            raise
        finally:
            # Close the browser
            browser.close()


class BaseTestUser(HttpUser):
    """
    Base class for all load testing scenarios with performance validation enabled.

    This class automatically enables performance validation by importing the
    performance_validator module and provides common authentication setup.
    """

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.cookies = None

    def on_start(self):
        """Set up authentication cookies when the test starts."""
        self.cookies = get_shared_cookies()
