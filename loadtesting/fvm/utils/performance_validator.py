#  This code is protected by intellectual property rights.
#  Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
#  © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.

"""
Performance validation plugin for Locust tests.

This module provides automatic performance validation for all Locust tests.
Simply importing this module will enable performance validation.
"""

import logging

from locust import events

from .config import RESPONSE_TIME_THRESHOLD_IN_MS
from .utils import log_performance_metrics


def on_test_stop(environment, **kwargs):
    """
    Handle test stop event to evaluate performance metrics and determine pass/fail status.

    Args:
        environment: Locust environment object
        **kwargs: Additional keyword arguments
    """
    logging.info("Starting performance validation...")

    # Get the 95th percentile response time for all tasks and check if any exceed the threshold
    test_failed = False

    # Check all individual task stats
    for stat_entry in environment.runner.stats.entries.values():
        if stat_entry.method:  # Only check actual HTTP requests, not aggregated stats
            response_time_95 = stat_entry.get_response_time_percentile(0.95)
            if response_time_95 > RESPONSE_TIME_THRESHOLD_IN_MS:
                logging.error(
                    f"Task '{stat_entry.name}' ({stat_entry.method}) failed: 95th percentile response time {response_time_95}ms exceeds threshold {RESPONSE_TIME_THRESHOLD_IN_MS}ms")
                test_failed = True
            else:
                logging.info(
                    f"Task '{stat_entry.name}' ({stat_entry.method}) passed: 95th percentile response time {response_time_95}ms")

    # Check if there are any failed requests
    total_failures = environment.runner.stats.total.num_failures

    # Log performance metrics for overall stats
    overall_response_time_95 = environment.runner.stats.total.get_response_time_percentile(0.95)
    log_performance_metrics(overall_response_time_95, total_failures)

    # Determine a test result based on thresholds
    if total_failures > 0 or test_failed:
        logging.error("Test Failed - Performance validation criteria not met")
        environment.process_exit_code = 1
    else:
        logging.info("Test Passed - All performance validation criteria met")


# Auto-register the event handler when this module is imported
events.test_stop.add_listener(on_test_stop)
logging.info("Performance validation enabled automatically")
