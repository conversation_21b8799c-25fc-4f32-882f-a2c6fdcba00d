#  This code is protected by intellectual property rights.
#  Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
#  © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.

import os

# Host configuration
HOST = os.getenv("HOST", "https://mobilityservicesdev.porsche.services/")

# Performance thresholds
RESPONSE_TIME_THRESHOLD_IN_MS = int(os.getenv("RESPONSE_TIME_THRESHOLD_IN_MS", "400"))

# Authentication credentials
AZURE_AD_TECHNICAL_USER_NAME = os.getenv("AZURE_AD_TECHNICAL_USER_NAME")
AZURE_AD_TECHNICAL_USER_SECRET = os.getenv("AZURE_AD_TECHNICAL_USER_SECRET")

# API endpoints
VEHICLE_SEARCH_ENDPOINT = "/api/vs/ui/vehicles/search"
VEHICLE_TRANSFER_SEARCH_ENDPOINT = "/api/vs/ui/vehicle-transfers/search"
PEOPLE_SEARCH_ENDPOINT = "/api/vs/ui/people/search"
VEHICLE_REGISTRATION_SEARCH_ENDPOINT= "/api/vs/ui/vehicleregistration/orders/search"

