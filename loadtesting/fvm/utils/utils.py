#  This code is protected by intellectual property rights.
#  Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
#  © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.

"""Utility functions for response validation and error handling."""

import logging
import traceback
from typing import Any


def check_response(response: Any) -> None:
    """
    Validate HTTP response and check for expected content.

    Args:
        response: HTTP response object from Locust
    """
    try:
        # Check if response is successful first
        if response.status_code >= 400:
            logging.error(f"HTTP error: {response.status_code}")
            response.failure(f"HTTP error: {response.status_code}")
            return

        # Check content type
        content_type = response.headers.get('Content-Type', '')
        if 'application/json' not in content_type:
            logging.error(f"Unexpected content type: {content_type}")
            response.failure(f"Unexpected content type: {content_type}")
            return

        # Try to parse JSON and validate data
        try:
            json_data = response.json()
            if json_data.get("data") is None:
                logging.error("Response data is None")
                response.failure("Response data is None")
        except ValueError as json_error:
            logging.error(f"Invalid JSON response: {json_error}")
            response.failure(f"Invalid JSON response: {json_error}")

    except Exception as e:
        handle_exception(e, "Response validation failed", response)


def handle_exception(exception: Exception, error_message: str, response: Any = None) -> None:
    """
    Handle exceptions with proper logging and response failure marking.

    Args:
        exception: The exception that occurred
        error_message: Custom error message to log
        response: HTTP response object from Locust
    """
    full_error_message = f"{error_message}: {exception}\n{traceback.format_exc()}"
    logging.error(full_error_message)
    if response is not None:
        response.failure(f"{error_message}: {exception}")


def log_performance_metrics(response_time_95: float, total_failures: int) -> None:
    """
    Log performance metrics for analysis.

    Args:
        response_time_95: 95th percentile response time in milliseconds
        total_failures: Total number of failed requests
    """
    logging.info(f"95th Percentile Response Time: {response_time_95} ms")
    logging.info(f"Total Failures: {total_failures}")
