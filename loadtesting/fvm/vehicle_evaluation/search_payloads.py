#  This code is protected by intellectual property rights.
#  Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
#  © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.

def tüv_beauftragung():
    return {
        "startRow": 0,
        "endRow": 150,
        "rowGroupCols": [],
        "valueCols": [],
        "pivotCols": [],
        "pivotMode": False,
        "groupKeys": [],
        "filterModel": {
            "vehicle.fleet.isResidualValueMarket": {
                "values": [
                    None,
                    "false"
                ],
                "filterType": "set"
            }
        },
        "sortModel": [
            {
                "sort": "asc",
                "colId": "vehicleRegistration.lastDeRegistrationDate"
            }
        ]
    }


def händler_reklamationen():
    return {
        "startRow": 0,
        "endRow": 150,
        "rowGroupCols": [],
        "valueCols": [],
        "pivotCols": [],
        "pivotMode": False,
        "groupKeys": [],
        "filterModel": {},
        "sortModel": [
            {
                "sort": "asc",
                "colId": "vehicle.fleet.vehicleSentToSalesDate"
            }
        ]
    }


def restwertbörse():
    return {
        "startRow": 0,
        "endRow": 150,
        "rowGroupCols": [],
        "valueCols": [],
        "pivotCols": [],
        "pivotMode": False,
        "groupKeys": [],
        "filterModel": {
            "vehicle.fleet.isResidualValueMarket": {
                "values": [
                    "true"
                ],
                "filterType": "set"
            }
        },
        "sortModel": []
    }
