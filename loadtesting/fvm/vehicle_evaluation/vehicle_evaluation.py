#  This code is protected by intellectual property rights.
#  Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
#  © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.

from locust import task
from utils import check_response, handle_exception, BaseTestUser, VEHICLE_SEARCH_ENDPOINT

from .search_payloads import (
    tüv_beauftragung,
    händler_reklamationen,
    restwertbörse
)


class VehicleEvaluation(BaseTestUser):

    @task
    def read_tüv_beauftragung_view(self):
        response = None
        try:
            with self.client.post(
                    VEHICLE_SEARCH_ENDPOINT,
                    name="Vehicle Evaluation: TÜV Beauftragung (Default)",
                    cookies=self.cookies,
                    headers={"x-trace-id": 'tüv-beauftragung'},
                    json=tüv_beauftragung(),
                    catch_response=True
            ) as response:
                check_response(response)
        except Exception as e:
            handle_exception(e, "Failed to fetch data", response)

    @task
    def read_händler_reklamationen_view(self):
        response = None
        try:
            with self.client.post(
                    VEHICLE_SEARCH_ENDPOINT,
                    name="Vehicle Evaluation: Händler Reklamationen (Default)",
                    cookies=self.cookies,
                    headers={"x-trace-id": 'händler-reklamationen'},
                    json=händler_reklamationen(),
                    catch_response=True
            ) as response:
                check_response(response)
        except Exception as e:
            handle_exception(e, "Failed to fetch data", response)

    @task
    def read_restwertbörse_view(self):
        response = None
        try:
            with self.client.post(
                    VEHICLE_SEARCH_ENDPOINT,
                    name="Vehicle Evaluation: Restwertbörse (Default)",
                    cookies=self.cookies,
                    headers={"x-trace-id": 'restwertbörse'},
                    json=restwertbörse(),
                    catch_response=True
            ) as response:
                check_response(response)
        except Exception as e:
            handle_exception(e, "Failed to fetch data", response)
